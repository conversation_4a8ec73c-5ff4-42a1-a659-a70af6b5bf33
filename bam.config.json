{"$schema": "https://sf-unpkg-src.bytedance.net/@byted-arch-fe/bam-code-generator@1.17.4/config-schema.json", "outDir": "app/bam", "services": {"aime": "flow.agentsphere.nextserver"}, "log": {"aime": {"psm": "flow.agentsphere.nextserver", "version": "1.0.726", "branch": "master", "apis": ["GET /api/agents/v2/showcases", "GET /api/agents/v2/replay/:replay_id", "POST /api/agents/v2/sessions", "GET /api/agents/v2/sessions", "GET /api/agents/v2/sessions/:session_id", "DELETE /api/agents/v2/sessions/:session_id", "POST /api/agents/v2/sessions/:session_id/events", "POST /api/agents/v2/sessions/:session_id/message", "PUT /api/agents/v2/artifacts/:artifact_id", "POST /api/agents/v2/artifacts/:artifact_id/files", "POST /api/agents/v2/artifacts/:artifact_id/upload/stream", "GET /api/agents/v2/artifacts/:artifact_id", "POST /api/agents/v2/artifacts/:artifact_id/upload", "POST /api/agents/v2/artifacts", "GET /api/agents/v2/artifacts/list", "GET /api/agents/v2/debug", "GET /api/agents/v2/sessions/:session_id/old_events", "GET /api/agents/v2/lark/auth/check", "GET /api/agents/v2/lark/auth", "POST /api/agents/v2/replay", "PUT /api/agents/v2/artifacts/:artifact_id/files/:path", "GET /api/agents/v2/artifacts/:artifact_id/raw/:path", "POST /api/agents/v2/share/replay/call_back", "GET /api/agents/v2/features", "GET /api/agents/v2/deployments", "GET /api/agents/v2/deployments/*path", "POST /api/agents/v2/deployments", "POST /api/agents/v2/sessions/collection", "GET /api/agents/v2/sessions/collection/list", "POST /api/agents/v2/sessions/collection/run", "POST /api/agents/v2/lark/send/replay_link", "POST /api/agents/v2/sessions/collection/run/notification", "GET /api/agents/v2/roles", "GET /api/agents/v2/sessions/check", "PATCH /api/agents/v2/sessions/:session_id", "GET /api/agents/v2/trace/session", "POST /api/agents/v2/save_event_key", "POST /api/agents/v2/trace/events", "GET /api/agents/v2/user/:user_name", "POST /api/agents/v2/trace/actions/resume", "GET /api/agents/v2/trace/session/chat", "POST /api/agents/v2/message_with_template", "GET /api/agents/v2/templates/list", "GET /api/agents/v2/trace/session/log", "GET /api/agents/v2/trace/session/agent_steps", "GET /api/agents/v2/templates/:template_id/history_variables", "POST /api/agents/v2/agent", "POST /api/agents/v2/agent/config/version/:agent_config_version_id/deploy", "DELETE /api/agents/v2/prompt/:prompt_id", "POST /api/agents/v2/agent/config/version", "POST /api/agents/v2/prompt/version", "DELETE /api/agents/v2/agent/:agent_id", "PUT /api/agents/v2/prompt/version/:prompt_version_id", "GET /api/agents/v2/agent", "PUT /api/agents/v2/prompt/:prompt_id", "PUT /api/agents/v2/agent/:agent_id", "GET /api/agents/v2/agent/:agent_id", "GET /api/agents/v2/prompt/:prompt_id", "PUT /api/agents/v2/agent/config/version/:agent_config_version_id", "GET /api/agents/v2/agent/config/:agent_config_id", "GET /api/agents/v2/agent/config", "PUT /api/agents/v2/agent/config/:agent_config_id", "DELETE /api/agents/v2/agent/config/:agent_config_id", "GET /api/agents/v2/agent/config/version", "POST /api/agents/v2/prompt", "POST /api/agents/v2/agent/config", "GET /api/agents/v2/prompt/version", "GET /api/agents/v2/prompt", "GET /api/agents/v2/prompt/version/download", "GET /api/agents/v2/agent/config/version/:agent_config_version_id", "GET /api/agents/v2/prompt/version/:prompt_version_id", "GET /api/agents/v2/sessions/partial/list", "GET /api/agents/v2/lark/auth/ticket", "POST /api/agents/v2/artifacts/download/batch", "GET /api/agents/v2/lark/get/lark_url", "POST /api/agents/v2/activity/verify", "GET /api/agents/v2/activity/progress", "PUT /api/agents/v2/user/invitation_code/:invitation_code", "GET /api/agents/v2/features/global", "POST /api/agents/v2/sessions/collection/close", "POST /api/agents/v2/sessions/collection/download", "GET /api/agents/v2/sessions/collection/notification_templates", "POST /api/agents/v2/sessions/collection/list", "GET /api/agents/v2/trace/models", "POST /api/agents/v2/trace/:model/chat/completions", "POST /api/agents/v2/mcp", "POST /api/agents/v2/sessions/collection/notification", "POST /api/agents/v2/mcp/activation", "PUT /api/agents/v2/mcp/update", "POST /api/agents/v2/mcp/list", "POST /api/agents/v2/mcp/validate", "POST /api/agents/v2/trace/actions/suspend", "GET /api/agents/v2/sessions/:session_id/agent_run", "PUT /api/agents/v2/build_in_mcp/update", "POST /api/agents/v2/build_in_mcp/create", "POST /api/agents/v2/templates", "DELETE /api/agents/v2/templates/:template_id", "GET /api/agents/v2/templates/:template_id", "POST /api/agents/v2/templates/:template_id/upload/stream", "POST /api/agents/v2/templates/draft", "PUT /api/agents/v2/templates/:template_id", "GET /api/agents/v2/templates/file/:file_id/raw", "PUT /api/agents/v2/templates/:template_id/experience", "GET /api/agents/v2/templates/:template_id/draft", "POST /api/agents/v2/trace/actions/delete", "GET /api/agents/v2/lark/documents/:document_id/blocks", "GET /api/agents/v2/templates/count", "POST /api/agents/v2/sessions/:session_id/tool_call", "GET /api/agents/v2/trace/session/documents", "POST /api/agents/v2/trace/session/documents/convert_to_lark", "POST /api/agents/v2/templates/:template_id/star", "POST /api/agents/v2/templates/:template_id/share", "DELETE /api/agents/v2/templates/:template_id/star", "DELETE /api/agents/v2/templates/shares/:share_id/user", "POST /api/agents/v2/templates/shares/:share_id/user", "POST /api/agents/v2/user/grant_access", "GET /api/agents/v2/resource/user/permission", "POST /api/agents/v2/trace/events/mcp", "PATCH /api/agents/v2/ops/templates/:template_id", "GET /api/agents/v2/ops/templates", "POST /api/agents/v2/space", "GET /api/agents/v2/user/list/space", "GET /api/agents/v2/space", "GET /api/agents/v2/space/list", "PUT /api/agents/v2/space", "GET /api/agents/v2/space/members", "DELETE /api/agents/v2/space", "POST /api/agents/v2/space/members", "DELETE /api/agents/v2/space/members", "POST /api/agents/v2/datasets/:dataset_id/documents", "DELETE /api/agents/v2/datasets/:dataset_id/documents/:document_id", "GET /api/agents/v2/datasets/:dataset_id/documents/:document_id", "PUT /api/agents/v2/datasets/:dataset_id/documents/:document_id", "PUT /api/agents/v2/datasets/:dataset_id/documents", "GET /api/agents/v2/mentions", "POST /api/agents/v2/templates/shares/form_data", "GET /api/agents/v2/templates/shares/form_data/:id", "GET /api/agents/v3/templates/list", "POST /api/agents/v3/mcp/list", "GET /api/agents/v3/sessions", "GET /api/agents/v3/templates/count", "GET /api/agents/v2/datasets/:dataset_id/lark_documents", "POST /api/agents/v2/datasets/:dataset_id/recommend_documents", "PUT /api/agents/v2/user/settings", "GET /api/agents/v2/user/settings", "GET /api/agents/v2/datasets/:dataset_id/documents/count", "DELETE /api/agents/v2/knowledgeset/:knowledge_set_id", "DELETE /api/agents/v2/knowledge/:knowledge_id", "GET /api/agents/v2/knowledgeset/:knowledge_set_id", "POST /api/agents/v2/knowledgeset", "GET /api/agents/v2/knowledgeset", "POST /api/agents/v2/knowledge", "GET /api/agents/v2/knowledge/:knowledge_id", "PUT /api/agents/v2/knowledgeset/:knowledge_set_id", "GET /api/agents/v2/knowledgeset/metadata/conf", "PUT /api/agents/v2/knowledge/:knowledge_id", "POST /api/agents/v2/knowledgeset/version", "DELETE /api/agents/v2/knowledgeset/version/:knowledge_set_version_id", "POST /api/agents/v2/knowledgeset/version/copy", "PUT /api/agents/v2/knowledgeset/version/:knowledge_set_version_id", "GET /api/agents/v2/knowledgeset/version/:knowledge_set_version_id", "GET /api/agents/v2/knowledgeset/version", "GET /api/agents/v2/internal/knowledge", "POST /api/agents/v3/mcp/pre_validate", "POST /api/agents/v3/templates/mcp/validate", "GET /api/agents/v2/knowledge/enableif/check", "POST /api/agents/v2/agent/config/version/copy", "GET /api/agents/v2/devops/session", "GET /api/agents/v2/devops/session/events", "POST /api/agents/v2/devops/session/debug", "POST /api/agents/v2/devops/session/events_stream", "GET /api/agents/v2/internal/knowledgeset", "GET /api/agents/v2/sessions/:session_id/mcp_details", "POST /api/agents/v3/mcp/cloud_psm_auth", "POST /api/agents/v2/mcp/user_config", "GET /api/agents/v2/project_artifacts/:artifact_key/versions/:version/files_content", "GET /api/agents/v2/project_artifacts/:artifact_key/versions/:version/files", "GET /api/agents/v2/project_artifacts/:artifact_key/revisions/:revision/diff", "POST /api/agents/v2/debug/mock_event_data", "GET /api/agents/v2/ops/sessions/:session_id/trajectory", "POST /api/agents/v2/trace/markdown/convert_to_lark", "POST /api/agents/v2/deploy/bpm/online", "POST /api/agents/v2/deploy/bpm/canary", "POST /api/agents/v2/deploy/bpm/cancel/canary", "POST /api/agents/v2/deploy/bpm/auth", "POST /api/agents/v2/deploy/bpm/close", "GET /api/agents/v2/deploy/process/info", "POST /api/agents/v2/deploy", "GET /api/agents/v2/deploy/:deploy_id", "GET /api/agents/v2/scm/version", "GET /api/agents/v2/icm/version", "POST /api/agents/v2/sessions/:session_id/star", "DELETE /api/agents/v2/sessions/:session_id/star", "POST /api/agents/v2/artifacts/images", "GET /api/agents/v2/deploy/review/user", "GET /api/agents/v2/deploy/list", "PUT /api/agents/v2/code_repos", "POST /api/agents/v2/space/init", "PUT /api/agents/v2/services", "POST /api/agents/v2/code_repos", "POST /api/agents/v2/services", "DELETE /api/agents/v2/datasets/:dataset_id/documents/batch", "DELETE /api/agents/v2/services", "POST /api/agents/v2/notification_message", "DELETE /api/agents/v2/code_repos", "PUT /api/agents/v2/datasets/:dataset_id/documents/batch", "GET /api/agents/v2/meego_space", "GET /api/agents/v2/platform_config", "PUT /api/agents/v2/platform_config", "PUT /api/agents/v2/notification_message_status", "GET /api/agents/v2/code_repo", "GET /api/agents/v2/notification_messages", "DELETE /api/agents/v2/sessions/batch", "GET /api/agents/v2/service", "GET /api/agents/v2/agent/user/sessions", "GET /api/agents/v2/datasets/:dataset_id/task_status", "POST /api/agents/v2/deploy/bits/upsert/agent/version"]}}}