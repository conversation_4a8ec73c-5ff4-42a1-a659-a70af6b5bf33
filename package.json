{"name": "aimo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@arco-design/theme-aime-admin": "^0.0.1", "@arco-design/web-react": "^2.66.1", "@bytecloud/common-lib": "^7.44.0", "@codemirror/autocomplete": "^6.18.6", "@codemirror/basic-setup": "^0.20.0", "@codemirror/commands": "^6.8.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/language": "^6.11.0", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.5", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@microsoft/fetch-event-source": "^2.0.1", "@monaco-editor/react": "^4.7.0", "@nanostores/react": "^0.8.4", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@radix-ui/themes": "^3.2.1", "@tanstack/react-query": "^5.74.4", "@tanstack/react-virtual": "^3.13.6", "@tod-m/materials": "~2.30.3", "@types/dagre": "^0.7.52", "@types/js-yaml": "^4.0.9", "@types/lodash-es": "^4.17.12", "@types/uuid": "^10.0.0", "@xyflow/react": "^12.5.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "codemirror": "^6.0.1", "dagre": "^0.8.5", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.6.3", "js-yaml": "^4.1.0", "lodash-es": "^4.17.21", "lucide-react": "^0.487.0", "nanostores": "^0.11.4", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-diff-viewer": "^3.1.1", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.6.0", "react-icons": "^5.5.0", "react-markdown": "^9.0.1", "recharts": "^3.0.2", "remark-gfm": "^4.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@byted-arch-fe/bam-code-generator": "^1.17.4", "@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.3", "@types/node": "^20.17.30", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "axios": "^1.8.4", "byted-tea-sdk": "^5.3.3", "eslint": "^9.24.0", "eslint-config-next": "15.2.4", "tailwindcss": "^4.1.3", "typescript": "^5.8.3"}}