import React, { ReactNode } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";

interface Column<T> {
  header: string;
  accessor: keyof T | ((data: T) => ReactNode);
  className?: string;
  sticky?: boolean;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading: boolean;
  keyField: keyof T;
  noDataMessage?: string;
  loadingMessage?: string;
  onRowClick?: (row: T) => void;
  rowClassName?: string;
}

export function DataTable<T>({
  data,
  columns,
  loading,
  keyField,
  noDataMessage = "暂无数据",
  loadingMessage = "加载中...",
  onRowClick,
  rowClassName,
}: DataTableProps<T>) {
  return (
    <div className="overflow-x-auto rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            {columns.map((column, index) => (
              <TableHead
                key={index}
                className={cn(
                  column.className,
                  column.sticky && "sticky right-0 bg-white",
                )}
              >
                {column.header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            <TableRow>
              <TableCell colSpan={columns.length} className="text-center">
                {loadingMessage}
              </TableCell>
            </TableRow>
          ) : data.length === 0 ? (
            <TableRow>
              <TableCell colSpan={columns.length} className="text-center">
                {noDataMessage}
              </TableCell>
            </TableRow>
          ) : (
            data.map(item => (
              <TableRow
                key={String(item[keyField])}
                className={cn(rowClassName, onRowClick && "cursor-pointer")}
                onClick={() => onRowClick?.(item)}
              >
                {columns.map((column, index) => (
                  <TableCell
                    key={index}
                    className={cn(
                      column.className,
                      column.sticky && "sticky right-0 bg-white",
                    )}
                  >
                    {typeof column.accessor === "function"
                      ? column.accessor(item)
                      : (item[column.accessor] as ReactNode) ?? "-"}
                  </TableCell>
                ))}
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
