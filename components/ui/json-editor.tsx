"use client";

import { useEffect, useRef, useState } from "react";
import { EditorView, keymap } from "@codemirror/view";
import { EditorState } from "@codemirror/state";
import { json } from "@codemirror/lang-json";
import { defaultKeymap, indentWithTab } from "@codemirror/commands";
import { AlertCircle } from "lucide-react";
import {
  indentUnit,
  indentOnInput,
  bracketMatching,
} from "@codemirror/language";
import { closeBrackets } from "@codemirror/autocomplete";
import { basicSetup } from "codemirror";

interface JSONEditorProps {
  value: string | object;
  onChange: (value: string) => void;
  height?: string;
  showValidation?: boolean;
}

function formatJSON(jsonString: string | object): string {
  try {
    // 如果输入是对象，先转换为字符串
    if (typeof jsonString === 'object') {
      return JSON.stringify(jsonString, null, 2);
    }
    // 如果输入是字符串，尝试解析并重新格式化
    return JSON.stringify(JSON.parse(jsonString), null, 2);
  } catch {
    // 如果解析失败，确保返回字符串
    return typeof jsonString === 'string' ? jsonString : JSON.stringify(jsonString);
  }
}

function validateJSON(jsonString: string | object): {
  isValid: boolean;
  error?: string;
} {
  try {
    // 如果输入是对象，直接返回有效
    if (typeof jsonString === 'object') {
      return { isValid: true };
    }
    // 如果输入是字符串，尝试解析
    JSON.parse(jsonString);
    return { isValid: true };
  } catch (e) {
    return {
      isValid: false,
      error: e instanceof Error ? e.message : "Invalid JSON format",
    };
  }
}

export function JSONEditor({
  value = "{}",
  onChange,
  height = "200px",
  showValidation = true,
}: JSONEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const viewRef = useRef<EditorView | null>(null);
  const [validation, setValidation] = useState<{
    isValid: boolean;
    error?: string;
  }>({ isValid: true });

  useEffect(() => {
    if (!editorRef.current) return;

    const formattedValue = formatJSON(value);
    const state = EditorState.create({
      doc: formattedValue,
      extensions: [
        basicSetup,
        json(),
        indentUnit.of("  "),
        indentOnInput(),
        bracketMatching(),
        closeBrackets(),
        keymap.of([
          ...defaultKeymap,
          indentWithTab,
          {
            key: "Mod-Enter",
            run: view => {
              const content = view.state.doc.toString();
              const formatted = formatJSON(content);
              view.dispatch({
                changes: {
                  from: 0,
                  to: view.state.doc.length,
                  insert: formatted,
                },
              });
              return true;
            },
          },
        ]),
        EditorView.updateListener.of(update => {
          if (update.docChanged) {
            const content = update.state.doc.toString();
            onChange(content);
            setValidation(validateJSON(content));
          }
        }),
        EditorView.theme({
          "&": {
            backgroundColor: "white",
            color: "#333",
            height: "100%",
            overflow: "auto",
          },
          ".cm-scroller": {
            overflow: "auto",
            height: "100%",
          },
          ".cm-content": {
            fontFamily: "monospace",
            fontSize: "14px",
            lineHeight: "1.5",
            padding: "8px 0",
          },
          ".cm-gutters": {
            backgroundColor: "#f5f5f5",
            borderRight: "1px solid #ddd",
            minHeight: "100%",
          },
          ".cm-activeLineGutter": {
            backgroundColor: "#e8e8e8",
          },
          ".cm-activeLine": {
            backgroundColor: "#f5f5f5",
          },
          ".cm-selectionBackground": {
            backgroundColor: "#d4d4d4",
          },
          ".cm-matchingBracket": {
            backgroundColor: "#e8e8e8",
            outline: "1px solid #999",
          },
        }),
      ],
    });

    const view = new EditorView({
      state,
      parent: editorRef.current,
    });

    viewRef.current = view;
    setValidation(validateJSON(value));

    return () => {
      view.destroy();
      viewRef.current = null;
    };
  }, []);

  useEffect(() => {
    if (viewRef.current && value !== viewRef.current.state.doc.toString()) {
      const formatted = formatJSON(value);
      viewRef.current.dispatch({
        changes: {
          from: 0,
          to: viewRef.current.state.doc.length,
          insert: formatted,
        },
      });
      setValidation(validateJSON(value));
    }
  }, [value]);

  return (
    <div className="space-y-2">
      <div
        ref={editorRef}
        className="rounded-md border overflow-hidden"
        style={{ height, display: "flex", flexDirection: "column" }}
      />
      {showValidation && !validation.isValid && (
        <div className="flex items-center text-sm text-red-500">
          <AlertCircle className="h-4 w-4 mr-1" />
          {validation.error}
        </div>
      )}
    </div>
  );
}
