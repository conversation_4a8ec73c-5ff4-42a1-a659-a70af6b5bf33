import type { NextConfig } from "next";
const isProduction = process.env.NODE_ENV === "production";
const nextConfig: NextConfig = {
  /* config options here */
  basePath: "/lab",
  output: "export",
  assetPrefix: isProduction
    ? "https://cdn-tos-cn.bytedance.net/obj/archi/aimo"
    : undefined,
  eslint: {
    ignoreDuringBuilds: true,
  },
   webpack(config, { webpack }) {
    config.plugins.push(
      new webpack.IgnorePlugin({
        // 忽略所有less引用，避免报错
        resourceRegExp: /\.less$/,
      })
    );
    // 必须返回修改后的 config
    return config;
  }
};

export default nextConfig;
