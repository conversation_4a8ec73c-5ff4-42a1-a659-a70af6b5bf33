/**
 * 表达式求值工具
 * 用于解析和执行 enableIf 条件表达式
 */

// 表达式求值函数
export const evaluateExpression = (
  expression: string,
  context: {
    agent?: string;
    variant?: string;
    tools?: string[];
    [key: string]: unknown;
  }
): boolean => {
  // 处理空值或 false
  if (!expression || expression.trim() === 'false') {
    return false;
  }

  // 处理 true
  if (expression.trim() === 'true') {
    return true;
  }

  try {
    // 创建一个安全的求值环境
    const safeEval = (expr: string, ctx: Record<string, unknown>): boolean => {
      // 替换 in 操作符
      let processedExpr = expr;
      
      // 处理 agent in ['value1', 'value2'] 格式
      processedExpr = processedExpr.replace(
        /agent\s+in\s+\[([^\]]+)\]/g,
        (match, values) => {
          const valueList = values
            .split(',')
            .map((v: string) => v.trim().replace(/['"]/g, ''));
          return `(${valueList.map((v: string) => `agent === '${v}'`).join(' || ')})`;
        }
      );
      
      // 处理 variant in ['value1', 'value2'] 格式
      processedExpr = processedExpr.replace(
        /variant\s+in\s+\[([^\]]+)\]/g,
        (match, values) => {
          const valueList = values
            .split(',')
            .map((v: string) => v.trim().replace(/['"]/g, ''));
          return `(${valueList.map((v: string) => `variant === '${v}'`).join(' || ')})`;
        }
      );
      
      // 处理 'tool' in tools 格式
      processedExpr = processedExpr.replace(
        /'([^']+)'\s+in\s+tools/g,
        (match, tool) => {
          return `Array.isArray(tools) && tools.includes('${tool}')`;
        }
      );
      
      // 处理 agent == 'value' 格式
      processedExpr = processedExpr.replace(
        /agent\s*==\s*['"]([^'"]+)['"]/g,
        (match, value) => `agent === '${value}'`
      );
      
      // 处理 agent != 'value' 格式
      processedExpr = processedExpr.replace(
        /agent\s*!=\s*['"]([^'"]+)['"]/g,
        (match, value) => `agent !== '${value}'`
      );
      
      // 处理 variant == 'value' 格式
      processedExpr = processedExpr.replace(
        /variant\s*==\s*['"]([^'"]+)['"]/g,
        (match, value) => `variant === '${value}'`
      );
      
      // 处理 variant != 'value' 格式
      processedExpr = processedExpr.replace(
        /variant\s*!=\s*['"]([^'"]+)['"]/g,
        (match, value) => `variant !== '${value}'`
      );
      
      // 处理 and 和 or 操作符
      processedExpr = processedExpr.replace(/\s+and\s+/g, ' && ');
      processedExpr = processedExpr.replace(/\s+or\s+/g, ' || ');
      
      // 创建一个新的函数来执行表达式
      const func = new Function(
        'agent',
        'variant',
        'tools',
        `return ${processedExpr}`
      );
      
      // 执行函数并返回结果
      return func(
        ctx.agent || '',
        ctx.variant || '',
        ctx.tools || []
      );
    };
    
    return safeEval(expression, context);
  } catch (error) {
    console.error('表达式求值错误:', error, '表达式:', expression);
    // 如果求值失败，默认返回 false
    return false;
  }
};

// 测试表达式求值
export const testExpression = (
  expression: string,
  context: {
    agent?: string;
    variant?: string;
    tools?: string[];
    [key: string]: unknown;
  }
): { result: boolean; error?: string } => {
  try {
    const result = evaluateExpression(expression, context);
    return { result };
  } catch (error) {
    return { 
      result: false, 
      error: error instanceof Error ? error.message : '未知错误' 
    };
  }
};