import { atom, map } from 'nanostores';
import { apiClient } from "@/app/api/request";
import { GetTraceEventsRequest } from '@/app/bam/aime/namespaces/trace';
import { EventSourceMessage } from "@microsoft/fetch-event-source";

// 事件数据定义
export interface EventDataContent {
  event: string;
  offset?: number;
  data?: Record<string, unknown>;
  timestamp?: string;
  [key: string]: unknown;
}

// 事件类型定义
export interface EventData {
  id: string;
  offset: number;
  event: string;
  timestamp: string;
  data: EventDataContent;
  run_id: string;
}

// 步骤数据定义
export interface StepData {
  step_id: string;
  parent_id?: string;
  executor?: string;
  status?: string;
  [key: string]: unknown;
}

// 步骤关系定义
export interface StepRelation {
  id: string;
  executor: string;
  status: string;
  parent_id: string | null;
  children: string[];
  data: StepData;
}

// 定义IndexedDB数据库名称和版本
export const DB_VERSION = 2;
export const EVENTS_STORE = 'events';

// 设置数据过期天数
export const DATA_EXPIRATION_DAYS = 1; // 默认1天后过期

// 数据库元数据存储键名
export const DB_METADATA_KEY = 'aimo_events_db_metadata';

// 检查是否处于调试模式
export function checkDebugMode(): boolean {
  if (typeof window === 'undefined') return false;
  
  try {
    return localStorage.getItem('aimo_debug') === 'true';
  } catch (error) {
    console.error('检查debug模式失败:', error);
    return false;
  }
}

// 调试模式
export const isDebugMode = checkDebugMode();

// 数据库元数据类型
interface DbMetadata {
  name: string;
  runId: string;
  createdAt: number;
  lastAccessedAt: number;
}

// 获取数据库名称
export function getDBName(identifier: string): string {
  return `aimo_events_${identifier}`;
}

// 状态存储
export const eventsStore = map({
  runId: '',
  sessionId: '',
  containerId: '',
  uri: '',
  provider: '',
  isLoading: false,
  error: '',
  showGraph: false,
  selectedEventId: null as string | null,
  selectedEventData: null as EventData | null,
  eventFilter: loadEventFilterFromLocalStorage(), // 从 localStorage 加载过滤器设置
  searchText: '',
});

// 事件数据存储
export const eventListStore = atom<EventData[]>([]);

// 事件类型集合
export const eventTypesStore = atom<Set<string>>(new Set());

// 步骤关系存储
export const stepRelationsStore = atom<Record<string, StepRelation>>({});

// 从 localStorage 加载事件过滤器设置
function loadEventFilterFromLocalStorage(): string[] {
  if (typeof window === 'undefined') return ['step']; // 默认选中 step
  
  try {
    const savedFilter = localStorage.getItem('aimo_event_filter');
    if (savedFilter) {
      return JSON.parse(savedFilter);
    }
  } catch (error) {
    console.error('从 localStorage 加载过滤器失败:', error);
  }
  
  return ['step']; // 默认选中 step
}

// 保存事件过滤器设置到 localStorage
function saveEventFilterToLocalStorage(filter: string[]): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('aimo_event_filter', JSON.stringify(filter));
  } catch (error) {
    console.error('保存过滤器到 localStorage 失败:', error);
  }
}

// 保存数据库元数据
export function saveDbMetadata(runId: string): void {
  if (typeof window === 'undefined') return;
  
  try {
    // 读取现有元数据
    const metadataJson = localStorage.getItem(DB_METADATA_KEY);
    let dbList: DbMetadata[] = [];
    
    if (metadataJson) {
      dbList = JSON.parse(metadataJson);
    }
    
    const dbName = getDBName(runId);
    const now = Date.now();
    
    // 检查是否已存在该数据库记录
    const existingIndex = dbList.findIndex(db => db.name === dbName);
    
    if (existingIndex !== -1) {
      // 更新现有记录的访问时间
      dbList[existingIndex].lastAccessedAt = now;
    } else {
      // 添加新记录
      dbList.push({
        name: dbName,
        runId: runId,
        createdAt: now,
        lastAccessedAt: now
      });
    }
    
    // 保存更新后的元数据
    localStorage.setItem(DB_METADATA_KEY, JSON.stringify(dbList));
  } catch (error) {
    console.error('保存数据库元数据失败:', error);
  }
}

// 获取所有IndexedDB数据库名称
export async function getAllIndexedDBDatabases(): Promise<string[]> {
  if (typeof window === 'undefined' || !window.indexedDB) return [];
  
  if ('databases' in window.indexedDB) {
    try {
      // 使用新API获取所有数据库
      const databases = await window.indexedDB.databases();
      return databases.map(db => db.name as string).filter(name => name?.startsWith('aimo_events_'));
    } catch (error) {
      console.error('获取数据库列表失败:', error);
    }
  }
  
  // 如果新API不可用或失败，返回空数组
  return [];
}

// 重建数据库元数据
export async function rebuildDatabaseMetadata(): Promise<void> {
  if (typeof window === 'undefined') return;
  
  try {
    // 获取所有数据库名称
    const dbNames = await getAllIndexedDBDatabases();
    
    // 构建新的元数据列表
    const now = Date.now();
    const dbList: DbMetadata[] = dbNames.map(name => {
      // 从数据库名称中提取runId
      const runId = name.replace('aimo_events_', '');
      
      return {
        name,
        runId,
        createdAt: now, // 无法知道确切的创建时间，使用当前时间
        lastAccessedAt: now // 设置为当前时间
      };
    });
    
    // 保存到localStorage
    localStorage.setItem(DB_METADATA_KEY, JSON.stringify(dbList));
    console.log(`已重建数据库元数据，共 ${dbList.length} 个数据库`);
  } catch (error) {
    console.error('重建数据库元数据失败:', error);
  }
}

// 清理过期数据库
export async function cleanupExpiredDatabases(): Promise<void> {
  if (typeof window === 'undefined') return;
  
  try {
    // 读取数据库元数据
    let metadataJson = localStorage.getItem(DB_METADATA_KEY);
    
    // 如果元数据不存在，尝试重建
    if (!metadataJson) {
      await rebuildDatabaseMetadata();
      metadataJson = localStorage.getItem(DB_METADATA_KEY);
      
      // 如果重建后仍然没有元数据，直接返回
      if (!metadataJson) return;
    }
    
    const dbList: DbMetadata[] = JSON.parse(metadataJson);
    const now = Date.now();
    const expirationTime = DATA_EXPIRATION_DAYS * 24 * 60 * 60 * 1000; // 转换为毫秒
    const expiredDbs: DbMetadata[] = [];
    const validDbs: DbMetadata[] = [];
    
    // 分离过期和有效的数据库
    dbList.forEach(db => {
      if (now - db.lastAccessedAt > expirationTime) {
        expiredDbs.push(db);
      } else {
        validDbs.push(db);
      }
    });
    
    // 删除过期数据库
    for (const db of expiredDbs) {
      await new Promise<void>((resolve, reject) => {
        const request = indexedDB.deleteDatabase(db.name);
        request.onsuccess = () => {
          console.log(`成功删除过期数据库: ${db.name}, runId: ${db.runId}`);
          resolve();
        };
        request.onerror = () => {
          console.error(`删除数据库失败: ${db.name}`, request.error);
          reject(request.error);
        };
      });
    }
    
    // 更新元数据
    if (expiredDbs.length > 0) {
      localStorage.setItem(DB_METADATA_KEY, JSON.stringify(validDbs));
      console.log(`已清理 ${expiredDbs.length} 个过期数据库`);
    }
  } catch (error) {
    console.error('清理过期数据库失败:', error);
  }
}

// 初始化IndexedDB
export async function initDatabase(): Promise<IDBDatabase> {
  const { runId, sessionId } = eventsStore.get();
  const identifier = runId || sessionId;
  if (!identifier) {
    throw new Error('runId or sessionId is required');
  }
  
  // 保存或更新数据库元数据
  saveDbMetadata(identifier);
  
  // 尝试清理过期数据库
  cleanupExpiredDatabases().catch(err => console.error('清理过期数据库失败:', err));
  
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(getDBName(identifier), DB_VERSION);
    
    request.onerror = () => reject(request.error);
    
    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      
      // 如果存在旧的存储对象，删除它
      if (db.objectStoreNames.contains(EVENTS_STORE)) {
        db.deleteObjectStore(EVENTS_STORE);
      }
      
      // 创建新的存储事件的对象仓库，使用offset作为主键
      const store = db.createObjectStore(EVENTS_STORE, { keyPath: 'offset' });
      store.createIndex('event', 'event', { unique: false });
      store.createIndex('timestamp', 'timestamp', { unique: false });
    };
    
    request.onsuccess = () => resolve(request.result);
  });
}

// 保存事件到IndexedDB
export async function saveEventToIndexedDB(event: EventData): Promise<void> {
  try {
    const db = await initDatabase();
    const transaction = db.transaction(EVENTS_STORE, 'readwrite');
    const store = transaction.objectStore(EVENTS_STORE);
    await new Promise<void>((resolve, reject) => {
      const request = store.put(event);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error('保存事件到IndexedDB失败:', error);
  }
}

// 从IndexedDB加载事件
export async function loadEventsFromIndexedDB(): Promise<EventData[]> {
  try {
    const db = await initDatabase();
    const transaction = db.transaction(EVENTS_STORE, 'readonly');
    const store = transaction.objectStore(EVENTS_STORE);
    
    return new Promise<EventData[]>((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => {
        const events = request.result;
        // 由于使用offset作为主键，已经确保了唯一性，直接按offset排序
        const sortedEvents = events.sort((a, b) => a.offset - b.offset);
        resolve(sortedEvents);
      };
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error('从IndexedDB加载事件失败:', error);
    return [];
  }
}

// 清空IndexedDB中的事件
export async function clearEventsInIndexedDB(): Promise<void> {
  try {
    const db = await initDatabase();
    const transaction = db.transaction(EVENTS_STORE, 'readwrite');
    const store = transaction.objectStore(EVENTS_STORE);
    return new Promise<void>((resolve, reject) => {
      const request = store.clear();
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error('清空IndexedDB事件失败:', error);
  }
}

// 设置查询参数
export function setQueryParams(params: {
  runId?: string;
  sessionId?: string;
  containerId?: string;
  uri?: string;
  provider?: string;
}) {
  eventsStore.setKey('runId', params.runId || '');
  eventsStore.setKey('sessionId', params.sessionId || '');
  eventsStore.setKey('containerId', params.containerId || '');
  eventsStore.setKey('uri', params.uri || '');
  eventsStore.setKey('provider', params.provider || '');
}

// 设置加载状态
export function setLoading(loading: boolean) {
  eventsStore.setKey('isLoading', loading);
}

// 设置错误信息
export function setError(error: string) {
  eventsStore.setKey('error', error);
}

// 设置选中事件
export function setSelectedEvent(eventId: string | null, eventData: EventData | null) {
  eventsStore.setKey('selectedEventId', eventId);
  eventsStore.setKey('selectedEventData', eventData);
}

// 设置事件过滤器
export function setEventFilter(filter: string[]) {
  eventsStore.setKey('eventFilter', filter);
  saveEventFilterToLocalStorage(filter); // 保存到 localStorage
}

// 设置搜索文本
export function setSearchText(text: string) {
  eventsStore.setKey('searchText', text);
}

// 切换流程图显示状态
export function toggleGraph() {
  const currentState = eventsStore.get().showGraph;
  eventsStore.setKey('showGraph', !currentState);
}

// 处理新事件
export function handleNewEvent(data: EventDataContent) {
  if (!data) return;
  
  // 忽略ping事件
  if (data.event === 'ping') return;
  
  // 如果没有offset，直接忽略此事件
  if (data.offset === undefined) {
    console.warn('事件没有offset，已忽略:', data);
    return;
  }
  
  // 创建事件数据对象
  const { runId, sessionId } = eventsStore.get();
  const eventData: EventData = {
    id: crypto.randomUUID(), // 生成唯一ID用于UI操作
    offset: data.offset,
    event: data.event,
    timestamp: data.timestamp || new Date().toISOString(),
    data: data,
    run_id: runId || sessionId // 使用 runId，如果没有则使用 sessionId
  };
  
  // 更新事件列表（去重逻辑）
  const currentEvents = eventListStore.get();
  
  // 检查是否已存在相同offset的事件
  const existingIndex = currentEvents.findIndex(e => e.offset === eventData.offset);
  if (existingIndex !== -1) {
    return;
  } else {
    // 不存在相同offset的事件，添加新事件
    eventListStore.set([...currentEvents, eventData]);
  }
  
  // 保存到IndexedDB
  saveEventToIndexedDB(eventData);
  
  // 处理单个事件，更新事件类型和关系
  processEvents([eventData]);
}

// 启动EventSource连接获取事件
export function startEventSource() {
  const { runId, sessionId, containerId, uri, provider } = eventsStore.get();
  
  // 验证必须的参数：runId 和 sessionId 至少填写一个
  if (!runId && !sessionId) {
    setError('请提供运行ID或会话ID，至少需要填写其中一个');
    return;
  }
  
  setLoading(true);
  setError('');
  
  try {
    // 准备请求参数
    const request: GetTraceEventsRequest = {};
    
    if (runId) request.run_id = runId;
    if (sessionId) request.session_id = sessionId;
    if (containerId) request.container_id = containerId;
    if (uri) request.uri = uri;
    if (provider) request.provider = provider;
    
    // 不再清空当前数据，保留现有缓存数据
    // 初始化事件类型集合（如果为空）
    if (!eventTypesStore.get().size) {
      eventTypesStore.set(new Set());
    }
    
    // 使用apiClient请求SSE数据
    apiClient.GetTraceEvents(request, {
      onmessage: (ev: EventSourceMessage) => {
        if (!ev.data) return;
        
        try {
          const data = JSON.parse(ev.data) as EventDataContent;
          handleNewEvent(data);
          // 收到第一条消息后，设置加载状态为false
          setLoading(false);
        } catch (error) {
          console.error('解析事件数据失败:', error);
        }
      },
      signal: new AbortController().signal
    }).catch(error => {
      setError(`连接事件流失败: ${error.message}`);
      setLoading(false);
    });
  } catch (error) {
    console.error('启动事件源失败:', error);
    setError(`启动事件源失败: ${error instanceof Error ? error.message : String(error)}`);
    setLoading(false);
  }
}

// 根据条件过滤事件
export function getFilteredEvents(): EventData[] {
  const { eventFilter, searchText } = eventsStore.get();
  const events = eventListStore.get();
  
  // 首先确保事件按offset排序
  const sortedEvents = [...events].sort((a, b) => a.offset - b.offset);
  
  // 然后应用过滤条件
  return sortedEvents.filter(event => {
    // 过滤事件类型
    if (eventFilter.length > 0 && !eventFilter.includes(event.event)) {
      return false;
    }
    
    // 过滤搜索文本
    if (searchText && !JSON.stringify(event.data).toLowerCase().includes(searchText.toLowerCase())) {
      return false;
    }
    
    return true;
  });
}

// 处理事件集合，提取事件类型和步骤关系
export function processEvents(events: EventData[]): void {
  if (!events.length) return;
  
  // 从现有存储获取当前值
  const currentEventTypes = eventTypesStore.get();
  const currentRelations = stepRelationsStore.get();
  
  // 用于收集新的事件类型
  const newEventTypes = new Set(currentEventTypes);
  // 复制当前关系对象
  const relations = { ...currentRelations };
  
  // 遍历处理每个事件
  events.forEach(event => {
    // 添加事件类型
    if (event.event && !newEventTypes.has(event.event)) {
      newEventTypes.add(event.event);
    }
    
    // 处理步骤关系
    if (event.event === 'step' && event.data.data) {
      const stepData = event.data.data as StepData;
      if (stepData.step_id) {
        // 更新或创建步骤
        if (!relations[stepData.step_id]) {
          relations[stepData.step_id] = {
            id: stepData.step_id,
            executor: stepData.executor || '',
            status: stepData.status || '',
            parent_id: stepData.parent_id || null,
            children: [],
            data: stepData
          };
        } else {
          // 更新现有步骤信息
          relations[stepData.step_id].executor = stepData.executor || relations[stepData.step_id].executor;
          relations[stepData.step_id].status = stepData.status || relations[stepData.step_id].status;
          relations[stepData.step_id].data = stepData;
        }
        
        // 处理父子关系
        if (stepData.parent_id && stepData.parent_id !== stepData.step_id) {
          if (!relations[stepData.parent_id]) {
            relations[stepData.parent_id] = {
              id: stepData.parent_id,
              executor: '',
              status: '',
              parent_id: null,
              children: [stepData.step_id],
              data: {} as StepData
            };
          } else if (!relations[stepData.parent_id].children.includes(stepData.step_id)) {
            relations[stepData.parent_id].children.push(stepData.step_id);
          }
        }
      }
    }
  });
  
  // 更新事件类型集合和步骤关系
  eventTypesStore.set(newEventTypes);
  stepRelationsStore.set(relations);
} 