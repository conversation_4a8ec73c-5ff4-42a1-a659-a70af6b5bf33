"use client";

import { useEffect, useState, useRef } from "react";
import { useStore } from "@nanostores/react";
import { useSearchParams } from "next/navigation";
import { PageHeader } from "@/app/components/PageHeader";
import { toast } from "sonner";
import {
  eventListStore,
  eventsStore,
  toggleGraph,
  initDatabase,
  loadEventsFromIndexedDB,
  clearEventsInIndexedDB,
  setQueryParams,
  startEventSource,
  saveEventToIndexedDB,
  cleanupExpiredDatabases,
  rebuildDatabaseMetadata,
  isDebugMode,
  processEvents,
  setSelectedEvent,
} from "./store/eventsStore";
import EventList from "./components/EventList";
import EventDetail from "./components/EventDetail";
import ProcessGraph from "./components/ProcessGraph";
import QueryForm from "./components/QueryForm";
import { Button } from "@/components/ui/button";
import {
  ChevronRight,
  ChevronLeft,
  RefreshCw,
  AlertCircle,
  Upload,
  Download,
  ChevronUp,
  ChevronDown,
  Clock,
  Database,
} from "lucide-react";
import { apiClient } from "@/app/api/request";

export default function EventsPage() {
  const events = useStore(eventListStore);
  const { showGraph, error, isLoading } = useStore(eventsStore);
  const [isClient, setIsClient] = useState(false);
  const [isDbInitialized, setIsDbInitialized] = useState(false);
  const [showQueryForm, setShowQueryForm] = useState(false);
  const [hasUserManuallySelected, setHasUserManuallySelected] = useState(false);
  const searchParams = useSearchParams();
  const hasStartedEventSourceRef = useRef(false);

  // 初始化IndexedDB并加载缓存数据，及从URL参数获取查询
  useEffect(() => {
    setIsClient(true);

    const init = async () => {
      try {
        // 从URL获取查询参数
        const runIdParam = searchParams.get("run_id");
        let sessionIdParam = searchParams.get("session_id");
        const containerIdParam = searchParams.get("container_id");
        const uriParam = searchParams.get("uri");
        const providerParam = searchParams.get("provider");
        const offsetParam = searchParams.get("offset");
        const replayIdParam = searchParams.get("replay_id");

        // 如果有 replay_id，优先通过 apiClient.GetTraceSession 获取 session_id
        if (replayIdParam && !sessionIdParam) {
          try {
            const result = await apiClient.GetTraceSession({
              replay_id: replayIdParam,
            });
            if (result && result.session && result.session.id) {
              sessionIdParam = result.session.id;
            } else {
              toast.error("通过 replay_id 获取 session_id 失败");
              return;
            }
          } catch (err) {
            console.error("通过 replay_id 获取 session_id 失败:", err);
            toast.error("通过 replay_id 获取 session_id 失败");
            return;
          }
        }

        // 如果没有 runId 和 sessionId，直接返回
        if (!runIdParam && !sessionIdParam) return;

        // 设置查询参数
        setQueryParams({
          runId: runIdParam || "",
          sessionId: sessionIdParam || "",
          containerId: containerIdParam || "",
          uri: uriParam || "",
          provider: providerParam || "",
        });

        // 初始化数据库
        await initDatabase();
        setIsDbInitialized(true);

        // 先从IndexedDB加载缓存数据
        const cachedEvents = await loadEventsFromIndexedDB();
        if (cachedEvents.length > 0) {
          // 更新事件列表
          eventListStore.set(cachedEvents);

          // 处理事件数据，提取事件类型和步骤关系
          processEvents(cachedEvents);

          // 如果有 offset 参数，设置对应的事件为选中状态
          if (offsetParam) {
            const targetOffset = parseInt(offsetParam, 10);
            if (!isNaN(targetOffset)) {
              const targetEvent = cachedEvents.find(
                event => event.offset === targetOffset,
              );
              if (targetEvent) {
                setSelectedEvent(targetEvent.id, targetEvent);
              }
            }
          }
        }

        // 然后启动EventSource连接获取最新数据
        if (!hasStartedEventSourceRef.current) {
          startEventSource();
          hasStartedEventSourceRef.current = true;
        }
      } catch (error) {
        console.error("初始化失败:", error);
      }
    };

    init();
  }, [searchParams]);

  // 监听事件列表变化，处理 offset 参数选中逻辑（仅在用户未手动选择时）
  useEffect(() => {
    if (!isClient || hasUserManuallySelected) return;

    const offsetParam = searchParams.get("offset");
    if (offsetParam) {
      const targetOffset = parseInt(offsetParam, 10);
      if (!isNaN(targetOffset)) {
        const currentEvents = eventListStore.get();
        const targetEvent = currentEvents.find(
          event => event.offset === targetOffset,
        );
        if (targetEvent) {
          // 检查当前是否已经选中了这个事件
          const currentSelectedId = eventsStore.get().selectedEventId;
          if (currentSelectedId !== targetEvent.id) {
            setSelectedEvent(targetEvent.id, targetEvent);
          }
        }
      }
    }
  }, [events, isClient, searchParams, hasUserManuallySelected]);

  // 清除所有缓存的事件数据
  const handleClearCache = async () => {
    if (!isDbInitialized) return;

    try {
      await clearEventsInIndexedDB();
      eventListStore.set([]);
    } catch (error) {
      console.error("清除缓存失败:", error);
    }
  };

  // 清理过期数据库
  const handleCleanupExpiredDbs = async () => {
    try {
      await cleanupExpiredDatabases();
      toast.success("过期数据库清理完成");
    } catch (error) {
      console.error("清理过期数据库失败:", error);
      toast.error("清理过期数据库失败");
    }
  };

  // 重建数据库元数据
  const handleRebuildMetadata = async () => {
    try {
      await rebuildDatabaseMetadata();
      toast.success("数据库元数据重建完成");
    } catch (error) {
      console.error("重建数据库元数据失败:", error);
      toast.error("重建数据库元数据失败");
    }
  };

  // 切换流程图显示状态
  const handleToggleGraph = () => {
    toggleGraph();
  };

  // 切换查询表单显示状态
  const handleToggleQueryForm = () => {
    setShowQueryForm(!showQueryForm);
  };

  // 处理数据导入
  const handleImportData = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".jsonl,.json";

    input.onchange = async e => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        // 显示加载提示
        const toastId = toast.loading("正在导入数据...");

        const text = await file.text();
        const lines = text.split("\n").filter(line => line.trim());

        // 为导入的数据生成一个新的临时ID
        const tempRunId = `import_${Date.now()}`;

        // 添加格式校验
        let invalidFormatCount = 0;
        const importedEvents = lines
          .map(line => {
            try {
              const parsedEvent = JSON.parse(line);

              // 处理简化格式 {e, o, t, d}
              if (parsedEvent.e !== undefined && parsedEvent.d !== undefined) {
                // 校验数据类型
                if (
                  typeof parsedEvent.o !== "number" ||
                  typeof parsedEvent.e !== "string" ||
                  typeof parsedEvent.t !== "string" ||
                  typeof parsedEvent.d !== "object"
                ) {
                  console.warn("简化格式事件字段类型不正确:", parsedEvent);
                  invalidFormatCount++;
                  return null;
                }

                // 转换为标准格式 - 构建与标准格式一致的data结构
                // 确保data字段包含event和内层data两层
                return {
                  event: parsedEvent.e,
                  offset: parsedEvent.o,
                  timestamp: parsedEvent.t,
                  // 创建与标准格式一致的嵌套结构
                  data: {
                    event: parsedEvent.e,
                    data: parsedEvent.d,
                    offset: parsedEvent.o,
                    timestamp: parsedEvent.t,
                  },
                  id: crypto.randomUUID(),
                  run_id: tempRunId,
                };
              }
              // 处理标准格式或其他格式
              else if (parsedEvent.event && parsedEvent.data) {
                const event = parsedEvent.event;
                const offset = parsedEvent.offset;
                const timestamp = parsedEvent.timestamp;
                let data = parsedEvent.data;

                // 校验必要字段
                if (offset === undefined || !event || !timestamp || !data) {
                  console.warn("事件缺少必要字段:", parsedEvent);
                  invalidFormatCount++;
                  return null;
                }

                // 检查data是否已经有正确的结构(包含event和data子字段)
                if (!data.event || !data.data) {
                  // 如果没有嵌套结构，构建一个
                  data = {
                    event: event,
                    data: data,
                    offset: offset,
                    timestamp: timestamp,
                  };
                }

                return {
                  event: event,
                  offset: offset,
                  timestamp: timestamp,
                  data: data,
                  id: crypto.randomUUID(),
                  run_id: tempRunId,
                };
              }
              // 其他格式，尝试提取字段
              else {
                console.warn("未知格式数据，尝试兼容处理:", parsedEvent);

                // 尝试获取必要字段
                const event = parsedEvent.event || parsedEvent.e;
                const offset =
                  parsedEvent.offset !== undefined
                    ? parsedEvent.offset
                    : parsedEvent.o;
                const timestamp = parsedEvent.timestamp || parsedEvent.t;
                const rawData = parsedEvent.data || parsedEvent.d;

                // 校验必要字段
                if (offset === undefined || !event || !timestamp || !rawData) {
                  console.warn("事件缺少必要字段:", parsedEvent);
                  invalidFormatCount++;
                  return null;
                }

                // 构建标准格式数据
                return {
                  event: event,
                  offset: offset,
                  timestamp: timestamp,
                  // 创建与标准格式一致的嵌套结构
                  data: {
                    event: event,
                    data: rawData,
                    offset: offset,
                    timestamp: timestamp,
                  },
                  id: crypto.randomUUID(),
                  run_id: tempRunId,
                };
              }
            } catch (error) {
              console.error("解析JSONL行失败:", error);
              invalidFormatCount++;
              return null;
            }
          })
          .filter(event => event !== null);

        if (importedEvents.length > 0) {
          try {
            // 设置临时ID作为当前runId
            setQueryParams({
              runId: tempRunId,
              containerId: "",
              uri: "",
              provider: "",
            });

            // 初始化数据库
            await initDatabase();
            setIsDbInitialized(true);

            // 导入新数据并处理
            for (const event of importedEvents) {
              await saveEventToIndexedDB(event);
            }

            // 更新状态
            eventListStore.set(importedEvents);

            // 处理事件数据，提取事件类型和步骤关系
            processEvents(importedEvents);

            if (invalidFormatCount > 0) {
              toast.success(
                `成功导入 ${importedEvents.length} 条事件数据，忽略了 ${invalidFormatCount} 条格式不正确的数据`,
                { id: toastId },
              );
            } else {
              toast.success(`成功导入 ${importedEvents.length} 条事件数据`, {
                id: toastId,
              });
            }
          } catch (error) {
            console.error("保存事件到数据库失败:", error);
            toast.error("导入数据失败：无法保存到数据库", { id: toastId });
          }
        } else {
          toast.error("无效的事件数据格式或文件为空", { id: toastId });
        }
      } catch (error) {
        console.error("导入数据失败:", error);
        toast.error(
          "导入数据失败：" +
            (error instanceof Error ? error.message : "未知错误"),
        );
      }
    };

    input.click();
  };

  // 处理数据导出
  const handleExportData = () => {
    try {
      // 获取当前的runId
      const currentRunId =
        eventsStore.get().runId || eventsStore.get().sessionId || "unknown";

      // 将数据转换为简化格式 {e, o, t, d}
      const eventsData = events.map(event => {
        // 提取原始事件数据，创建简化格式对象
        const exportData = {
          e: event.event,
          o: event.offset,
          t: event.timestamp,
          // 如果data有嵌套结构，使用内层data
          d: event.data?.data ?? event.data,
        };
        return JSON.stringify(exportData) + "\n";
      });

      const blob = new Blob(eventsData, { type: "application/jsonl" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;

      // 使用runId作为文件名的一部分
      const timestamp = new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/:/g, "-");
      a.download = `events-${currentRunId}-${timestamp}.jsonl`;

      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success("导出数据成功");
    } catch (error) {
      console.error("导出数据失败:", error);
      toast.error("导出数据失败");
    }
  };

  // 布局类计算
  const mainContentClass =
    "grid grid-cols-1 md:grid-cols-[1fr_2fr] gap-4 flex-1 overflow-hidden";

  const graphPanelClass = showGraph
    ? "fixed top-0 right-0 h-full w-[55vw] bg-white shadow-lg border-l z-10 transition-all transform translate-x-0"
    : "fixed top-0 right-0 h-full w-[55vw] bg-white shadow-lg border-l z-10 transition-all transform translate-x-full";

  useEffect(() => {
    console.log("当前事件数量:", events?.length);
    console.log(
      "是否有step_id的事件:",
      events?.some(e => e.data?.step_id),
    );
  }, [events]);

  return (
    <div className="flex flex-col h-[calc(100vh-60px)] gap-3">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <PageHeader
          title="事件追踪"
          description="查看系统产生的各种事件记录，包括步骤执行、日志和错误等"
        />
        <Button
          variant="outline"
          size="sm"
          onClick={handleToggleQueryForm}
          className="flex items-center gap-1"
        >
          {showQueryForm ? (
            <>
              <ChevronUp className="h-4 w-4" />
              收起查询
            </>
          ) : (
            <>
              <ChevronDown className="h-4 w-4" />
              展开查询
            </>
          )}
        </Button>
      </div>

      {/* 查询表单 */}
      {showQueryForm && (
        <div className="transition-all duration-300">
          <QueryForm />
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
          <div>{error}</div>
        </div>
      )}

      {/* 工具栏 */}
      {isClient && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearCache}
              className="flex items-center gap-1"
              disabled={events.length === 0}
            >
              <AlertCircle className="h-4 w-4" />
              清除缓存
            </Button>

            {isDebugMode && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCleanupExpiredDbs}
                  className="flex items-center gap-1"
                >
                  <Clock className="h-4 w-4" />
                  清理过期库
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRebuildMetadata}
                  className="flex items-center gap-1"
                >
                  <Database className="h-4 w-4" />
                  重建元数据
                </Button>
              </>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={handleImportData}
              className="flex items-center gap-1"
            >
              <Upload className="h-4 w-4" />
              导入
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleExportData}
              className="flex items-center gap-1"
              disabled={events.length === 0}
            >
              <Download className="h-4 w-4" />
              导出
            </Button>

            <span className="text-sm text-gray-500">
              共 {events.length} 个事件
            </span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleGraph}
            className="flex items-center gap-1"
          >
            {showGraph ? (
              <>
                <ChevronRight className="h-4 w-4" />
                隐藏流程图
              </>
            ) : (
              <>
                <ChevronLeft className="h-4 w-4" />
                查看流程图
              </>
            )}
          </Button>
        </div>
      )}

      {/* 主体内容区 */}
      <div className={mainContentClass}>
        {/* 事件列表 */}
        <div className="border rounded-md overflow-hidden h-full relative">
          {/* 加载提示（仅在事件列表中显示） */}
          {isLoading && (
            <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-10">
              <div className="flex items-center gap-2 bg-white px-4 py-2 rounded-md shadow-sm">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span className="text-sm">正在加载事件数据...</span>
              </div>
            </div>
          )}
          <EventList onManualSelect={() => setHasUserManuallySelected(true)} />
        </div>

        {/* 事件详情 */}
        <div className="border rounded-md overflow-hidden h-full">
          <EventDetail />
        </div>
      </div>

      {/* 流程图面板（可滑入滑出） */}
      <div className={graphPanelClass}>
        <div className="flex items-center justify-between border-b p-2">
          <h3 className="font-medium">步骤流程图</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggleGraph}
            className="p-1 h-8 w-8"
          >
            <ChevronRight className="h-5 w-5" />
          </Button>
        </div>
        <div className="h-[calc(100%-44px)]">
          <div className="w-full h-full" style={{ minHeight: "400px" }}>
            <ProcessGraph />
          </div>
        </div>
      </div>
    </div>
  );
}
