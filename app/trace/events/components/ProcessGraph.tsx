import React, { useCallback, useEffect, useState } from "react";
import {
  ReactFlow,
  useNodesState,
  useEdgesState,
  Background,
  Controls,
  MiniMap,
  Edge,
  Node,
  Handle,
  Position,
  EdgeProps,
  getBezierPath,
  BaseEdge,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { useStore } from "@nanostores/react";
import {
  eventListStore,
  setSelectedEvent,
  stepRelationsStore,
  eventsStore,
} from "../store/eventsStore";

// 定义节点类型
type StepNode = Node & {
  type: "step";
  data: {
    label: string;
    status: string;
    executor: string;
    error?: string;
    isSelected: boolean;
    isCollapsed: boolean;
    hasChildren: boolean;
  };
};

// 定义边类型
type StepEdge = Edge & {
  type: "step";
  animated: boolean;
  style?: React.CSSProperties;
  markerEnd?: string;
};

// 自定义边组件
const StepEdge = ({
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
}: EdgeProps) => {
  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  return (
    <BaseEdge
      path={edgePath}
      style={{
        stroke: "#94a3b8",
        strokeWidth: 2,
        opacity: 0.8,
        ...style,
      }}
      markerEnd={markerEnd}
    />
  );
};

const ProcessGraph = () => {
  const events = useStore(eventListStore);
  const stepRelations = useStore(stepRelationsStore);
  const selectedEventId = useStore(eventsStore).selectedEventId;
  const [nodes, setNodes, onNodesChange] = useNodesState<StepNode>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<StepEdge>([]);
  const [collapsedNodes, setCollapsedNodes] = useState<Set<string>>(new Set());
  const [debugInfo, setDebugInfo] = useState<string>("");

  // 初始化折叠状态
  useEffect(() => {
    if (Object.keys(stepRelations).length > 0) {
      const initialCollapsed = new Set<string>();

      // 先找出所有根节点
      const rootIds = new Set(
        Object.values(stepRelations)
          .filter(relation => !relation.parent_id)
          .map(relation => relation.id),
      );

      // 遍历所有节点，找出需要折叠的节点
      Object.values(stepRelations).forEach(relation => {
        // 跳过根节点
        if (rootIds.has(relation.id)) {
          return;
        }

        // 找出节点的所有祖先节点
        let currentId = relation.parent_id;
        let depth = 1; // 从1开始计数，1表示是根节点的直接子节点

        while (currentId) {
          const parent = stepRelations[currentId];
          if (!parent) break;

          depth++;
          currentId = parent.parent_id;
        }

        // 如果深度大于1（即不是根节点的直接子节点），则折叠
        if (depth > 1) {
          initialCollapsed.add(relation.id);
        }
      });

      setCollapsedNodes(initialCollapsed);
    }
  }, [stepRelations]);

  // 处理步骤事件，构建节点和边
  const processStepEvents = useCallback(() => {
    const stepNodes = new Map<string, StepNode>();
    const stepEdges: StepEdge[] = [];

    // 检查是否有步骤关系数据
    const relationIds = Object.keys(stepRelations);
    if (relationIds.length === 0) {
      setDebugInfo("没有步骤关系数据");
      return;
    }

    setDebugInfo(`找到${relationIds.length}个步骤关系`);

    // 使用stepRelations直接构建节点和边
    const rootIds: string[] = [];

    // 找出所有需要隐藏的节点（包括被折叠节点的所有子节点）
    const hiddenNodes = new Set<string>();
    const findHiddenNodes = (nodeId: string) => {
      const children = stepRelations[nodeId]?.children || [];
      if (collapsedNodes.has(nodeId)) {
        children.forEach(childId => {
          hiddenNodes.add(childId);
          findHiddenNodes(childId);
        });
      } else {
        // 检查每个子节点的祖先是否被折叠
        children.forEach(childId => {
          let currentId = childId;
          let shouldHide = false;

          // 向上查找是否有被折叠的祖先
          while (stepRelations[currentId]?.parent_id) {
            const parentId = stepRelations[currentId].parent_id;
            if (parentId && collapsedNodes.has(parentId)) {
              shouldHide = true;
              break;
            }
            if (parentId) {
              currentId = parentId;
            }
          }

          if (shouldHide) {
            hiddenNodes.add(childId);
            findHiddenNodes(childId);
          }
        });
      }
    };

    // 从所有节点开始查找需要隐藏的节点
    Object.keys(stepRelations).forEach(nodeId => {
      findHiddenNodes(nodeId);
    });

    // 先创建所有节点
    Object.values(stepRelations).forEach(relation => {
      // 检查是否是当前选中的节点
      const isSelected = events.some(
        event =>
          event.event === "step" &&
          event.data.data?.step_id === relation.id &&
          event.id === selectedEventId,
      );

      const isCollapsed = collapsedNodes.has(relation.id);

      // 如果节点需要隐藏，跳过创建
      if (hiddenNodes.has(relation.id)) {
        return;
      }

      stepNodes.set(relation.id, {
        id: relation.id,
        type: "step",
        position: { x: 0, y: 0 },
        data: {
          label: relation.executor || "",
          status: relation.status || "",
          executor: relation.executor || "",
          error:
            typeof relation.data.error === "string"
              ? relation.data.error
              : undefined,
          isSelected,
          isCollapsed,
          hasChildren: (relation.children || []).length > 0,
        },
      });

      // 找出根节点
      if (!relation.parent_id) {
        rootIds.push(relation.id);
      }

      // 如果节点未折叠，才创建子节点的边
      if (!isCollapsed) {
        relation.children.forEach(childId => {
          if (stepRelations[childId] && !hiddenNodes.has(childId)) {
            stepEdges.push({
              id: `${relation.id}-${childId}`,
              source: relation.id,
              target: childId,
              type: "step",
              animated: true,
            });
          }
        });
      }
    });

    if (rootIds.length === 0) {
      setDebugInfo("没有找到根节点");
      return;
    }

    setDebugInfo(`找到${rootIds.length}个根节点和${stepEdges.length}条边`);

    // 布局常量
    const nodeWidth = 180;
    const nodeHeight = 120;
    const horizontalSpacing = 250; // 父子节点之间的水平间距
    const verticalSpacing = 50; // 兄弟节点之间的垂直间距

    // 使用 DFS 计算每个节点子树所需的高度
    const getSubtreeHeight = (
      nodeId: string,
      collapsedNodes: Set<string>,
      memo = new Map<string, number>(),
    ): number => {
      if (memo.has(nodeId)) return memo.get(nodeId)!;

      // 如果节点被折叠，只返回节点自身高度
      if (collapsedNodes.has(nodeId)) {
        memo.set(nodeId, nodeHeight);
        return nodeHeight;
      }

      const children = stepRelations[nodeId]?.children || [];
      if (children.length === 0) {
        memo.set(nodeId, nodeHeight);
        return nodeHeight;
      }

      // 子树高度 = 所有子节点高度之和 + 子节点间垂直间距
      const height =
        children.reduce(
          (sum, childId) =>
            sum +
            getSubtreeHeight(childId, collapsedNodes, memo) +
            verticalSpacing,
          0,
        ) - verticalSpacing; // 减去最后一个多余的间距

      memo.set(nodeId, Math.max(height, nodeHeight)); // 确保至少有节点自身的高度
      return memo.get(nodeId)!;
    };

    // 布局函数 - 思维导图风格（父子水平展开，兄弟垂直排列）
    const layoutNode = (
      nodeId: string,
      x: number,
      y: number,
      availableHeight: number,
      collapsedNodes: Set<string>,
      memo = new Map<string, { x: number; y: number }>(),
    ): void => {
      if (memo.has(nodeId)) return;

      const node = stepNodes.get(nodeId);
      if (!node) return;

      // 设置当前节点位置
      node.position = { x, y: y + availableHeight / 2 - nodeHeight / 2 };
      memo.set(nodeId, node.position);

      // 如果节点被折叠，不布局子节点
      if (collapsedNodes.has(nodeId)) return;

      const children = stepRelations[nodeId]?.children || [];
      if (children.length === 0) return;

      // 计算子节点的X坐标（所有子节点都在父节点右侧同一水平线上）
      const childX = x + nodeWidth + horizontalSpacing;

      // 计算第一个子节点的Y起始位置
      let currentY = y;

      // 为每个子节点及其子树分配空间并设置位置
      children.forEach(childId => {
        // 计算该子节点子树所需的高度
        const subtreeHeight = getSubtreeHeight(childId, collapsedNodes);

        // 布局该子节点及其子树
        layoutNode(
          childId,
          childX,
          currentY,
          subtreeHeight,
          collapsedNodes,
          memo,
        );

        // 更新下一个子节点的Y起始位置
        currentY += subtreeHeight + verticalSpacing;
      });
    };

    // 计算所有根节点的总高度
    const totalHeight =
      rootIds.reduce(
        (sum, rootId) =>
          sum + getSubtreeHeight(rootId, collapsedNodes) + verticalSpacing,
        0,
      ) - verticalSpacing; // 减去最后多余的间距

    // 从根节点开始布局
    let currentY = -totalHeight / 2; // 从画布中央开始
    rootIds.forEach(rootId => {
      const rootHeight = getSubtreeHeight(rootId, collapsedNodes);
      layoutNode(rootId, -400, currentY, rootHeight, collapsedNodes); // 根节点放在左侧
      currentY += rootHeight + verticalSpacing;
    });

    // 设置节点和边
    setNodes(Array.from(stepNodes.values()));
    setEdges(stepEdges);
  }, [events, stepRelations, selectedEventId, collapsedNodes]);

  // 处理节点点击
  const onNodeClick = useCallback(
    (event: React.MouseEvent, node: StepNode) => {
      // 找到对应的事件
      const stepEvent = events.find(
        event => event.event === "step" && event.data.data?.step_id === node.id,
      );

      if (stepEvent) {
        setSelectedEvent(stepEvent.id, stepEvent);
      } else {
        console.error(`没有找到节点 ${node.id} 对应的事件`);
      }
    },
    [events],
  );

  // 处理节点折叠/展开
  const toggleNodeCollapse = useCallback((nodeId: string) => {
    setCollapsedNodes(prev => {
      const next = new Set(prev);
      if (next.has(nodeId)) {
        next.delete(nodeId);
      } else {
        next.add(nodeId);
      }
      return next;
    });
  }, []);

  useEffect(() => {
    processStepEvents();
  }, [processStepEvents]);

  // 定义节点和边的样式
  const nodeTypes = {
    step: ({ data, id }: { data: StepNode["data"]; id: string }) => {
      const getStatusColor = (status: string) => {
        switch (status) {
          case "created":
            return "#94a3b8";
          case "running":
            return "#3b82f6";
          case "completed":
            return "#22c55e";
          case "failed":
            return "#ef4444";
          default:
            return "#94a3b8";
        }
      };

      return (
        <div
          className={`px-4 py-2 shadow-lg rounded-lg border cursor-pointer hover:shadow-md transition-shadow relative ${
            data.isSelected ? "ring-2 ring-blue-500" : ""
          }`}
          style={{
            borderColor: getStatusColor(data.status),
            backgroundColor: data.isSelected ? "#f0f9ff" : "white",
          }}
        >
          <Handle
            type="target"
            position={Position.Left}
            className="!bg-gray-400 hover:!bg-gray-600 transition-colors"
          />
          <div className="font-medium">{data.executor}</div>
          <div className="text-sm text-gray-500">{data.status}</div>
          {data.error && (
            <div className="text-xs text-red-500 mt-1">{data.error}</div>
          )}
          {data.hasChildren && (
            <div
              className="absolute -right-3 top-1/2 -translate-y-1/2 w-6 h-6 flex items-center justify-center text-white bg-blue-500 rounded-full cursor-pointer hover:bg-blue-600 active:bg-blue-700 transition-all duration-150 shadow-md hover:shadow-lg active:shadow z-10 group"
              onClick={e => {
                e.stopPropagation();
                toggleNodeCollapse(id);
              }}
              title={data.isCollapsed ? "展开" : "折叠"}
            >
              <div className="transform transition-transform duration-200 group-hover:scale-110 group-active:scale-95">
                {data.isCollapsed ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    className="w-4 h-4"
                  >
                    <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    className="w-4 h-4"
                  >
                    <path d="M6.75 9.25a.75.75 0 000 1.5h6.5a.75.75 0 000-1.5h-6.5z" />
                  </svg>
                )}
              </div>
            </div>
          )}
          <Handle
            type="source"
            position={Position.Right}
            className="!bg-gray-400 hover:!bg-gray-600 transition-colors"
          />
        </div>
      );
    },
  };

  const edgeTypes = {
    step: StepEdge,
  };

  return (
    <div className="w-full h-full">
      {debugInfo && (
        <div className="absolute top-2 left-2 z-10 bg-white bg-opacity-80 p-2 rounded text-xs">
          {debugInfo}
        </div>
      )}
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onNodeClick={onNodeClick}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        attributionPosition="bottom-right"
      >
        <Background />
        <Controls />
        <MiniMap />
      </ReactFlow>
    </div>
  );
};

export default ProcessGraph;
