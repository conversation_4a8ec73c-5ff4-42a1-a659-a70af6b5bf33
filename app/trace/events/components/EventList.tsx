"use client";

import { useRef, useState, useMemo, useEffect, forwardRef } from "react";
import { useVirtualizer } from "@tanstack/react-virtual";
import { useStore } from "@nanostores/react";
import {
  eventListStore,
  eventsStore,
  EventData,
  getFilteredEvents,
  setSelectedEvent,
  eventTypesStore,
  setEventFilter,
  setSearchText,
  StepData,
} from "../store/eventsStore";
import { Search, X, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface ToolData {
  description: string;
  error: string;
  id: string;
  inputs: {
    arguments: string;
    base_url: string;
    name: string;
  };
  outputs: {
    attachments: null;
    image_contents: unknown[];
    text_contents: string[];
  };
  status: string;
  step_id: string;
  tool: string;
}

// 如果项目中没有这些组件，我们需要创建它们
// 临时使用简化版的DropdownMenu组件
const DropdownMenu = ({
  children,
}: {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children: React.ReactNode;
}) => {
  return <div className="relative">{children}</div>;
};

const DropdownMenuTrigger = ({
  children,
}: {
  asChild?: boolean;
  children: React.ReactNode;
}) => {
  return <div>{children}</div>;
};

const DropdownMenuContent = forwardRef<
  HTMLDivElement,
  {
    className?: string;
    align?: string;
    children: React.ReactNode;
  }
>(({ className, children }, ref) => {
  return (
    <div
      ref={ref}
      className={`absolute right-0 mt-2 py-2 w-48 bg-white rounded-md shadow-lg z-10 ${className}`}
    >
      {children}
    </div>
  );
});

DropdownMenuContent.displayName = "DropdownMenuContent";

const DropdownMenuCheckboxItem = ({
  checked,
  onCheckedChange,
  children,
}: {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  children: React.ReactNode;
}) => {
  return (
    <div
      className="px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 flex items-center"
      onClick={() => onCheckedChange(!checked)}
    >
      <input type="checkbox" checked={checked} readOnly className="mr-2" />
      {children}
    </div>
  );
};

export default function EventList({
  showFilter = true,
  // 新增的外部数据源 props
  externalEvents,
  externalEventTypes,
  externalSelectedEventId,
  externalEventFilter,
  externalSearchText,
  onExternalEventClick,
  onExternalFilterChange,
  onExternalSearchChange,
  onManualSelect,
}: {
  showFilter?: boolean;
  // 外部数据源 props（可选）
  externalEvents?: EventData[];
  externalEventTypes?: Set<string>;
  externalSelectedEventId?: string | null;
  externalEventFilter?: string[];
  externalSearchText?: string;
  onExternalEventClick?: (event: EventData) => void;
  onExternalFilterChange?: (filter: string[]) => void;
  onExternalSearchChange?: (text: string) => void;
  onManualSelect?: () => void;
}) {
  // 使用外部数据源或默认的 store
  const storeEvents = useStore(eventListStore);
  const storeEventTypes = useStore(eventTypesStore);
  const storeState = useStore(eventsStore);

  const events = externalEvents ?? storeEvents;
  const eventTypes = externalEventTypes ?? storeEventTypes;
  const selectedEventId = externalSelectedEventId ?? storeState.selectedEventId;
  const eventFilter = externalEventFilter ?? storeState.eventFilter;
  const searchText = externalSearchText ?? storeState.searchText;

  const [dropdownOpen, setDropdownOpen] = useState(false);

  const parentRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // 添加点击外部关闭下拉菜单的功能
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownOpen &&
        dropdownRef.current &&
        buttonRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownRef, buttonRef, dropdownOpen]);

  // 过滤事件
  const filteredEvents = useMemo(() => {
    if (externalEvents) {
      // 使用外部过滤逻辑
      const sortedEvents = [...events].sort((a, b) => a.offset - b.offset);
      return sortedEvents.filter(event => {
        // 过滤事件类型
        if (eventFilter.length > 0 && !eventFilter.includes(event.event)) {
          return false;
        }
        // 过滤搜索文本
        if (
          searchText &&
          !JSON.stringify(event.data)
            .toLowerCase()
            .includes(searchText.toLowerCase())
        ) {
          return false;
        }
        return true;
      });
    } else {
      // 使用默认的过滤逻辑
      return getFilteredEvents();
    }
  }, [events, eventFilter, searchText, externalEvents]);

  // 虚拟列表实现
  const virtualizer = useVirtualizer({
    count: filteredEvents.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 75, // 更新行高以适应新增内容
    overscan: 10, // 额外渲染的行数，提高滚动体验
  });

  // 检查是否有某些事件类型被筛选
  const hasActiveFilters = eventFilter.length > 0;

  // 事件类型选项（排序后）
  const eventTypeOptions = useMemo(
    () => Array.from(eventTypes).sort(),
    [eventTypes],
  );

  // 处理点击事件项
  const handleEventClick = (event: EventData) => {
    // 标记用户已手动选择事件
    if (onManualSelect) {
      onManualSelect();
    }

    if (onExternalEventClick) {
      onExternalEventClick(event);
    } else {
      setSelectedEvent(event.id, event);
    }
  };

  // 处理事件类型过滤变化
  const handleFilterChange = (checked: boolean, type: string) => {
    let newFilter: string[];
    if (checked) {
      // 添加到过滤器
      newFilter = [...eventFilter, type];
    } else {
      // 从过滤器中移除
      newFilter = eventFilter.filter(t => t !== type);
    }

    if (onExternalFilterChange) {
      onExternalFilterChange(newFilter);
    } else {
      setEventFilter(newFilter);
    }
  };

  // 清除所有过滤器
  const clearFilters = () => {
    const emptyFilter: string[] = [];
    if (onExternalFilterChange) {
      onExternalFilterChange(emptyFilter);
    } else {
      setEventFilter(emptyFilter);
    }
  };

  // 处理搜索输入
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSearchText = e.target.value;
    if (onExternalSearchChange) {
      onExternalSearchChange(newSearchText);
    } else {
      setSearchText(newSearchText);
    }
  };

  // 清除搜索
  const clearSearch = () => {
    if (onExternalSearchChange) {
      onExternalSearchChange("");
    } else {
      setSearchText("");
    }
  };

  // 计算事件类型的标签颜色
  const getEventTagColor = (eventType: string) => {
    switch (eventType) {
      case "step":
        return "bg-blue-50 text-blue-700 border border-blue-200 font-medium";
      case "log":
        return "bg-green-50 text-green-700 border border-green-200 font-medium";
      case "error":
        return "bg-red-50 text-red-700 border border-red-200 font-medium";
      case "observation":
        return "bg-purple-50 text-purple-700 border border-purple-200 font-medium";
      case "tool":
        return "bg-amber-50 text-amber-700 border border-amber-200 font-medium";
      case "think":
        return "bg-indigo-50 text-indigo-700 border border-indigo-200 font-medium";
      case "message":
        return "bg-sky-50 text-sky-700 border border-sky-200 font-medium";
      default:
        return "bg-slate-50 text-slate-700 border border-slate-200 font-medium";
    }
  };

  // 截断文本的辅助函数
  const truncateText = (text: string, maxLength: number = 25) => {
    if (!text) return "未知";
    return text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;
  };

  useEffect(() => {
    if (selectedEventId) {
      const index = filteredEvents.findIndex(e => e.id === selectedEventId);
      if (index !== -1) {
        virtualizer.scrollToIndex(index, {
          behavior: "smooth",
        });
      }
    }
  }, [selectedEventId, filteredEvents, virtualizer]);

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* 搜索和过滤工具栏 */}
      <div className="p-4 border-b flex items-center gap-2 flex-wrap event-list-toolbar">
        <div className="relative flex-1 min-w-[200px]">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="搜索事件..."
            className="pl-8 pr-8"
            value={searchText}
            onChange={handleSearchChange}
          />
          {searchText && (
            <button
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              onClick={clearSearch}
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        {showFilter && (
          <DropdownMenu>
            <DropdownMenuTrigger>
              <Button
                ref={buttonRef}
                variant="outline"
                size="sm"
                className={cn(
                  "flex items-center gap-1",
                  hasActiveFilters &&
                    "bg-blue-50 text-blue-600 border-blue-200",
                )}
                onClick={() => setDropdownOpen(!dropdownOpen)}
              >
                <Filter className="h-4 w-4" />
                过滤
                {hasActiveFilters && (
                  <Badge
                    variant="secondary"
                    className="ml-1 bg-blue-100 text-blue-800"
                  >
                    {eventFilter.length}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            {dropdownOpen && (
              <DropdownMenuContent ref={dropdownRef} className="w-[200px]">
                {hasActiveFilters && (
                  <div className="px-2 py-1.5">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start text-blue-600"
                      onClick={clearFilters}
                    >
                      清除所有过滤
                    </Button>
                  </div>
                )}
                {eventTypeOptions.map(type => (
                  <DropdownMenuCheckboxItem
                    key={type}
                    checked={eventFilter.includes(type)}
                    onCheckedChange={checked =>
                      handleFilterChange(checked, type)
                    }
                  >
                    {type}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            )}
          </DropdownMenu>
        )}
      </div>

      {/* 事件列表 */}
      <div ref={parentRef} className="flex-1 overflow-auto" data-virtual-list>
        <div
          style={{
            height: `${virtualizer.getTotalSize()}px`,
            width: "100%",
            position: "relative",
          }}
        >
          {virtualizer.getVirtualItems().map(virtualItem => {
            const event = filteredEvents[virtualItem.index];
            return (
              <div
                key={event.id}
                data-event-id={event.id}
                className={cn(
                  "absolute top-0 left-0 w-full p-3 border-b cursor-pointer hover:bg-gray-50 transition-colors",
                  selectedEventId === event.id && "bg-blue-50 border-blue-200",
                )}
                style={{
                  height: `${virtualItem.size}px`,
                  transform: `translateY(${virtualItem.start}px)`,
                }}
                onClick={() => handleEventClick(event)}
              >
                <div className="flex flex-col">
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center gap-2 flex-wrap">
                      <Badge
                        className={cn(
                          getEventTagColor(event.event),
                          "px-2 py-0.5 text-xs leading-5 shadow-sm",
                        )}
                      >
                        {event.event}
                      </Badge>
                      {event.data?.data && (
                        <>
                          {event.event === "step" && (
                            <span
                              className="text-xs bg-slate-50 text-slate-600 px-2 py-0.5 rounded-full max-w-[150px] truncate hover:bg-slate-100 transition-colors border border-slate-200/60"
                              title={
                                (event.data.data as StepData)?.executor ||
                                "未知"
                              }
                            >
                              {truncateText(
                                (event.data.data as StepData)?.executor ||
                                  "未知",
                                20,
                              )}
                            </span>
                          )}
                          {event.event === "tool" && (
                            <span
                              className="text-xs bg-slate-50 text-slate-600 px-2 py-0.5 rounded-full max-w-[150px] truncate hover:bg-slate-100 transition-colors border border-slate-200/60"
                              title={
                                (event.data.data as unknown as ToolData)
                                  ?.tool || "未知"
                              }
                            >
                              {truncateText(
                                (event.data.data as unknown as ToolData)
                                  ?.tool || "未知",
                                20,
                              )}
                            </span>
                          )}
                          {(event.data.data as { status?: string })?.status && (
                            <span
                              className={cn(
                                "text-xs px-2 py-0.5 rounded-full",
                                (event.data.data as { status?: string })
                                  .status === "success"
                                  ? "bg-green-50 text-green-600 border border-green-200/60"
                                  : (event.data.data as { status?: string })
                                      .status === "error"
                                  ? "bg-red-50 text-red-600 border border-red-200/60"
                                  : "bg-slate-50 text-slate-500 border border-slate-200/60",
                              )}
                            >
                              {(event.data.data as { status?: string }).status}
                            </span>
                          )}
                        </>
                      )}
                    </div>
                    <div className="flex items-center text-xs text-gray-400 whitespace-nowrap">
                      <span>
                        {new Date(event.timestamp).toLocaleTimeString()}
                      </span>
                      {event.offset !== undefined && (
                        <span className="ml-1 font-mono opacity-70">
                          #{event.offset}
                        </span>
                      )}
                    </div>
                  </div>
                  <div
                    className="text-sm text-gray-700 overflow-hidden text-ellipsis whitespace-nowrap"
                    title={JSON.stringify(event.data)}
                  >
                    {JSON.stringify(event.data).length > 100
                      ? JSON.stringify(event.data).substring(0, 100) + "..."
                      : JSON.stringify(event.data)}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* 事件数量统计 */}
      <div className="p-2 text-xs text-gray-500 border-t">
        显示 {filteredEvents.length} 个事件（共 {events.length} 个）
      </div>
    </div>
  );
}
