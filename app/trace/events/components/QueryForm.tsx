"use client";

import { useState } from "react";
import { useStore } from "@nanostores/react";
import {
  eventsStore,
  setQueryParams,
  startEventSource,
  eventListStore,
  eventTypesStore,
  stepRelationsStore,
} from "../store/eventsStore";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PlayIcon, RefreshCw } from "lucide-react";

type IdType = "runId" | "sessionId";

export default function QueryForm() {
  const { runId, sessionId, containerId, uri, provider, isLoading } =
    useStore(eventsStore);

  // 确定初始的ID类型
  const getInitialIdType = (): IdType => {
    if (runId) return "runId";
    if (sessionId) return "sessionId";
    return "runId"; // 默认为运行ID
  };

  const [idType, setIdType] = useState<IdType>(getInitialIdType());
  const [formData, setFormData] = useState({
    runId: runId || "",
    sessionId: sessionId || "",
    containerId: containerId || "",
    uri: uri || "",
    provider: provider || "",
  });

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // 处理ID类型变化
  const handleIdTypeChange = (value: IdType) => {
    setIdType(value);
  };

  // 获取当前选择类型的值
  const getCurrentIdValue = () => {
    return idType === "runId" ? formData.runId : formData.sessionId;
  };

  // 处理ID输入变化
  const handleIdInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (idType === "runId") {
      setFormData(prev => ({ ...prev, runId: value }));
    } else {
      setFormData(prev => ({ ...prev, sessionId: value }));
    }
  };

  // 提交查询
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 验证必须填写ID值
    const currentValue = getCurrentIdValue();
    if (!currentValue) {
      alert(`请填写${idType === "runId" ? "运行 ID" : "会话 ID"}`);
      return;
    }

    // 如果查询参数变更，只清空内存中的数据，不清空IndexedDB
    if (
      formData.runId !== runId ||
      formData.sessionId !== sessionId ||
      formData.containerId !== containerId ||
      formData.uri !== uri ||
      formData.provider !== provider
    ) {
      // 只清空内存中的数据
      eventListStore.set([]);
      eventTypesStore.set(new Set());
      stepRelationsStore.set({});

      // 不再清空IndexedDB缓存
      // await clearEventsInIndexedDB();
    }

    // 设置查询参数
    setQueryParams({
      runId: formData.runId,
      sessionId: formData.sessionId,
      containerId: formData.containerId,
      uri: formData.uri,
      provider: formData.provider,
    });

    // 启动事件源
    startEventSource();
  };

  // 获取当前选择类型的标签和占位符
  const getIdLabel = () => {
    return idType === "runId" ? "运行 ID" : "会话 ID";
  };

  const getIdPlaceholder = () => {
    return idType === "runId" ? "输入运行 ID" : "输入会话 ID";
  };

  return (
    <Card>
      <CardContent className="p-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {getIdLabel()} <span className="text-red-500">*</span>
              </label>
              <div className="flex gap-2">
                <Select value={idType} onValueChange={handleIdTypeChange}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="runId">运行 ID</SelectItem>
                    <SelectItem value="sessionId">会话 ID</SelectItem>
                  </SelectContent>
                </Select>
                <Input
                  placeholder={getIdPlaceholder()}
                  value={getCurrentIdValue()}
                  onChange={handleIdInputChange}
                  className="flex-1"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="containerId" className="text-sm font-medium">
                容器 ID <span className="text-gray-400">(可选)</span>
              </label>
              <Input
                id="containerId"
                name="containerId"
                placeholder="输入容器 ID"
                value={formData.containerId}
                onChange={handleInputChange}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="uri" className="text-sm font-medium">
                URI <span className="text-gray-400">(可选)</span>
              </label>
              <Input
                id="uri"
                name="uri"
                placeholder="输入 URI"
                value={formData.uri}
                onChange={handleInputChange}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="provider" className="text-sm font-medium">
                提供者 <span className="text-gray-400">(可选)</span>
              </label>
              <Input
                id="provider"
                name="provider"
                placeholder="输入提供者"
                value={formData.provider}
                onChange={handleInputChange}
              />
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button
              type="submit"
              disabled={isLoading || !getCurrentIdValue()}
              className="gap-1"
            >
              {isLoading ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <PlayIcon className="h-4 w-4" />
              )}
              {isLoading ? "加载中..." : "开始查询"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
