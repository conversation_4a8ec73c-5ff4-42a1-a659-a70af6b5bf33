"use client";

import { useState } from "react";
import { useSearchParams } from "next/navigation";
import { useStore } from "@nanostores/react";
import { eventsStore, type EventData } from "../store/eventsStore";
import {
  FileJson,
  Copy,
  Check,
  Calendar,
  Wrench,
  ChevronRight,
  ChevronDown,
  Eye,
  EyeOff,
  ExternalLink,
  Bug,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { type ReactNode } from "react";
import { apiClient } from "@/app/api/request";
import { v4 as uuid } from "uuid";
import { parseRoleContentFromTags } from "@/app/playground/chat2/utils";
import { toast } from "sonner";
import { CHAT_DATA_STORAGE_KEY, genChatData, CustomChatData } from "@/app/store/playground";

export default function EventDetail({
  externalSelectedEventData,
}: {
  externalSelectedEventData?: EventData | null;
}) {
  const searchParams = useSearchParams();
  const storeState = useStore(eventsStore);
  const selectedEventData =
    externalSelectedEventData ?? storeState.selectedEventData;
  const [copied, setCopied] = useState(false);
  const [isEscaped, setIsEscaped] = useState(false);
  const [collapsedPaths, setCollapsedPaths] = useState<string[]>([]);

  // 格式化数字，处理精度问题
  const formatNumber = (num: number): string => {
    // 如果是整数，直接返回
    if (Number.isInteger(num)) return String(num);

    // 处理小数精度
    const str = num.toString();
    if (str.includes("e")) {
      // 处理科学计数法
      return Number(num.toFixed(6)).toString();
    }

    // 处理普通小数
    const parts = str.split(".");
    if (parts.length === 2) {
      // 移除尾部多余的0
      const decimalPart = parts[1].replace(/0+$/, "");
      // 如果小数部分全是0，返回整数部分
      if (!decimalPart) return parts[0];
      // 否则保留有效的小数部分
      return `${parts[0]}.${decimalPart}`;
    }

    return str;
  };

  const isCollapsed = (path: string) => collapsedPaths.includes(path);

  const toggleCollapse = (path: string) => {
    if (isCollapsed(path)) {
      setCollapsedPaths(collapsedPaths.filter((p) => p !== path));
    } else {
      setCollapsedPaths([...collapsedPaths, path]);
    }
  };

  // 复制原始JSON数据
  const copyJsonData = () => {
    if (!selectedEventData) return;
    const jsonString = JSON.stringify(selectedEventData.data);
    navigator.clipboard.writeText(jsonString).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  const toggleEscapeMode = () => {
    setIsEscaped(!isEscaped);
  };

  const renderJsonValue = (
    value: unknown,
    path: string = "",
    indent: number = 0
  ): ReactNode => {
    const renderCollapseButton = (isCollapsed: boolean, path: string) => (
      <button
        onClick={(e) => {
          e.stopPropagation();
          toggleCollapse(path);
        }}
        className="absolute -left-4 top-0.5 p-0.5 hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100 transition-opacity"
      >
        {isCollapsed ? (
          <ChevronRight className="h-3 w-3 text-gray-600" />
        ) : (
          <ChevronDown className="h-3 w-3 text-gray-600" />
        )}
      </button>
    );

    if (value === null) return <span className="text-rose-600">null</span>;
    if (typeof value === "boolean")
      return <span className="text-violet-600">{String(value)}</span>;
    if (typeof value === "number")
      return <span className="text-sky-600">{formatNumber(value)}</span>;

    if (typeof value === "string") {
      let displayValue = value;
      if (isEscaped) {
        displayValue = value
          .replace(/\n/g, "\\n")
          .replace(/\t/g, "\\t")
          .replace(/"/g, '\\"')
          .replace(/'/g, "\\'");
      } else {
        try {
          displayValue = JSON.parse(`"${value.replace(/"/g, '\\"')}"`);
        } catch {
          displayValue = value;
        }
      }

      const lines = displayValue.split("\n");
      const isMultiline = lines.length > 1;
      const isLongText = displayValue.length > 500;
      const shouldCollapse = isMultiline || isLongText;

      if (!shouldCollapse) {
        return <span className="text-emerald-600">{`"${displayValue}"`}</span>;
      }

      return (
        <span className="text-emerald-600">
          {isCollapsed(path) ? (
            <span className="opacity-85">
              {`"${isMultiline ? lines[0] : displayValue.slice(0, 100)}..."`}
            </span>
          ) : (
            <pre className="whitespace-pre-wrap break-all">{`"${displayValue}"`}</pre>
          )}
        </span>
      );
    }

    if (Array.isArray(value)) {
      if (value.length === 0) return <span>[]</span>;

      return (
        <span>
          <span className="text-slate-700">[</span>
          <div className="ml-4">
            {value.map((item, index) => (
              <div key={index}>
                {renderJsonValue(item, `${path}[${index}]`, indent + 1)}
                {index < value.length - 1 && (
                  <span className="text-slate-700">,</span>
                )}
              </div>
            ))}
          </div>
          <span className="text-slate-700">]</span>
        </span>
      );
    }

    if (typeof value === "object" && value !== null) {
      const entries = Object.entries(value as Record<string, unknown>);
      if (entries.length === 0) return <span>{"{}"}</span>;

      const hasComplexValues = entries.some(
        ([, val]) =>
          typeof val === "object" ||
          (typeof val === "string" && (val.length > 500 || val.includes("\n")))
      );
      const shouldShowCollapse = entries.length > 20 || hasComplexValues;

      return (
        <span>
          <span className="text-slate-700">{"{"}</span>
          <div className="ml-4">
            {entries.map(([key, val], index) => {
              const currentPath = path ? `${path}.${key}` : key;
              return (
                <div key={key} className="group relative">
                  {shouldShowCollapse &&
                    renderCollapseButton(isCollapsed(currentPath), currentPath)}
                  <span className="text-amber-600">{JSON.stringify(key)}</span>
                  <span className="text-slate-700">: </span>
                  {isCollapsed(currentPath) ? (
                    <span className="text-slate-700">
                      {typeof val === "object" && val !== null
                        ? Array.isArray(val)
                          ? `[${(val as unknown[]).length} items]`
                          : `{${Object.keys(val as object).length} keys}`
                        : typeof val === "string"
                        ? '"..."'
                        : typeof val === "number"
                        ? formatNumber(val as number)
                        : String(val)}
                    </span>
                  ) : (
                    renderJsonValue(val, currentPath, indent + 1)
                  )}
                  {index < entries.length - 1 && (
                    <span className="text-slate-700">,</span>
                  )}
                </div>
              );
            })}
          </div>
          <span className="text-slate-700">{"}"}</span>
        </span>
      );
    }

    return <span className="text-slate-700">undefined</span>;
  };

  // 安全获取嵌套属性
  const getNestedProp = (
    obj: Record<string, unknown> | undefined,
    path: string
  ): string => {
    if (!obj) return "-";
    const value = path.split(".").reduce((acc, part) => {
      if (acc && typeof acc === "object" && part in acc) {
        return (acc as Record<string, unknown>)[part];
      }
      return undefined;
    }, obj as unknown);
    if (value === undefined || value === null) return "-";
    return String(value);
  };

  // 获取 trace_id 用于 think 事件的跳转
  const getTraceId = (): string => {
    if (selectedEventData?.event !== "think") return "";
    const traceId = getNestedProp(stepData, "thought.llm_call.trace_id");
    return traceId !== "-" ? traceId : "";
  };

  const getSessionId = (): string | null => {
    const sessionIdFromUrl = searchParams.get("session_id");
    if (sessionIdFromUrl) {
      return sessionIdFromUrl;
    }

    const sessionIdFromStore = storeState.sessionId;
    if (sessionIdFromStore) {
      return sessionIdFromStore;
    }
    return null;
  };

  // 检查是否可以显示跳转按钮
  const canShowJumpButton = (): boolean => {
    return (
      selectedEventData?.event === "think" &&
      getTraceId() !== "" &&
      getSessionId() !== null
    );
  };

  // 跳转到 LLM trace 页面
  const navigateToLLMTrace = () => {
    const traceId = getTraceId();
    const sessionId = getSessionId();
    if (traceId && sessionId) {
      window.open(
        `/lab/trace/llm?trace_id=${traceId}&session_id=${sessionId}`,
        "_blank"
      );
    }
  };

  // 调试功能 - 获取 trace 数据并跳转到 playground
  const handleDebug = async () => {
    const traceId = getTraceId();
    const sessionId = getSessionId();

    if (!traceId || !sessionId) {
      toast.error("缺少必要的参数");
      return;
    }

    try {
      const result = await apiClient.GetTraceSessionChat({
        session_id: sessionId,
        page_num: 1,
        page_size: 100,
        trace_id: traceId,
      });

      if (result.chat_completions && result.chat_completions.length > 0) {
        // 使用第一个 chat completion 的数据
        const chatData = result.chat_completions[0];
        const metadata = JSON.parse(chatData.metadata || "{}");
        const processedChatData = [
          {
            groupName: "default",
            groupId: uuid(),
            data: genChatData({
              model: chatData.model_name,
              messages: parseRoleContentFromTags(chatData.prompt),
              max_tokens: metadata.max_tokens,
              temperature: metadata.temperature,
              top_p: metadata.top_p,
            } as any as CustomChatData),
            response: chatData.response,
          },
        ];
        debugger;
        localStorage.setItem(
          CHAT_DATA_STORAGE_KEY,
          JSON.stringify(processedChatData)
        );
        setTimeout(() => {
          window.open("/lab/playground/chat2", "_blank");
        }, 100);
      } else {
        toast.error("未找到对应的 trace 数据");
      }
    } catch (error) {
      console.error("获取 trace 数据失败:", error);
      toast.error("获取 trace 数据失败");
    }
  };

  if (!selectedEventData) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center text-gray-400">
          <FileJson className="mx-auto h-12 w-12 mb-2 opacity-50" />
          <p>选择一个事件查看详情</p>
        </div>
      </div>
    );
  }

  const stepData = selectedEventData.data.data as
    | Record<string, unknown>
    | undefined;

  return (
    <div className="flex flex-col h-full">
      {/* 事件标题 */}
      <div className="px-4 py-2.5 bg-white sticky top-0 z-10 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span
              className={cn(
                "w-1.5 h-1.5 rounded-full",
                selectedEventData.event === "step" && "bg-blue-500",
                selectedEventData.event === "log" && "bg-green-500",
                selectedEventData.event === "error" && "bg-red-500",
                selectedEventData.event === "observation" && "bg-purple-500",
                selectedEventData.event === "think" && "bg-orange-500",
                !["step", "log", "error", "observation", "think"].includes(
                  selectedEventData.event
                ) && "bg-gray-500"
              )}
            />
            <h2 className="text-sm font-medium">
              {selectedEventData.event === "step"
                ? `Step: ${getNestedProp(stepData, "executor")}`
                : `${selectedEventData.event} 事件`}
            </h2>
            {selectedEventData.event === "step" && stepData && (
              <span
                className={cn(
                  "px-1.5 py-0.5 text-[11px] rounded-full font-medium",
                  getNestedProp(stepData, "status") === "success" &&
                    "bg-green-50 text-green-600",
                  getNestedProp(stepData, "status") === "failed" &&
                    "bg-red-50 text-red-600",
                  getNestedProp(stepData, "status") === "running" &&
                    "bg-blue-50 text-blue-600"
                )}
              >
                {getNestedProp(stepData, "status")}
              </span>
            )}
          </div>
          <div className="flex items-center gap-3 text-[11px] text-gray-500">
            {getNestedProp(stepData, "mcp_tool") !== "-" && (
              <div className="flex items-center gap-1">
                <Wrench className="h-3 w-3" />
                {getNestedProp(stepData, "mcp_tool")}
              </div>
            )}
            {canShowJumpButton() && (
              <div className="flex items-center gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 text-xs px-2 border-blue-200 text-blue-600 hover:bg-blue-50"
                  onClick={navigateToLLMTrace}
                >
                  <ExternalLink className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 text-xs px-2 border-orange-200 text-orange-600 hover:bg-orange-50"
                  onClick={handleDebug}
                >
                  <Bug className="h-3 w-3" />
                </Button>
              </div>
            )}
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {new Date(selectedEventData.timestamp).toLocaleString()}
            </div>
          </div>
        </div>
      </div>

      {/* JSON内容 */}
      <div className="flex-1 overflow-auto bg-gray-50/80">
        <div className="sticky top-0 right-0 px-4 py-1.5 z-10 bg-white/80 backdrop-blur-sm border-b border-gray-100 flex justify-end items-center gap-1.5">
          <Button
            size="sm"
            variant="ghost"
            className={cn(
              "h-6 text-xs px-2 hover:bg-gray-100 transition-colors",
              isEscaped && "text-blue-300"
            )}
            onClick={toggleEscapeMode}
          >
            {isEscaped ? (
              <EyeOff className="h-3.5 w-3.5 mr-1" />
            ) : (
              <Eye className="h-3.5 w-3.5 mr-1" />
            )}
            {isEscaped ? "隐藏转义" : "显示转义"}
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="h-6 text-xs px-2 hover:bg-gray-100 transition-colors"
            onClick={copyJsonData}
          >
            {copied ? (
              <Check className="h-3.5 w-3.5 mr-1" />
            ) : (
              <Copy className="h-3.5 w-3.5 mr-1" />
            )}
            {copied ? "已复制" : "复制"}
          </Button>
        </div>
        <div className="px-6 py-3 text-sm font-mono">
          {renderJsonValue(selectedEventData.data)}
        </div>
      </div>
    </div>
  );
}
