"use client";

import { useState } from "react";
import { PageHeader } from "@/app/components/PageHeader";
import { LoadingSpinner } from "@/app/components/LoadingSpinner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Eye,
  ExternalLink,
  AlertCircle,
  CheckCircle,
  X,
  FileText,
  Code,
  EyeIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { apiClient } from "@/app/api/request";
import { type Document } from "@/app/bam/aime/namespaces/trace";
// TODO: 安装 react-markdown 依赖后启用 markdown 渲染
// import ReactMarkdown from "react-markdown";

type MarkdownViewMode = "preview" | "source";

export default function TraceDocumentsPage() {
  const [sessionId, setSessionId] = useState("");
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(
    null,
  );
  const [selectedDocumentKey, setSelectedDocumentKey] = useState<string>("");
  const [convertingDocuments, setConvertingDocuments] = useState<Set<string>>(
    new Set(),
  );
  const [convertedDocuments, setConvertedDocuments] = useState<Set<string>>(
    new Set(),
  );
  const [conversionErrors, setConversionErrors] = useState<Map<string, string>>(
    new Map(),
  );
  const [documentLarkUrls, setDocumentLarkUrls] = useState<Map<string, string>>(
    new Map(),
  );
  const [forceRegenerate, setForceRegenerate] = useState(false);
  const [markdownViewMode, setMarkdownViewMode] =
    useState<MarkdownViewMode>("preview");

  // 获取文档的唯一标识符
  const getDocumentKey = (doc: Document, index: number) => {
    return doc.artifact_id || `${sessionId}_${index}`;
  };

  const handleSearch = async () => {
    if (!sessionId.trim()) {
      setError("请输入有效的 Session ID");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const result = await apiClient.ListSessionDocuments({
        session_id: sessionId.trim(),
      });

      setDocuments(result.documents || []);

      if (!result.documents || result.documents.length === 0) {
        setError("该 Session 下没有找到文档");
      } else {
        // 自动选择第一个文档进行预览
        setSelectedDocument(result.documents[0]);
        setSelectedDocumentKey(getDocumentKey(result.documents[0], 0));
      }
    } catch (err) {
      console.error("获取文档列表失败:", err);
      setError("获取文档列表失败: " + (err as Error).message);
      setDocuments([]);
      setSelectedDocument(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const handlePreview = (document: Document, index: number) => {
    setSelectedDocument(document);
    setSelectedDocumentKey(getDocumentKey(document, index));
    // 切换文档时重置为预览模式
    setMarkdownViewMode("preview");
  };

  const handleOpenLarkUrl = (document: Document, index: number) => {
    const docKey = getDocumentKey(document, index);
    const larkUrl = documentLarkUrls.get(docKey);
    if (larkUrl) {
      window.open(larkUrl, "_blank");
    }
  };

  const handleConvertToFeishu = async (document: Document, index: number) => {
    const docKey = getDocumentKey(document, index);

    setConvertingDocuments(prev => new Set(prev).add(docKey));
    // 清除之前的错误
    setConversionErrors(prev => {
      const newMap = new Map(prev);
      newMap.delete(docKey);
      return newMap;
    });

    try {
      const result = await apiClient.ConvertSessionDocumentToLark({
        session_id: sessionId,
        file_path: document.file_path,
        artifact_id: document.artifact_id,
        force_regenerate: forceRegenerate,
      });

      if (result.lark_url) {
        // 转换成功
        setConvertedDocuments(prev => new Set(prev).add(docKey));
        // 保存飞书链接
        setDocumentLarkUrls(prev => {
          const newMap = new Map(prev);
          newMap.set(docKey, result.lark_url);
          return newMap;
        });
        // 打开转换后的飞书文档链接
        window.open(result.lark_url, "_blank");
        console.log(
          `文档 "${document.name}" 已成功转换为飞书文档，链接: ${result.lark_url}`,
        );
      } else {
        throw new Error("转换失败：未获取到飞书文档链接");
      }
    } catch (err) {
      console.error("转换失败:", err);

      // 设置友好的错误消息
      let errorMessage = "转换失败，请稍后重试";

      if (err instanceof Error) {
        errorMessage = `转换失败：${err.message}`;
      }

      setConversionErrors(prev => {
        const newMap = new Map(prev);
        newMap.set(docKey, errorMessage);
        return newMap;
      });
    } finally {
      setConvertingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(docKey);
        return newSet;
      });
    }
  };

  const getDocumentIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "markdown":
      case "md":
        return "📝";
      case "pdf":
        return "📄";
      case "word":
      case "doc":
      case "docx":
        return "📋";
      case "txt":
      case "text":
        return "📃";
      default:
        return "📄";
    }
  };

  const clearSessionId = () => {
    setSessionId("");
    setDocuments([]);
    setError("");
    setSelectedDocument(null);
    setSelectedDocumentKey("");
    setConvertingDocuments(new Set());
    setConvertedDocuments(new Set());
    setConversionErrors(new Map());
    setDocumentLarkUrls(new Map());
  };

  const renderMarkdownContent = (content: string) => {
    return (
      content
        // 处理代码块
        .replace(
          /```(\w+)?\n([\s\S]*?)```/g,
          '<pre class="code-block"><code>$2</code></pre>',
        )
        // 处理行内代码
        .replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>')
        // 处理图片（需要在链接之前处理）
        .replace(
          /!\[([^\]]*)\]\(([^)]+)\)/g,
          '<img src="$2" alt="$1" class="markdown-image" loading="lazy" />',
        )
        // 处理链接
        .replace(
          /\[?\[([^\]]+)\]\]?\(([^)]+)\)/g,
          '<a href="$2" class="markdown-link" target="_blank" rel="noopener noreferrer">[$1]</a>',
        )
        // 处理粗体
        .replace(/\*\*(.*?)\*\*/g, '<strong class="markdown-bold">$1</strong>')
        // 处理斜体
        .replace(/\*(.*?)\*/g, '<em class="markdown-italic">$1</em>')
        // 处理标题
        .replace(/^#### (.*$)/gim, '<h4 class="markdown-h4">$1</h4>')
        .replace(/^### (.*$)/gim, '<h3 class="markdown-h3">$1</h3>')
        .replace(/^## (.*$)/gim, '<h2 class="markdown-h2">$1</h2>')
        .replace(/^# (.*$)/gim, '<h1 class="markdown-h1">$1</h1>')
        // 处理无序列表 - 修改这部分
        .replace(/^[\s]*[-\*] (.*$)/gm, '<li class="markdown-li">$1</li>')
        .replace(
          /(<li class="markdown-li">[\s\S]*?<\/li>(\n|$))+/g,
          '<ul class="markdown-ul">$&</ul>',
        )
        // 处理有序列表
        .replace(/^\d+\. (.*$)/gim, '<li class="markdown-oli">$1</li>')
        .replace(
          /(<li class="markdown-oli">[\s\S]*?<\/li>(\n|$))+/g,
          '<ol class="markdown-ol">$1</ol>',
        )
        // 处理引用
        .replace(
          /^> (.*$)/gim,
          '<blockquote class="markdown-blockquote">$1</blockquote>',
        )
        // 处理分割线
        .replace(/^---$/gim, '<hr class="markdown-hr" />')
        // 处理表格（需在段落和换行前）
        .replace(
          // 匹配 markdown 表格
          /((?:^\|.*\|\s*\n)+^\|?(?:\s*[:-]+[-| :]*\|?)+\s*\n(?:^\|.*\|\s*\n?)*)/gim,
          function (table) {
            // 拆分表格为行
            const rows = table.trim().split(/\n/).filter(Boolean);
            if (rows.length < 2) return table; // 不是合法表格
            // 表头
            const header = rows[0]
              .replace(/^\||\|$/g, "")
              .split("|")
              .map(cell => cell.trim());
            // 对齐行（忽略）
            // const align = rows[1];
            // 表体
            const bodyRows = rows.slice(2).map(row =>
              row
                .replace(/^\||\|$/g, "")
                .split("|")
                .map(cell => cell.trim()),
            );
            // 构建表头HTML
            const thead = `<thead><tr>${header
              .map(cell => `<th class=\"markdown-th\">${cell}</th>`)
              .join("")}</tr></thead>`;
            // 构建表体HTML
            const tbody = `<tbody>${bodyRows
              .map(
                row =>
                  `<tr>${row
                    .map(cell => `<td class=\"markdown-td\">${cell}</td>`)
                    .join("")}</tr>`,
              )
              .join("")}</tbody>`;
            return `<table class=\"markdown-table\">${thead}${tbody}</table>`;
          },
        )
        // 处理段落（换行）
        .replace(/\n\n/g, '</p><p class="markdown-p">')
        .replace(/\n/g, "<br />")
    );
  };

  const isMarkdownDocument = (doc: Document | null) => {
    if (!doc) return false;
    return (
      doc.type.toLowerCase() === "markdown" || doc.type.toLowerCase() === "md"
    );
  };

  return (
    <div className="flex flex-col gap-6 h-[calc(100vh-120px)]">
      {/* 页面标题 */}
      <PageHeader
        title="文档追踪"
        description="查看 Session 中的文档，支持预览和转换为飞书文档"
      />

      {/* 搜索区域 */}
      <Card className="flex-shrink-0">
        <CardHeader>
          <CardTitle className="text-lg">Session 文档查询</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-3">
            <div className="relative flex-1">
              <Input
                placeholder="请输入 Session ID"
                value={sessionId}
                onChange={e => setSessionId(e.target.value)}
                onKeyDown={handleKeyDown}
                className="pr-8"
              />
              {sessionId && (
                <button
                  className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-sm hover:bg-gray-200 transition-colors"
                  onClick={clearSessionId}
                  type="button"
                >
                  <X className="h-4 w-4 text-gray-500" />
                </button>
              )}
            </div>
            <Button
              onClick={handleSearch}
              disabled={isLoading || !sessionId.trim()}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <div className="h-4 w-4">
                  <div className="h-4 w-4 rounded-full border-2 border-gray-200 border-t-gray-600 animate-spin"></div>
                </div>
              ) : (
                <Search className="h-4 w-4" />
              )}
              查询文档
            </Button>
          </div>

          {error && (
            <div className="mt-3 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
              <div>{error}</div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 主内容区域：左右布局 */}
      {documents.length > 0 && (
        <div className="flex gap-6 flex-1 min-h-0">
          {/* 左侧：文档列表 */}
          <Card className="w-1/3 min-w-[280px] max-w-[400px] flex flex-col">
            <CardHeader className="flex-shrink-0">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <CardTitle className="text-lg">
                    文档列表
                    <Badge variant="secondary" className="ml-2">
                      {documents.length} 个文档
                    </Badge>
                  </CardTitle>
                </div>
              </div>

              {/* 转换选项 */}
              <div className="flex items-center space-x-2 pt-2 border-t border-gray-100">
                <input
                  type="checkbox"
                  id="force-regenerate"
                  checked={forceRegenerate}
                  onChange={e => setForceRegenerate(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer"
                />
                <label
                  htmlFor="force-regenerate"
                  className="text-xs text-gray-600 cursor-pointer select-none"
                >
                  强制重新生成（即使文档已存在）
                </label>
              </div>
            </CardHeader>
            <CardContent className="flex-1 overflow-y-auto p-4">
              <div className="space-y-3">
                {documents.map((doc, index) => {
                  const docKey = getDocumentKey(doc, index);
                  const isConverting = convertingDocuments.has(docKey);
                  const isConverted = convertedDocuments.has(docKey);
                  const isSelected = selectedDocumentKey === docKey;
                  const conversionError = conversionErrors.get(docKey);
                  const hasError = Boolean(conversionError);

                  return (
                    <div
                      key={`${doc.name}_${index}`}
                      className={cn(
                        "border rounded-lg p-3 cursor-pointer transition-all duration-200 min-h-[100px]",
                        isSelected
                          ? "border-blue-500 bg-blue-50 shadow-md"
                          : "border-gray-200 hover:border-gray-300 hover:shadow-sm",
                      )}
                      onClick={() => handlePreview(doc, index)}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          <span className="text-lg flex-shrink-0">
                            {getDocumentIcon(doc.type)}
                          </span>
                          <div className="flex-1 min-w-0">
                            <h3
                              className={cn(
                                "font-medium text-sm leading-tight mb-1",
                                isSelected ? "text-blue-900" : "text-gray-900",
                              )}
                              title={doc.name}
                            >
                              {doc.name}
                            </h3>
                            <div className="flex items-center gap-1 flex-wrap">
                              <Badge
                                variant="outline"
                                className="text-xs px-1 py-0.5"
                              >
                                {doc.type.toUpperCase()}
                              </Badge>
                              {isConverted && (
                                <Badge className="text-xs px-1 py-0.5 bg-green-100 text-green-800">
                                  <CheckCircle className="h-3 w-3 mr-0.5" />
                                  已转换
                                </Badge>
                              )}
                              {hasError && (
                                <Badge className="text-xs px-1 py-0.5 bg-red-100 text-red-800">
                                  <AlertCircle className="h-3 w-3 mr-0.5" />
                                  转换失败
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* 错误消息 */}
                      {conversionError && (
                        <div className="mb-2 bg-red-50 border border-red-200 text-red-700 px-2 py-1 rounded text-xs">
                          <div className="flex items-start justify-between">
                            <span className="flex-1">{conversionError}</span>
                            <button
                              onClick={e => {
                                e.stopPropagation();
                                setConversionErrors(prev => {
                                  const newMap = new Map(prev);
                                  newMap.delete(docKey);
                                  return newMap;
                                });
                              }}
                              className="ml-1 text-red-400 hover:text-red-600 flex-shrink-0"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </div>
                        </div>
                      )}

                      {/* 操作按钮 */}
                      <div className="flex items-center gap-1.5">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={e => {
                            e.stopPropagation();
                            handlePreview(doc, index);
                          }}
                          className={cn(
                            "h-6 text-xs flex-1 px-2",
                            isSelected && "bg-blue-100 border-blue-300",
                          )}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          预览
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={e => {
                            e.stopPropagation();
                            if (isConverted) {
                              handleOpenLarkUrl(doc, index);
                            } else {
                              handleConvertToFeishu(doc, index);
                            }
                          }}
                          disabled={isConverting}
                          className={cn(
                            "h-6 text-xs flex-1 px-2",
                            isConverted &&
                              "bg-green-50 text-green-700 border-green-200 hover:bg-green-100",
                            hasError &&
                              "bg-red-50 text-red-700 border-red-200 hover:bg-red-100",
                          )}
                        >
                          {isConverting ? (
                            <>
                              <LoadingSpinner />
                              <span className="ml-0.5 hidden sm:inline">
                                转换中
                              </span>
                            </>
                          ) : isConverted ? (
                            <>
                              <ExternalLink className="h-3 w-3 mr-0.5" />
                              <span className="hidden sm:inline">打开飞书</span>
                            </>
                          ) : hasError ? (
                            <>
                              <AlertCircle className="h-3 w-3 mr-0.5" />
                              <span className="hidden sm:inline">重试</span>
                            </>
                          ) : (
                            <>
                              <ExternalLink className="h-3 w-3 mr-0.5" />
                              <span className="hidden sm:inline">
                                获取飞书链接
                              </span>
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* 右侧：文档预览 */}
          <Card className="flex-1 flex flex-col min-w-0">
            <CardHeader className="flex-shrink-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {selectedDocument ? (
                    <>
                      <span className="text-xl">
                        {getDocumentIcon(selectedDocument.type)}
                      </span>
                      <CardTitle className="text-lg">
                        {selectedDocument.name}
                      </CardTitle>
                      <Badge variant="outline">
                        {selectedDocument.type.toUpperCase()}
                      </Badge>
                    </>
                  ) : (
                    <>
                      <FileText className="h-5 w-5 text-gray-400" />
                      <CardTitle className="text-lg text-gray-500">
                        选择一个文档进行预览
                      </CardTitle>
                    </>
                  )}
                </div>

                {/* Markdown 切换按钮 */}
                {selectedDocument && isMarkdownDocument(selectedDocument) && (
                  <div className="flex items-center bg-gray-100 rounded-lg p-1">
                    <Button
                      variant={
                        markdownViewMode === "preview" ? "default" : "ghost"
                      }
                      size="sm"
                      onClick={() => setMarkdownViewMode("preview")}
                      className="h-7 px-3"
                    >
                      <EyeIcon className="h-3 w-3 mr-1" />
                      预览
                    </Button>
                    <Button
                      variant={
                        markdownViewMode === "source" ? "default" : "ghost"
                      }
                      size="sm"
                      onClick={() => setMarkdownViewMode("source")}
                      className="h-7 px-3"
                    >
                      <Code className="h-3 w-3 mr-1" />
                      原文
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent className="flex-1 overflow-hidden">
              {selectedDocument ? (
                <div className="h-full overflow-y-auto">
                  {isMarkdownDocument(selectedDocument) ? (
                    <>
                      {markdownViewMode === "preview" ? (
                        <div className="markdown-preview">
                          <div
                            className="markdown-content"
                            dangerouslySetInnerHTML={{
                              __html: renderMarkdownContent(
                                selectedDocument.content,
                              ),
                            }}
                          />
                        </div>
                      ) : (
                        <pre className="whitespace-pre-wrap text-sm text-gray-700 bg-gray-50 p-4 rounded border h-full overflow-y-auto font-mono leading-relaxed">
                          {selectedDocument.content}
                        </pre>
                      )}
                    </>
                  ) : (
                    <pre className="whitespace-pre-wrap text-sm text-gray-700 bg-gray-50 p-4 rounded border h-full overflow-y-auto font-mono leading-relaxed">
                      {selectedDocument.content}
                    </pre>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <FileText className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                    <p className="text-lg font-medium">暂无预览内容</p>
                    <p className="text-sm">请从左侧列表中选择一个文档</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* 自定义 Markdown 样式 */}
      <style jsx>{`
        .markdown-preview {
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
            "Oxygen", "Ubuntu", "Cantarell", sans-serif;
          line-height: 1.6;
          color: #333;
          background: #fff;
          padding: 16px;
          border-radius: 8px;
          max-width: 100%;
          word-wrap: break-word;
          overflow-wrap: break-word;
        }

        @media (min-width: 768px) {
          .markdown-preview {
            padding: 24px;
          }
        }

        .markdown-content :global(.markdown-h1) {
          font-size: 1.8em;
          font-weight: 700;
          margin: 1.2em 0 0.4em 0;
          padding-bottom: 0.3em;
          border-bottom: 2px solid #e5e7eb;
          color: #1f2937;
          word-wrap: break-word;
        }

        @media (min-width: 768px) {
          .markdown-content :global(.markdown-h1) {
            font-size: 2em;
            margin: 1.5em 0 0.5em 0;
          }
        }

        .markdown-content :global(.markdown-h2) {
          font-size: 1.4em;
          font-weight: 600;
          margin: 1.1em 0 0.4em 0;
          padding-bottom: 0.2em;
          border-bottom: 1px solid #e5e7eb;
          color: #374151;
          word-wrap: break-word;
        }

        @media (min-width: 768px) {
          .markdown-content :global(.markdown-h2) {
            font-size: 1.6em;
            margin: 1.3em 0 0.5em 0;
          }
        }

        .markdown-content :global(.markdown-h3) {
          font-size: 1.2em;
          font-weight: 600;
          margin: 1em 0 0.3em 0;
          color: #4b5563;
          word-wrap: break-word;
        }

        @media (min-width: 768px) {
          .markdown-content :global(.markdown-h3) {
            font-size: 1.3em;
            margin: 1.2em 0 0.4em 0;
          }
        }

        .markdown-content :global(.markdown-h4) {
          font-size: 1.05em;
          font-weight: 600;
          margin: 0.8em 0 0.2em 0;
          color: #6b7280;
          word-wrap: break-word;
        }

        @media (min-width: 768px) {
          .markdown-content :global(.markdown-h4) {
            font-size: 1.1em;
            margin: 1em 0 0.3em 0;
          }
        }

        .markdown-content :global(.markdown-p) {
          margin: 0.6em 0;
          line-height: 1.6;
          word-wrap: break-word;
          overflow-wrap: break-word;
        }

        @media (min-width: 768px) {
          .markdown-content :global(.markdown-p) {
            margin: 0.8em 0;
            line-height: 1.7;
          }
        }

        .markdown-content :global(.markdown-bold) {
          font-weight: 600;
          color: #1f2937;
        }

        .markdown-content :global(.markdown-italic) {
          font-style: italic;
          color: #4b5563;
        }

        .markdown-content :global(.markdown-link) {
          color: #3b82f6;
          text-decoration: none;
          border-bottom: 1px solid transparent;
          transition: border-bottom-color 0.2s;
          word-wrap: break-word;
        }

        .markdown-content :global(.markdown-link:hover) {
          border-bottom-color: #3b82f6;
        }

        .markdown-content :global(.markdown-image) {
          max-width: 100%;
          height: auto;
          margin: 1em 0;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          display: block;
          border: 1px solid #e5e7eb;
        }

        @media (min-width: 768px) {
          .markdown-content :global(.markdown-image) {
            margin: 1.5em 0;
          }
        }

        .markdown-content :global(.inline-code) {
          background: #f3f4f6;
          color: #dc2626;
          padding: 0.1em 0.3em;
          border-radius: 3px;
          font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo,
            monospace;
          font-size: 0.85em;
          word-wrap: break-word;
        }

        @media (min-width: 768px) {
          .markdown-content :global(.inline-code) {
            font-size: 0.9em;
          }
        }

        .markdown-content :global(.code-block) {
          background: #1f2937;
          color: #f9fafb;
          padding: 0.8em;
          border-radius: 8px;
          overflow-x: auto;
          margin: 0.8em 0;
          border: 1px solid #374151;
          max-width: 100%;
        }

        @media (min-width: 768px) {
          .markdown-content :global(.code-block) {
            padding: 1em;
            margin: 1em 0;
          }
        }

        .markdown-content :global(.code-block code) {
          font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo,
            monospace;
          font-size: 0.8em;
          line-height: 1.4;
        }

        @media (min-width: 768px) {
          .markdown-content :global(.code-block code) {
            font-size: 0.9em;
            line-height: 1.5;
          }
        }

        .markdown-content :global(.markdown-ul) {
          margin: 0.8em 0;
          padding-left: 2em;
          list-style-type: disc;
        }

        .markdown-content :global(.markdown-li) {
          margin: 0.3em 0;
          line-height: 1.6;
          display: list-item;
        }

        .markdown-content :global(.markdown-blockquote) {
          border-left: 4px solid #e5e7eb;
          margin: 0.8em 0;
          padding: 0.4em 0.8em;
          background: #f9fafb;
          color: #6b7280;
          font-style: italic;
          word-wrap: break-word;
        }

        @media (min-width: 768px) {
          .markdown-content :global(.markdown-blockquote) {
            margin: 1em 0;
            padding: 0.5em 1em;
          }
        }

        .markdown-content :global(.markdown-hr) {
          border: none;
          border-top: 2px solid #e5e7eb;
          margin: 1.5em 0;
        }

        @media (min-width: 768px) {
          .markdown-content :global(.markdown-hr) {
            margin: 2em 0;
          }
        }

        .markdown-content :global(.markdown-table) {
          width: 100%;
          border-collapse: collapse;
          margin: 1.2em 0;
          background: #fff;
        }
        .markdown-content :global(.markdown-th),
        .markdown-content :global(.markdown-td) {
          border: 1px solid #e5e7eb;
          padding: 8px 12px;
          text-align: left;
        }
        .markdown-content :global(.markdown-th) {
          background: #f3f4f6;
          font-weight: 600;
        }
        .markdown-content :global(.markdown-td) {
          background: #fff;
        }
      `}</style>
    </div>
  );
}
