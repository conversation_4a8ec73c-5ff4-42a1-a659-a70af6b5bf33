"use client";

import { PageHeader } from "@/app/components/PageHeader";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ChartBar, FileSearch, List, Stethoscope } from "lucide-react";
import Link from "next/link";

export default function TracePage() {
  return (
    <div className="flex flex-col gap-3">
      <PageHeader title="追踪系统" description="查看和管理系统的追踪记录" />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
        {/* 会话追踪卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>会话追踪</CardTitle>
            <CardDescription>查看会话的详细信息和运行时数据</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-24 flex items-center justify-center text-blue-500">
              <FileSearch className="h-12 w-12" />
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link href="/trace/session">访问会话追踪</Link>
            </Button>
          </CardFooter>
        </Card>

        {/* 事件追踪卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>事件追踪</CardTitle>
            <CardDescription>查看系统产生的各种事件记录</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-24 flex items-center justify-center text-green-500">
              <List className="h-12 w-12" />
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link href="/trace/events">访问事件追踪</Link>
            </Button>
          </CardFooter>
        </Card>

        {/* LLM 请求卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>LLM 请求</CardTitle>
            <CardDescription>查看 LLM 请求的追踪记录</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-24 flex items-center justify-center text-purple-500">
              <ChartBar className="h-12 w-12" />
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link href="/trace/llm">访问 LLM 追踪</Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Diagnose 诊断卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>诊断</CardTitle>
            <CardDescription>通过 Session ID 进行诊断和对话</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-24 flex items-center justify-center text-red-500">
              <Stethoscope className="h-12 w-12" />
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link href="/trace/diagnose">访问诊断</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
