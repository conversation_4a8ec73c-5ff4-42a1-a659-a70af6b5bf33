import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye, Bug } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type {
  GetTraceSessionChatResponse,
  ChatCompletion,
} from "@/app/bam/aime/namespaces/trace";
import { setChatData } from "@/app/store/playground";
import { useRouter } from "next/navigation";
import { parseRoleContentFromTags } from "@/app/playground/chat2/utils";

// 结果表格组件
export const ResultTable = ({
  chatData,
  showFullContent,
}: {
  chatData: GetTraceSessionChatResponse;
  showFullContent: (
    prompt: string,
    response: string,
    id?: string,
    type?: string,
    model?: string,
    metadata?: string,
    defaultTab?: "prompt" | "response" | "metadata",
  ) => void;
}) => {
  const router = useRouter();

  if (!window) {
    return null;
  }
  const handleDebug = (data: ChatCompletion) => {
    setChatData({
      ...data,
      messages: parseRoleContentFromTags(data.prompt),
    });
    router.push("/playground/chat2");
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[50px]">ID</TableHead>
          <TableHead className="w-[200px]">Prompt</TableHead>
          <TableHead className="w-[200px]">Response</TableHead>
          <TableHead className="w-[70px]">Type</TableHead>
          <TableHead className="w-[80px]">Status</TableHead>
          <TableHead className="w-[100px]">Model</TableHead>
          <TableHead className="w-[120px]">Time</TableHead>
          <TableHead className="w-[50px] sticky right-0 bg-white">
            Action
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {chatData.chat_completions.map(
          (chat: ChatCompletion, index: number) => (
            <TableRow key={index}>
              <TableCell className="text-xs">{chat.id}</TableCell>
              <TableCell>
                <div
                  className="text-xs truncate max-w-[180px] cursor-pointer hover:text-blue-600"
                  onClick={() =>
                    showFullContent(
                      chat.prompt,
                      chat.response,
                      chat.id,
                      chat.type,
                      chat.model_name,
                      chat.metadata,
                    )
                  }
                >
                  {chat.prompt.length > 100
                    ? chat.prompt.slice(0, 100) + "..."
                    : chat.prompt}
                </div>
              </TableCell>
              <TableCell>
                <div
                  className="text-xs truncate max-w-[180px] cursor-pointer hover:text-blue-600"
                  onClick={() =>
                    showFullContent(
                      chat.prompt,
                      chat.response,
                      chat.id,
                      chat.type,
                      chat.model_name,
                      chat.metadata,
                      "response",
                    )
                  }
                >
                  {chat.response.length > 100
                    ? chat.response.slice(0, 100) + "..."
                    : chat.response}
                </div>
              </TableCell>
              <TableCell>
                <Badge
                  variant="outline"
                  className="bg-gray-50 text-xs font-normal"
                >
                  {chat.type}
                </Badge>
              </TableCell>

              <TableCell>
                <Badge
                  variant={chat.status === "success" ? "default" : "secondary"}
                  className={`text-xs font-normal ${
                    chat.status === "success"
                      ? "bg-green-100 hover:bg-green-100 text-green-800"
                      : "bg-amber-100 hover:bg-amber-100 text-amber-800"
                  }`}
                >
                  {chat.status}
                </Badge>
              </TableCell>
              <TableCell className="text-xs">{chat.model_name}</TableCell>
              <TableCell className="text-xs">
                {new Date(chat.created_at).toLocaleString()}
              </TableCell>
              <TableCell className="sticky right-0 bg-white">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() =>
                    showFullContent(
                      chat.prompt,
                      chat.response,
                      chat.id,
                      chat.type,
                      chat.model_name,
                      chat.metadata,
                    )
                  }
                >
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleDebug(chat)}
                >
                  <Bug className="h-4 w-4" />
                </Button>
              </TableCell>
            </TableRow>
          ),
        )}
      </TableBody>
    </Table>
  );
};

// 分页组件
const PaginationControls = ({
  currentPage,
  totalPages,
  onPageChange,
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}) => {
  // 生成页码数组
  const generatePagination = () => {
    // 如果总页数小于等于7，显示所有页码
    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // 否则显示当前页附近的页码，以及首尾页码
    if (currentPage <= 3) {
      return [1, 2, 3, 4, 5, "...", totalPages];
    } else if (currentPage >= totalPages - 2) {
      return [
        1,
        "...",
        totalPages - 4,
        totalPages - 3,
        totalPages - 2,
        totalPages - 1,
        totalPages,
      ];
    } else {
      return [
        1,
        "...",
        currentPage - 1,
        currentPage,
        currentPage + 1,
        "...",
        totalPages,
      ];
    }
  };

  const pagination = generatePagination();

  return (
    <Pagination className="mt-4 justify-end">
      <PaginationContent className="ml-auto">
        <PaginationItem>
          <PaginationPrevious
            href="#"
            onClick={e => {
              e.preventDefault();
              if (currentPage > 1) onPageChange(currentPage - 1);
            }}
            className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
          />
        </PaginationItem>

        {/* 显示页码 */}
        {pagination.map((page, index) => (
          <PaginationItem key={index}>
            {page === "..." ? (
              <span className="px-3 flex items-center">...</span>
            ) : (
              <PaginationLink
                href="#"
                onClick={e => {
                  e.preventDefault();
                  if (typeof page === "number") onPageChange(page);
                }}
                isActive={page === currentPage}
              >
                {page}
              </PaginationLink>
            )}
          </PaginationItem>
        ))}

        <PaginationItem>
          <PaginationNext
            href="#"
            onClick={e => {
              e.preventDefault();
              if (currentPage < totalPages) onPageChange(currentPage + 1);
            }}
            className={
              currentPage >= totalPages ? "pointer-events-none opacity-50" : ""
            }
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
};

// 结果卡片组件
const ResultCard = ({
  chatData,
  showFullContent,
  currentPage,
  totalPages,
  onPageChange,
}: {
  chatData: GetTraceSessionChatResponse;
  showFullContent: (
    prompt: string,
    response: string,
    id?: string,
    type?: string,
    model?: string,
    metadata?: string,
    defaultTab?: "prompt" | "response" | "metadata",
  ) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}) => {
  if (!chatData) return null;

  return (
    <div className="flex flex-col h-[calc(100vh-180px)] flex-1">
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold">查询结果 ({chatData.total})</h2>
          <p className="text-sm text-gray-500">
            第 {currentPage} 页，每页 {chatData.chat_completions.length} 条
          </p>
        </div>
        <Separator className="my-2" />
      </div>
      <div className="flex-1 overflow-auto">
        <div className="rounded-md">
          <ResultTable chatData={chatData} showFullContent={showFullContent} />
        </div>
      </div>
      {totalPages > 1 && (
        <div className="mt-4">
          <PaginationControls
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
          />
        </div>
      )}
    </div>
  );
};

export default ResultCard;
