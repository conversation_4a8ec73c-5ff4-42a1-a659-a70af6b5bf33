import { Text, Table, Flex, IconButton } from "@radix-ui/themes";
import { EyeOpenIcon } from "@radix-ui/react-icons";

interface TruncatedCellProps {
  text: string;
  maxLength?: number;
  maxWidth?: string;
  showFullContent: (
    content: string,
    responseText?: string,
    metadata?: string,
  ) => void;
  metadata?: string;
  responseText?: string;
}

const TruncatedCell = ({
  text,
  maxLength = 100,
  maxWidth = "250px",
  showFullContent,
  metadata,
  responseText,
}: TruncatedCellProps) => {
  const truncatedText =
    text.length > maxLength ? text.substring(0, maxLength) + "..." : text;

  const handleViewClick = () => {
    showFullContent(text, responseText, metadata);
  };

  return (
    <Table.Cell
      style={{
        maxWidth,
        overflow: "hidden",
        textOverflow: "ellipsis",
        whiteSpace: "nowrap",
      }}
    >
      <Flex align="center" gap="1">
        <Text size="2" style={{ overflow: "hidden", textOverflow: "ellipsis" }}>
          {truncatedText}
        </Text>
        <IconButton
          size="1"
          variant="soft"
          onClick={handleViewClick}
          style={{ flexShrink: 0 }}
        >
          <EyeOpenIcon />
        </IconButton>
      </Flex>
    </Table.Cell>
  );
};

export default TruncatedCell;
