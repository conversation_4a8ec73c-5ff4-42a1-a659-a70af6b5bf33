import { KeyboardEvent, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X, Loader2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface StatusOption {
  label: string;
  value: string;
}

interface SearchPanelProps {
  sessionId: string;
  setSessionId: (id: string) => void;
  traceId: string;
  setTraceId: (id: string) => void;
  statusFilter: string;
  onStatusChange: (status: string) => void;
  statusOptions: StatusOption[];
  typeFilter: string;
  onTypeChange: (type: string) => void;
  typeOptions: StatusOption[];
  handleSearch: () => void;
  isLoading: boolean;
  error: string;
}

const SearchPanel = ({
  sessionId,
  setSessionId,
  traceId,
  setTraceId,
  statusFilter,
  onStatusChange,
  statusOptions,
  typeFilter,
  onTypeChange,
  typeOptions,
  handleSearch,
  isLoading,
  error,
}: SearchPanelProps) => {
  const [customType, setCustomType] = useState("");

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const clearSessionId = () => {
    setSessionId("");
  };

  const clearTraceId = () => {
    setTraceId("");
  };

  const handleCustomTypeKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    e.stopPropagation();
    if (e.key === "Enter") {
      const value = e.currentTarget.value.trim();
      if (value) {
        onTypeChange(value);
        setCustomType("");
      }
    }
  };

  return (
    <div className="flex flex-col gap-1 mb-3">
      <div className="flex items-stretch gap-2 flex-wrap xl:flex-nowrap">
        <div className="relative flex-1 min-w-[180px] max-w-none">
          <Input
            placeholder="请输入 Session ID"
            value={sessionId}
            onChange={e => setSessionId(e.target.value)}
            onKeyDown={handleKeyDown}
            className="h-10 pr-8"
          />
          {sessionId && (
            <button
              className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-sm hover:bg-gray-200 transition-colors"
              onClick={clearSessionId}
              type="button"
            >
              <X className="h-4 w-4 text-gray-500" />
            </button>
          )}
        </div>

        <div className="relative flex-1 min-w-[180px] max-w-none">
          <Input
            placeholder="请输入 Trace ID（可选）"
            value={traceId}
            onChange={e => setTraceId(e.target.value)}
            onKeyDown={handleKeyDown}
            className="h-10 pr-8"
          />
          {traceId && (
            <button
              className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-sm hover:bg-gray-200 transition-colors"
              onClick={clearTraceId}
              type="button"
            >
              <X className="h-4 w-4 text-gray-500" />
            </button>
          )}
        </div>

        <div className="w-[120px] flex-shrink-0">
          <Select value={statusFilter} onValueChange={onStatusChange}>
            <SelectTrigger className="h-10 w-full" style={{ height: "40px" }}>
              <SelectValue placeholder="状态筛选" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="w-[120px] flex-shrink-0">
          <Select value={typeFilter} onValueChange={onTypeChange}>
            <SelectTrigger className="h-10 w-full" style={{ height: "40px" }}>
              <SelectValue placeholder="类型筛选" />
            </SelectTrigger>
            <SelectContent>
              {typeOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
              <div className="p-2">
                <Input
                  placeholder="输入类型"
                  className="h-8"
                  value={customType}
                  onChange={e => setCustomType(e.target.value)}
                  onClick={e => e.stopPropagation()}
                  onKeyDown={handleCustomTypeKeyDown}
                />
              </div>
            </SelectContent>
          </Select>
        </div>

        <Button
          onClick={handleSearch}
          disabled={isLoading}
          className="w-18 h-10 flex-shrink-0 px-3"
          size="lg"
        >
          {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : "搜索"}
        </Button>
      </div>
      {error && <p className="text-sm text-red-500 mt-0.5">{error}</p>}
    </div>
  );
};

export default SearchPanel;
