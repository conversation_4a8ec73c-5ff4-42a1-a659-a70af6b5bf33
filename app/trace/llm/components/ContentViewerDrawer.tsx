import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Check, Copy, ChevronDown, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface ContentViewerDrawerProps {
  content: {
    prompt: string;
    response: string;
    id?: string;
    type?: string;
    model?: string;
    metadata?: string;
    defaultTab?: "prompt" | "response" | "metadata";
  } | null;
  onClose: () => void;
  defaultTab?: "prompt" | "response" | "metadata";
}

const ContentViewerDrawer = ({
  content,
  onClose,
  defaultTab = "prompt",
}: ContentViewerDrawerProps) => {
  const [copied, setCopied] = useState(false);
  const [selectedTab, setSelectedTab] = useState(defaultTab);
  const [copiedContent, setCopiedContent] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(),
  );

  useEffect(() => {
    if (content?.defaultTab) {
      setSelectedTab(content.defaultTab);
    }
  }, [content?.defaultTab]);

  useEffect(() => {
    if (copied) {
      const timer = setTimeout(() => {
        setCopied(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [copied]);

  const handleCopy = (text: string, tabId: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        setCopied(true);
        setCopiedContent(tabId);
      },
      err => {
        console.error("复制失败: ", err);
      },
    );
  };

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const next = new Set(prev);
      if (next.has(sectionId)) {
        next.delete(sectionId);
      } else {
        next.add(sectionId);
      }
      return next;
    });
  };

  // 处理标签内容，只处理一层 (提取到组件级别以便复用)
  const processTagContent = (text: string): string => {
    // 每次创建新的正则表达式实例，避免 lastIndex 的影响
    const tagRegex = new RegExp(/<([^\s>\/]+)([^>]*)>([\s\S]*?)<\/\1>/, "g");

    let result = text;
    let match;

    // 找到所有 reasoning_content 标签
    while ((match = tagRegex.exec(text)) !== null) {
      const [fullMatch, tagName, attrs, content] = match;
      if (tagName !== "reasoning_content") {
        continue;
      }

      // 处理 reasoning_content 的内容
      try {
        const jsonContent = JSON.parse(content);

        // 处理加密内容的显示
        if (jsonContent.encrypted_content) {
          const shortContent =
            jsonContent.encrypted_content.slice(0, 20) + "...";
          jsonContent.encrypted_content = shortContent;
        }

        // 处理推理内容的格式化显示
        if (jsonContent.reasoning) {
          // 如果推理内容很长，进行适当的格式化
          if (
            typeof jsonContent.reasoning === "string" &&
            jsonContent.reasoning.length > 200
          ) {
            // 保持原始内容，但在换行处进行优化显示
            jsonContent.reasoning = jsonContent.reasoning
              .replace(/\\n/g, "\n")
              .replace(/\n{3,}/g, "\n\n"); // 减少过多的空行
          }
        }

        // 获取内容的缩进级别
        const lines = content.split("\n");
        const baseIndent =
          lines.length > 1 ? lines[1].match(/^\s*/)?.[0] || "" : "";

        let formattedContent = JSON.stringify(jsonContent, null, 2);

        // 处理所有的转义换行符为实际换行
        formattedContent = formattedContent.replace(/\\n/g, "\n");

        // 格式化 JSON 并保持缩进
        const processedLines = formattedContent
          .split("\n")
          .map((line, index) => {
            // 第一行和最后一行使用原始缩进
            if (index === 0) return line;
            return baseIndent + line;
          });

        // 保持原始标签结构，只替换内容部分
        const newTag = `<${tagName}${attrs}>\n${processedLines.join(
          "\n",
        )}\n${baseIndent}</${tagName}>`;
        result = result.replace(fullMatch, newTag);
      } catch (e) {
        console.debug("Failed to parse reasoning_content as JSON:", e);
        // 如果不是有效的 JSON，尝试简单的文本格式化
        const lines = content.split("\n");
        const baseIndent =
          lines.length > 1 ? lines[1].match(/^\s*/)?.[0] || "" : "";

        // 简单的文本格式化，处理换行和缩进
        const formattedContent = content
          .replace(/\\n/g, "\n")
          .replace(/\n{3,}/g, "\n\n");

        const newTag = `<${tagName}${attrs}>\n${baseIndent}${formattedContent}\n${baseIndent}</${tagName}>`;
        result = result.replace(fullMatch, newTag);
      }
    }

    return result;
  };

  const renderTaggedContent = (text: string) => {
    // 解析标签的正则表达式
    const outerTagRegex = /<([^\s>\/]+)([^>]*)>([\s\S]*?)<\/\1>/g;
    // 解析属性的正则
    const attrRegex = /([^\s=]+)=["']([^"']*)["']/g;

    // 解析属性字符串为对象
    const parseAttributes = (attrString: string): Record<string, string> => {
      const attrs: Record<string, string> = {};
      const matches = attrString.matchAll(attrRegex);
      for (const match of matches) {
        const [, name, value] = match;
        attrs[name] = value;
      }
      return attrs;
    };

    // 将属性对象转换为显示字符串
    const formatAttributes = (attrs: Record<string, string>): string => {
      return Object.entries(attrs)
        .map(([key, value]) => `${key}="${value}"`)
        .join(" ");
    };

    // 渲染未标记的文本块
    const renderUntaggedText = (text: string, index: number) => {
      if (!text.trim()) return null;
      const sectionId = `untagged-${index}`;

      return (
        <div key={sectionId} className="mb-4">
          <div className="sticky top-0 z-10">
            <button
              onClick={() => toggleSection(sectionId)}
              className="flex items-center gap-2 text-sm font-medium text-muted-foreground/70 hover:text-muted-foreground py-2 w-full px-2 rounded-md bg-muted/30 hover:bg-muted/50 transition-colors"
            >
              {expandedSections.has(sectionId) ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
              <span className="opacity-70 flex items-center gap-2">
                <span className="inline-flex items-center justify-center rounded-full bg-muted-foreground/20 w-5 h-5 text-xs font-medium">
                  {index + 1}
                </span>
                Untagged Content
              </span>
            </button>
          </div>
          {expandedSections.has(sectionId) && (
            <div className="mt-2 pl-6">
              <pre
                className="text-sm whitespace-pre-wrap font-mono leading-relaxed text-muted-foreground"
                style={{ wordBreak: "break-word" }}
              >
                {text.trim()}
              </pre>
            </div>
          )}
        </div>
      );
    };

    const result = [];
    let lastIndex = 0;
    let match;
    let sectionIndex = 0;

    // 遍历查找最外层标签
    while ((match = outerTagRegex.exec(text)) !== null) {
      // 如果标签前有未包含在标签中的文本，直接显示
      if (match.index > lastIndex) {
        const untaggedText = text.slice(lastIndex, match.index);
        const untaggedBlock = renderUntaggedText(untaggedText, sectionIndex);
        if (untaggedBlock) {
          result.push(untaggedBlock);
          sectionIndex++;
        }
      }

      const [, tagName, attrString, content] = match;
      const attrs = parseAttributes(attrString.trim());
      const sectionId = `${tagName}-${result.length}`;

      // 处理内容，包括嵌套标签
      const processedContent = processTagContent(content);

      result.push(
        <div key={sectionId} className="mb-4">
          <div className="sticky top-0 z-10">
            <button
              onClick={() => toggleSection(sectionId)}
              className="flex items-center gap-2 text-sm font-medium text-muted-foreground hover:text-foreground py-2 w-full px-2 rounded-md bg-muted/50 hover:bg-muted transition-colors"
            >
              {expandedSections.has(sectionId) ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
              <span className="flex items-center gap-2">
                <span className="inline-flex items-center justify-center rounded-full bg-muted-foreground/20 w-5 h-5 text-xs font-medium">
                  {sectionIndex + 1}
                </span>
                {tagName}
              </span>
              {Object.keys(attrs).length > 0 && (
                <span className="text-xs text-muted-foreground">
                  {formatAttributes(attrs)}
                </span>
              )}
            </button>
          </div>
          {expandedSections.has(sectionId) && (
            <div className="mt-2 pl-6">
              <pre
                className="text-sm whitespace-pre-wrap font-mono leading-relaxed text-foreground"
                style={{ wordBreak: "break-word" }}
              >
                {processedContent.trim()}
              </pre>
            </div>
          )}
        </div>,
      );

      lastIndex = match.index + match[0].length;
      sectionIndex++;
    }

    // 处理剩余的未标记文本
    if (lastIndex < text.length) {
      const remainingText = text.slice(lastIndex);
      const untaggedBlock = renderUntaggedText(remainingText, sectionIndex);
      if (untaggedBlock) {
        result.push(untaggedBlock);
        sectionIndex++;
      }
    }

    return result.length > 0 ? (
      result
    ) : (
      <pre
        className="text-sm whitespace-pre-wrap font-mono leading-relaxed text-foreground"
        style={{ wordBreak: "break-word" }}
      >
        {text}
      </pre>
    );
  };

  const renderContent = (content: string, tabId: string) => {
    return (
      <div className="relative h-full">
        <ScrollArea className="h-full border rounded-md bg-muted/50 dark:bg-muted/20">
          <button
            className={cn(
              "absolute top-2 right-2 z-20 p-1.5 rounded-md bg-muted/70 hover:bg-muted transition-all dark:bg-muted/50 dark:hover:bg-muted/70",
              copied && copiedContent === tabId ? "opacity-100" : "opacity-70",
              "hover:opacity-100 hover:scale-105",
            )}
            onClick={() => handleCopy(content, tabId)}
            aria-label={
              copied && copiedContent === tabId ? "已复制" : "复制内容"
            }
          >
            {copied && copiedContent === tabId ? (
              <Check className="h-4 w-4" />
            ) : (
              <Copy className="h-4 w-4" />
            )}
          </button>
          <div className="p-3">
            {tabId === "prompt" ? (
              renderTaggedContent(content)
            ) : tabId === "response" ? (
              <pre className="text-sm whitespace-pre-wrap font-mono leading-relaxed text-foreground">
                {processTagContent(content)}
              </pre>
            ) : (
              <pre className="text-sm whitespace-pre-wrap font-mono leading-relaxed text-foreground">
                {content}
              </pre>
            )}
          </div>
        </ScrollArea>
      </div>
    );
  };

  const renderMetadata = (metadata: string) => {
    try {
      const parsed = JSON.parse(metadata);
      const formattedJson = JSON.stringify(parsed, null, 2);

      return (
        <div className="relative h-full">
          <ScrollArea className="h-full border rounded-md bg-muted/50 dark:bg-muted/20">
            <button
              className={cn(
                "absolute top-2 right-2 z-10 p-1.5 rounded-md bg-muted/70 hover:bg-muted transition-all dark:bg-muted/50 dark:hover:bg-muted/70",
                copied && copiedContent === "metadata"
                  ? "opacity-100"
                  : "opacity-70",
                "hover:opacity-100 hover:scale-105",
              )}
              onClick={() => handleCopy(formattedJson, "metadata")}
              aria-label={
                copied && copiedContent === "metadata" ? "已复制" : "复制元数据"
              }
            >
              {copied && copiedContent === "metadata" ? (
                <Check className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </button>
            <div className="p-3">
              <pre className="text-sm whitespace-pre-wrap font-mono leading-relaxed text-foreground">
                {formattedJson}
              </pre>
            </div>
          </ScrollArea>
        </div>
      );
    } catch {
      return renderContent(metadata, "metadata");
    }
  };

  if (!content) return null;

  return (
    <Sheet open={!!content} onOpenChange={onClose}>
      <SheetContent
        side="right"
        className="w-[80vw] sm:w-[75vw] md:w-[70vw] lg:w-[65vw] xl:w-[60vw] 2xl:w-[1000px] p-4 flex flex-col overflow-hidden !max-w-none"
      >
        <SheetHeader className="pb-2">
          <div className="flex items-center justify-between">
            <SheetTitle className="text-base font-semibold">
              请求详情
            </SheetTitle>
            <div className="flex items-center gap-4 text-xs">
              {content.id && (
                <div className="flex items-center gap-1">
                  <span className="text-muted-foreground">ID:</span>
                  <code className="px-1 py-0.5 bg-muted rounded font-mono">
                    {content.id}
                  </code>
                </div>
              )}
              {content.type && (
                <div className="flex items-center gap-1">
                  <span className="text-muted-foreground">Type:</span>
                  <Badge variant="outline" className="text-xs h-5">
                    {content.type}
                  </Badge>
                </div>
              )}
              {content.model && (
                <div className="flex items-center gap-1">
                  <span className="text-muted-foreground">Model:</span>
                  <code className="px-1 py-0.5 bg-muted rounded font-mono">
                    {content.model}
                  </code>
                </div>
              )}
            </div>
          </div>
        </SheetHeader>
        <Separator className="my-2" />

        <div className="flex-1 overflow-hidden min-h-0">
          <Tabs
            defaultValue={content.defaultTab || defaultTab}
            value={selectedTab}
            onValueChange={value =>
              setSelectedTab(value as "prompt" | "response" | "metadata")
            }
            className="h-full flex flex-col"
          >
            <TabsList
              className={`grid w-full ${
                content.metadata ? "grid-cols-3" : "grid-cols-2"
              }`}
            >
              <TabsTrigger value="prompt">Prompt</TabsTrigger>
              <TabsTrigger value="response">Response</TabsTrigger>
              {content.metadata && (
                <TabsTrigger value="metadata">Metadata</TabsTrigger>
              )}
            </TabsList>

            <div className="pt-3 flex-1 min-h-0">
              <TabsContent value="prompt" className="h-full m-0">
                {renderContent(content.prompt, "prompt")}
              </TabsContent>

              <TabsContent value="response" className="h-full m-0">
                {renderContent(content.response, "response")}
              </TabsContent>

              {content.metadata && (
                <TabsContent value="metadata" className="h-full m-0">
                  {renderMetadata(content.metadata)}
                </TabsContent>
              )}
            </div>
          </Tabs>
        </div>

        <SheetFooter className="mt-4">
          <Button onClick={onClose}>关闭</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default ContentViewerDrawer;
