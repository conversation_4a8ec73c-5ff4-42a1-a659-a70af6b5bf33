"use client";

import { useState, useEffect, useRef } from "react";
import { apiClient } from "@/app/api/request";
import { useSearchParams } from "next/navigation";
import { LoadingSpinner } from "@/app/components/LoadingSpinner";
import { PageHeader } from "@/app/components/PageHeader";

// 导入拆分的组件
import SearchPanel from "./components/SearchPanel";
import ContentViewerDrawer from "./components/ContentViewerDrawer";
import ResultCard from "./components/ResultCard";

// 从接口定义中导入类型
import {
  type GetTraceSessionChatResponse,
  type GetTraceSessionChatRequest,
  ChatCompletionStatus,
} from "@/app/bam/aime/namespaces/trace";

// 扩展请求类型，添加可选的状态字段
interface ExtendedTraceSessionChatRequest
  extends Omit<GetTraceSessionChatRequest, "session_id"> {
  session_id?: string;
  status?: ChatCompletionStatus;
}

interface StatusOption {
  label: string;
  value: string;
}

// 定义状态选项
const STATUS_OPTIONS = [
  { label: "全部", value: "all" },
  { label: "成功", value: ChatCompletionStatus.Success },
  { label: "失败", value: ChatCompletionStatus.Fail },
];

export default function TraceLLMPage() {
  const searchParams = useSearchParams();
  const [sessionId, setSessionId] = useState("");
  const [traceId, setTraceId] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const typeOptionsRef = useRef<StatusOption[]>([
    { label: "全部", value: "all" },
  ]);
  const [typeOptions, setTypeOptions] = useState<StatusOption[]>(
    typeOptionsRef.current,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isFirstSearch, setIsFirstSearch] = useState(true);
  const [chatData, setChatData] = useState<GetTraceSessionChatResponse | null>(
    null,
  );
  const [error, setError] = useState("");
  const [selectedContent, setSelectedContent] = useState<{
    prompt: string;
    response: string;
    id?: string;
    type?: string;
    model?: string;
    metadata?: string;
    defaultTab?: "prompt" | "response" | "metadata";
  } | null>(null);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [totalPages, setTotalPages] = useState(1);

  // 初始化时从URL参数中获取session_id
  useEffect(() => {
    const sessionIdFromUrl = searchParams.get("session_id");
    const traceIdFromUrl = searchParams.get("trace_id");
    const statusFromUrl = searchParams.get("status");
    const typeFromUrl = searchParams.get("type");

    if (statusFromUrl) {
      setStatusFilter(statusFromUrl);
    }

    if (typeFromUrl) {
      setTypeFilter(typeFromUrl);
    }

    if (sessionIdFromUrl) {
      setSessionId(sessionIdFromUrl);
    }

    if (traceIdFromUrl) {
      setTraceId(traceIdFromUrl);
    }

    if (sessionIdFromUrl || traceIdFromUrl) {
      handleSearch(
        sessionIdFromUrl || undefined,
        traceIdFromUrl || undefined,
        1,
        statusFromUrl || "all",
        typeFromUrl || "all",
      );
    }
  }, [searchParams]);

  const handleSearch = async (
    sid?: string,
    tid?: string,
    page = 1,
    status?: string,
    type?: string,
  ) => {
    const searchSessionId = sid || sessionId;
    const searchTraceId = tid || traceId;
    const searchStatus = status !== undefined ? status : statusFilter;
    const searchType = type !== undefined ? type : typeFilter;

    if (!searchSessionId && !searchTraceId) {
      setError("请输入有效的 Session ID 或 Trace ID");
      return;
    }

    setIsLoading(true);
    setError("");
    setCurrentPage(page);

    try {
      const params: ExtendedTraceSessionChatRequest = {
        page_num: page,
        page_size: pageSize,
      };

      // 添加 session_id 参数（如果有的话）
      if (searchSessionId && searchSessionId.trim()) {
        params.session_id = searchSessionId;
      }

      // 添加 trace_id 参数（如果有的话）
      if (searchTraceId && searchTraceId.trim()) {
        params.trace_id = searchTraceId;
      }

      // 只有当状态不是 "all" 时才添加状态过滤
      if (searchStatus && searchStatus !== "all") {
        params.status = searchStatus as ChatCompletionStatus;
      }

      // 只有当类型不是 "all" 时才添加类型过滤
      if (searchType && searchType !== "all") {
        params.type = searchType;
      }

      const result = await apiClient.GetTraceSessionChat(
        params as GetTraceSessionChatRequest,
      );

      setChatData(result);

      // 从数据中提取类型选项
      if (result.chat_completions.length > 0) {
        const uniqueTypes = new Set<string>();
        result.chat_completions.forEach(chat => {
          if (chat.type) {
            uniqueTypes.add(chat.type);
          }
        });

        // 更新缓存的类型选项
        const newTypeOptions = Array.from(uniqueTypes).map(type => ({
          label: type,
          value: type,
        }));

        // 合并现有的类型选项和新发现的类型
        const existingTypes = new Set(
          typeOptionsRef.current.map(opt => opt.value),
        );
        const mergedOptions = [
          ...typeOptionsRef.current,
          ...newTypeOptions.filter(opt => !existingTypes.has(opt.value)),
        ];

        typeOptionsRef.current = mergedOptions;
        setTypeOptions(mergedOptions);
      }

      // 计算总页数
      const totalItems = typeof result.total === "number" ? result.total : 0;
      const calculatedPages = Math.max(1, Math.ceil(totalItems / pageSize));
      setTotalPages(calculatedPages);

      // 如果数据加载成功，后续搜索将不显示居中loading
      if (isFirstSearch) {
        setIsFirstSearch(false);
      }
    } catch (err) {
      console.error("获取聊天数据失败:", err);
      setError("获取聊天数据失败: " + (err as Error).message);
      setChatData(null);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    handleSearch(sessionId, traceId, page);
  };

  const handleStatusChange = (status: string) => {
    setStatusFilter(status);
    handleSearch(sessionId, traceId, 1, status);
  };

  const handleTypeChange = (type: string) => {
    setTypeFilter(type);
    handleSearch(sessionId, traceId, 1, undefined, type);
  };

  const showFullContent = (
    prompt: string,
    response: string,
    id?: string,
    type?: string,
    model?: string,
    metadata?: string,
    defaultTab: "prompt" | "response" | "metadata" = "prompt",
  ) => {
    setSelectedContent({
      prompt,
      response,
      id,
      type,
      model,
      metadata,
      defaultTab,
    });
  };

  return (
    <div className="flex flex-col gap-3">
      {/* 页面标题 */}
      <PageHeader
        title="LLM Requests"
        description="查看和管理 LLM 请求的追踪记录"
      />

      {/* 搜索面板 */}
      <div className="max-w-5xl">
        <SearchPanel
          sessionId={sessionId}
          setSessionId={setSessionId}
          traceId={traceId}
          setTraceId={setTraceId}
          statusFilter={statusFilter}
          onStatusChange={handleStatusChange}
          statusOptions={STATUS_OPTIONS}
          typeFilter={typeFilter}
          onTypeChange={handleTypeChange}
          typeOptions={typeOptions}
          handleSearch={() => handleSearch(undefined, undefined, 1)}
          isLoading={isLoading && !isFirstSearch}
          error={error}
        />
      </div>

      {/* 加载状态 - 仅在首次加载时显示在页面中央 */}
      {isLoading && isFirstSearch && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 bg-white py-6 px-8 rounded-xl shadow-xl border border-gray-100 flex flex-col items-center justify-center gap-4">
          <LoadingSpinner />
          <p className="text-sm font-medium text-gray-700">正在加载数据...</p>
        </div>
      )}

      {/* 结果显示 */}
      <div
        className={`transition-opacity duration-200 ${
          isLoading && isFirstSearch ? "opacity-50" : "opacity-100"
        } ${chatData ? "flex-1" : "flex-none"}`}
      >
        {chatData && (
          <ResultCard
            chatData={chatData}
            showFullContent={showFullContent}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        )}
      </div>

      {/* 内容查看对话框 */}
      <ContentViewerDrawer
        content={selectedContent}
        onClose={() => setSelectedContent(null)}
      />
    </div>
  );
}
