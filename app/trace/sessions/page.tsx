"use client";

"use client";

import { useState, useEffect, useMemo } from "react";
import { apiClient } from "@/app/api/request";
import { PageHeader } from "@/app/components/PageHeader";
import { LoadingSpinner } from "@/app/components/LoadingSpinner";

import { Button } from "@/components/ui/button";
import { ChevronUpIcon, ChevronDownIcon } from "@radix-ui/react-icons";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { type Session } from "@/app/bam/aime/namespaces/session";
import { format } from 'date-fns';
import { DatePicker, Input } from '@arco-design/web-react';
import '@arco-design/web-react/dist/css/arco.css';
import dayjs from 'dayjs';

export default function TraceSessionsPage() {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [dateRange, setDateRange] = useState<[string, string] | undefined>(undefined);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [sortKey, setSortKey] = useState<'created_at' | 'updated_at'>('created_at');
  const [baseUrl, setBaseUrl] = useState('');

  useEffect(() => {
    setBaseUrl(window.location.origin);
    const fetchAllSessions = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const initialResponse = await apiClient.ListSessions({ page_num: 1, page_size: 1 });
        const total = initialResponse.total;
        const pageSize = 10;

        if (Number(total) > 0) {
          const pageCount = Math.ceil(Number(total) / pageSize);
          const fetchPromises = Array.from({ length: pageCount }, (_, i) =>
            apiClient.ListSessions({ page_num: i + 1, page_size: pageSize })
          );

          const responses = await Promise.all(fetchPromises);
          const allSessions = responses.flatMap(response => response.sessions || []);
          setSessions(allSessions);
        } else {
          setSessions([]);
        }
      } catch (err) {
        console.error("Failed to fetch sessions:", err);
        setError("Failed to fetch sessions. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllSessions();
  }, []);

  const filteredAndSortedSessions = useMemo(() => {
    let filtered = sessions;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(session =>
        session.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Date range filter
    if (dateRange && dateRange[0] && dateRange[1]) {
      const [startDate, endDate] = dateRange;
      filtered = filtered.filter(session => {
        const sessionDate = new Date(session.created_at);
        return sessionDate >= new Date(startDate) && sessionDate <= new Date(endDate);
      });
    }

    // Sorting
    filtered.sort((a, b) => {
      const dateA = new Date(a[sortKey]).getTime();
      const dateB = new Date(b[sortKey]).getTime();
      return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
    });

    return filtered;
  }, [sessions, searchTerm, dateRange, sortKey, sortOrder]);

  const handleSort = (key: 'created_at' | 'updated_at') => {
    if (sortKey === key) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortKey(key);
      setSortOrder('desc');
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  return (
    <div className="w-full p-6 bg-gray-50 min-h-screen">
      <PageHeader
        title="Session List"
        description="Browse and manage all sessions."
      />
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid sm:grid-cols-2 gap-4">
            <Input
              allowClear
              placeholder="Search by title..."
              value={searchTerm}
              onChange={setSearchTerm}
            />
            <DatePicker.RangePicker
              onChange={(dateString) => setDateRange(dateString as [string, string])}
              shortcuts={[
                {
                  text: 'Last 3 Days',
                  value: () => {
                    const end = dayjs();
                    const start = end.subtract(3, 'day');
                    return [start, end];
                  },
                  onClick: () => {
                    const end = dayjs();
                    const start = end.subtract(3, 'day');
                    setDateRange([start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')]);
                  },
                },
                {
                  text: 'Last 7 Days',
                  value: () => {
                    const end = dayjs();
                    const start = end.subtract(7, 'day');
                    return [start, end];
                  },
                  onClick: () => {
                    const end = dayjs();
                    const start = end.subtract(7, 'day');
                    setDateRange([start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')]);
                  },
                }
              ]}
            />
          </div>
        </CardContent>
      </Card>
      <div className="w-full">
        {/* 表头 */}
        <div className="bg-gray-100 border-b border-gray-200 sticky top-0 z-10 rounded-t-lg">
          <div className="grid grid-cols-12 gap-4 p-3 text-sm font-medium text-gray-700">
            <div className="col-span-3">Title</div>
            <div className="col-span-1">Creator</div>
            <div className="col-span-1">Status</div>
            <div className="col-span-2">
              <button
                onClick={() => handleSort('created_at')}
                className="flex items-center gap-1 hover:text-blue-600 transition-colors"
              >
                Created At
                {sortKey === 'created_at' && (
                  sortOrder === 'asc' ? <ChevronUpIcon className="w-4 h-4" /> : <ChevronDownIcon className="w-4 h-4" />
                )}
              </button>
            </div>
            <div className="col-span-2">
              <button
                onClick={() => handleSort('updated_at')}
                className="flex items-center gap-1 hover:text-blue-600 transition-colors"
              >
                Updated At
                {sortKey === 'updated_at' && (
                  sortOrder === 'asc' ? <ChevronUpIcon className="w-4 h-4" /> : <ChevronDownIcon className="w-4 h-4" />
                )}
              </button>
            </div>
            <div className="col-span-1">Template</div>
            <div className="col-span-2 text-right">Actions</div>
          </div>
        </div>

        {/* 数据行 */}
        <div className="divide-y divide-gray-200 bg-white rounded-b-lg shadow-sm">
          {filteredAndSortedSessions.map((session) => (
            <div
              key={session.id}
              className="grid grid-cols-12 gap-4 p-4 items-center hover:bg-gray-50 transition-colors duration-200"
            >
              <div className="col-span-3 font-medium text-gray-800 truncate" title={session.title}>
                {session.title}
              </div>
              <div className="col-span-1 text-sm text-gray-600">{session.creator}</div>
              <div className="col-span-1">
                 <span className={`px-2 py-1 rounded text-white text-xs font-medium ${session.status === 'running' ? 'bg-blue-500' : 'bg-green-500'}`}>
                  {session.status}
                </span>
              </div>
              <div className="col-span-2 text-sm text-gray-600">
                {format(new Date(session.created_at), 'yyyy-MM-dd HH:mm:ss')}
              </div>
              <div className="col-span-2 text-sm text-gray-600">
                {format(new Date(session.updated_at), 'yyyy-MM-dd HH:mm:ss')}
              </div>
              <div className="col-span-1 text-sm text-gray-600">
                {session.template_id ? 'Yes' : 'No'}
              </div>
              <div className="col-span-2 flex justify-end space-x-2">
                <Button asChild variant="outline" size="sm">
                  <a href={`${baseUrl}/chat/${session.id}`} target="_blank" rel="noopener noreferrer">Chat</a>
                </Button>
                <Button asChild variant="outline" size="sm">
                  <a href={`${baseUrl}/lab/trace/graph?session_id=${session.id}`} target="_blank" rel="noopener noreferrer">Graph</a>
                </Button>
              </div>
            </div>
          ))}
        </div>
        {filteredAndSortedSessions.length === 0 && (
           <div className="text-center py-12 text-gray-500 bg-white rounded-b-lg">
             No sessions found.
           </div>
        )}
      </div>
    </div>
  );
}