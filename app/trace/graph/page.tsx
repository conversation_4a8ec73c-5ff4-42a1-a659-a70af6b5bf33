"use client";

import { useState, useEffect, useRef, useMemo } from "react";
import { apiClient } from "@/app/api/request";
import { useSearchParams } from "next/navigation";
import { LoadingSpinner } from "@/app/components/LoadingSpinner";
import { PageHeader } from "@/app/components/PageHeader";

// 导入组件
import SearchPanel from "./components/SearchPanel";
import TimelineView from "./components/TimelineView";
import ListView from "./components/ListView";
import ToolListView from "./components/ToolListView";
import FlowView from "./components/FlowView";
import StatsCards from "./components/StatsCards";
import DetailModal from "./components/DetailModal";
import SearchBox from "./components/SearchBox";
import RedundancyAnalysis from "./components/RedundancyAnalysis";

// 从接口定义中导入类型
import {
  type GetTraceSessionChatResponse,
  type GetTraceSessionChatRequest,
  type GetTraceSessionRequest,
  type GetTraceEventsRequest,
  ChatCompletionStatus,
  type ChatCompletion,
} from "@/app/bam/aime/namespaces/trace";
import { EventSourceMessage } from "@microsoft/fetch-event-source";
import { type EvaluationResult } from "./utils/aimeEvaluator";

// 扩展请求类型，添加可选的状态字段
interface ExtendedTraceSessionChatRequest extends GetTraceSessionChatRequest {
  status?: ChatCompletionStatus;
}

// Tool 事件数据定义
interface ToolEventData {
  id: string;
  tool: string;
  description: string;
  step_id: string;
  status: 'started' | 'success' | 'failed';
  inputs?: Record<string, unknown>;
  error?: string;
  timestamp: string;
  offset: number;
}

// Tool 调用项定义（合并 started 和 success/failed 事件）
interface ToolCallItem {
  id: string;
  tool: string;
  description: string;
  step_id: string;
  status: 'success' | 'failed' | 'running';
  inputs?: Record<string, unknown>;
  error?: string;
  startTime: string;
  endTime?: string;
  duration?: number; // 毫秒
  startOffset: number;
  endOffset?: number;
}

interface StatusOption {
  label: string;
  value: string;
}

// 定义状态选项
const STATUS_OPTIONS = [
  { label: "全部", value: "all" },
  { label: "成功", value: ChatCompletionStatus.Success },
  { label: "失败", value: ChatCompletionStatus.Fail },
];

export default function TraceGraphPage() {
  const searchParams = useSearchParams();
  const [sessionId, setSessionId] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const typeOptionsRef = useRef<StatusOption[]>([
    { label: "全部", value: "all" },
  ]);
  const [typeOptions, setTypeOptions] = useState<StatusOption[]>(
    typeOptionsRef.current,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isFirstSearch, setIsFirstSearch] = useState(true);
  const [isLoadingTools, setIsLoadingTools] = useState(false);
  const [chatData, setChatData] = useState<GetTraceSessionChatResponse | null>(
    null,
  );
  const [allChatData, setAllChatData] = useState<ChatCompletion[]>([]);
  const [originalChatData, setOriginalChatData] = useState<ChatCompletion[]>([]);
  const [toolCallData, setToolCallData] = useState<ToolCallItem[]>([]);
  const [error, setError] = useState("");
  const [selectedItem, setSelectedItem] = useState<ChatCompletion | null>(null);
  const [activeTab, setActiveTab] = useState<"timeline" | "list" | "tools" | "analysis" | "flow">("timeline");
  const [loadingProgress, setLoadingProgress] = useState({ current: 0, total: 0 });
  
  // 添加高亮冗余步骤的状态
  const [highlightedSteps, setHighlightedSteps] = useState<string[]>([]);
  const [highlightedRelatedSteps, setHighlightedRelatedSteps] = useState<string[]>([]); // 新增：相关步骤高亮
  const [redundancyResult, setRedundancyResult] = useState<EvaluationResult | null>(null);

  // 添加智能分析相关的状态
  const [analysisResult, setAnalysisResult] = useState<EvaluationResult | null>(null);
  const [analysisError, setAnalysisError] = useState<string>('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string>(''); // 追踪当前 sessionId

  // 根据筛选条件过滤数据
  const filteredByFilters = useMemo(() => {
    let filtered = originalChatData;

    // 状态筛选
    if (statusFilter && statusFilter !== "all") {
      filtered = filtered.filter(item => item.status === statusFilter);
    }

    // 类型筛选
    if (typeFilter && typeFilter !== "all") {
      filtered = filtered.filter(item => item.type === typeFilter);
    }

    return filtered;
  }, [originalChatData, statusFilter, typeFilter]);

  // 根据搜索查询过滤数据
  const filteredChatData = useMemo(() => {
    let filtered = filteredByFilters;

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item => {
        const prompt = (item.prompt || "").toLowerCase();
        const response = (item.response || "").toLowerCase();
        return prompt.includes(query) || response.includes(query);
      });
    }

    return filtered;
  }, [filteredByFilters, searchQuery]);

  // 注释掉这行，让 allChatData 保持所有原始数据
  // useEffect(() => {
  //   setAllChatData(filteredByFilters);
  // }, [filteredByFilters]);

  // 初始化时从URL参数中获取session_id
  useEffect(() => {
    const sessionIdFromUrl = searchParams.get("session_id");
    const statusFromUrl = searchParams.get("status");
    const typeFromUrl = searchParams.get("type");

    if (statusFromUrl) {
      setStatusFilter(statusFromUrl);
    }

    if (typeFromUrl) {
      setTypeFilter(typeFromUrl);
    }

    if (sessionIdFromUrl) {
      setSessionId(sessionIdFromUrl);
      handleSearch(sessionIdFromUrl);
    }
  }, [searchParams]);

  // 页面刷新时重置智能分析状态
  useEffect(() => {
    // 重置智能分析相关状态（页面刷新时）
    setAnalysisResult(null);
    setAnalysisError('');
    setIsAnalyzing(false);
    setCurrentSessionId('');
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // 加载所有数据的函数
  const loadAllData = async (
    sessionId: string,
  ) => {
    const allData: ChatCompletion[] = [];
    let page = 1;
    const pageSize = 200; // 使用较大的页面大小来减少请求次数
    let totalPages = 0;

    while (true) {
      const params: ExtendedTraceSessionChatRequest = {
        session_id: sessionId,
        page_num: page,
        page_size: pageSize,
      };

      // 更新加载进度
      if (page === 1) {
        // 第一页请求，先估算总页数
        setLoadingProgress({ current: 1, total: 1 });
      } else {
        setLoadingProgress({ current: page, total: totalPages });
      }

      const result = await apiClient.GetTraceSessionChat(
        params as GetTraceSessionChatRequest,
      );

      // print the first 10 elements for chat_completions
      console.log('chat_completions:', result.chat_completions.slice(0, 10));
      allData.push(...result.chat_completions);

      // 第一页时计算总页数
      if (page === 1 && result.total) {
        totalPages = Math.ceil(Number(result.total) / pageSize);
        setLoadingProgress({ current: 1, total: totalPages });
      }

      // 如果当前页的数据少于页面大小，说明已经是最后一页
      if (result.chat_completions.length < pageSize) {
        break;
      }

      page++;
    }

    // 重置加载进度
    setLoadingProgress({ current: 0, total: 0 });
    return allData;
  };

  // 获取 tool 事件数据的函数
  const loadToolEvents = async (sessionId: string) => {
    try {
      // 首先获取 session 信息以获取 run_id
      const sessionRequest: GetTraceSessionRequest = {
        session_id: sessionId,
      };
      
      const sessionResult = await apiClient.GetTraceSession(sessionRequest);
      const runId = sessionResult.session.run_id;
      
      if (!runId) {
        console.warn('Session 没有 run_id，跳过 tool 事件加载');
        return [];
      }

      // 使用 run_id 获取事件数据
      const eventsRequest: GetTraceEventsRequest = {
        run_id: runId,
      };

      // 收集所有 tool 事件
      const toolEvents: ToolEventData[] = [];
      
      // 使用 EventSource 获取事件流
      await new Promise<void>((resolve, reject) => {
        let count =0;
        apiClient.GetTraceEvents(eventsRequest, {
          onmessage: (ev: EventSourceMessage) => {
            if (!ev.data) return;
            
            try {
              const eventData = JSON.parse(ev.data);
              
              // 检查是否是结束事件
              if (eventData.event === 'done') {
                console.log('收到 done 事件，结束加载');
                resolve();
                return;
              }

              if(eventData.event == "ping"){
                count++;
              }
              if(count > 2){
                resolve();
                return;
              }
              
              // 处理 tool 类型的事件
              if (eventData.event === 'tool') {
                // 根据你提供的事件数据结构，tool 数据在 eventData.data 中
                const toolData = eventData.data;
                
                
                // 只收集 started 和 success/failed 状态的事件，且必须有 tool、status 和 step_id
                if (toolData && toolData.tool && toolData.status && toolData.step_id && 
                    (toolData.status === 'started' || toolData.status === 'success' || toolData.status === 'failed' || toolData.status === 'completed')) {
                  const toolEvent = {
                    id: `${toolData.step_id}_${toolData.tool}_${toolData.status}_${eventData.offset || 0}`,
                    tool: toolData.tool,
                    description: toolData.description || '',
                    step_id: toolData.step_id,
                    status: toolData.status,
                    inputs: toolData.inputs,
                    error: toolData.error,
                    timestamp: eventData.timestamp || toolData.timestamp,
                    offset: eventData.offset || 0,
                  };
                  
                  console.log('添加 tool 事件:', toolEvent);
                  toolEvents.push(toolEvent);
                } else {
                  console.log('跳过 tool 事件 - 不符合条件:', {
                    hasToolData: !!toolData,
                    hasTool: !!(toolData && toolData.tool),
                    hasStatus: !!(toolData && toolData.status),
                    hasStepId: !!(toolData && toolData.step_id),
                    status: toolData?.status,
                    isValidStatus: toolData?.status === 'started' || toolData?.status === 'success' || toolData?.status === 'failed'
                  });
                }
              }
            } catch (error) {
              console.error('解析事件数据失败:', error, ev.data);
            }
          },
          signal: new AbortController().signal
        }).catch(error => {
          console.error('获取事件流失败:', error);
          reject(error);
        });

        // 设置超时，避免无限等待
        setTimeout(() => {
          resolve();
        }, 50000); // 50秒超时
      });

      // 处理 tool 事件，合并 started 和 success/failed 事件
      const toolCallMap = new Map<string, ToolCallItem>();
      
      toolEvents.forEach(event => {
        const key = `${event.step_id}_${event.tool}`;
        
        if (event.status === 'started') {
          const toolItem: ToolCallItem = {
            id: key,
            tool: event.tool,
            description: event.description,
            step_id: event.step_id,
            status: 'running',
            inputs: event.inputs,
            startTime: event.timestamp,
            startOffset: event.offset,
          };
          toolCallMap.set(key, toolItem);
        } else if (event.status === 'success' || event.status === 'failed' || event.status === 'completed') {
          const existingItem = toolCallMap.get(key);
          if (existingItem) {
            existingItem.status = event.status;
            existingItem.endTime = event.timestamp;
            existingItem.endOffset = event.offset;
            existingItem.error = event.error;
            
            // 计算耗时
            if (existingItem.startTime && event.timestamp) {
              const startTime = new Date(existingItem.startTime);
              const endTime = new Date(event.timestamp);
              existingItem.duration = endTime.getTime() - startTime.getTime();
            }
          } else {
            // 如果没有对应的 started 事件，创建一个新的项
            const toolItem: ToolCallItem = {
              id: key,
              tool: event.tool,
              description: event.description,
              step_id: event.step_id,
              status: event.status,
              inputs: event.inputs,
              error: event.error,
              startTime: event.timestamp,
              endTime: event.timestamp,
              duration: 0,
              startOffset: event.offset,
              endOffset: event.offset,
            };
            toolCallMap.set(key, toolItem);
          }
        }
      });
      
      const result = Array.from(toolCallMap.values()).sort((a, b) => a.startOffset - b.startOffset);

      return Array.from(toolCallMap.values()).sort((a, b) => a.startOffset - b.startOffset);
    } catch (error) {
      return [];
    }
  };

  const handleSearch = async (
    sid?: string,
  ) => {
    const searchSessionId = sid || sessionId;

    if (!searchSessionId.trim()) {
      return;
    }

    // 如果 sessionId 发生变化，重置智能分析状态
    if (currentSessionId !== searchSessionId) {
      setAnalysisResult(null);
      setAnalysisError('');
      setIsAnalyzing(false);
      setCurrentSessionId(searchSessionId);
    }

    setIsLoading(true);
    setError("");

    try {
      // 先加载聊天数据
      const allData = await loadAllData(searchSessionId);
      
      // 按创建时间排序
      allData.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      
      // 保存原始数据
      setOriginalChatData(allData);

      // 同时加载第一页数据用于显示总数等信息
      const params: ExtendedTraceSessionChatRequest = {
        session_id: searchSessionId,
        page_num: 1,
        page_size: 1,
      };

      const result = await apiClient.GetTraceSessionChat(
        params as GetTraceSessionChatRequest,
      );

      setChatData(result);
      // 设置所有原始数据（未过滤）
      setAllChatData(allData);
      setOriginalChatData(allData);

      // 从数据中提取类型选项
      if (allData.length > 0) {
        const uniqueTypes = new Set<string>();
        allData.forEach(chat => {
          if (chat.type) {
            uniqueTypes.add(chat.type);
          }
        });

        // 更新缓存的类型选项
        const newTypeOptions = Array.from(uniqueTypes).map(type => ({
          label: type,
          value: type,
        }));

        // 合并现有的类型选项和新发现的类型
        const existingTypes = new Set(
          typeOptionsRef.current.map(opt => opt.value),
        );
        const mergedOptions = [
          ...typeOptionsRef.current,
          ...newTypeOptions.filter(opt => !existingTypes.has(opt.value)),
        ];

        typeOptionsRef.current = mergedOptions;
        setTypeOptions(mergedOptions);
      }

      // 如果数据加载成功，后续搜索将不显示居中loading
      if (isFirstSearch) {
        setIsFirstSearch(false);
      }

      // 聊天数据加载完成后，停止主要的loading状态
      setIsLoading(false);

      // 异步加载 tool 事件数据，不阻塞UI显示
      setIsLoadingTools(true);
      loadToolEvents(searchSessionId).then(toolData => {
        console.log('Tool 事件数据加载完成:', toolData.length);
        setToolCallData(toolData);
        setIsLoadingTools(false);
      }).catch(err => {
        console.error('Tool 事件数据加载失败:', err);
        // 即使 tool 数据加载失败，也不影响主要功能
        setToolCallData([]);
        setIsLoadingTools(false);
      });

    } catch (err) {
      console.error("获取数据失败:", err);
      setError("获取数据失败，请检查 Session ID 是否正确");
      setChatData(null);
      setAllChatData([]);
      setOriginalChatData([]);
      setToolCallData([]);
      setIsLoading(false);
      setIsLoadingTools(false);
    }
  };

  const handleStatusChange = (status: string) => {
    setStatusFilter(status);
    // 不再调用 handleSearch，直接通过 useMemo 进行本地筛选
  };

  const handleTypeChange = (type: string) => {
    setTypeFilter(type);
    // 不再调用 handleSearch，直接通过 useMemo 进行本地筛选
  };

  const showDetail = (item: ChatCompletion) => {
    setSelectedItem(item);
  };

  const navigateDetail = (direction: 'prev' | 'next') => {
    if (!selectedItem || !allChatData.length) return;
    
    const currentIndex = allChatData.findIndex(item => item.id === selectedItem.id);
    let newIndex = currentIndex;
    
    if (direction === 'prev') {
      newIndex = Math.max(0, currentIndex - 1);
    } else {
      newIndex = Math.min(allChatData.length - 1, currentIndex + 1);
    }
    
    if (newIndex !== currentIndex) {
      setSelectedItem(allChatData[newIndex]);
    }
  };

  // 处理智能分析完成
  const handleAnalysisComplete = (result: EvaluationResult) => {
    console.log('智能分析完成:', result);
    
    // 保存分析结果到智能分析状态
    setAnalysisResult(result);
    setAnalysisError('');
    setIsAnalyzing(false);
    
    // 保存分析结果到冗余分析状态（兼容现有逻辑）
    setRedundancyResult(result);
    
    if (result.redundant && result.steps.length > 0) {
      // 提取需要关注的步骤对应的 ID
      const stepIds = result.steps.map(step => step.step);
      setHighlightedSteps(stepIds);
      
      // 不要自动切换标签页，让用户自己选择查看方式
      // setActiveTab("timeline");
      
      // 显示提示信息
      console.log(`发现 ${result.steps.length} 个需要关注的步骤，可在时间线视图中查看高亮显示`);
    } else {
      // 清除高亮
      setHighlightedSteps([]);
    }
  };

  // 处理智能分析开始
  const handleAnalysisStart = () => {
    setIsAnalyzing(true);
    setAnalysisError('');
  };

  // 处理智能分析错误
  const handleAnalysisError = (error: string) => {
    setAnalysisError(error);
    setIsAnalyzing(false);
  };

  // 处理重新分析（清除之前的结果）
  const handleResetAnalysis = () => {
    setAnalysisResult(null);
    setAnalysisError('');
    setIsAnalyzing(false);
    // 清除相关的高亮状态
    setHighlightedSteps([]);
    setHighlightedRelatedSteps([]);
    setRedundancyResult(null);
  };

  // 处理高亮单个步骤以及相关步骤
  const handleHighlightStep = (stepId: string) => {
    // 查找对应的步骤及其相关步骤
    const step = analysisResult?.steps.find(s => s.step === stepId);
    
    if (step && step.related_steps && step.related_steps.length > 0) {
      // 高亮当前步骤（冗余步骤）
      setHighlightedSteps([stepId]);
      // 高亮相关步骤
      setHighlightedRelatedSteps(step.related_steps);
      console.log(`高亮冗余步骤 ${stepId} 及其相关步骤:`, step.related_steps);
    } else {
      // 如果没有相关步骤，只高亮当前步骤
      setHighlightedSteps([stepId]);
      setHighlightedRelatedSteps([]);
      console.log(`高亮步骤 ${stepId}`);
    }
    
    setActiveTab("timeline");
  };

  // 处理清除高亮
  const handleClearHighlight = () => {
    setHighlightedSteps([]);
    setHighlightedRelatedSteps([]);
    // 注意：这里不应该清除分析结果，只清除高亮状态
    // setRedundancyResult(null);
    console.log('已清除所有高亮');
  };

  // 处理切换到时间线视图
  const handleSwitchToTimeline = () => {
    setActiveTab("timeline");
  };

  return (
    <div className="flex flex-col gap-3">
      {/* 页面标题 */}
      <PageHeader
        title="LLM Timeline Graph"
        description="LLM 请求的时间线可视化分析"
      />

      {/* 搜索面板 */}
      <div>
        <SearchPanel
          sessionId={sessionId}
          setSessionId={setSessionId}
          statusFilter={statusFilter}
          onStatusChange={handleStatusChange}
          statusOptions={STATUS_OPTIONS}
          typeFilter={typeFilter}
          onTypeChange={handleTypeChange}
          typeOptions={typeOptions}
          handleSearch={() => handleSearch()}
          isLoading={isLoading && !isFirstSearch}
          error={error}
        />
      </div>

      {/* 加载状态 - 仅在首次加载时显示在页面中央 */}
      {isLoading && isFirstSearch && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 bg-white py-6 px-8 rounded-xl shadow-xl border border-gray-100 flex flex-col items-center justify-center gap-4">
          <LoadingSpinner />
          <p className="text-sm font-medium text-gray-700">正在加载数据...</p>
          {loadingProgress.total > 0 && (
            <div className="text-xs text-gray-500 text-center">
              <div>正在加载第 {loadingProgress.current} 页，共 {loadingProgress.total} 页</div>
              <div className="w-48 bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${(loadingProgress.current / loadingProgress.total) * 100}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 结果显示 */}
      <div
        className={`transition-opacity duration-200 ${
          isLoading && isFirstSearch ? "opacity-50" : "opacity-100"
        } ${chatData ? "flex-1" : "flex-none"}`}
      >
        {chatData && allChatData.length > 0 && (
          <div className="flex flex-col gap-4">
            {/* 统计卡片 */}
            <StatsCards data={allChatData} toolData={toolCallData} />

            {/* 搜索框 */}
            <SearchBox
              onSearch={setSearchQuery}
            />

            {/* 标签页 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="flex border-b border-gray-200">
                <button
                  className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
                    activeTab === "timeline"
                      ? "bg-blue-50 text-blue-700 border-b-2 border-blue-700"
                      : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveTab("timeline")}
                >
                  时间线视图
                  {(highlightedSteps.length > 0 || highlightedRelatedSteps.length > 0) && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                      {highlightedSteps.length + highlightedRelatedSteps.length} 个关注步骤
                    </span>
                  )}
                </button>
                <button
                  className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
                    activeTab === "list"
                      ? "bg-blue-50 text-blue-700 border-b-2 border-blue-700"
                      : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveTab("list")}
                >
                  列表视图
                  {(highlightedSteps.length > 0 || highlightedRelatedSteps.length > 0) && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                      {highlightedSteps.length + highlightedRelatedSteps.length} 个关注步骤
                    </span>
                  )}
                </button>
                <button
                  className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
                    activeTab === "tools"
                      ? "bg-blue-50 text-blue-700 border-b-2 border-blue-700"
                      : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveTab("tools")}
                >
                  工具调用
                  {isLoadingTools && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      加载中...
                    </span>
                  )}
                </button>
                <button
                  className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
                    activeTab === "analysis"
                      ? "bg-blue-50 text-blue-700 border-b-2 border-blue-700"
                      : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveTab("analysis")}
                >
                  智能分析
                  {analysisResult?.redundant && analysisResult.steps.length > 0 && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                      {analysisResult.steps.length} 个关注步骤
                    </span>
                  )}
                </button>
                <button
                  className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
                    activeTab === "flow"
                      ? "bg-blue-50 text-blue-700 border-b-2 border-blue-700"
                      : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveTab("flow")}
                >
                  Flow消息
                </button>
              </div>

              <div className="p-6">
                {activeTab === "timeline" && (
                  <TimelineView 
                    data={(highlightedSteps.length > 0 || highlightedRelatedSteps.length > 0) ? allChatData : filteredChatData} 
                    toolData={toolCallData} 
                    onItemClick={showDetail}
                    highlightedItems={highlightedSteps}
                    highlightedRelatedItems={highlightedRelatedSteps}
                    redundancyResult={redundancyResult}
                    onClearHighlight={handleClearHighlight}
                  />
                )}
                {activeTab === "list" && (
                  <ListView 
                    data={(highlightedSteps.length > 0 || highlightedRelatedSteps.length > 0) ? allChatData : filteredChatData} 
                    onItemClick={showDetail}
                    highlightedItems={highlightedSteps}
                    highlightedRelatedItems={highlightedRelatedSteps}
                    redundancyResult={redundancyResult}
                    onClearHighlight={handleClearHighlight}
                  />
                )}
                {activeTab === "tools" && (
                  <ToolListView data={toolCallData} isLoading={isLoadingTools} />
                )}
                {activeTab === "analysis" && (
                  <RedundancyAnalysis 
                    chatData={allChatData} 
                    onAnalysisComplete={handleAnalysisComplete}
                    onAnalysisStart={handleAnalysisStart}
                    onAnalysisError={handleAnalysisError}
                    onResetAnalysis={handleResetAnalysis}
                    onHighlightStep={handleHighlightStep}
                    onClearHighlight={handleClearHighlight}
                    onSwitchToTimeline={handleSwitchToTimeline}
                    // 传递状态
                    result={analysisResult}
                    error={analysisError}
                    isAnalyzing={isAnalyzing}
                  />
                )}
                {activeTab === "flow" && (
                  <FlowView data={allChatData} />
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 详情模态框 */}
      <DetailModal
        item={selectedItem}
        onClose={() => setSelectedItem(null)}
        onNavigate={navigateDetail}
        hasPrev={selectedItem ? allChatData.findIndex(item => item.id === selectedItem.id) > 0 : false}
        hasNext={selectedItem ? allChatData.findIndex(item => item.id === selectedItem.id) < allChatData.length - 1 : false}
        redundancyResult={redundancyResult}
        highlightedItems={highlightedSteps}
        highlightedRelatedItems={highlightedRelatedSteps}
      />
    </div>
  );
}