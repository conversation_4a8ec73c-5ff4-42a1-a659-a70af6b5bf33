import { Dialog, Flex, Text, Heading, <PERSON>rollArea, Tabs } from "@radix-ui/themes";
import { Cross2Icon, ChevronDownIcon, ChevronRightIcon } from "@radix-ui/react-icons";
import { type ChatCompletion } from "@/app/bam/aime/namespaces/trace";
import { type EvaluationResult } from "../utils/aimeEvaluator";
import { Bug } from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { setChatData } from "@/app/store/playground";
import { parseRoleContentFromTags } from "@/app/playground/chat2/utils";
import { Button } from "@/components/ui/button";

interface DetailModalProps {
  item: ChatCompletion | null;
  onClose: () => void;
  onNavigate: (direction: 'prev' | 'next') => void;
  hasPrev: boolean;
  hasNext: boolean;
  redundancyResult?: EvaluationResult | null;
  highlightedItems?: string[]; // 冗余步骤 ID 列表
  highlightedRelatedItems?: string[]; // 相关步骤 ID 列表
}

interface ParsedXmlBlock {
  type: 'system' | 'assistant' | 'user' | 'tool' | 'tools' | 'other';
  content: string;
  raw: string;
}

// XML解析函数
const parseXmlContent = (content: string): ParsedXmlBlock[] => {
  if (!content) return [];
  
  const blocks: ParsedXmlBlock[] = [];
  
  // 使用正则表达式匹配XML标签
  const xmlRegex = /<(system|assistant|user|tool|tools)\b[^>]*>([\s\S]*?)<\/\1>/gi;
  let lastIndex = 0;
  let match;
  
  while ((match = xmlRegex.exec(content)) !== null) {
    // 添加标签前的内容（如果有）
    if (match.index > lastIndex) {
      const beforeContent = content.slice(lastIndex, match.index).trim();
      if (beforeContent) {
        blocks.push({
          type: 'other',
          content: beforeContent,
          raw: beforeContent
        });
      }
    }
    
    // 添加匹配的XML块
    const tagType = match[1].toLowerCase() as 'system' | 'assistant' | 'user' | 'tool' | 'tools';
    const tagContent = match[2].trim();
    
    blocks.push({
      type: tagType,
      content: tagContent,
      raw: match[0]
    });
    
    lastIndex = match.index + match[0].length;
  }
  
  // 添加最后剩余的内容
  if (lastIndex < content.length) {
    const remainingContent = content.slice(lastIndex).trim();
    if (remainingContent) {
      blocks.push({
        type: 'other',
        content: remainingContent,
        raw: remainingContent
      });
    }
  }
  
  // 如果没有找到任何XML标签，返回整个内容作为other类型
  if (blocks.length === 0) {
    blocks.push({
      type: 'other',
      content: content,
      raw: content
    });
  }
  
  return blocks;
};

// 可折叠的XML块组件
const CollapsibleXmlBlock = ({ block }: { block: ParsedXmlBlock }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const getBlockColor = (type: string) => {
    switch (type) {
      case 'system': return 'border-red-500 bg-red-50';
      case 'assistant': return 'border-blue-500 bg-blue-50';
      case 'user': return 'border-green-500 bg-green-50';
      case 'tool': return 'border-gray-500 bg-gray-50';
      case 'tools': return 'border-gray-500 bg-gray-50';
      default: return 'border-gray-500 bg-gray-50';
    }
  };
  
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'system': return 'System';
      case 'assistant': return 'Assistant';
      case 'user': return 'User';
      case 'tool': return 'Tool';
      case 'tools': return 'Tools';
      default: return 'Other';
    }
  };
  
  return (
    <div className={`rounded-lg border-l-4 ${getBlockColor(block.type)} mb-4`}>
      <div 
        className="flex items-center justify-between p-3 cursor-pointer hover:bg-opacity-80"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          {isExpanded ? (
            <ChevronDownIcon className="w-4 h-4" />
          ) : (
            <ChevronRightIcon className="w-4 h-4" />
          )}
          <span className="font-medium text-sm">
            {getTypeLabel(block.type)} {block.type !== 'other' && `(${block.content.length} 字符)`}
          </span>
        </div>
        <span className="text-xs text-gray-500">
          点击{isExpanded ? '收起' : '展开'}
        </span>
      </div>
      {isExpanded && (
        <div className="px-3 pb-3 overflow-x-hidden max-w-full">
          <pre className="whitespace-pre-wrap break-words text-sm font-mono leading-relaxed bg-white p-3 rounded border w-full max-w-full overflow-hidden" style={{ wordBreak: 'break-word' }}>
            {block.content}
          </pre>
        </div>
      )}
    </div>
  );
};

// Prompt内容渲染组件
const PromptContent = ({ prompt }: { prompt: string }) => {
  const parsedBlocks = parseXmlContent(prompt);
  
  // 如果只有一个other类型的块，说明没有XML结构，直接显示原始内容
  if (parsedBlocks.length === 1 && parsedBlocks[0].type === 'other') {
    return (
      <div className="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500 overflow-x-hidden max-w-full">
        <pre className="whitespace-pre-wrap break-words text-sm font-mono leading-relaxed w-full max-w-full overflow-hidden" style={{ wordBreak: 'break-word' }}>
          {prompt}
        </pre>
      </div>
    );
  }
  
  // 有XML结构，显示解析后的块
  return (
    <div className="space-y-2">
      {parsedBlocks.map((block, index) => (
        <CollapsibleXmlBlock key={index} block={block} />
      ))}
    </div>
  );
};

export default function DetailModal({ item, onClose, onNavigate, hasPrev, hasNext, redundancyResult, highlightedItems, highlightedRelatedItems }: DetailModalProps) {
  const router = useRouter();

  const handleDebug = (data: ChatCompletion) => {
    setChatData({
      ...data,
      messages: parseRoleContentFromTags(data.prompt),
    });
    router.push("/playground/chat2");
  };

  if (!item) return null;

  let metadata = "";
  try {
    if (item.metadata) {
      metadata = JSON.stringify(JSON.parse(item.metadata), null, 2);
    }
  } catch {
    metadata = item.metadata || 'N/A';
  }

  return (
    <Dialog.Root open={!!item} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Content maxWidth="90%" maxHeight="90%" className="overflow-x-hidden max-w-[90vw]">
        <Flex justify="between" align="center" mb="4" className="w-full max-w-full">
          <Dialog.Title>
            <Heading size="6">详情 - ID: {item.id}</Heading>
          </Dialog.Title>
          <div className="flex items-center gap-x-4">
            <div className="flex items-center gap-x-2">
              <Button 
                variant="outline" 
                onClick={() => onNavigate('prev')}
                disabled={!hasPrev}
                className="px-2 py-1 h-8"
              >
                ← 上一个
              </Button>
              <Button 
                variant="outline" 
                onClick={() => onNavigate('next')}
                disabled={!hasNext}
                className="px-2 py-1 h-8"
              >
                下一个 →
              </Button>
            </div>
            <Button variant="outline" onClick={() => handleDebug(item)}>
              <Bug className="h-4 w-4" />
            </Button>
            <Dialog.Close>
              <button className="p-1 hover:bg-gray-100 rounded">
                <Cross2Icon width="16" height="16" />
              </button>
            </Dialog.Close>
          </div>
        </Flex>

        <ScrollArea style={{ height: "75vh" }} className="w-full overflow-x-hidden">
          <div className="space-y-6 w-full">
            {/* 步骤分析信息 */}
            {redundancyResult && (highlightedItems?.includes(item.id) || highlightedRelatedItems?.includes(item.id)) && (() => {
              const isRedundant = highlightedItems?.includes(item.id);
              const isRelated = highlightedRelatedItems?.includes(item.id);
              const redundantStep = redundancyResult.steps.find(step => step.step === item.id);
              
              if (isRedundant && redundantStep) {
                return (
                  <div className="bg-red-50 p-4 rounded-lg border-l-4 border-red-500">
                    <div className="flex items-start gap-3">
                      <div className="text-red-500 text-xl">⚠️</div>
                      <div className="flex-1">
                        <div className="font-semibold text-red-800 mb-2">
                          冗余步骤警告
                        </div>
                        <div className="text-sm text-red-700 mb-2">
                          <span className="font-medium">步骤摘要:</span> {redundantStep.abstract}
                        </div>
                        <div className="text-sm text-red-600">
                          <span className="font-medium">冗余原因:</span> {redundantStep.reason}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              } else if (isRelated) {
                 return (
                   <div className="bg-orange-50 p-4 rounded-lg border-l-4 border-orange-500">
                     <div className="flex items-start gap-3">
                       <div className="text-orange-500 text-xl">🔗</div>
                       <div className="flex-1">
                         <div className="font-semibold text-orange-800 mb-2">
                           相关步骤
                         </div>
                         <div className="text-sm text-orange-700">
                           此步骤与当前分析的冗余步骤相关联。
                         </div>
                       </div>
                     </div>
                   </div>
                 );
              }
              return null;
            })()}

            {/* 基本信息 */}
            <div>
              <div className="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  {/* <div>
                    <Text weight="medium" color="gray">ID:</Text>
                    <Text ml="2">{item.id}</Text>
                  </div> */}
                  <div>
                    <Text weight="medium" color="gray">类型:</Text>
                    <Text ml="2">{item.type}</Text>
                  </div>
                  <div>
                    <Text weight="medium" color="gray">状态:</Text>
                    <Text ml="2" color={item.status === 'success' ? 'green' : 'red'}>
                      {item.status}
                    </Text>
                  </div>
                  {/* <div>
                    <Text weight="medium" color="gray">模型:</Text>
                    <Text ml="2">{item.model_name}</Text>
                  </div> */}
                  {/* <div>
                    <Text weight="medium" color="gray">创建时间:</Text>
                    <Text ml="2">{item.created_at}</Text>
                  </div>
                  <div>
                    <Text weight="medium" color="gray">更新时间:</Text>
                    <Text ml="2">{item.updated_at}</Text>
                  </div> */}
                </div>
              </div>
            </div>

            {/* 内容标签页 */}
            <Tabs.Root defaultValue="response">
              <Tabs.List>
                <Tabs.Trigger value="prompt">Prompt</Tabs.Trigger>
                <Tabs.Trigger value="response">Response</Tabs.Trigger>
                <Tabs.Trigger value="metadata">Metadata</Tabs.Trigger>
                <Tabs.Trigger value="flow">Flow</Tabs.Trigger>
              </Tabs.List>

              <Tabs.Content value="prompt" className="mt-4">
                <div>
                  <Heading size="4" mb="3" color="blue">
                    Prompt
                  </Heading>
                  <PromptContent prompt={item.prompt || 'N/A'} />
                </div>
              </Tabs.Content>

              <Tabs.Content value="response" className="mt-4">
                <div>
                  <Heading size="4" mb="3" color="blue">
                    Response
                  </Heading>
                  <div className="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500 overflow-x-hidden max-w-full">
                    <pre className="whitespace-pre-wrap break-words text-sm font-mono leading-relaxed w-full max-w-full overflow-hidden" style={{ wordBreak: 'break-word' }}>
                      {item.response || 'N/A'}
                    </pre>
                  </div>
                </div>
              </Tabs.Content>

              <Tabs.Content value="metadata" className="mt-4">
                <div>
                  <Heading size="4" mb="3" color="blue">
                    Metadata
                  </Heading>
                  <div className="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500 overflow-x-hidden max-w-full">
                    <pre className="whitespace-pre-wrap break-words text-sm font-mono leading-relaxed w-full max-w-full overflow-hidden" style={{ wordBreak: 'break-word' }}>
                      {metadata}
                    </pre>
                  </div>
                </div>
              </Tabs.Content>

              <Tabs.Content value="flow" className="mt-4">
                <div className="space-y-6">
                  {/* 最后一条tool类型消息 */}
                  {(() => {
                    const parsedBlocks = parseXmlContent(item.prompt || '');
                    const toolBlocks = parsedBlocks.filter(block => block.type === 'tool');
                    const lastToolBlock = toolBlocks[toolBlocks.length - 1];
                    
                    if (lastToolBlock) {
                      return (
                        <div>
                          <Heading size="4" mb="3" color="blue">
                            最后一条Tool消息
                          </Heading>
                          <div className="bg-gray-50 p-4 rounded-lg border-l-4 border-purple-500 overflow-x-hidden max-w-full">
                            <pre className="whitespace-pre-wrap break-words text-sm font-mono leading-relaxed w-full max-w-full overflow-hidden" style={{ wordBreak: 'break-word' }}>
                              {lastToolBlock.content}
                            </pre>
                          </div>
                        </div>
                      );
                    } else if (toolBlocks.length === 0 && parsedBlocks.some(block => block.type !== 'other')) {
                      return (
                        <div>
                          <Heading size="4" mb="3" color="blue">
                            最后一条Tool消息
                          </Heading>
                          <div className="bg-gray-100 p-4 rounded-lg border-l-4 border-gray-400">
                            <Text color="gray" className="text-sm">
                              未找到tool类型的消息
                            </Text>
                          </div>
                        </div>
                      );
                    }
                    return null;
                  })()}

                  {/* Response内容 */}
                  <div>
                    <Heading size="4" mb="3" color="blue">
                      Response
                    </Heading>
                    <div className="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500 overflow-x-hidden max-w-full">
                      <pre className="whitespace-pre-wrap break-words text-sm font-mono leading-relaxed w-full max-w-full overflow-hidden" style={{ wordBreak: 'break-word' }}>
                        {item.response || 'N/A'}
                      </pre>
                    </div>
                  </div>
                </div>
              </Tabs.Content>
            </Tabs.Root>
          </div>
        </ScrollArea>
      </Dialog.Content>
    </Dialog.Root>
  );
}