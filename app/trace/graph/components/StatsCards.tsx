import { useState } from "react";
import { createPortal } from "react-dom";
import { useStore } from "@nanostores/react";
import { userAtom } from "@/app/store/auth";
import { type ChatCompletion } from "@/app/bam/aime/namespaces/trace";

// Tool 调用项定义
interface ToolCallItem {
  id: string;
  tool: string;
  description: string;
  step_id: string;
  status: 'success' | 'failed' | 'running' | 'completed';
  inputs?: Record<string, unknown>;
  error?: string;
  startTime: string;
  endTime?: string;
  duration?: number; // 毫秒
  startOffset: number;
  endOffset?: number;
}

interface StatsCardsProps {
  data: ChatCompletion[];
  toolData?: ToolCallItem[];
}

// 模型价格配置（人民币/百万tokens）
interface ModelPrice {
  input: number;
  output: number;
  cache?: number; // 读取 cache 的价格
  cache_write?: number; // 写入 cache 的价格
  inputHigh?: number; // 长上下文价格
  outputHigh?: number; // 长上下文价格
  outputReasoning?: number; // 推理 token 价格
}

const MODEL_PRICES: Record<string, ModelPrice> = {
  // Doubao 系列
  'doubao-1.5-pro-32k': { input: 0.8, output: 2.0, cache: 0.16 },
  'doubao-1.5-lite-32k': { input: 0.3, output: 0.6, cache: 0.06 },

  // Claude 系列 (美元转人民币，按7.15汇率)，写 cache 1.25 倍，读 cache 0.1 倍折扣
  'claude-3.7': { input: 3 * 7.15, output: 15 * 7.15, cache: 3 * 7.15 * 0.1, cache_write: 3 * 7.15 * 1.25 },
  'claude': { input: 3 * 7.15, output: 15 * 7.15, cache: 3 * 7.15 * 0.1, cache_write: 3 * 7.15 * 1.25 },

  // Gemini 系列 (美元转人民币，按7.15汇率)
  'gemini-2.5': { input: 1.25 * 7.15, output: 10 * 7.15, inputHigh: 2.50 * 7.15, outputHigh: 15 * 7.15 },
  'gemini-2.5-flash': { input: 0.3 * 7.15, output: 2.5 * 7.15, outputReasoning: 2.5 * 7.15 },
  'gemini': { input: 1.25 * 7.15, output: 10 * 7.15 },

  'gpt-5': { input: 1.25 * 7.15, output: 10 * 7.15, cache: 1.25 * 7.15 * 0.5 },
};

// 模糊匹配模型名称
const getModelPrice = (modelName: string): ModelPrice | null => {
  const lowerModelName = modelName.toLowerCase();

  // 精确匹配
  if (MODEL_PRICES[lowerModelName]) {
    return MODEL_PRICES[lowerModelName];
  }

  // 模糊匹配
  for (const [key, price] of Object.entries(MODEL_PRICES)) {
    if (lowerModelName.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerModelName)) {
      return price;
    }
  }

  // 按关键词匹配
  if (lowerModelName.includes('doubao') || lowerModelName.includes('豆包')) {
    if (lowerModelName.includes('pro')) {
      return MODEL_PRICES['doubao-1.5-pro-32k'];
    } else if (lowerModelName.includes('lite')) {
      return MODEL_PRICES['doubao-1.5-lite-32k'];
    }
    return MODEL_PRICES['doubao-1.5-pro-32k']; // 默认pro
  }

  if (lowerModelName.includes('claude')) {
    return MODEL_PRICES['claude'];
  }

  if (lowerModelName.includes('gemini')) {
    if (lowerModelName.includes('flash')) {
      return MODEL_PRICES['gemini-2.5-flash'];
    }
    return MODEL_PRICES['gemini'];
  }

  return null;
};

// 计算单个请求的价格
// cache: 是否计算 cache 折扣价格
const calculateRequestCost = (item: ChatCompletion, cache: boolean = false) => {
  try {
    const metadata = JSON.parse(item.metadata || '{}');
    const usage = metadata.usage;

    if (!usage || !usage.prompt_tokens || !usage.completion_tokens) {
      return 0;
    }

    const modelPrice = getModelPrice(item.model_name);
    if (!modelPrice) {
      return 0;
    }

    const inputTokens = Number(usage.prompt_tokens);
    const outputTokens = Number(usage.completion_tokens);
    // prompt_tokens + cache_read_input_tokens + cache_read_write_tokens 才是实际总的 prompt tokens
    const cacheTokens = Number(usage.cache_read_input_tokens || 0);
    const cacheWriteTokens = Number(usage.cache_read_write_tokens || 0);

    // 基础价格计算
    let inputCost = (inputTokens / 1000000) * modelPrice.input;
    let outputCost = (outputTokens / 1000000) * modelPrice.output;

    // Gemini Pro 特殊处理（超过20万tokens的高价格）
    if (item.model_name.toLowerCase().includes('gemini') &&
        item.model_name.toLowerCase().includes('pro') &&
        modelPrice.inputHigh && modelPrice.outputHigh) {
      if (inputTokens+outputTokens > 200000) {
        inputCost = (inputTokens / 1000000) * modelPrice.inputHigh;
        outputCost = (outputTokens / 1000000) * modelPrice.outputHigh;
      }
    }

    if (cache) {
      if (modelPrice.cache) {
        inputCost += (cacheTokens / 1000000) * modelPrice.cache;
      } else {
        inputCost += (cacheTokens / 1000000) * modelPrice.input;
      }
      // 有些模型（openai、gemini 等）没有显示创建 cache 的过程和开销。
      if (modelPrice.cache_write) {
        inputCost += (cacheWriteTokens / 1000000) * modelPrice.cache_write;
      } else {
        inputCost += (cacheWriteTokens / 1000000) * modelPrice.input;
      }
    } else {
      // 否则按正常价格计价
      inputCost += (cacheTokens / 1000000) * modelPrice.input;
      inputCost += (cacheWriteTokens / 1000000) * modelPrice.input;
    }

    return inputCost + outputCost;
  } catch (error) {
    console.warn('解析metadata失败:', error);
    return 0;
  }
};

// 计算总价格
const calculateTotalCost = (data: ChatCompletion[]) => {
  const totalCost = data.reduce((total, item) => {
    return total + calculateRequestCost(item, true);
  }, 0);
  const withoutCacheCost = data.reduce((total, item) => {
    return total + calculateRequestCost(item, false);
  }, 0);

  let costStr = '';

  const formatCost = (cost: number) => {
    if (cost < 0.01) {
      return '< ¥0.01';
    } else if (cost < 1) {
      return `¥${cost.toFixed(3)}`;
    } else {
      return `¥${cost.toFixed(2)}`;
    }
  }

  costStr = formatCost(totalCost);

  if (Math.abs(totalCost - withoutCacheCost) > 0.01) {
    costStr = `${costStr}/${formatCost(withoutCacheCost)}\n${((totalCost/withoutCacheCost)*100).toFixed(0)}%`;
  }
  return costStr;
};

// 计算总耗时并格式化为时分秒
const calculateTotalDuration = (data: ChatCompletion[]) => {
  if (data.length === 0) {
    return '0秒';
  }

  // 找到所有时间点中的最早和最晚时间
  let earliestTime = Infinity;
  let latestTime = -Infinity;

  data.forEach(item => {
    const start = new Date(item.created_at).getTime();
    const end = new Date(item.updated_at).getTime();

    if (start < earliestTime) {
      earliestTime = start;
    }
    if (end > latestTime) {
      latestTime = end;
    }
  });

  const totalMs = latestTime - earliestTime;
  const totalSeconds = Math.floor(totalMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  if (hours > 0) {
    return `${hours}时${minutes}分${seconds}秒`;
  } else if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  } else {
    return `${seconds}秒`;
  }
};

// 计算各模型的价格分布
const calculateModelCosts = (data: ChatCompletion[]) => {
  const modelCosts: Record<string, number> = {};

  data.forEach(item => {
    const cost = calculateRequestCost(item, true);
    if (cost > 0) {
      if (!modelCosts[item.model_name]) {
        modelCosts[item.model_name] = 0;
      }
      modelCosts[item.model_name] += cost;
    }
  });

  return Object.entries(modelCosts)
    .map(([model, cost]) => ({
      model,
      cost: cost < 0.01 ? '< ¥0.01' : cost < 1 ? `¥${cost.toFixed(3)}` : `¥${cost.toFixed(2)}`
    }))
    .sort((a, b) => {
      // 提取数值进行排序
      const getNumericValue = (costStr: string) => {
        if (costStr === '< ¥0.01') return 0.005;
        return parseFloat(costStr.replace('¥', ''));
      };
      return getNumericValue(b.cost) - getNumericValue(a.cost);
    });
};

// 计算 tool 调用总耗时
const calculateToolTotalDuration = (toolData: ToolCallItem[]) => {
  if (toolData.length === 0) {
    return '0秒';
  }

  const totalMs = toolData.reduce((total, item) => {
    return total + (item.duration || 0);
  }, 0);

  const totalSeconds = Math.floor(totalMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  if (hours > 0) {
    return `${hours}时${minutes}分${seconds}秒`;
  } else if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  } else {
    return `${seconds}秒`;
  }
};

// 计算各 tool 的耗时分布
const calculateToolDurations = (toolData: ToolCallItem[]) => {
  const toolDurations: Record<string, number> = {};

  toolData.forEach(item => {
    if (item.duration && item.duration > 0) {
      if (!toolDurations[item.tool]) {
        toolDurations[item.tool] = 0;
      }
      toolDurations[item.tool] += item.duration;
    }
  });

  return Object.entries(toolDurations)
    .map(([tool, duration]) => {
      const seconds = Math.floor(duration / 1000);
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;

      let durationStr;
      if (minutes > 0) {
        durationStr = `${minutes}分${remainingSeconds}秒`;
      } else {
        durationStr = `${remainingSeconds}秒`;
      }

      return {
        tool,
        duration: durationStr,
        rawDuration: duration
      };
    })
    .sort((a, b) => b.rawDuration - a.rawDuration);
};

// 计算各 type 的耗时分布
const calculateTypeDurations = (data: ChatCompletion[]) => {
  const typeDurations: Record<string, number> = {};

  data.forEach(item => {
    const start = new Date(item.created_at).getTime();
    const end = new Date(item.updated_at).getTime();
    const duration = end - start;

    if (duration > 0) {
      if (!typeDurations[item.type]) {
        typeDurations[item.type] = 0;
      }
      typeDurations[item.type] += duration;
    }
  });

  return Object.entries(typeDurations)
    .map(([type, duration]) => {
      const seconds = Math.floor(duration / 1000);
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;

      let durationStr;
      if (minutes > 0) {
        durationStr = `${minutes}分${remainingSeconds}秒`;
      } else {
        durationStr = `${remainingSeconds}秒`;
      }

      return {
        type,
        duration: durationStr,
        rawDuration: duration
      };
    })
    .sort((a, b) => b.rawDuration - a.rawDuration);
};

// 计算 type 总耗时
const calculateTypeTotalDuration = (data: ChatCompletion[]) => {
  if (data.length === 0) {
    return '0秒';
  }

  const totalMs = data.reduce((total, item) => {
    const start = new Date(item.created_at).getTime();
    const end = new Date(item.updated_at).getTime();
    return total + (end - start);
  }, 0);

  const totalSeconds = Math.floor(totalMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  if (hours > 0) {
    return `${hours}时${minutes}分${seconds}秒`;
  } else if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  } else {
    return `${seconds}秒`;
  }
};

// 从response中提取function name
const extractFunctionNames = (response: string): string[] => {
  try {
    // 查找<tool_calls>标签内容
    const toolCallsMatch = response.match(/<tool_calls>[\s\S]*?\[([\s\S]*?)\][\s\S]*?<\/tool_calls>/);
    if (!toolCallsMatch) return [];

    const toolCallsContent = toolCallsMatch[1];
    const functionNames: string[] = [];

    // 解析JSON数组中的每个tool call
    try {
      const toolCalls = JSON.parse(`[${toolCallsContent}]`);
      toolCalls.forEach((call: any) => {
        if (call.type === 'function_call' && call.function?.name) {
          functionNames.push(call.function.name);
        }
      });
    } catch (jsonError) {
      // 如果JSON解析失败，尝试正则表达式提取
      const nameRegex = /"name":\s*"([^"]+)"/g;
      let match;
      while ((match = nameRegex.exec(toolCallsContent)) !== null) {
        functionNames.push(match[1]);
      }
    }

    return functionNames;
  } catch (error) {
    console.warn('提取function name失败:', error);
    return [];
  }
};

// 计算各 function name 的耗时分布和调用次数（仅针对mewtwo类型）
const calculateFunctionDurations = (data: ChatCompletion[]) => {
  const functionStats: Record<string, { duration: number; count: number }> = {};

  // 只处理type为mewtwo的事件
  const mewtwoEvents = data.filter(item => item.type === 'mewtwo');

  mewtwoEvents.forEach(item => {
    const start = new Date(item.created_at).getTime();
    const end = new Date(item.updated_at).getTime();
    const duration = end - start;

    if (duration > 0) {
      const functionNames = extractFunctionNames(item.response);

      // 如果有多个function name，平均分配耗时
      if (functionNames.length > 0) {
        const avgDuration = duration / functionNames.length;
        functionNames.forEach(funcName => {
          if (!functionStats[funcName]) {
            functionStats[funcName] = { duration: 0, count: 0 };
          }
          functionStats[funcName].duration += avgDuration;
          functionStats[funcName].count += 1;
        });
      }
    }
  });

  return Object.entries(functionStats)
    .map(([funcName, stats]) => {
      const seconds = Math.floor(stats.duration / 1000);
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;

      let durationStr;
      if (minutes > 0) {
        durationStr = `${minutes}分${remainingSeconds}秒`;
      } else {
        durationStr = `${remainingSeconds}秒`;
      }

      return {
        functionName: funcName,
        duration: durationStr,
        count: stats.count,
        rawDuration: stats.duration
      };
    })
    .sort((a, b) => b.rawDuration - a.rawDuration);
};

export default function StatsCards({ data, toolData = [] }: StatsCardsProps) {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });
  const user = useStore(userAtom);

  const types = [...new Set(data.map(item => item.type))];
  const models = [...new Set(data.map(item => item.model_name))];
  const successCount = data.filter(item => item.status === 'success').length;
  const totalDuration = calculateTotalDuration(data);
  const totalCost = calculateTotalCost(data);
  const modelCosts = calculateModelCosts(data);

  // Tool 相关统计
  const toolTotalDuration = calculateToolTotalDuration(toolData);
  const toolDurations = calculateToolDurations(toolData);

  // Type 相关统计
  const typeTotalDuration = calculateTypeTotalDuration(data);
  const typeDurations = calculateTypeDurations(data);

  // Function 相关统计（仅针对mewtwo类型）
  const functionDurations = calculateFunctionDurations(data);

  // 基础统计卡片
  const baseStats = [
    {
      id: "total",
      label: "调用统计",
      value: `失败 ${data.length - successCount} / 总 ${data.length} `,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
    },
    {
      id: "types",
      label: "任务类型",
      value: types.length,
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
    },
    {
      id: "models",
      label: "模型数量",
      value: models.length,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
    },
    {
      id: "duration",
      label: "总耗时",
      value: totalDuration,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200",
    },
    // {
    //   id: "type_duration",
    //   label: "Type耗时",
    //   value: typeDurations.map(item => `${item.type}: ${item.duration}`).join('\n'),
    //   color: "text-indigo-600",
    //   bgColor: "bg-indigo-50",
    //   borderColor: "border-indigo-200",
    // },
    {
      id: "function_duration",
      label: "Function耗时",
      value: functionDurations.map(item => `${item.functionName}: ${item.duration} (${item.count}次)`).join('\n'),
      color: "text-pink-600",
      bgColor: "bg-pink-50",
      borderColor: "border-pink-200",
    },
    {
      id: "tool_duration",
      label: "Tool耗时",
      value: toolTotalDuration,
      color: "text-teal-600",
      bgColor: "bg-teal-50",
      borderColor: "border-teal-200",
    },
  ];

  // 价格卡片（如果有权限）
  const costCard = user?.username === 'dingdegao' || user?.username ===  'zhangshunchen'
  || user?.username ===  'lanjunjian' || user?.username ===  'sunjuntao.rex'
  || user?.username === 'wangmingyu.x' ||  user?.username === 'zhuyuanshuo'
  || user?.username === 'caoyunxiang' || user?.username === 'laihongjie'
  || user?.username === 'shiyexuan'? {
    id: "cost",
    label: "总价格",
    value: totalCost,
    color: "text-red-600",
    bgColor: "bg-red-50",
    borderColor: "border-red-200",
    hoverContent: modelCosts,
  } : null;

  // 所有统计卡片
  const stats = costCard ? [...baseStats, costCard] : baseStats;

  const handleMouseEnter = (stat: typeof stats[0], event: React.MouseEvent) => {
    // 只有有 hoverContent 的卡片才显示 hover
    if (!stat.hoverContent) return;

    const rect = event.currentTarget.getBoundingClientRect();
    setHoverPosition({
      x: rect.left + rect.width / 2,
      y: rect.bottom + 8
    });
    setHoveredCard(stat.id);
  };

  return (
    <>
      <div className={`grid gap-3 relative ${
        costCard
          ? 'grid-cols-[repeat(7,1fr)_2fr] md:grid-cols-[repeat(7,1fr)_2fr] lg:grid-cols-[repeat(7,1fr)_2fr]'
          : 'grid-cols-2 md:grid-cols-3 lg:grid-cols-7'
      }`}>
        {stats.map((stat, index) => (
          <div
            key={index}
            className={`${stat.bgColor} ${stat.borderColor} rounded-lg p-4 border relative ${stat.hoverContent ? 'cursor-pointer' : ''} transition-transform hover:scale-105`}
            onMouseEnter={(e) => handleMouseEnter(stat, e)}
            onMouseLeave={() => setHoveredCard(null)}
          >
            {stat.id === 'types' ? (
              <div className="text-center">
                <div className={`text-2xl font-bold ${stat.color} mb-1`}>
                  {stat.value}
                </div>
                <div className="text-xs text-gray-600 font-medium mb-2">
                  {stat.label}
                </div>
                <div className={`text-sm ${stat.color} space-y-1`}>
                  {types.map((type, idx) => (
                    <div key={idx} className="text-xs">
                      {type}
                    </div>
                  ))}
                </div>
              </div>
            ) : stat.id === 'models' ? (
              <div className="text-center">
                <div className={`text-2xl font-bold ${stat.color} mb-1`}>
                  {stat.value}
                </div>
                <div className="text-xs text-gray-600 font-medium mb-2">
                  {stat.label}
                </div>
                <div className={`text-sm ${stat.color} space-y-1`}>
                  {models.map((model, idx) => (
                    <div key={idx} className="text-xs truncate">
                      {model}
                    </div>
                  ))}
                </div>
              </div>
            ) : stat.id === 'function_duration' ? (
              <div className="text-center">
                <div className="text-xs text-gray-600 font-medium mb-2">
                  {stat.label}
                </div>
                <div className={`text-sm ${stat.color} space-y-1`}>
                  {functionDurations.map((item, idx) => (
                    <div key={idx} className="flex justify-between items-start">
                      <span className="text-xs truncate mr-2">{item.functionName}</span>
                      <div className="text-xs font-medium text-right">
                        <div>{item.duration}</div>
                        <div className="text-gray-500">({item.count}次)</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : stat.id === 'tool_duration' ? (
              <div className="text-center">
                <div className={`text-2xl font-bold ${stat.color} mb-1`}>
                  {stat.value}
                </div>
                <div className="text-xs text-gray-600 font-medium mb-2">
                  {stat.label}
                </div>
                <div className={`text-sm ${stat.color} space-y-1`}>
                  {toolDurations.map((item, idx) => (
                    <div key={idx} className="flex justify-between items-start">
                      <span className="text-xs truncate mr-2">{item.tool}</span>
                      <span className="text-xs font-medium">{item.duration}</span>
                    </div>
                  ))}
                </div>
              </div>
            ) :  stat.id === 'duration' ? (
                <div className="text-center">
                  <div className={`text-2xl font-bold ${stat.color} mb-1`}>
                    {stat.value}
                  </div>
                  <div className="text-xs text-gray-600 font-medium">
                    {stat.label}
                  </div>
                  <div className={`text-sm ${stat.color} space-y-1`}>
                    {typeDurations.map((item, idx) => (
                        <div key={idx} className="flex justify-between items-center">
                          <span className="text-xs truncate mr-2">{item.type}</span>
                          <span className="text-xs font-medium">{item.duration}</span>
                        </div>
                    ))}
                  </div>
                </div>
            ) : (
              <div className="text-center">
                <div className={`text-2xl font-bold ${stat.color} mb-1`}>
                  {stat.value}
                </div>
                <div className="text-xs text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Hover 提示框 - 使用 Portal 渲染到 body */}
      {hoveredCard && typeof window !== 'undefined' && createPortal(
        <div
          className="fixed z-[9999] bg-white border border-gray-200 rounded-lg shadow-lg p-3 min-w-[200px] max-w-[300px] pointer-events-none"
          style={{
            left: hoverPosition.x,
            top: hoverPosition.y,
            transform: 'translateX(-50%)'
          }}
        >
          {(() => {
            const stat = stats.find(s => s.id === hoveredCard);
            if (!stat?.hoverContent) return null;

            // 特殊处理价格hover显示
            if (stat.id === 'cost') {
              return (
                <>
                  <div className="text-sm font-medium text-gray-700 mb-2">
                    各模型价格分布:
                  </div>
                  <div className="max-h-40 overflow-y-auto">
                    {(stat.hoverContent as Array<{model: string, cost: string}>).map((item, idx) => (
                      <div key={idx} className="flex justify-between items-center text-xs py-1 border-b border-gray-100 last:border-b-0">
                        <span className="text-gray-600 truncate mr-2">{item.model}</span>
                        <span className="text-red-600 font-medium">{item.cost}</span>
                      </div>
                    ))}
                  </div>
                </>
              );
            }





            return (
              <>
                <div className="text-sm font-medium text-gray-700 mb-2">
                  {stat.label}列表:
                </div>
                <div className="max-h-40 overflow-y-auto">
                  {(stat.hoverContent as string[]).map((item, idx) => (
                    <div key={idx} className="text-xs text-gray-600 py-1 border-b border-gray-100 last:border-b-0">
                      {item}
                    </div>
                  ))}
                </div>
              </>
            );
          })()}
        </div>,
        document.body
      )}
    </>
  );
}