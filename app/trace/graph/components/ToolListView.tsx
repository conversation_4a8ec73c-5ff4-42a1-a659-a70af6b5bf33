import { useState, useMemo } from "react";
import { ChevronUpIcon, ChevronDownIcon } from "@radix-ui/react-icons";

// Tool 调用项定义
interface ToolCallItem {
  id: string;
  tool: string;
  description: string;
  step_id: string;
  status: 'success' | 'failed' | 'running';
  inputs?: Record<string, unknown>;
  error?: string;
  startTime: string;
  endTime?: string;
  duration?: number; // 毫秒
  startOffset: number;
  endOffset?: number;
}

interface ToolListViewProps {
  data: ToolCallItem[];
  isLoading?: boolean;
}

type SortOrder = 'asc' | 'desc' | null;

// 不同状态的颜色映射
const STATUS_COLORS: Record<string, string> = {
  success: "bg-green-500",
  failed: "bg-red-500",
  running: "bg-yellow-500",
  default: "bg-gray-500",
};

// 获取状态对应的颜色
const getStatusColor = (status: string) => {
  return STATUS_COLORS[status] || STATUS_COLORS.default;
};

// 格式化时间显示
const formatTime = (timeString: string) => {
  const date = new Date(timeString);
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZone: 'Asia/Shanghai'
  });
};

// 格式化耗时显示
const formatDuration = (duration?: number) => {
  if (!duration || duration < 0) return 'N/A';
  
  if (duration < 1000) {
    return `${duration}ms`;
  }
  
  const seconds = Math.floor(duration / 1000);
  const ms = duration % 1000;
  
  if (seconds < 60) {
    return ms > 0 ? `${seconds}.${Math.floor(ms / 100)}s` : `${seconds}s`;
  }
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m${remainingSeconds}s`;
};

// 截断文本
const truncateText = (text: string, maxLength: number = 50) => {
  if (!text) return 'N/A';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

export default function ToolListView({ data, isLoading = false }: ToolListViewProps) {
  const [sortOrder, setSortOrder] = useState<SortOrder>(null);

  // 排序后的数据
  const sortedData = useMemo(() => {
    if (!sortOrder) return data;
    
    return [...data].sort((a, b) => {
      const durationA = a.duration || 0;
      const durationB = b.duration || 0;
      
      if (sortOrder === 'asc') {
        return durationA - durationB;
      } else {
        return durationB - durationA;
      }
    });
  }, [data, sortOrder]);

  const handleSortClick = () => {
    if (sortOrder === null) {
      setSortOrder('desc'); // 首次点击降序
    } else if (sortOrder === 'desc') {
      setSortOrder('asc'); // 第二次点击升序
    } else {
      setSortOrder(null); // 第三次点击取消排序
    }
  };

  if (data.length === 0) {
    return (
      <div className="text-center py-12 text-gray-500">
        {isLoading ? '正在加载工具调用数据...' : '暂无工具调用数据'}
      </div>
    );
  }

  return (
    <div className="max-h-[600px] overflow-y-auto">
      <div className="w-full">
        {/* 表头 */}
        <div className="bg-gray-100 border-b border-gray-200 sticky top-0 z-10">
          <div className="grid grid-cols-12 gap-2 p-3 text-sm font-medium text-gray-700">
            <div className="col-span-2">工具名称</div>
            <div className="col-span-2">Step ID</div>
            <div className="col-span-2">调用时间</div>
            <div className="col-span-1">
              <button
                onClick={handleSortClick}
                className="flex items-center gap-1 hover:text-blue-600 transition-colors"
              >
                耗时
                {sortOrder === 'asc' && <ChevronUpIcon className="w-4 h-4" />}
                {sortOrder === 'desc' && <ChevronDownIcon className="w-4 h-4" />}
                {sortOrder === null && <div className="w-4 h-4" />}
              </button>
            </div>
            <div className="col-span-1">状态</div>
            <div className="col-span-4">描述</div>
          </div>
        </div>
        
        {/* 数据行 */}
        <div className="divide-y divide-gray-200">
          {sortedData.map(item => {
            const statusColor = getStatusColor(item.status);
            
            return (
              <div
                key={item.id}
                className="grid grid-cols-12 gap-2 p-3 bg-white transition-all duration-200 hover:bg-gray-50"
                title={`工具: ${item.tool}\n描述: ${item.description || 'N/A'}\n开始时间: ${item.startTime}\n结束时间: ${item.endTime || 'N/A'}\n错误信息: ${item.error || 'N/A'}`}
              >
                {/* 工具名称 */}
                <div className="col-span-2 font-semibold text-blue-600 text-sm truncate">
                  {item.tool}
                </div>
                
                {/* Step ID */}
                <div className="col-span-2 text-gray-700 text-sm truncate" title={item.step_id}>
                  {truncateText(item.step_id, 20)}
                </div>
                
                {/* 调用时间 */}
                <div className="col-span-2 text-gray-700 text-sm truncate">
                  {formatTime(item.startTime)}
                </div>
                
                {/* 耗时 */}
                <div className="col-span-1 text-gray-700 text-sm truncate">
                  {formatDuration(item.duration)}
                </div>
                
                {/* 状态 */}
                <div className="col-span-1">
                  <span className={`px-2 py-1 rounded text-white text-xs font-medium ${statusColor} inline-block text-center`}>
                    {item.status}
                  </span>
                </div>
                
                {/* 描述 */}
                <div className="col-span-4 text-gray-600 text-sm truncate" title={item.description || 'N/A'}>
                  {truncateText(item.description || '', 60)}
                </div>
              </div>
            );
          })}
        </div>
        
        {/* 加载状态提示 */}
        {isLoading && (
          <div className="text-center py-4 text-sm text-gray-500 border-t border-gray-200">
            正在加载更多数据...
          </div>
        )}
      </div>
    </div>
  );
}