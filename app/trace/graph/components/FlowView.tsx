"use client";

import { useState, useMemo } from "react";
import { type ChatCompletion } from "@/app/bam/aime/namespaces/trace";
import { ChevronDownIcon, ChevronRightIcon, PlayIcon, Cross1Icon } from "@radix-ui/react-icons";

interface FlowViewProps {
  data: ChatCompletion[];
}

// 解析XML内容中的消息
const parseMessages = (prompt: string): { type: string; content: string }[] => {
  if (!prompt) return [];
  
  const messages = [];
  const userRegex = /<user>([\s\S]*?)<\/user>/gi;
  const assistantRegex = /<assistant>([\s\S]*?)<\/assistant>/gi;
  
  let match;
  
  // 收集所有user消息
  while ((match = userRegex.exec(prompt)) !== null) {
    messages.push({ type: 'user', content: match[1].trim() });
  }
  
  // 收集所有assistant消息
  while ((match = assistantRegex.exec(prompt)) !== null) {
    messages.push({ type: 'assistant', content: match[1].trim() });
  }
  
  return messages;
};

// 解析response中的Action信息和前置内容
const parseActionInfo = (response: string): { 
  preActionContent: string; 
  rationale: string; 
  actionName: string; 
  params: string 
} | null => {
  if (!response) return null;
  
  // 匹配 Action 部分
  const actionMatch = response.match(/## Action \(([^)]+)\)([\s\S]*?)(?=\n##|$)/i);
  if (!actionMatch) return null;
  
  const actionName = actionMatch[1];
  const actionContent = actionMatch[2].trim();
  
  // 获取 Action 之前的内容
  const actionIndex = response.indexOf('## Action');
  const preActionPart = actionIndex > 0 ? response.substring(0, actionIndex).trim() : '';
  
  // 分离 Rationale 和其他前置内容
  const rationaleMatch = preActionPart.match(/## Rationale\s*([\s\S]*?)(?=\n##|$)/i);
  const rationale = rationaleMatch ? rationaleMatch[1].trim() : '';
  
  // 获取 Rationale 之前的内容作为前置消息
  let preActionContent = '';
  if (rationaleMatch) {
    const rationaleIndex = preActionPart.indexOf('## Rationale');
    preActionContent = rationaleIndex > 0 ? preActionPart.substring(0, rationaleIndex).trim() : '';
  } else {
    preActionContent = preActionPart;
  }
  
  const paramBlocks = parseParamBlocks(actionContent);
  const params = paramBlocks.map(p => `${p.param}: ${p.value}`).join('\n\n');
  
  return {
    preActionContent,
    rationale,
    actionName,
    params: params || actionContent
  };
};

// 解析参数块
const parseParamBlocks = (content: string): { param: string; value: string }[] => {
  const paramRegex = /```param="([^"]+)"([\s\S]*?)```/g;
  const params = [];
  let match;
  
  while ((match = paramRegex.exec(content)) !== null) {
    params.push({
      param: match[1],
      value: match[2].trim()
    });
  }
  
  return params;
};

// 处理数据获取规则
const processFlowData = (data: ChatCompletion[]) => {
  const sortedData = data.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
  
  // 1. 获取所有 dynamic_planner
  const dynamicPlanners = sortedData.filter(item => item.type.includes('dynamic_planner'));
  const lastDynamicPlanner = dynamicPlanners.length > 0 ? dynamicPlanners[dynamicPlanners.length - 1] : null;
  
  // 2. 获取每个 dynamic_planner 区间内的最后一个 mewtwo
  const mewtwoList: ChatCompletion[] = [];
  
  for (let i = 0; i < dynamicPlanners.length - 1; i++) {
    const currentPlannerTime = new Date(dynamicPlanners[i].created_at).getTime();
    const nextPlannerTime = new Date(dynamicPlanners[i + 1].created_at).getTime();
    
    // 获取当前 dynamic_planner 和下一个 dynamic_planner 之间的所有 mewtwo
    const mewtwoInRange = sortedData.filter(item => 
      item.type.includes('mewtwo') && 
      new Date(item.created_at).getTime() > currentPlannerTime &&
      new Date(item.created_at).getTime() < nextPlannerTime
    );
    
    // 取该区间内的最后一个 mewtwo
    if (mewtwoInRange.length > 0) {
      mewtwoList.push(mewtwoInRange[mewtwoInRange.length - 1]);
    }
  }
  
  return { lastDynamicPlanner, mewtwoList };
};

// 解析消息内容 - 从指定消息开始获取所有Assistant消息
const parseMessageContent = (item: ChatCompletion, type: 'dynamic_planner' | 'mewtwo') => {
  const messages = parseMessages(item.prompt);
  const startIndex = type === 'dynamic_planner' ? 1 : 0; 
  
  if (messages.length <= startIndex) {
    return null;
  }
  
  const userMessage = messages[startIndex]; // 指定的用户消息
  
  // 从指定消息之后的所有Assistant消息，加上当前item的response
  const assistantMessages = messages.slice(startIndex + 1).filter(msg => msg.type === 'assistant');
  assistantMessages.push({ type: 'assistant', content: item.response });
  
  // 解析每个Assistant消息为Action
  const actions = assistantMessages.map((msg, index) => {
    const actionInfo = parseActionInfo(msg.content);
    return {
      id: `${item.id}-action-${index}`,
      content: msg.content,
      actionInfo,
      index: index + 1
    };
  }).filter(action => action.actionInfo); // 只保留有Action信息的
  
  return {
    userMessage: userMessage?.content || '',
    actions
  };
};

// 获取执行结果
const getExecutionResult = (data: ChatCompletion[], currentItem: ChatCompletion, actionIndex: number, actionInfo: any) => {
  const nextMessages = parseMessages(currentItem.prompt);
  
  // 获取最后一条用户消息作为执行结果
  const lastUserMessage = nextMessages.length > 0 && nextMessages.length > actionIndex ? nextMessages[actionIndex] : null;
  return {
    actionIndex,
    executionTime: currentItem.created_at,
    result: lastUserMessage?.content || currentItem.response,
    // 将action参数信息也包含进来
    actionName: actionInfo?.actionName,
    params: actionInfo?.params
  };
};

// 单个流程组件
interface FlowItemProps {
  item: ChatCompletion;
  type: 'dynamic_planner' | 'mewtwo';
  data: ChatCompletion[];
  isExpanded: boolean;
  onToggle: () => void;
  onViewResult: (result: any) => void;
}

const FlowItem: React.FC<FlowItemProps> = ({ 
  item, 
  type, 
  data, 
  isExpanded, 
  onToggle, 
  onViewResult 
}) => {
  const parsedContent = parseMessageContent(item, type);
  
  if (!parsedContent) {
    return (
      <div className="border border-gray-200 rounded-lg p-4">
        <div className="text-center text-gray-500">
          <p>无法解析{type === 'dynamic_planner' ? 'Dynamic Planner' : 'Mewtwo'}消息</p>
          <p className="text-xs mt-1">
            需要至少{type === 'dynamic_planner' ? '4' : '2'}条消息
          </p>
        </div>
      </div>
    );
  }
  
  const { userMessage, actions } = parsedContent;
  
  const handleViewResult = (actionIndex: number, actionInfo: any) => {
    const result = getExecutionResult(data, item, actionIndex, actionInfo);
    onViewResult(result);
  };
  
  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`w-3 h-3 rounded-full ${
              type === 'dynamic_planner' ? 'bg-blue-500' : 'bg-green-500'
            }`} />
            <span className="font-medium text-gray-900">
              {type === 'dynamic_planner' ? 'Dynamic Planner' : 'Mewtwo'}
            </span>
            <span className="text-xs text-gray-500">
              {new Date(item.created_at).toLocaleString()}
            </span>
            <span className="text-xs text-gray-500">
              ({actions.length} 个 Actions)
            </span>
            {item.status && (
              <span className={`px-2 py-1 text-xs rounded-full ${
                item.status === 'success' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {item.status}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={onToggle}
              className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900"
            >
              {isExpanded ? (
                <ChevronDownIcon className="w-4 h-4" />
              ) : (
                <ChevronRightIcon className="w-4 h-4" />
              )}
              {isExpanded ? '收起' : '展开'}
            </button>
          </div>
        </div>
      </div>
      
      {isExpanded && (
        <div className="p-4 space-y-4">
          {/* 用户消息 - 支持换行 */}
          <div>
            <div className="bg-blue-50 p-3 rounded-md text-sm text-gray-800 whitespace-pre-wrap max-h-[400px] overflow-y-auto">
              {userMessage}
            </div>
          </div>
          
          {/* Actions列表 */}
          <div className="space-y-6">
            {actions.map((action, index) => (
              <div key={action.id}>
                {index > 0 && <div className="border-t border-gray-300 my-4"></div>}
                <div className="space-y-3">
                  {/* 合并的消息和理由内容 */}
                  {(action.actionInfo?.preActionContent || action.actionInfo?.rationale) && (
                    <div className="bg-gray-50 p-3 rounded-md overflow-y-auto">
                      <div className="text-sm text-gray-800 whitespace-pre-wrap">
                        {action.actionInfo.preActionContent && (
                          <div className="mb-3">
                            {action.actionInfo.preActionContent}
                          </div>
                        )}
                        {action.actionInfo.rationale && (
                          <div>
                            {action.actionInfo.preActionContent && <div className="border-t border-gray-300 my-3"></div>}
                            {action.actionInfo.rationale}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Action 标题 - 可点击查看结果 */}
                  <div 
                    onClick={() => handleViewResult(action.index, action.actionInfo)}
                    className="bg-blue-100 text-blue-700 px-4 py-2 rounded-lg cursor-pointer hover:bg-blue-200 transition-colors inline-flex items-center gap-2 w-fit"
                  >
                    <PlayIcon className="w-4 h-4" />
                    <span className="text-sm font-medium">
                       {action.actionInfo?.actionName}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default function FlowView({ data }: FlowViewProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [selectedResult, setSelectedResult] = useState<any>(null);
  
  // 处理数据
  const { lastDynamicPlanner, mewtwoList } = useMemo(() => {
    return processFlowData(data);
  }, [data]);
  
  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };
  
  const handleViewResult = (result: any) => {
    setSelectedResult(result);
  };
  
  const closeResultPanel = () => {
    setSelectedResult(null);
  };
  
  if (!lastDynamicPlanner && mewtwoList.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">没有找到符合条件的消息</p>
        <p className="text-xs text-gray-400 mt-1">
          需要至少一个Dynamic Planner或在两个Dynamic Planner之间的Mewtwo消息
        </p>
      </div>
    );
  }
  
  return (
    <div className="flex h-full max-h-[calc(100vh-200px)]">
      {/* 左侧面板 - 流程列表 */}
      <div className={`${selectedResult ? 'w-1/2' : 'w-full'} transition-all duration-300 pr-${selectedResult ? '3' : '0'} overflow-y-auto`}>
        <div className="space-y-6 p-4">
          
          {/* Dynamic Planner */}
          {lastDynamicPlanner && (
            <FlowItem
              item={lastDynamicPlanner}
              type="dynamic_planner"
              data={data}
              isExpanded={expandedItems.has(lastDynamicPlanner.id)}
              onToggle={() => toggleExpanded(lastDynamicPlanner.id)}
              onViewResult={handleViewResult}
            />
          )}
          
          {/* Mewtwo 列表 */}
          {mewtwoList.map((mewtwo) => (
            <FlowItem
              key={mewtwo.id}
              item={mewtwo}
              type="mewtwo"
              data={data}
              isExpanded={expandedItems.has(mewtwo.id)}
              onToggle={() => toggleExpanded(mewtwo.id)}
              onViewResult={handleViewResult}
            />
          ))}
        </div>
      </div>
      
      {/* 右侧面板 - 执行结果 */}
      {selectedResult && (
        <div className="w-1/2 pl-3 border-l border-gray-200 flex flex-col max-h-full">
          <div className="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
            <h3 className="text-lg font-medium text-gray-900">Action 执行结果</h3>
            <button
              onClick={closeResultPanel}
              className="text-gray-400 hover:text-gray-600"
            >
              <Cross1Icon className="w-5 h-5" />
            </button>
          </div>
          
          <div className="p-4 space-y-4 overflow-y-auto flex-1">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                Action {selectedResult.actionIndex}: {selectedResult.actionName}
              </h4>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">执行时间:</h4>
              <div className="bg-blue-50 p-3 rounded-md text-sm text-gray-800">
                {new Date(selectedResult.executionTime).toLocaleString()}
              </div>
            </div>
            
            {/* 参数信息 */}
            {selectedResult.params && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Action 参数:</h4>
                <div className="bg-gray-50 p-3 rounded-md overflow-y-auto">
                  <pre className="whitespace-pre-wrap font-mono text-xs text-gray-800">
                    {selectedResult.params}
                  </pre>
                </div>
              </div>
            )}
            
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">执行结果:</h4>
              <div className="bg-gray-50 p-4 rounded-md overflow-y-auto">
                <pre className="whitespace-pre-wrap font-mono text-sm text-gray-800">
                  {selectedResult.result}
                </pre>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 