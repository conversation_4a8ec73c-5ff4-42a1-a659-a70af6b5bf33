import { Select, TextField } from "@radix-ui/themes";
import { Button } from '@arco-design/web-react';

interface StatusOption {
  label: string;
  value: string;
}

interface SearchPanelProps {
  sessionId: string;
  setSessionId: (value: string) => void;
  statusFilter: string;
  onStatusChange: (status: string) => void;
  statusOptions: StatusOption[];
  typeFilter: string;
  onTypeChange: (type: string) => void;
  typeOptions: StatusOption[];
  handleSearch: () => void;
  isLoading: boolean;
  error: string;
}

export default function SearchPanel({
  sessionId,
  setSessionId,
  statusFilter,
  onStatusChange,
  statusOptions,
  typeFilter,
  onTypeChange,
  typeOptions,
  handleSearch,
  isLoading,
  error,
}: SearchPanelProps) {
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 w-full">
      <div className="space-y-3">
        {/* 单行布局的搜索控件 */}
        <div className="flex gap-3 items-center w-full">
          {/* Session ID - 占据最大空间 */}
          <div className="flex items-center gap-2 flex-1">
            <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
              Session ID:
            </label>
            <TextField.Root
              placeholder="请输入 Session ID"
              value={sessionId}
              onChange={(e) => setSessionId(e.target.value)}
              onKeyPress={handleKeyPress}
              size="2"
              className="flex-1"
            />
          </div>

          {/* 状态过滤 */}
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
              状态:
            </label>
            <Select.Root value={statusFilter} onValueChange={onStatusChange}>
              <Select.Trigger className="w-[120px]" />
              <Select.Content>
                {statusOptions.map((option) => (
                  <Select.Item key={option.value} value={option.value}>
                    {option.label}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select.Root>
          </div>

          {/* 类型过滤 */}
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
              类型:
            </label>
            <Select.Root value={typeFilter} onValueChange={onTypeChange}>
              <Select.Trigger className="w-[120px]" />
              <Select.Content>
                {typeOptions.map((option) => (
                  <Select.Item key={option.value} value={option.value}>
                    {option.label}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select.Root>
          </div>

          {/* 搜索按钮 */}
          <Button
            onClick={handleSearch}
            disabled={isLoading}
            type="primary"
            className="whitespace-nowrap"
          >
            {/* <MagnifyingGlassIcon width="16" height="16" /> */}
            {isLoading ? "搜索中..." : "搜索"}
          </Button>
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}
      </div>
    </div>
  );
}