import { useMemo, useState } from "react";
import { type ChatCompletion } from "@/app/bam/aime/namespaces/trace";
import { ZoomInIcon, ZoomOutIcon, ResetIcon } from "@radix-ui/react-icons";
import { type EvaluationResult } from "../utils/aimeEvaluator";

// Tool 调用项定义
interface ToolCallItem {
  id: string;
  tool: string;
  description: string;
  step_id: string;
  status: 'success' | 'failed' | 'running' | 'completed';
  inputs?: Record<string, unknown>;
  error?: string;
  startTime: string;
  endTime?: string;
  duration?: number; // 毫秒
  startOffset: number;
  endOffset?: number;
}

interface TimelineViewProps {
  data: ChatCompletion[];
  toolData?: ToolCallItem[];
  onItemClick: (item: ChatCompletion) => void;
  highlightedItems?: string[]; // 需要高亮的冗余步骤 ID 列表
  highlightedRelatedItems?: string[]; // 需要高亮的相关步骤 ID 列表
  redundancyResult?: EvaluationResult | null; // 冗余分析结果
  onClearHighlight?: () => void; // 清除高亮的回调
}

// 定义解析后的XML块类型
interface ParsedXmlBlock {
  type: 'system' | 'assistant' | 'user' | 'tool' | 'tools' | 'other';
  content: string;
  raw: string;
}

// XML解析函数（从 DetailModal 复用）
const parseXmlContent = (content: string): ParsedXmlBlock[] => {
  if (!content) return [];
  
  const blocks: ParsedXmlBlock[] = [];
  
  // 使用正则表达式匹配XML标签
  const xmlRegex = /<(system|assistant|user|tool|tools)\b[^>]*>([\s\S]*?)<\/\1>/gi;
  let lastIndex = 0;
  let match;
  
  while ((match = xmlRegex.exec(content)) !== null) {
    // 添加标签前的内容（如果有）
    if (match.index > lastIndex) {
      const beforeContent = content.slice(lastIndex, match.index).trim();
      if (beforeContent) {
        blocks.push({
          type: 'other',
          content: beforeContent,
          raw: beforeContent
        });
      }
    }
    
    // 添加匹配的XML块
    const tagType = match[1].toLowerCase() as 'system' | 'assistant' | 'user' | 'tool' | 'tools';
    const tagContent = match[2].trim();
    
    blocks.push({
      type: tagType,
      content: tagContent,
      raw: match[0]
    });
    
    lastIndex = match.index + match[0].length;
  }
  
  // 添加最后剩余的内容
  if (lastIndex < content.length) {
    const remainingContent = content.slice(lastIndex).trim();
    if (remainingContent) {
      blocks.push({
        type: 'other',
        content: remainingContent,
        raw: remainingContent
      });
    }
  }
  
  // 如果没有找到任何XML标签，返回整个内容作为other类型
  if (blocks.length === 0) {
    blocks.push({
      type: 'other',
      content: content,
      raw: content
    });
  }
  
  return blocks;
};

// 检查是否只有一个 ai 标签
const isOnlyUserTag = (prompt: string) => {
  if (!prompt) return false;
  
  const parsedBlocks = parseXmlContent(prompt);
  const userBlocks = parsedBlocks.filter(block => block.type === 'assistant');
//   const nonOtherBlocks = parsedBlocks.filter(block => block.type !== 'other');

//   console.log("nonOtherBlocks", nonOtherBlocks.length);

  if(userBlocks.length == 1 && userBlocks[0].content.indexOf('Memory Retrieval') > 0) {
    return true;
  }
  
  // 只有一个 ai 标签，且没有其他类型的标签
  return userBlocks.length === 0;
};

// 不同类型的颜色映射
const TYPE_COLORS: Record<string, string> = {
  response_conclude: "bg-green-500",
  task_concluder_selected_by_default: "bg-cyan-500",
  dynamic_planner: "bg-yellow-500",
  retrieve_knowledge: "bg-purple-500",
  summarize: "bg-orange-500",
  mewtwo: "bg-pink-500",
  mewtwo_agent: "bg-blue-500", // mewtwo agent 的特殊颜色
  tool: "bg-gray-500", // tool 类型的颜色
  tools: "bg-gray-500", // tools 类型的颜色
  default: "bg-gray-500",
};

// 获取类型对应的颜色
const getTypeColor = (type: string, item?: ChatCompletion) => {
  // 如果是 mewtwo 类型且只有一个 user 标签，使用特殊颜色
  if (type === 'mewtwo' && item && isOnlyUserTag(item.prompt)) {
    return TYPE_COLORS.mewtwo_agent;
  }
  return TYPE_COLORS[type] || TYPE_COLORS.default;
};

// 计算时间差并格式化显示
const formatDuration = (startTime: string, endTime: string) => {
  const start = new Date(startTime);
  const end = new Date(endTime);
  const diffMs = end.getTime() - start.getTime();
  
  if (diffMs < 0) return '0秒';
  
  const diffSeconds = Math.floor(diffMs / 1000);
  const minutes = Math.floor(diffSeconds / 60);
  const seconds = diffSeconds % 60;
  
  if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  } else {
    return `${seconds}秒`;
  }
};

// 格式化时间显示（只显示时分秒）
const formatTime = (timeString: string) => {
  const date = new Date(timeString);
  return date.toLocaleTimeString('zh-CN', { 
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

export default function TimelineView({ data, toolData = [], onItemClick, highlightedItems = [], highlightedRelatedItems = [], redundancyResult, onClearHighlight }: TimelineViewProps) {
  const [zoomLevel, setZoomLevel] = useState(1);
  const [hoveredItem, setHoveredItem] = useState<ChatCompletion | null>(null);
  const [hoveredTool, setHoveredTool] = useState<ToolCallItem | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // 已移除调试日志

  const timelineData = useMemo(() => {
    if (data.length === 0 && toolData.length === 0) return { types: [], minTime: new Date(), maxTime: new Date(), timeRange: 0, timeLabels: [] };

    // 按类型分组，包含 tool 类型
    const chatTypes = [...new Set(data.map(item => item.type))];
    const toolTypes = toolData.length > 0 ? ['tool'] : [];
    const types = [...chatTypes, ...toolTypes];
    
    // 计算时间范围，包含 tool 数据
    const chatTimes = data.flatMap(item => [
      new Date(item.created_at),
      new Date(item.updated_at)
    ]);
    const toolTimes = toolData.flatMap(item => {
      const times = [new Date(item.startTime)];
      if (item.endTime) {
        times.push(new Date(item.endTime));
      }
      return times;
    });
    
    const allTimes = [...chatTimes, ...toolTimes];
    if (allTimes.length === 0) return { types: [], minTime: new Date(), maxTime: new Date(), timeRange: 0, timeLabels: [] };
    
    const minTime = new Date(Math.min(...allTimes.map(t => t.getTime())));
    const maxTime = new Date(Math.max(...allTimes.map(t => t.getTime())));
    const timeRange = maxTime.getTime() - minTime.getTime();

    // 生成时间轴标签
    const timeLabels = [];
    for (let i = 0; i <= 10; i++) {
      const time = new Date(minTime.getTime() + (timeRange * i / 10));
      timeLabels.push(time.toLocaleTimeString('zh-CN', { 
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }));
    }

    return { types, minTime, maxTime, timeRange, timeLabels };
  }, [data, toolData]);

  // 获取类型显示标题（特殊处理 mewtwo、tool 和 tools）
  const getTypeDisplayTitle = (type: string) => {
    if (type === 'tool') {
      const successCount = toolData.filter(item => (item.status === 'success' || item.status === 'completed')).length;
      const failedCount = toolData.filter(item => item.status === 'failed').length;
      // const runningCount = toolData.filter(item => item.status === 'running').length;
      
      return `tool (成功:${successCount} 失败:${failedCount})`;
    }
    
    const typeData = data.filter(item => item.type === type);
    
    if (type === 'tools') {
      // tools 类型显示数据计数
      return `tools (${typeData.length})`;
    }
    
    if (type === 'mewtwo' || type.includes("mewtwo")) {
      const normalMewtwo = typeData.filter(item => !isOnlyUserTag(item.prompt));
      const agentMewtwo = typeData.filter(item => isOnlyUserTag(item.prompt));
      
      return `mewtwo (${normalMewtwo.length}) mewtwo agent(${agentMewtwo.length})`;
    }
    
    return `${type} (${typeData.length})`;
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev * 1.5, 5));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev / 1.5, 0.5));
  };

  const handleResetZoom = () => {
    setZoomLevel(1);
  };

  const handleMouseEnter = (item: ChatCompletion, event: React.MouseEvent) => {
    setHoveredItem(item);
    setHoveredTool(null);
    setMousePosition({ x: event.clientX, y: event.clientY });
  };

  const handleToolMouseEnter = (item: ToolCallItem, event: React.MouseEvent) => {
    setHoveredTool(item);
    setHoveredItem(null);
    setMousePosition({ x: event.clientX, y: event.clientY });
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    if (hoveredItem || hoveredTool) {
      setMousePosition({ x: event.clientX, y: event.clientY });
    }
  };

  const handleMouseLeave = () => {
    setHoveredItem(null);
    setHoveredTool(null);
  };

  if (data.length === 0) {
    return (
      <div className="text-center py-12 text-gray-500">
        暂无数据
      </div>
    );
  }

  const timelineWidth = Math.max(1200 * zoomLevel, 1200);

  return (
    <div className="relative">
      {/* 高亮状态提示 */}
      {(highlightedItems.length > 0 || highlightedRelatedItems.length > 0) && (
        <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-orange-800 font-medium">🔍 步骤高亮</span>
              <div className="flex items-center gap-4">
                {highlightedItems.length > 0 && (
                  <span className="text-orange-700 flex items-center gap-1">
                    <span className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></span>
                    {highlightedItems.length} 个冗余步骤
                  </span>
                )}
                                 {highlightedRelatedItems.length > 0 && (
                   <span className="text-orange-700 flex items-center gap-1">
                     <span className="w-3 h-3 bg-orange-500 rounded-full"></span>
                     {highlightedRelatedItems.length} 个相关步骤
                   </span>
                 )}
              </div>
            </div>
            <button
              onClick={onClearHighlight}
              className="px-3 py-1 text-sm bg-orange-100 hover:bg-orange-200 text-orange-800 rounded-md transition-colors"
            >
              清除高亮
            </button>
          </div>
        </div>
      )}
      
      {/* 缩放控制按钮 */}
      <div className="flex justify-end gap-2 mb-4">
        <button
          onClick={handleZoomOut}
          className="p-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          title="缩小"
        >
          <ZoomOutIcon className="w-4 h-4" />
        </button>
        <button
          onClick={handleResetZoom}
          className="p-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          title="重置缩放"
        >
          <ResetIcon className="w-4 h-4" />
        </button>
        <button
          onClick={handleZoomIn}
          className="p-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          title="放大"
        >
          <ZoomInIcon className="w-4 h-4" />
        </button>
        <span className="flex items-center text-sm text-gray-600 ml-2">
          {Math.round(zoomLevel * 100)}%
        </span>
      </div>

      <div className="relative overflow-x-auto overflow-y-auto max-h-[2000px] border border-gray-200 rounded-lg">
        <div style={{ minWidth: `${timelineWidth}px` }} className="p-5" onMouseMove={handleMouseMove}>
          {/* 时间轴标题 */}
          <div className="sticky top-0 bg-white z-10 border-b border-gray-200 pb-3 mb-5">
            <div className="flex justify-between text-xs text-gray-600">
              {timelineData.timeLabels.map((label, index) => (
                <span key={index}>{label}</span>
              ))}
            </div>
          </div>

          {/* 时间线行 */}
          {timelineData.types.map(type => {
            // 处理 tool 类型
            if (type === 'tool') {
              return (
                <div key={type} className="mb-10 relative">
                  <div className="font-semibold mb-3 text-sm text-gray-700">
                    {getTypeDisplayTitle(type)}
                  </div>
                  <div className="h-8 bg-gray-50 rounded border border-gray-200 relative overflow-visible">
                    {toolData.map(item => {
                      const startTime = new Date(item.startTime);
                      const endTime = item.endTime ? new Date(item.endTime) : startTime;
                      const left = ((startTime.getTime() - timelineData.minTime.getTime()) / timelineData.timeRange) * 100;
                      const width = Math.max(((endTime.getTime() - startTime.getTime()) / timelineData.timeRange) * 100, 0.1);
                      
                      // 计算耗时显示
                      const duration = item.duration ? `${Math.floor(item.duration / 1000)}s` : '0s';
                      
                      // 根据宽度决定显示内容
                      const actualWidth = (width * timelineWidth) / 100;
                      let displayText = '';
                      if (actualWidth > 120) {
                        displayText = `${item.tool} (${duration})`;
                      } else if (actualWidth > 60) {
                        displayText = item.tool;
                      } else if (actualWidth > 30) {
                        displayText = duration;
                      } else {
                        displayText = item.tool.substring(0, 3);
                      }

                      // 根据状态设置颜色
                      let bgColor = 'bg-gray-500';
                      if (item.status === 'success') {
                        bgColor = 'bg-green-500';
                      } else if (item.status === 'failed') {
                        bgColor = 'bg-red-500';
                      } else if (item.status === 'running') {
                        bgColor = 'bg-yellow-500';
                      }

                      return (
                        <div
                          key={item.id}
                          className={`absolute h-6 top-1 rounded cursor-pointer transition-all duration-200 hover:transform hover:-translate-y-1 hover:shadow-lg hover:z-20 flex items-center px-2 text-white text-xs font-medium overflow-hidden whitespace-nowrap ${bgColor}`}
                          style={{ 
                            left: `${left}%`, 
                            width: `${width}%`, 
                            minWidth: actualWidth < 20 ? '20px' : 'auto',
                            maxWidth: `${100 - left}%`
                          }}
                          onMouseEnter={(e) => handleToolMouseEnter(item, e)}
                          onMouseLeave={handleMouseLeave}
                          title={`${item.tool}: ${item.description} (${duration})`}
                        >
                          <span className="truncate">{displayText}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            }
            
            // 处理其他类型
            const typeData = data.filter(item => item.type === type);
            
            return (
              <div key={type} className="mb-10 relative">
                <div className="font-semibold mb-3 text-sm text-gray-700">
                  {getTypeDisplayTitle(type)}
                </div>
                <div className="h-8 bg-gray-50 rounded border border-gray-200 relative overflow-visible">
                  {typeData.map(item => {
                    const startTime = new Date(item.created_at);
                    const endTime = new Date(item.updated_at);
                    const left = ((startTime.getTime() - timelineData.minTime.getTime()) / timelineData.timeRange) * 100;
                    const width = Math.max(((endTime.getTime() - startTime.getTime()) / timelineData.timeRange) * 100, 0.1);
                    
                    // 计算耗时
                    const duration = formatDuration(item.created_at, item.updated_at);
                    // 获取模型简称
                    const modelName = item.model_name || 'Unknown';
                    const modelShort = modelName.split('-')[0];
                    
                    // 根据宽度和缩放级别决定显示内容
                    const actualWidth = (width * timelineWidth) / 100;
                    let displayText = '';
                    if (actualWidth > 120) {
                      // 宽度足够，显示模型和耗时
                      displayText = `${modelShort} (${duration})`;
                    } else if (actualWidth > 60) {
                      // 中等宽度，只显示模型简称
                      displayText = modelShort;
                    } else if (actualWidth > 30) {
                      // 较窄，显示耗时
                      displayText = duration;
                    } else {
                      // 很窄，显示点
                      displayText = modelShort;
                    }

                    // 检查当前项是否需要高亮
                    const isRedundantHighlighted = highlightedItems.includes(item.id);
                    const isRelatedHighlighted = highlightedRelatedItems.includes(item.id);
                    const baseColor = getTypeColor(type, item);
                    
                    let highlightedColor = baseColor;
                    let highlightEffect = '';
                    
                    if (isRedundantHighlighted) {
                      // 冗余步骤：红色高亮，闪烁效果
                      highlightedColor = 'bg-red-500 ring-2 ring-red-300 ring-offset-1';
                      highlightEffect = 'animate-pulse';
                                         } else if (isRelatedHighlighted) {
                       // 相关步骤：橙色高亮，静态效果
                       highlightedColor = 'bg-orange-500 ring-2 ring-orange-300 ring-offset-1';
                       highlightEffect = '';
                     }
                    
                    // 已移除调试日志

                    return (
                      <div
                        key={item.id}
                        className={`absolute h-6 top-1 rounded cursor-pointer transition-all duration-200 hover:transform hover:-translate-y-1 hover:shadow-lg hover:z-20 flex items-center px-2 text-white text-xs font-medium overflow-hidden whitespace-nowrap ${highlightedColor} ${highlightEffect}`}
                        style={{ 
                          left: `${left}%`, 
                          width: `${width}%`, 
                          minWidth: actualWidth < 20 ? '20px' : 'auto',
                          maxWidth: `${100 - left}%` // 防止超出右边界
                        }}
                        onClick={() => onItemClick(item)}
                        onMouseEnter={(e) => handleMouseEnter(item, e)}
                        onMouseLeave={handleMouseLeave}
                        title={isRedundantHighlighted ? `冗余步骤: ${item.id}` : isRelatedHighlighted ? `相关步骤: ${item.id}` : undefined}
                      >
                        <span className="truncate">{displayText}</span>
                        {isRedundantHighlighted && (
                          <span className="ml-1 text-white font-bold">⚠</span>
                        )}
                        {isRelatedHighlighted && (
                          <span className="ml-1 text-white font-bold">🔗</span>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Hover 提示框 */}
      {hoveredItem && (
        <div 
          className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-3 max-w-[300px] pointer-events-none"
          style={{ 
            left: mousePosition.x + 10, 
            top: mousePosition.y - 10,
            transform: mousePosition.x > window.innerWidth - 320 ? 'translateX(-100%)' : 'none'
          }}
        >
          <div className="text-sm space-y-1">
            <div className="font-semibold text-blue-600">ID: {hoveredItem.id}</div>
            <div><span className="font-medium">模型:</span> {hoveredItem.model_name}</div>
            <div><span className="font-medium">耗时:</span> {formatDuration(hoveredItem.created_at, hoveredItem.updated_at)}</div>
            <div><span className="font-medium">状态:</span> <span className={hoveredItem.status === 'success' ? 'text-green-600' : 'text-red-600'}>{hoveredItem.status}</span></div>
            <div><span className="font-medium">时间:</span> {formatTime(hoveredItem.created_at)} ~ {formatTime(hoveredItem.updated_at)}</div>
            {hoveredItem.type === 'mewtwo' && (
              <div><span className="font-medium">类型:</span> <span className={isOnlyUserTag(hoveredItem.prompt) ? 'text-purple-600' : 'text-pink-600'}>{isOnlyUserTag(hoveredItem.prompt) ? 'mewtwo agent' : 'mewtwo'}</span></div>
            )}
            {hoveredItem.prompt && (
              <div><span className="font-medium">Prompt:</span> {hoveredItem.prompt.length > 50 ? hoveredItem.prompt.substring(0, 50) + '...' : hoveredItem.prompt}</div>
            )}
            {/* 显示步骤分析信息 */}
            {redundancyResult && (highlightedItems.includes(hoveredItem.id) || highlightedRelatedItems.includes(hoveredItem.id)) && (() => {
              const isRedundant = highlightedItems.includes(hoveredItem.id);
              const isRelated = highlightedRelatedItems.includes(hoveredItem.id);
              const redundantStep = redundancyResult.steps.find(step => step.step === hoveredItem.id);
              
              if (isRedundant && redundantStep) {
                return (
                  <div className="border-t border-red-200 pt-2 mt-2">
                    <div><span className="font-medium text-red-700">⚠️ 冗余步骤:</span> {redundantStep.abstract}</div>
                    <div className="text-xs text-red-600 mt-1">{redundantStep.reason}</div>
                  </div>
                );
                             } else if (isRelated) {
                 return (
                   <div className="border-t border-orange-200 pt-2 mt-2">
                     <div><span className="font-medium text-orange-700">🔗 相关步骤:</span> 与当前分析的冗余步骤相关联</div>
                   </div>
                 );
              }
              return null;
            })()}
          </div>
        </div>
      )}
      
      {/* Tool Hover 提示框 */}
      {hoveredTool && (
        <div 
          className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-3 max-w-[300px] pointer-events-none"
          style={{ 
            left: mousePosition.x + 10, 
            top: mousePosition.y - 10,
            transform: mousePosition.x > window.innerWidth - 320 ? 'translateX(-100%)' : 'none'
          }}
        >
          <div className="text-sm space-y-1">
            <div className="font-semibold text-blue-600">Tool: {hoveredTool.tool}</div>
            <div><span className="font-medium">描述:</span> {hoveredTool.description}</div>
            <div><span className="font-medium">耗时:</span> {hoveredTool.duration ? `${Math.floor(hoveredTool.duration / 1000)}秒` : '0秒'}</div>
            <div><span className="font-medium">状态:</span> <span className={hoveredTool.status === 'success' || hoveredTool.status === 'completed' ? 'text-green-600' : hoveredTool.status === 'failed' ? 'text-red-600' : 'text-yellow-600'}>{hoveredTool.status}</span></div>
            <div><span className="font-medium">时间:</span> {formatTime(hoveredTool.startTime)}{hoveredTool.endTime ? ` ~ ${formatTime(hoveredTool.endTime)}` : ''}</div>
            {hoveredTool.error && (
              <div><span className="font-medium text-red-600">错误:</span> {hoveredTool.error.length > 50 ? hoveredTool.error.substring(0, 50) + '...' : hoveredTool.error}</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}