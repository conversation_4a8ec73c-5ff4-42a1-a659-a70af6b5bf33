"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, AlertCircle, CheckCircle, FileText, Trash2 } from 'lucide-react';
import { analyzeRedundancy, type EvaluationResult, type EvaluationStep } from '../utils/aimeEvaluator';
import { ChatCompletion } from '@/app/bam/aime/namespaces/trace';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { apiClient } from '@/app/api/request';
import { useQuery } from '@tanstack/react-query';
import { ListModelsResponse, Model } from '@/app/bam/aime/namespaces/trace';

// 自定义 Alert 组件
interface AlertProps {
  children: React.ReactNode;
  variant?: 'default' | 'destructive';
  className?: string;
}

const Alert: React.FC<AlertProps> = ({ children, variant = 'default', className = '' }) => {
  const baseClasses = 'relative w-full rounded-lg border p-4';
  const variantClasses = variant === 'destructive' 
    ? 'border-red-200 bg-red-50 text-red-900' 
    : 'border-blue-200 bg-blue-50 text-blue-900';
  
  return (
    <div className={`${baseClasses} ${variantClasses} ${className}`}>
      {children}
    </div>
  );
};

const AlertTitle: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <h5 className="mb-1 font-medium leading-none tracking-tight">{children}</h5>
);

const AlertDescription: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="text-sm [&_p]:leading-relaxed">{children}</div>
);

// 本地存储键名
const STORAGE_KEYS = {
  SELECTED_MODEL: 'intelligent_analysis_selected_model'
};

interface IntelligentAnalysisProps {
  chatData: ChatCompletion[];
  onAnalysisComplete?: (result: EvaluationResult) => void;
  onAnalysisStart?: () => void;
  onAnalysisError?: (error: string) => void;
  onResetAnalysis?: () => void;
  onHighlightStep?: (stepId: string) => void;
  onClearHighlight?: () => void; // 清除高亮的回调
  onSwitchToTimeline?: () => void; // 切换到时间线视图的回调
  // 从父组件传递的状态
  result?: EvaluationResult | null;
  error?: string;
  isAnalyzing?: boolean;
}

export default function RedundancyAnalysis({ 
  chatData, 
  onAnalysisComplete, 
  onAnalysisStart,
  onAnalysisError,
  onResetAnalysis,
  onHighlightStep, 
  onClearHighlight, 
  onSwitchToTimeline,
  result,
  error,
  isAnalyzing = false
}: IntelligentAnalysisProps) {
  // 移除内部状态管理，使用从父组件传递的状态
  // const [isAnalyzing, setIsAnalyzing] = useState(false);
  // const [result, setResult] = useState<EvaluationResult | null>(null);
  // const [error, setError] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<string>('gemini-2.5-flash');

  // 获取模型列表
  const { data: modelsData, isLoading: isLoadingModels } = useQuery({
    queryKey: ["list-models"],
    queryFn: () => apiClient.ListModels({}),
    staleTime: Infinity,
  });

  // 处理模型数据，合并相同类型的模型
  const models = React.useMemo(() => {
    const processedModels: Model[] = [];
    for (const model of modelsData?.models ?? []) {
      const exists = processedModels?.find((item) => item.type === model?.type);
      if (!exists) {
        processedModels.push(model);
      } else {
        exists?.models?.push(...(model?.models ?? []));
        // 去重
        exists.models = Array.from(new Set(exists?.models));
      }
    }
    return processedModels;
  }, [modelsData]);

  // 从本地存储加载设置
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedModel = localStorage.getItem(STORAGE_KEYS.SELECTED_MODEL);
      if (savedModel) {
        setSelectedModel(savedModel);
      }
    }
  }, []);

  // 保存模型选择到本地存储
  const saveModelSelection = (model: string) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(STORAGE_KEYS.SELECTED_MODEL, model);
    }
  };

  // 清除保存的设置
  const clearSettings = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(STORAGE_KEYS.SELECTED_MODEL);
      setSelectedModel('gemini-2.5-flash');
    }
  };

  // 当模型选择变化时自动保存
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      saveModelSelection(selectedModel);
    }, 500); // 防抖，500ms 后保存

    return () => clearTimeout(timeoutId);
  }, [selectedModel]);

  const handleAnalyze = async () => {
    if (!selectedModel) {
      onAnalysisError?.('请选择分析模型');
      return;
    }

    if (!chatData || chatData.length === 0) {
      onAnalysisError?.('没有可分析的数据');
      return;
    }

    // 开始新的分析前，先重置之前的结果
    onResetAnalysis?.();
    onAnalysisStart?.();

    try {
      const analysisResult = await analyzeRedundancy(chatData, selectedModel);
      onAnalysisComplete?.(analysisResult);
    } catch (err) {
      console.error('分析失败:', err);
      onAnalysisError?.(err instanceof Error ? err.message : '分析过程中发生未知错误');
    }
  };

  const handleExportResult = () => {
    if (!result) return;

    const exportData = {
      timestamp: new Date().toISOString(),
      analysis_result: result,
      selected_model: selectedModel,
      data_summary: {
        total_completions: chatData.length,
        planner_items: chatData.filter(item => item.type.includes('planner')).length,
        mewtwo_items: chatData.filter(item => item.type.includes('mewtwo')).length,
      }
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `intelligent_analysis_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderStepCard = (step: EvaluationStep, index: number) => (
    <Card 
      key={index} 
      className="mb-3 border-l-4 border-l-orange-500 cursor-pointer hover:shadow-md transition-shadow duration-200"
      onClick={() => onHighlightStep?.(step.step)}
    >
      <CardContent className="pt-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline" className="bg-orange-50 text-orange-700">
                步骤 {step.step}
              </Badge>
              <span className="text-sm font-medium text-gray-900">{step.abstract}</span>
            </div>
            <p className="text-sm text-gray-600 leading-relaxed">{step.reason}</p>
            <div className="mt-2 text-xs text-gray-500">
              💡 点击此卡片可在时间线视图和列表视图中高亮该步骤
            </div>
          </div>
          <AlertCircle className="h-5 w-5 text-orange-500 mt-1 flex-shrink-0" />
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-4">
      {/* 设置面板 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                智能分析
              </CardTitle>
              <CardDescription>
                使用 AI 分析 Agent 执行过程中的潜在问题和优化建议
              </CardDescription>
            </div>
            <div className="flex gap-2">
              {selectedModel !== 'gemini-2.5-flash' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearSettings}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  重置设置
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* 模型选择 */}
          <div className="space-y-4 mb-4 p-4 border rounded-lg bg-gray-50">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-gray-900">模型配置</h4>
              <div className="text-xs text-green-600 flex items-center gap-1">
                <CheckCircle className="h-3 w-3" />
                设置自动保存
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <Label htmlFor="model-select">选择分析模型</Label>
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger className="w-full mt-1">
                    <SelectValue placeholder="请选择分析模型" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingModels ? (
                      <div className="px-2 py-2 text-sm text-gray-500 flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        加载模型列表...
                      </div>
                    ) : models.length === 0 ? (
                      <div className="px-2 py-2 text-sm text-gray-500">
                        暂无可用模型
                      </div>
                    ) : (
                      models.map((modelGroup, groupIndex) => (
                        <div key={`${modelGroup.type}_${groupIndex}`}>
                          {modelGroup.type && (
                            <div className="px-2 py-1 text-xs font-medium text-gray-500 bg-gray-100">
                              {modelGroup.type}
                            </div>
                          )}
                          {modelGroup.models?.map((modelName, modelIndex) => (
                            <SelectItem 
                              key={`${modelGroup.type}_${groupIndex}_${modelIndex}`}
                              value={modelName}
                            >
                              <span className="ml-2">{modelName}</span>
                            </SelectItem>
                          ))}
                        </div>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {selectedModel && (
                  <div className="text-xs text-gray-500 mt-1">
                    已选择: {selectedModel}
                  </div>
                )}
              </div>
            </div>
            <div className="text-xs text-gray-600 bg-blue-50 p-2 rounded">
              💡 提示：您的模型选择会自动保存在浏览器本地，下次访问时无需重新选择。
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleAnalyze}
              disabled={isAnalyzing || !selectedModel || isLoadingModels}
              className="flex-1"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  正在分析...
                </>
              ) : (
                '开始智能分析'
              )}
            </Button>
            
            {result && (
              <Button variant="outline" onClick={handleExportResult}>
                导出结果
              </Button>
            )}
          </div>

          {/* 数据概览 */}
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 mb-2">数据概览</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-blue-700">总记录数</span>
                <div className="font-medium text-blue-900">{chatData.length}</div>
              </div>
              <div>
                <span className="text-blue-700">Planner 记录</span>
                <div className="font-medium text-blue-900">
                  {chatData.filter(item => item.type.includes('planner')).length}
                </div>
              </div>
              <div>
                <span className="text-blue-700">Mewtwo 记录</span>
                <div className="font-medium text-blue-900">
                  {chatData.filter(item => item.type.includes('mewtwo')).length}
                </div>
              </div>
              <div>
                <span className="text-blue-700">分析模型</span>
                <div className="font-medium text-blue-900 flex items-center gap-1">
                  {selectedModel ? (
                    <>
                      <CheckCircle className="h-3 w-3 text-green-600" />
                      {selectedModel}
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-3 w-3 text-red-600" />
                      未选择
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && error.trim() && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>分析失败</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 分析结果 */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {result.redundant ? (
                <AlertCircle className="h-5 w-5 text-orange-500" />
              ) : (
                <CheckCircle className="h-5 w-5 text-green-500" />
              )}
              智能分析结果
            </CardTitle>
            <CardDescription>
              {result.redundant 
                ? `发现 ${result.steps.length} 个需要关注的步骤` 
                : '执行流程良好，未发现明显问题'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {result.redundant ? (
              <div className="space-y-4">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>发现可优化步骤</AlertTitle>
                  <AlertDescription>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>系统检测到 {result.steps.length} 个可能需要优化的步骤，建议进一步审查。</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={onSwitchToTimeline}
                          className="ml-4 text-blue-600 hover:text-blue-700"
                        >
                          查看时间线
                        </Button>
                      </div>
                      <div className="text-xs text-gray-600 bg-yellow-50 p-2 rounded">
                        💡 提示：分析发现的步骤会在时间线视图和列表视图中以橙色高亮显示。点击下方的步骤卡片可以高亮单个步骤。
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">需要关注的步骤：</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onClearHighlight}
                      className="text-gray-600 hover:text-gray-800"
                    >
                      清除高亮
                    </Button>
                  </div>
                  {result.steps.map((step, index) => renderStepCard(step, index))}
                </div>

                {/* 优化建议 */}
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">优化建议</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• 检查并优化标记的步骤，确保它们对最终目标有贡献</li>
                    <li>• 评估执行流程的逻辑性和必要性</li>
                    <li>• 考虑合并相似功能的步骤以提高效率</li>
                    <li>• 根据具体情况调整 Agent 的执行策略</li>
                  </ul>
                </div>
              </div>
            ) : (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertTitle>执行流程优化良好</AlertTitle>
                <AlertDescription>
                  智能分析表明当前执行流程相对高效，未发现明显的优化空间。
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
} 