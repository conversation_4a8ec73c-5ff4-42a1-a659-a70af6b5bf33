import { useState, useMemo } from "react";
import { type ChatCompletion } from "@/app/bam/aime/namespaces/trace";
import { ChevronUpIcon, ChevronDownIcon } from "@radix-ui/react-icons";
import { AlertCircle } from "lucide-react";
import { type EvaluationResult } from "../utils/aimeEvaluator";

interface ListViewProps {
  data: ChatCompletion[];
  onItemClick: (item: ChatCompletion) => void;
  highlightedItems?: string[]; // 冗余步骤 ID 列表
  highlightedRelatedItems?: string[]; // 相关步骤 ID 列表
  redundancyResult?: EvaluationResult | null;
  onClearHighlight?: () => void;
}

type SortOrder = 'asc' | 'desc' | null;

// 不同类型的颜色映射
const TYPE_COLORS: Record<string, string> = {
  response_conclude: "bg-green-500",
  task_concluder_selected_by_default: "bg-cyan-500",
  dynamic_planner: "bg-yellow-500",
  retrieve_knowledge: "bg-purple-500",
  summarize: "bg-orange-500",
  mewtwo: "bg-pink-500",
  default: "bg-gray-500",
};

// 获取类型对应的颜色
const getTypeColor = (type: string) => {
  return TYPE_COLORS[type] || TYPE_COLORS.default;
};

// 计算时间差并格式化显示
const formatDuration = (startTime: string, endTime: string) => {
  const start = new Date(startTime);
  const end = new Date(endTime);
  const diffMs = end.getTime() - start.getTime();
  
  if (diffMs < 0) return '0秒';
  
  const diffSeconds = Math.floor(diffMs / 1000);
  const minutes = Math.floor(diffSeconds / 60);
  const seconds = diffSeconds % 60;
  
  if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  } else {
    return `${seconds}秒`;
  }
};

// 截断文本
const truncateText = (text: string, maxLength: number = 100) => {
  if (!text) return 'N/A';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

export default function ListView({ 
  data, 
  onItemClick, 
  highlightedItems = [], 
  highlightedRelatedItems = [],
  redundancyResult, 
  onClearHighlight 
}: ListViewProps) {
  const [sortOrder, setSortOrder] = useState<SortOrder>(null);

  // 计算耗时的函数
  const calculateDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    return end.getTime() - start.getTime();
  };

  // 排序后的数据
  const sortedData = useMemo(() => {
    if (!sortOrder) return data;
    
    return [...data].sort((a, b) => {
      const durationA = calculateDuration(a.created_at, a.updated_at);
      const durationB = calculateDuration(b.created_at, b.updated_at);
      
      if (sortOrder === 'asc') {
        return durationA - durationB;
      } else {
        return durationB - durationA;
      }
    });
  }, [data, sortOrder]);

  const handleSortClick = () => {
    if (sortOrder === null) {
      setSortOrder('desc'); // 首次点击降序
    } else if (sortOrder === 'desc') {
      setSortOrder('asc'); // 第二次点击升序
    } else {
      setSortOrder(null); // 第三次点击取消排序
    }
  };

  if (data.length === 0) {
    return (
      <div className="text-center py-12 text-gray-500">
        暂无数据
      </div>
    );
  }

  return (
    <div>
      {/* 高亮状态提示 */}
      {(highlightedItems.length > 0 || highlightedRelatedItems.length > 0) && (
        <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-orange-600" />
              <span className="text-orange-800 font-medium">🔍 步骤高亮</span>
              <div className="flex items-center gap-4">
                {highlightedItems.length > 0 && (
                  <span className="text-orange-700 flex items-center gap-1">
                    <span className="w-3 h-3 bg-red-500 rounded-full"></span>
                    {highlightedItems.length} 个冗余步骤
                  </span>
                )}
                                 {highlightedRelatedItems.length > 0 && (
                   <span className="text-orange-700 flex items-center gap-1">
                     <span className="w-3 h-3 bg-orange-500 rounded-full"></span>
                     {highlightedRelatedItems.length} 个相关步骤
                   </span>
                 )}
              </div>
            </div>
            <button
              onClick={onClearHighlight}
              className="px-3 py-1 text-sm bg-orange-100 hover:bg-orange-200 text-orange-800 rounded-md transition-colors"
            >
              清除高亮
            </button>
          </div>
        </div>
      )}

      <div className="w-full">
        {/* 表头 */}
        <div className="bg-gray-100 border-b border-gray-200 sticky top-0 z-10">
          <div className="grid grid-cols-12 gap-2 p-3 text-sm font-medium text-gray-700">
            <div className="col-span-1">ID</div>
            <div className="col-span-2">类型</div>
            <div className="col-span-1">
              <button
                onClick={handleSortClick}
                className="flex items-center gap-1 hover:text-blue-600 transition-colors"
              >
                耗时
                {sortOrder === 'asc' && <ChevronUpIcon className="w-4 h-4" />}
                {sortOrder === 'desc' && <ChevronDownIcon className="w-4 h-4" />}
                {sortOrder === null && <div className="w-4 h-4" />}
              </button>
            </div>
            <div className="col-span-1">状态</div>
            <div className="col-span-2">模型</div>
            <div className="col-span-5">Response</div>
          </div>
        </div>
        
        {/* 数据行 */}
        <div className="divide-y divide-gray-200">
          {sortedData.map(item => {
            const duration = formatDuration(item.created_at, item.updated_at);
            const typeColor = getTypeColor(item.type);
            
            // 检查当前项是否需要高亮
            const isRedundantHighlighted = highlightedItems.includes(item.id);
            const isRelatedHighlighted = highlightedRelatedItems.includes(item.id);
            
            // 获取冗余步骤信息
            const redundantStep = redundancyResult?.steps.find(step => step.step === item.id);
            
            // 设置高亮样式
            let highlightClass = 'bg-white hover:bg-gray-50';
            let titleText = undefined;
            
            if (isRedundantHighlighted) {
              highlightClass = 'bg-red-50 border-l-4 border-l-red-500 hover:bg-red-100';
              titleText = redundantStep ? `冗余步骤: ${redundantStep.abstract}` : `冗余步骤: ${item.id}`;
                         } else if (isRelatedHighlighted) {
               highlightClass = 'bg-orange-50 border-l-4 border-l-orange-500 hover:bg-orange-100';
               titleText = `相关步骤: ${item.id}`;
             }
            
            return (
              <div
                key={item.id}
                className={`grid grid-cols-12 gap-2 p-3 cursor-pointer transition-all duration-200 hover:shadow-sm relative ${highlightClass}`}
                onClick={() => onItemClick(item)}
                title={titleText}
              >
                {/* 高亮标识 */}
                {(isRedundantHighlighted || isRelatedHighlighted) && (
                  <div className="absolute top-2 right-2">
                    {isRedundantHighlighted && <AlertCircle className="h-4 w-4 text-red-500" />}
                    {isRelatedHighlighted && <div className="h-4 w-4 text-orange-500">🔗</div>}
                  </div>
                )}
                
                {/* ID */}
                <div className={`col-span-1 font-semibold text-sm truncate ${
                  isRedundantHighlighted ? 'text-red-700' : isRelatedHighlighted ? 'text-orange-700' : 'text-blue-600'
                }`}>
                  {item.id}
                  {isRedundantHighlighted && <span className="ml-1 text-red-500">⚠</span>}
                                      {isRelatedHighlighted && <span className="ml-1 text-orange-500">🔗</span>}
                </div>
                
                {/* 类型标签 */}
                <div className="col-span-2">
                  <span className={`px-2 py-1 rounded text-white text-xs font-medium ${
                                         isRedundantHighlighted ? 'bg-red-500' : isRelatedHighlighted ? 'bg-orange-500' : typeColor
                  } inline-block text-center`}>
                    {item.type}
                    {isRedundantHighlighted && <span className="ml-1">⚠</span>}
                                          {isRelatedHighlighted && <span className="ml-1">🔗</span>}
                  </span>
                </div>
                
                {/* 耗时 */}
                <div className={`col-span-1 text-sm truncate ${
                  isRedundantHighlighted ? 'text-red-700' : isRelatedHighlighted ? 'text-orange-700' : 'text-gray-700'
                }`}>
                  {duration}
                </div>
                
                {/* 状态 */}
                <div className="col-span-1 text-sm truncate">
                  <span className={`font-medium ${
                    item.status === 'success' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {item.status}
                  </span>
                </div>
                
                {/* 模型 */}
                <div className={`col-span-2 text-sm truncate ${
                  isRedundantHighlighted ? 'text-red-700' : isRelatedHighlighted ? 'text-orange-700' : 'text-gray-700'
                }`} title={item.model_name}>
                  {item.model_name}
                </div>
                
                {/* Response 预览 */}
                <div className={`col-span-5 text-sm truncate ${
                  isRedundantHighlighted ? 'text-red-600' : isRelatedHighlighted ? 'text-orange-600' : 'text-gray-600'
                }`} title={item.response || 'N/A'}>
                  {truncateText(item.response || '', 80)}
                </div>
                
                {/* 冗余分析信息提示 */}
                {isRedundantHighlighted && redundantStep && (
                  <div className="col-span-12 mt-2 p-2 bg-orange-100 rounded border-l-2 border-orange-300">
                    <div className="text-xs text-orange-800">
                      <span className="font-medium">问题描述:</span> {redundantStep.abstract}
                    </div>
                    <div className="text-xs text-orange-700 mt-1">
                      <span className="font-medium">原因分析:</span> {redundantStep.reason}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
} 