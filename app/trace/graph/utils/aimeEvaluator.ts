import { ChatCompletion, ChatCompletionStatus, ChatCompletionMessage } from "@/app/bam/aime/namespaces/trace";
import { apiClient } from "@/app/api/request";

// 扩展 ChatCompletion 接口以包含解析后的 metadata
export interface ParsedChatCompletion extends ChatCompletion {
  parsedMetadata?: Record<string, any>;
}

// AimeChat 数据结构
export interface AimeChat {
  chat_completions: ParsedChatCompletion[];
}

// 评估结果数据结构
export interface EvaluationStep {
  step: string;
  abstract: string;
  reason: string;
  related_steps: string[];
}

export interface EvaluationResult {
  redundant: boolean;
  steps: EvaluationStep[];
}

/**
 * AIME 评估器类
 * 用于分析 Agent 执行过程中的冗余步骤
 */
export class AimeEvaluator {
  private selectedModel: string;

  constructor(selectedModel: string = 'gemini-2.5-flash') {
    this.selectedModel = selectedModel;
  }

  /**
   * 解析 metadata JSON 字符串
   */
  static parseMetadata(metadata: string): Record<string, any> {
    try {
      return JSON.parse(metadata);
    } catch (error) {
      console.warn('Failed to parse metadata:', metadata);
      return {};
    }
  }

  /**
   * 从 JSON 数据创建 AimeChat 实例
   */
  static fromJson(jsonData: any): AimeChat {
    const chatCompletions: ParsedChatCompletion[] = [];
    
    for (const completion of jsonData.chat_completions || []) {
      const parsedCompletion: ParsedChatCompletion = {
        ...completion,
        parsedMetadata: this.parseMetadata(completion.metadata)
      };
      chatCompletions.push(parsedCompletion);
    }

    return {
      chat_completions: chatCompletions
    };
  }

  /**
   * 从 ChatCompletion 数组创建 AimeChat 实例
   */
  static fromChatCompletions(chatCompletions: ChatCompletion[]): AimeChat {
    const parsedCompletions: ParsedChatCompletion[] = chatCompletions.map(completion => ({
      ...completion,
      parsedMetadata: this.parseMetadata(completion.metadata)
    }));

    return {
      chat_completions: parsedCompletions
    };
  }

  /**
   * 将 AimeChat 实例转换为 JSON 数据
   */
  static toJson(aimeChat: AimeChat): any {
    return {
      chat_completions: aimeChat.chat_completions.map(completion => ({
        id: completion.id,
        created_at: completion.created_at,
        updated_at: completion.updated_at,
        prompt: completion.prompt,
        response: completion.response,
        type: completion.type,
        metadata: completion.metadata,
        status: completion.status,
        model_name: completion.model_name
      }))
    };
  }

  /**
   * 从文件读取 JSON 数据
   */
  static async readJsonFile(file: File): Promise<any> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const jsonData = JSON.parse(e.target?.result as string);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`Invalid JSON file: ${error}`));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  /**
   * 调用内部模型 API
   */
  private async callModelAPI(messages: ChatCompletionMessage[]): Promise<string> {
    try {
      const response = await apiClient.ChatStream({
        model: this.selectedModel,
        messages: messages,
        temperature: 0.0,
        stream: false,
        max_tokens: 10000
      });

      // 处理响应格式
      if (response && typeof response === 'object' && 'choices' in response) {
        const choices = (response as any).choices;
        if (Array.isArray(choices) && choices.length > 0 && choices[0].message?.content) {
          return choices[0].message.content;
        }
      }

      throw new Error('模型 API 响应格式异常');
    } catch (error) {
      console.error('模型 API 调用失败:', error);
      throw new Error(`模型 API 调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 评估 Agent 执行过程是否存在冗余步骤
   */
  async evaluate(aimeChat: AimeChat): Promise<EvaluationResult> {
    const systemPrompt = `你是一个智能助手，用于评估 Agent 执行过程中是否包含冗余步骤。
你需要仔细阅读 Agent 的执行过程，判断是否存在冗余步骤。

<important>
所谓冗余步骤**包括但不限于**如下行为：
1. 步骤 2 的执行结果已经包含在步骤 1 的执行结果中。
2. 执行过程中与完成用户任务无关的步骤，例如当用户需求是分析崩溃数据时，分析数据过程中生成了 HTML 可视化报告。（注意：如果用户需求中包含了生成可视化报告，但是在第一步中生成了可视化报告且与最终报告无关，也可以视为冗余步骤）
3. 在读取了文件完整内容后，又重新读取了文件内容。
4. 使用编写的代码生成了一个新的文件，但是这个文件的内容与最终报告无关。
5. 在读取了文件完整内容后，又通过编写代码的方式提取了文件中的关键信息。

NOTE: 
1. 如果最后一步生成了飞书文档（即使用户没有要求），这并不被视为冗余步骤。
2. 不同的步骤可能是由不同的 Agent 执行的，当非最终步骤包含了 \`conclude\` 的 Action 时，标记当前子 Agent 分配的任务完成并总结子任务的完成情况，不可以视为冗余步骤。
3. 如果不同的步骤中间包含了执行 \`conclude\` 的 Action，则表明后面的步骤与前面的步骤位于不同的子 Agent 中。对于不同的子 Agent，前一个子 Agent 生成文件，后一个子 Agent 读取相同的文件，视为合理的信息传递，不是冗余步骤。
</important>`;

    // 筛选出 type 包含 'planner' 的项并按创建时间排序
    const plannerItems = aimeChat.chat_completions
      .filter(item => item.type.includes('planner'))
      .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());

    if (plannerItems.length === 0) {
      throw new Error('未找到包含 planner 的聊天记录');
    }

    // 从 prompt 中提取用户输入
    let userInput = '';
    const firstPlannerPrompt = plannerItems[0].prompt;
    
    if (firstPlannerPrompt.includes('[Problem Statement]')) {
      userInput = firstPlannerPrompt.split('[Problem Statement]')[1].trim();
    }

    if (userInput.length === 0) {
      throw new Error('无法从 prompt 中提取用户输入');
    }

    // 构建执行过程
    let process = '';
    const mewtwoItems = aimeChat.chat_completions
      .filter(item => item.type.includes('mewtwo'))
      .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());

    mewtwoItems.forEach((item) => {
      process += `
<step${item.id}>
${JSON.stringify(item.response, null, 2)}
</step${item.id}>
`;
    });

    const userPrompt = `请评估以下 Agent 的执行过程是否存在冗余步骤：

<content_to_evaluate>
用户输入：${userInput}

Agent 执行过程：${process}

请给出你的评估结果，包括是否存在冗余步骤、冗余步骤的位置、冗余步骤的原因。
</content_to_evaluate>

<output_format>
严格按照以下 JSON 格式输出，不允许包含任何其他内容：
{"redundant": true, "steps": [{"step": "1", "abstract":"输出报告", "reason": "步骤 1 的执行结果已经包含在步骤 2 的执行结果中", "related_steps": ["2"]}]}

如果不存在冗余步骤，输出：
{"redundant": false, "steps": []}
</output_format>

<json_schema_requirements>
JSON 对象必须包含以下字段，严格按照类型要求：

1. "redundant" (boolean, 必填)
   - 类型：布尔值
   - 取值：true 或 false
   - 描述：是否存在冗余步骤

2. "steps" (array, 必填)
   - 类型：数组
   - 描述：包含所有冗余步骤的详细信息
   - 如果 redundant 为 false，则为空数组 []
   - 数组中的每个元素必须是对象，包含以下字段：
     
     2.1 "step" (string, 必填)
         - 类型：字符串
         - 描述：冗余步骤的序号或标识符（如 "0", "1", "2" 等）
         
     2.2 "abstract" (string, 必填)
         - 类型：字符串
         - 描述：冗余步骤的简要概括，用一句话描述该步骤做了什么
         
     2.3 "reason" (string, 必填)
         - 类型：字符串
         - 描述：该步骤被认定为冗余的具体原因和详细说明
         
     2.4 "related_steps" (array, 必填)
         - 类型：字符串数组
         - 描述：导致当前步骤被判断为冗余的相关步骤的 id 集合
         - 数组中的每个元素必须是字符串类型的步骤 id
</json_schema_requirements>

<critical_requirements>
1. 你的回复必须只包含一个有效的 JSON 对象，不能包含任何解释、说明、代码块标记或其他文本
2. 严格遵循上述 JSON 结构要求，所有字段都必须存在且类型正确
3. 字符串字段不能为空字符串，必须包含有意义的内容
4. 不要在 JSON 前后添加任何文字说明、反引号、换行符或其他字符
5. 请确保输出的 JSON 能够被 JSON.parse() 成功解析
6. 数组和对象的嵌套结构必须完全正确
</critical_requirements>
`;

    const messages: ChatCompletionMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ];

    const response = await this.callModelAPI(messages);
    
    // 优化的JSON解析逻辑
    let result: EvaluationResult;
    try {
      // 1. 先尝试直接解析原始响应
      result = JSON.parse(response.trim());
    } catch (directParseError) {
      try {
        // 2. 尝试从```json ```代码块中提取JSON内容
        const jsonBlockMatch = response.match(/```json\s*\n?([\s\S]*?)\n?```/);
        if (jsonBlockMatch && jsonBlockMatch[1]) {
          result = JSON.parse(jsonBlockMatch[1].trim());
        } else {
          // 3. 如果没有找到代码块，尝试清理所有代码块标记后解析
          const cleanedResponse = response.replace(/```json\n?/g, '').replace(/```/g, '').trim();
          result = JSON.parse(cleanedResponse);
        }
      } catch (codeBlockParseError) {
        throw new Error(`无法解析评估结果。原始响应: ${response}`);
      }
    }
    
    return result;
  }
}

/**
 * 分析 ChatCompletion 列表中的冗余操作
 * 这是一个便捷函数，用于直接分析页面中的 ChatCompletion 数据
 */
export async function analyzeRedundancy(
  chatCompletions: ChatCompletion[],
  selectedModel: string = 'gemini-2.5-flash'
): Promise<EvaluationResult> {
  const aimeChat = AimeEvaluator.fromChatCompletions(chatCompletions);
  const evaluator = new AimeEvaluator(selectedModel);
  return await evaluator.evaluate(aimeChat);
}

/**
 * 从文件分析冗余操作
 */
export async function analyzeRedundancyFromFile(
  file: File,
  selectedModel: string = 'gemini-2.5-flash'
): Promise<EvaluationResult> {
  const jsonData = await AimeEvaluator.readJsonFile(file);
  const aimeChat = AimeEvaluator.fromJson(jsonData);
  const evaluator = new AimeEvaluator(selectedModel);
  return await evaluator.evaluate(aimeChat);
} 