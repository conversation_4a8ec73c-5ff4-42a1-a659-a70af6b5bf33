import { SessionStatus } from "@/app/bam/aime/namespaces/session";
import { isBoe, isI18N } from "@/app/api/env";
import { ARGOS_BOE_URL, ARGOS_I18N_URL, ARGOS_ONLINE_URL } from "@/app/api/const";

// 格式化 ID 显示
export const formatIdForDisplay = (
  id: string | undefined,
  prefixLength = 10,
  suffixLength = 4,
) => {
  if (!id) return "未知";
  if (id.length <= prefixLength + suffixLength + 3) return id;
  return `${id.slice(0, prefixLength)}...${id.slice(-suffixLength)}`;
};

// 格式化日期时间
export const formatDateTime = (dateString?: string) => {
  if (!dateString) return "未知";
  return new Date(dateString).toLocaleString();
};

// 获取状态对应的颜色
export const getStatusColor = (
  status?: SessionStatus,
): "default" | "destructive" | "secondary" | "outline" => {
  if (!status) return "default";

  const statusColorMap: Record<
    SessionStatus,
    "default" | "destructive" | "secondary" | "outline"
  > = {
    [SessionStatus.SessionStatusRunning]: "default",
    [SessionStatus.SessionStatusStopped]: "destructive",
    [SessionStatus.SessionStatusCreated]: "secondary",
    [SessionStatus.SessionStatusCanceled]: "secondary",
    [SessionStatus.SessionStatusError]: "destructive",
    [SessionStatus.SessionStatusIdle]: "default",
    [SessionStatus.SessionStatusWaiting]: "secondary",
    [SessionStatus.SessionStatusAbnormal]: "destructive",
    [SessionStatus.SessionStatusClosed]: "destructive",
  };

  return statusColorMap[status] || "default";
};

// 获取运行状态对应的颜色
export const getRunStatusColor = (
  runStatus?: string,
): "default" | "destructive" | "secondary" | "outline" => {
  if (!runStatus) return "default";
  const status = runStatus.toLowerCase();

  if (status.includes("running")) return "default";
  if (status.includes("error") || status.includes("failed"))
    return "destructive";
  if (status.includes("complete") || status.includes("success"))
    return "default";

  return "secondary";
};

// 生成 URL 函数
export const generateURL = (url: string, query: Record<string, string>) => {
  const urlObj = new URL(url);
  Object.entries(query).forEach(([key, value]) => {
    urlObj.searchParams.append(key, value);
  });
  return urlObj.toString();
};

// 生成 Argos URLs
export const generateArgosURLs = (
  id: string | undefined,
  run_id: string | undefined,
  created_at: string | undefined,
  updated_at: string | undefined,
) => {
  const startTimestamp = (
    new Date(created_at || "").getTime() / 1000
  ).toString();
  const endTimestamp = (
    new Date(updated_at || "").getTime() / 1000 +
    60
  ).toString();

  const argosBaseUrl = isBoe() ? ARGOS_BOE_URL : isI18N() ? ARGOS_I18N_URL : ARGOS_ONLINE_URL;

  const serverArgosURL = generateURL(argosBaseUrl + "/streamlog/info_overview/keyword_search", {
    patterns: id || "",
    psm: "flow.agentsphere.nextserver",
    start_time: startTimestamp,
    end_time: endTimestamp,
    auto_search: "true",
  });

  const runtimeArgosURL = generateURL((isI18N() ? ARGOS_I18N_URL : ARGOS_ONLINE_URL)+"/streamlog/info_overview/keyword_search", {
    patterns: run_id || "",
    unIncludePatterns: "[KITEX],[TCC],HERTZ:",
    unIncludeOr: "true",
    psm: "flow.agentsphere.runtime",
    start_time: startTimestamp,
    end_time: endTimestamp,
    auto_search: "true",
    filter_tag:isI18N() ? '' : '_idc||hl::lf::lq::yg::tjdt'
  });

  return { serverArgosURL, runtimeArgosURL };
};

// 生成视频链接
export const generateVideoUrl = (webshell_url: string | undefined) => {
  if (!webshell_url) return null;

  try {
    const url = new URL(webshell_url);
    const cubeId = url.searchParams.get("id");
    if (!cubeId) return null;

    const domain = isBoe()
      ? "strato-cloudbuild-ppe"
      : "cube-kubestrato-online";
    return `https://strato-https-proxy.bytedance.net/${cubeId}.${domain}/novnc/vnc.html?autoconnect=true&password=password&resize=scale&view_only=true`;
  } catch (err) {
    console.error("解析 webshell_url 失败:", err);
    return null;
  }
}; 