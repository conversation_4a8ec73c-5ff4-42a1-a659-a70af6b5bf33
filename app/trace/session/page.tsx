"use client";

import { useEffect } from "react";
import { apiClient } from "@/app/api/request";
import { useSearchParams, useRouter } from "next/navigation";
import { LoadingSpinner } from "@/app/components/LoadingSpinner";
import { PageHeader } from "@/app/components/PageHeader";
import { AlertCircle } from "lucide-react";
import { useStore } from "@nanostores/react";

// 导入拆分的组件
import SearchPanel from "@/app/trace/session/components/SearchPanel";
import SessionDetailCard from "@/app/trace/session/components/SessionDetailCard";

// 导入状态管理相关内容
import {
  searchStore,
  initRecentSearches,
  setSearchType,
  setSearchId,
  setLoading,
  setError,
  setSessionData,
  SearchType,
  addToRecentSearches,
} from "./store/searchStore";

// 导入类型
import { type GetTraceSessionRequest } from "@/app/bam/aime/namespaces/trace";

export interface SearchParams {
  type: SearchType;
  id: string;
  onSuccess?: () => void;
  updateUrl?: boolean;
}

export default function TraceSessionPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { searchType, searchId, isLoading, isFirstSearch, error, sessionData } =
    useStore(searchStore);

  // 初始化并从本地存储加载最近搜索记录
  useEffect(() => {
    initRecentSearches();
  }, []);

  // 初始化时从URL参数中获取session_id或replay_id
  useEffect(() => {
    const sessionIdFromUrl = searchParams.get("session_id");
    const replayIdFromUrl = searchParams.get("replay_id");

    if (sessionIdFromUrl) {
      setSearchType(SearchType.SESSION);
      setSearchId(sessionIdFromUrl);
      handleSearch({
        type: SearchType.SESSION,
        id: sessionIdFromUrl,
        updateUrl: false, // 来自URL的初始化搜索不需要再更新URL
        onSuccess: () =>
          addToRecentSearches(sessionIdFromUrl, SearchType.SESSION),
      });
    } else if (replayIdFromUrl) {
      setSearchType(SearchType.REPLAY);
      setSearchId(replayIdFromUrl);
      handleSearch({
        type: SearchType.REPLAY,
        id: replayIdFromUrl,
        updateUrl: false, // 来自URL的初始化搜索不需要再更新URL
        onSuccess: () =>
          addToRecentSearches(replayIdFromUrl, SearchType.REPLAY),
      });
    }
  }, [searchParams]);

  // 更新URL参数的函数
  const updateUrlParams = (type: SearchType, id: string) => {
    const paramKey = type === SearchType.SESSION ? "session_id" : "replay_id";

    // Next.js 提供的方法：更新查询参数但保持当前路径
    const params = new URLSearchParams(searchParams.toString());

    // 清除现有的session_id和replay_id参数
    params.delete("session_id");
    params.delete("replay_id");

    // 添加新的参数
    if (id) {
      params.set(paramKey, id);
    }

    // 使用相对路径，Next.js 会自动处理 basePath
    router.replace(`?${params.toString()}`);
  };

  const handleSearch = async (params: SearchParams) => {
    const { type, id, onSuccess, updateUrl = true } = params;
    if (!id.trim()) {
      setError("请输入有效的 ID");
      return;
    }

    // 仅当需要时才更新URL参数（来自历史记录的点击不更新URL）
    if (updateUrl) {
      updateUrlParams(type, id.trim());
    }

    setLoading(true);
    setError("");

    try {
      const request: GetTraceSessionRequest = {};
      if (type === SearchType.SESSION) {
        request.session_id = id;
      } else {
        request.replay_id = id;
      }

      const result = await apiClient.GetTraceSession(request);

      setSessionData(result.session);

      // 如果有成功回调，则调用
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error("获取会话数据失败:", err);
      setError("获取会话数据失败: " + (err as Error).message);
      setSessionData(null);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col gap-6">
      {/* 页面标题 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-100 shadow-sm">
        <PageHeader
          title="会话追踪"
          description="查询和管理会话的详细信息，包括会话状态、运行时环境和相关资源"
        />
      </div>

      {/* 搜索面板 */}
      <div className="w-full lg:max-w-[800px]">
        <SearchPanel
          handleSearch={params => {
            if (params) {
              handleSearch(params);
            } else {
              handleSearch({ type: searchType, id: searchId });
            }
          }}
        />
      </div>

      {/* 加载状态 - 仅在首次加载时显示在页面中央 */}
      {isLoading && isFirstSearch && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 bg-white py-8 px-10 rounded-xl shadow-xl border border-gray-100 flex flex-col items-center justify-center gap-5">
          <LoadingSpinner />
          <p className="text-base font-medium text-gray-700">
            正在加载会话数据...
          </p>
        </div>
      )}

      {/* 结果显示 */}
      {!isLoading && !isFirstSearch && !sessionData && !error && (
        <div className="flex flex-col items-center justify-center p-10 bg-gray-50 rounded-lg border border-gray-200 text-center">
          <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-700 mb-2">
            未找到会话数据
          </h3>
          <p className="text-gray-500 max-w-md">
            没有找到匹配的会话信息，请检查ID是否正确，或尝试使用不同的查询条件。
          </p>
        </div>
      )}

      <div
        className={`transition-opacity duration-200 w-full ${
          isLoading && isFirstSearch ? "opacity-50" : "opacity-100"
        } ${sessionData ? "flex-1" : "flex-none"}`}
      >
        {sessionData && <SessionDetailCard sessionData={sessionData} />}
      </div>
    </div>
  );
}
