import { atom, map } from 'nanostores';
import { TraceSession, GetTraceSessionRequest } from '@/app/bam/aime/namespaces/trace';
import { apiClient } from '@/app/api/request';

export enum SearchType {
    SESSION = "session",
    REPLAY = "replay",
}

// 定义最近搜索记录的本地存储KEY
export const RECENT_SEARCHES_KEY = "aimo_recent_searches";

// 存储搜索相关状态
export const searchStore = map({
  searchType: SearchType.SESSION,
  searchId: '',
  isLoading: false,
  isFirstSearch: true,
  error: '',
  sessionData: null as TraceSession | null,
});

// 存储最近搜索记录
export const recentSearchesStore = atom<string[]>([]);

// 初始化加载最近搜索记录
export function initRecentSearches() {
  try {
    const storedSearches = localStorage.getItem(RECENT_SEARCHES_KEY);
    if (storedSearches) {
      recentSearchesStore.set(JSON.parse(storedSearches));
    }
  } catch (e) {
    console.error("解析最近搜索记录失败", e);
    recentSearchesStore.set([]);
  }
}

// 添加搜索记录
export function addToRecentSearches(id: string, type: SearchType) {
  if (!id.trim()) return;
  
  // 标准化ID格式，确保带有正确的前缀
  const isReplaySearch = type === SearchType.REPLAY;
  let normalizedId = id;
  
  // 分享ID搜索 - 添加replay_前缀
  if (isReplaySearch) {
    // 如果已经有前缀就不添加
    if (!id.startsWith("replay_")) {
      normalizedId = `replay_${id}`;
    }
  } 
  // 会话ID搜索 - 添加sess_前缀
  else {
    // 如果已经有前缀就不添加
    if (!id.startsWith("sess_")) {
      normalizedId = `sess_${id}`;
    }
  }

  const currentSearches = recentSearchesStore.get();
  
  // 创建新的搜索记录数组，将当前ID添加到最前面
  const updatedSearches = [
    normalizedId,
    ...currentSearches.filter(item => item !== normalizedId),
  ].slice(0, 5);
  
  recentSearchesStore.set(updatedSearches);
  
  // 保存到本地存储
  try {
    localStorage.setItem(
      RECENT_SEARCHES_KEY,
      JSON.stringify(updatedSearches),
    );
  } catch (e) {
    console.error("保存最近搜索记录失败", e);
  }
}

// 更新搜索类型
export function setSearchType(type: SearchType) {
  searchStore.setKey('searchType', type);
}

// 更新搜索ID
export function setSearchId(id: string) {
  searchStore.setKey('searchId', id);
}

// 设置加载状态
export function setLoading(loading: boolean) {
  searchStore.setKey('isLoading', loading);
}

// 设置错误信息
export function setError(error: string) {
  searchStore.setKey('error', error);
}

// 设置会话数据
export function setSessionData(data: TraceSession | null) {
  searchStore.setKey('sessionData', data);
  
  // 如果数据加载成功且是首次搜索，更新首次搜索标志
  if (data && searchStore.get().isFirstSearch) {
    searchStore.setKey('isFirstSearch', false);
  }
}

// 设置非首次搜索状态
export function setNotFirstSearch() {
  searchStore.setKey('isFirstSearch', false);
}

// 获取最新的会话数据
export async function refreshSessionData() {
  const { searchType, searchId } = searchStore.get();
  if (!searchId.trim()) return;

  setLoading(true);
  setError("");

  try {
    const request: GetTraceSessionRequest = {};
    if (searchType === SearchType.SESSION) {
      request.session_id = searchId;
    } else {
      request.replay_id = searchId;
    }

    const result = await apiClient.GetTraceSession(request);

    setSessionData(result.session);
  } catch (err) {
    console.error("获取会话数据失败:", err);
    setError("获取会话数据失败，请检查 ID 是否正确");
    setSessionData(null);
  } finally {
    setLoading(false);
  }
} 