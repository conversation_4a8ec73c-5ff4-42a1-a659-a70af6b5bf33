import { useState } from "react";
import { TraceSession, RuntimeProvider } from "@/app/bam/aime/namespaces/trace";
import { apiClient } from "@/app/api/request";
import { toast } from "sonner";
import { refreshSessionData } from "../store/searchStore";
import { SessionInfoCard } from "./SessionInfoCard";
import { RuntimeInfoCard } from "./RuntimeInfoCard";
import { ResourceLinksCard } from "./ResourceLinksCard";
import { ActivateRuntimeDialog } from "./ActivateRuntimeDialog";
import { generateArgosURLs } from "../utils/sessionUtils";

interface SessionDetailCardProps {
  sessionData: TraceSession;
  // 该属性仅为保持接口兼容，实际不再使用
  onRefresh?: () => void;
}

const SessionDetailCard = ({ sessionData }: SessionDetailCardProps) => {
  const [isActivateModalOpen, setIsActivateModalOpen] = useState(false);
  const [isActivating, setIsActivating] = useState(false);
  const [showCopiedId, setShowCopiedId] = useState(false);
  const [showCopiedLogId, setShowCopiedLogId] = useState(false);
  const [showCopiedRunId, setShowCopiedRunId] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const {
    id,
    title,
    creator,
    status,
    created_at,
    updated_at,
    events_url,
    run_provider,
    run_status,
    run_debug_status,
    run_id,
    webshell_url,
    file_server_url,
    agent_metadata,
    log_id,
    browser_url,
  } = sessionData;

  const isStratoCube = run_provider === RuntimeProvider.StratoCube;

  // 计算 Argos 链接
  const { serverArgosURL, runtimeArgosURL } = generateArgosURLs(
    id,
    run_id,
    created_at,
    updated_at,
  );

  // 复制文本到剪贴板
  const copyToClipboard = (
    text: string,
    message: string,
    type: "id" | "logId" | "runId" = "id",
  ) => {
    navigator.clipboard.writeText(text).then(
      () => {
        toast.success(message);
        if (type === "runId") {
          setShowCopiedRunId(true);
          setTimeout(() => setShowCopiedRunId(false), 2000);
        } else if (type === "logId") {
          setShowCopiedLogId(true);
          setTimeout(() => setShowCopiedLogId(false), 2000);
        } else {
          setShowCopiedId(true);
          setTimeout(() => setShowCopiedId(false), 2000);
        }
      },
      () => {
        toast.error("复制失败");
      },
    );
  };

  // 激活运行时
  const handleActivateRuntime = async () => {
    if (!run_id) {
      toast.error("无法激活：缺少运行 ID");
      return;
    }

    // 防止重复点击
    if (isActivating) return;

    setIsActivating(true);
    // 创建一个toast ID，以便后续关闭
    const toastId = toast.loading("正在激活环境...");

    try {
      // 调用激活API
      await apiClient.ResumeRuntime({
        run_id,
      });

      // 关闭对话框，但保持加载状态
      setIsActivateModalOpen(false);

      // 使用Promise和setTimeout结合，更加可靠
      await new Promise(resolve => {
        // 延长3秒的加载提示，然后再显示成功
        setTimeout(() => {
          // 3秒后更新toast为成功
          toast.success("激活成功", { id: toastId });
          resolve(true);
        }, 3000);
      });

      // 使用全局状态更新数据，而不是通过回调刷新页面
      await refreshSessionData();
    } catch (err) {
      console.error("激活失败:", err);
      // 使用同一个ID更新toast
      toast.error("激活失败，请稍后重试", { id: toastId });
      setIsActivateModalOpen(false);
    } finally {
      // 确保无论成功还是失败，都会重置状态
      setIsActivating(false);
    }
  };

  // 下载会话日志
  const handleDownloadLog = async () => {
    if (!id) {
      toast.error("无法下载：缺少会话 ID");
      return;
    }

    if (isDownloading) return;

    setIsDownloading(true);
    const toastId = toast.loading("正在下载日志...");

    try {
      const response = (await apiClient.DownloadSessionLog({
        session_id: id,
      })) as unknown as Response;

      // 从响应头中获取文件名
      const contentDisposition = response.headers.get("content-disposition");
      let filename = `session_${id}_log.txt`;
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(
          /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/,
        );
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, "");
        }
      }

      // 获取响应体
      const blob = await response.blob();

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success("日志下载成功", { id: toastId });
    } catch (err) {
      console.error("下载日志失败:", err);
      toast.error("下载日志失败，请稍后重试", { id: toastId });
    } finally {
      setIsDownloading(false);
    }
  };

  const handleStopRuntime = async () => {
    if (!run_id) {
      toast.error("无法停止：缺少运行 ID");
      return;
    }
    const toastId = toast.loading("正在停止容器...");
    try {
      await apiClient.SuspendRuntime({
        run_id,
      });
      toast.success("已发送停止指令，请稍后刷新", { id: toastId });
      await refreshSessionData();
    } catch (err) {
      console.error("停止容器失败:", err);
      toast.error(`停止容器失败：${(err as Error).message}`, { id: toastId });
    }
  };

  return (
    <div className="flex flex-col gap-5 w-full">
      {/* 会话信息卡片区域，使用三列响应式布局 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-5 w-full">
        <SessionInfoCard
          id={id}
          title={title}
          creator={creator}
          status={status}
          created_at={created_at}
          updated_at={updated_at}
          agent_metadata={agent_metadata}
          log_id={log_id}
          onCopyId={id => copyToClipboard(id, "ID 已复制到剪贴板", "id")}
          onCopyLogId={id =>
            copyToClipboard(id, "Log ID 已复制到剪贴板", "logId")
          }
          showCopiedId={showCopiedId}
          showCopiedLogId={showCopiedLogId}
        />

        <RuntimeInfoCard
          run_provider={run_provider}
          run_status={run_status}
          run_debug_status={run_debug_status}
          run_id={run_id}
          webshell_url={webshell_url}
          file_server_url={file_server_url}
          // TODO: 临时代码 后端修复后删除
          videoUrl={browser_url?.replace('strato-https-proxy.byted.org', 'strato-https-proxy.bytedance.net')}
          isStratoCube={isStratoCube}
          onCopyRunId={id =>
            copyToClipboard(id, "Runtime ID 已复制到剪贴板", "runId")
          }
          showCopiedRunId={showCopiedRunId}
          onActivateRuntime={handleActivateRuntime}
          isActivating={isActivating}
          onStopRuntime={handleStopRuntime}
        />

        {(events_url || id || run_id) && (
          <ResourceLinksCard
            events_url={events_url}
            id={id}
            run_id={run_id}
            serverArgosURL={serverArgosURL}
            runtimeArgosURL={runtimeArgosURL}
            isDownloading={isDownloading}
            onDownloadLog={handleDownloadLog}
          />
        )}
      </div>

      <ActivateRuntimeDialog
        isOpen={isActivateModalOpen}
        onOpenChange={setIsActivateModalOpen}
        onActivate={handleActivateRuntime}
        isActivating={isActivating}
      />
    </div>
  );
};

export default SessionDetailCard;
