import {
  Activity,
  Cloud,
  MessageSquare,
  Download,
  ChevronRight,
  <PERSON>rkles,
  ChartGantt,
  Stethoscope,
  Eye,
} from "lucide-react";
import { useRouter } from "next/navigation";

interface ResourceLinksCardProps {
  events_url?: string;
  id?: string;
  run_id?: string;
  serverArgosURL: string;
  runtimeArgosURL: string;
  isDownloading: boolean;
  onDownloadLog: () => void;
}

const renderResourceLink = (
  icon: React.ReactNode,
  text: string,
  onClick: () => void,
  hoverColorClass: string,
  showNewBadge = false,
) => (
  <div
    className={`flex items-center justify-between p-2 border border-gray-200 rounded-md hover:${hoverColorClass} cursor-pointer transition-colors`}
    onClick={onClick}
  >
    <div className="flex items-center">
      {icon}
      <span className="text-sm">{text}</span>
      {showNewBadge && (
        <span className="ml-2 px-1.5 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
          new
        </span>
      )}
    </div>
    <ChevronRight className="h-4 w-4 text-gray-400" />
  </div>
);

export const ResourceLinksCard = ({
  events_url,
  id,
  run_id,
  serverArgosURL,
  runtimeArgosURL,
  isDownloading,
  onDownloadLog,
}: ResourceLinksCardProps) => {
  const router = useRouter();

  return (
    <div className="rounded-lg overflow-hidden border border-gray-200 shadow-sm">
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <div className="relative pl-3 text-sm font-medium text-gray-700">
          <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-4 bg-purple-500 rounded-r"></div>
          资源链接
        </div>
      </div>
      <div className="bg-white p-4">
        <div className="flex flex-col space-y-2">
          {run_id &&
            renderResourceLink(
              <Sparkles className="h-4 w-4 text-indigo-500 mr-2" />,
              "查看事件分析",
              () => router.push(`/trace/events?session_id=${id}`),
              "border-indigo-300 hover:bg-indigo-50",
              true,
            )}

          {events_url &&
            renderResourceLink(
              <Activity className="h-4 w-4 text-blue-500 mr-2" />,
              "查看事件分析",
              () => window.open(events_url, "_blank"),
              "border-blue-300 hover:bg-blue-50",
            )}

          {id &&
            renderResourceLink(
              <Cloud className="h-4 w-4 text-purple-500 mr-2" />,
              "查看 Argos(server)",
              () => window.open(serverArgosURL, "_blank"),
              "border-purple-300 hover:bg-purple-50",
            )}

          {run_id &&
            renderResourceLink(
              <Cloud className="h-4 w-4 text-green-500 mr-2" />,
              "查看 Argos(runtime)",
              () => window.open(runtimeArgosURL, "_blank"),
              "border-green-300 hover:bg-green-50",
            )}

          {id &&
            renderResourceLink(
              <MessageSquare className="h-4 w-4 text-teal-500 mr-2" />,
              "查看 LLM 调用",
              () => router.push(`/trace/llm?session_id=${id}`),
              "border-teal-300 hover:bg-teal-50",
            )}

          {id &&
            renderResourceLink(
              <ChartGantt className="h-4 w-4 text-teal-500 mr-2" />,
              "查看 LLM-Graph 调用",
              () => router.push(`/trace/graph?session_id=${id}`),
              "border-teal-300 hover:bg-teal-50",
            )}

          {id &&
            renderResourceLink(
              <Stethoscope className="h-4 w-4 text-pink-500 mr-2" />,
              "问题诊断（提出问题生成诊断报告）",
              () => router.push(`/trace/diagnose?session_id=${id}`),
              "border-pink-300 hover:bg-pink-50",
            )}

          {id &&
            renderResourceLink(
              <Eye className="h-4 w-4 text-cyan-500 mr-2" />,
              "Trace 可视化分析",
              () => router.push(`/experience/visual?sessionId=${id}`),
              "border-cyan-300 hover:bg-cyan-50",
            )}

          {id && (
            <div
              className={`flex items-center justify-between p-2 border border-gray-200 rounded-md ${
                isDownloading
                  ? "opacity-60 cursor-not-allowed"
                  : "hover:border-orange-300 hover:bg-orange-50 cursor-pointer"
              } transition-colors`}
              onClick={() => !isDownloading && onDownloadLog()}
            >
              <div className="flex items-center">
                <Download
                  className={`h-4 w-4 mr-2 ${
                    isDownloading ? "text-gray-400" : "text-orange-500"
                  }`}
                />
                <span
                  className={`text-sm ${isDownloading ? "text-gray-400" : ""}`}
                >
                  {isDownloading
                    ? "下载中..."
                    : "下载完整日志（仅任务结束后可用）"}
                </span>
              </div>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
