import {
  Runtime<PERSON>rovider,
  RunDebugStatus,
} from "@/app/bam/aime/namespaces/trace";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Rocket,
  AlertCircle,
  CheckCircle2,
  RefreshCw,
  TerminalSquare,
  FileCode,
  Video,
  ChevronRight,
  Copy,
  StopCircle,
  Trash2,
} from "lucide-react";
import { formatIdForDisplay, getRunStatusColor } from "../utils/sessionUtils";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { useState } from "react";
import { <PERSON>confirm, Button as ArcoButton } from "@arco-design/web-react";
import { apiClient } from "@/app/api/request";
import { toast } from "sonner";

interface RuntimeInfoCardProps {
  run_provider?: RuntimeProvider;
  run_status?: string;
  run_debug_status?: RunDebugStatus;
  run_id?: string;
  webshell_url?: string;
  file_server_url?: string;
  videoUrl?: string | null;
  isStratoCube: boolean;
  onCopyRunId: (id: string) => void;
  showCopiedRunId: boolean;
  onActivateRuntime: () => void;
  isActivating: boolean;
  onStopRuntime?: () => Promise<void>;
}

const renderInfoItem = (
  label: string,
  value: React.ReactNode,
  icon?: React.ReactNode
) => (
  <div className="group py-3 border-b last:border-0 border-gray-100">
    <div className="flex items-center">
      <div className="w-24 text-right text-xs text-gray-500 font-medium flex-shrink-0 pr-3">
        {icon && (
          <span className="inline-block mr-1 text-gray-400">{icon}</span>
        )}
        {label}
      </div>
      <div className="flex-1 text-sm text-gray-800">{value}</div>
    </div>
  </div>
);

const renderStableHeightIdItem = (
  label: string,
  id: string | undefined,
  showCopied: boolean,
  onCopy: () => void
) => (
  <div className="group py-3 border-b last:border-0 border-gray-100">
    <div className="flex items-center">
      <div className="w-24 text-right text-xs text-gray-500 font-medium flex-shrink-0 pr-3">
        {label}
      </div>
      <div className="flex-1 text-sm text-gray-800">
        <div className="flex items-center h-6">
          {id ? (
            <>
              <span className="font-mono text-xs text-blue-500">
                {formatIdForDisplay(id)}
              </span>
              <div className="flex items-center ml-2">
                <button
                  className="text-gray-400 hover:text-gray-600 p-1 rounded-md relative"
                  onClick={() => id && onCopy()}
                >
                  {showCopied ? (
                    <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
                  ) : (
                    <Copy className="h-3.5 w-3.5" />
                  )}
                  {showCopied && (
                    <span className="absolute -top-8 -left-3 text-xs bg-black/90 text-white px-2 py-1 rounded shadow-sm whitespace-nowrap">
                      已复制
                    </span>
                  )}
                </button>
              </div>
            </>
          ) : (
            <span className="text-gray-400 text-xs">未知</span>
          )}
        </div>
      </div>
    </div>
  </div>
);

const renderResourceLink = (
  icon: React.ReactNode,
  text: string,
  onClick: () => void,
  hoverColorClass: string,
  disabled = false
) => (
  <div
    className={`flex items-center justify-between p-2 border border-gray-200 rounded-md ${
      disabled
        ? "opacity-60 cursor-not-allowed"
        : `hover:${hoverColorClass} cursor-pointer`
    } transition-colors`}
    onClick={() => !disabled && onClick()}
  >
    <div className="flex items-center">
      {icon}
      <span className="text-sm">{text}</span>
    </div>
    <ChevronRight className="h-4 w-4 text-gray-400" />
  </div>
);

export const RuntimeInfoCard = ({
  run_provider,
  run_status,
  run_debug_status,
  run_id,
  webshell_url,
  file_server_url,
  videoUrl,
  isStratoCube,
  onCopyRunId,
  showCopiedRunId,
  onActivateRuntime,
  isActivating,
  onStopRuntime,
}: RuntimeInfoCardProps) => {
  const [showStopConfirm, setShowStopConfirm] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  const isRunning = run_status && ["running", "ready"].includes(run_status);

  const handleStopClick = () => {
    setShowStopConfirm(true);
  };

  const handleStopConfirm = async () => {
    if (!onStopRuntime) return;
    setIsStopping(true);
    try {
      await onStopRuntime();
      setShowStopConfirm(false);
    } finally {
      setIsStopping(false);
    }
  };

  const handleDeleteContainer = async () => {
    if (!run_id) return;
    try {
      await apiClient.DeleteRuntime({
        run_id,
      });
      toast.success("容器删除成功");
      setTimeout(() => {
        window.location.reload();
      }, 800);
    } catch {
      toast.error("删除容器失败");
    }
  };

  return (
    <div className="rounded-lg overflow-hidden border border-gray-200 shadow-sm">
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <div className="relative pl-3 text-sm font-medium text-gray-700">
          <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-4 bg-green-500 rounded-r"></div>
          Runtime 信息
        </div>
      </div>
      <div className="bg-white px-4">
        <div>
          {renderInfoItem(
            "运行环境:",
            <span className="font-medium">
              {run_provider
                ? Object.keys(RuntimeProvider).find(
                    (key) =>
                      RuntimeProvider[key as keyof typeof RuntimeProvider] ===
                      run_provider
                  ) || String(run_provider)
                : "未知"}
            </span>
          )}

          {renderInfoItem(
            "运行状态:",
            <div className="flex items-center gap-2">
              <Badge
                variant={getRunStatusColor(run_status)}
                className="flex items-center gap-1 px-2 py-0.5 font-medium text-xs"
              >
                {run_status === "running" && <Rocket className="h-3 w-3" />}
                {run_status === "error" && <AlertCircle className="h-3 w-3" />}
                <span>{run_status || "未知"}</span>
              </Badge>
              {isRunning && onStopRuntime && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleStopClick}
                  className="h-6 px-2 text-xs text-gray-500 hover:text-red-500 hover:bg-red-50"
                  disabled={isStopping}
                >
                  {isStopping ? (
                    <span className="mr-1">
                      <RefreshCw className="h-3 w-3 animate-spin" />
                    </span>
                  ) : (
                    <StopCircle className="h-3 w-3 mr-1" />
                  )}
                  {isStopping ? "停止中..." : "停止"}
                </Button>
              )}
              <Popconfirm
                title="确定要删除该容器吗？"
                content="删除后容器将无法恢复"
                okText="确定"
                cancelText="取消"
                position="br"
                style={{ width: 260 }}
                onOk={handleDeleteContainer}
              >
                <ArcoButton
                  size="mini"
                  status="danger"
                  type="secondary"
                  className="flex items-center h-6 px-2  hover:text-red-500 hover:bg-red-50 text-red-500 bg-red-50 cursor-pointer"
                >
                  <Trash2 className="h-3 w-3" />
                  删除容器
                </ArcoButton>
              </Popconfirm>
            </div>
          )}

          {renderStableHeightIdItem(
            "Runtime ID:",
            run_id,
            showCopiedRunId,
            () => run_id && onCopyRunId(run_id)
          )}
        </div>
      </div>

      {isStratoCube && run_debug_status && (
        <div className="bg-gray-50 px-4 py-3 border-t border-gray-200">
          {run_debug_status === RunDebugStatus.Recoverable ? (
            <div className="p-3 bg-amber-50 rounded-md border border-amber-200">
              <div className="flex justify-between items-center">
                <p className="text-xs text-amber-800 font-medium">
                  容器已休眠，环境链接不可用
                </p>
                <Button
                  size="sm"
                  onClick={onActivateRuntime}
                  disabled={isActivating}
                  className="ml-2 bg-amber-500 hover:bg-amber-600 text-white text-xs h-7 px-2 whitespace-nowrap"
                >
                  <RefreshCw
                    className={`h-3 w-3 mr-1 ${
                      isActivating ? "animate-spin" : ""
                    }`}
                  />
                  {isActivating ? "激活中..." : "激活环境"}
                </Button>
              </div>
            </div>
          ) : run_debug_status === RunDebugStatus.UnRecoverable ? (
            <div className="p-3 bg-gray-100 rounded-md border border-gray-300 flex items-center">
              <AlertCircle className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
              <p className="text-xs font-medium text-gray-500">
                容器可能已删除，无法恢复
              </p>
            </div>
          ) : (
            <div
              className={`p-3 rounded-md flex items-center ${
                run_debug_status === RunDebugStatus.Running
                  ? "bg-green-50 border border-green-200"
                  : "bg-gray-100 border border-gray-300"
              }`}
            >
              {run_debug_status === RunDebugStatus.Running ? (
                <CheckCircle2 className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
              ) : (
                <AlertCircle className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
              )}
              <p
                className={`text-xs font-medium ${
                  run_debug_status === RunDebugStatus.Running
                    ? "text-green-700"
                    : "text-gray-500"
                }`}
              >
                {run_debug_status === RunDebugStatus.Running
                  ? "运行环境正在运行中"
                  : "运行环境无法恢复"}
              </p>
            </div>
          )}
        </div>
      )}

      {isStratoCube && (webshell_url || file_server_url || videoUrl) && (
        <div className="bg-white px-4 py-3 border-t border-gray-100">
          <div className="text-xs font-medium text-gray-500 mb-2">环境链接</div>
          <div className="space-y-2">
            {webshell_url &&
              renderResourceLink(
                <TerminalSquare
                  className={`h-4 w-4 mr-2 ${
                    isRunning ? "text-yellow-600" : "text-gray-400"
                  }`}
                />,
                "访问 WebShell",
                () => window.open(webshell_url, "_blank"),
                "border-yellow-300 hover:bg-yellow-50",
                !isRunning
              )}

            {file_server_url &&
              renderResourceLink(
                <FileCode
                  className={`h-4 w-4 mr-2 ${
                    isRunning ? "text-indigo-500" : "text-gray-400"
                  }`}
                />,
                "查看 Workspace 目录",
                () => window.open(file_server_url, "_blank"),
                "border-indigo-300 hover:bg-indigo-50",
                !isRunning
              )}

            {videoUrl &&
              renderResourceLink(
                <Video
                  className={`h-4 w-4 mr-2 ${
                    isRunning ? "text-red-500" : "text-gray-400"
                  }`}
                />,
                "视频链接",
                () => window.open(videoUrl, "_blank"),
                "border-red-300 hover:bg-red-50",
                !isRunning
              )}
          </div>
        </div>
      )}

      <Dialog open={showStopConfirm} onOpenChange={setShowStopConfirm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认停止容器</DialogTitle>
            <DialogDescription>
              此操作将强制停止当前容器。此操作仅建议在容器异常情况下使用，可能会导致数据丢失。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowStopConfirm(false)}
              disabled={isStopping}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleStopConfirm}
              disabled={isStopping}
            >
              {isStopping ? (
                <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
              ) : null}
              {isStopping ? "停止中..." : "确认停止"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
