import { SessionStatus } from "@/app/bam/aime/namespaces/session";
import { AgentMetadata } from "@/app/bam/aime/namespaces/trace";
import { Badge } from "@/components/ui/badge";
import {
  Rocket,
  AlertCircle,
  CheckCircle2,
  Copy,
  ExternalLink,
} from "lucide-react";
import {
  formatDateTime,
  formatIdForDisplay,
  getStatusColor,
} from "../utils/sessionUtils";
import { isBoe, isI18N } from "@/app/api/env";
import { useRouter } from "next/navigation";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import {
  ARGOS_BOE_URL,
  ARGOS_I18N_URL,
  ARGOS_ONLINE_URL,
} from "@/app/api/const";

// 格式化 log_id 显示，保留前6位和后6位
const formatLogId = (logId: string) => {
  if (logId.length <= 12) return logId;
  return `${logId.slice(0, 6)}...${logId.slice(-6)}`;
};

interface SessionInfoCardProps {
  id?: string;
  title?: string;
  creator?: string;
  status?: SessionStatus;
  created_at?: string;
  updated_at?: string;
  log_id?: string;
  agent_metadata?: AgentMetadata;
  onCopyId: (id: string) => void;
  onCopyLogId: (id: string) => void;
  showCopiedId: boolean;
  showCopiedLogId: boolean;
}

interface StableHeightIdItemProps {
  label: string;
  id?: string;
  showCopied: boolean;
  onCopy: () => void;
  onClickId?: () => void;
  formatId?: (id: string) => string;
  extraLinks?: React.ReactNode;
}

const getStatusIcon = (status?: SessionStatus) => {
  if (!status) return null;

  if (status === SessionStatus.SessionStatusRunning) {
    return <Rocket className="h-3 w-3" />;
  } else if (
    status === SessionStatus.SessionStatusError ||
    status === SessionStatus.SessionStatusStopped
  ) {
    return <AlertCircle className="h-3 w-3" />;
  } else if (status === SessionStatus.SessionStatusIdle) {
    return <CheckCircle2 className="h-3 w-3" />;
  }
  return null;
};

const renderInfoItem = (
  label: string,
  value: React.ReactNode,
  icon?: React.ReactNode,
) => (
  <div className="group py-3 border-b last:border-0 border-gray-100">
    <div className="flex items-center">
      <div className="w-24 text-right text-xs text-gray-500 font-medium flex-shrink-0 pr-3">
        {icon && (
          <span className="inline-block mr-1 text-gray-400">{icon}</span>
        )}
        {label}
      </div>
      <div className="flex-1 text-sm text-gray-800">{value}</div>
    </div>
  </div>
);

const StableHeightIdItem = ({
  label,
  id,
  showCopied,
  onCopy,
  onClickId,
  formatId = formatIdForDisplay,
  extraLinks,
}: StableHeightIdItemProps) => (
  <div className="group py-3 border-b last:border-0 border-gray-100">
    <div className="flex items-center">
      <div className="w-24 text-right text-xs text-gray-500 font-medium flex-shrink-0 pr-3">
        {label}
      </div>
      <div className="flex-1 text-sm text-gray-800">
        <div className="flex items-center min-h-[24px] gap-3">
          {id ? (
            <>
              <div className="flex items-center">
                <span
                  className="font-mono text-xs text-blue-500 hover:text-blue-600 cursor-pointer"
                  onClick={onClickId}
                >
                  {formatId(id)}
                </span>
                <button
                  className="text-gray-400 hover:text-gray-600 p-1 rounded-md relative ml-2"
                  onClick={onCopy}
                >
                  {showCopied ? (
                    <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
                  ) : (
                    <Copy className="h-3.5 w-3.5" />
                  )}
                  {showCopied && (
                    <span className="absolute -top-8 -left-3 text-xs bg-black/90 text-white px-2 py-1 rounded shadow-sm whitespace-nowrap">
                      已复制
                    </span>
                  )}
                </button>
              </div>
              {extraLinks}
            </>
          ) : (
            <span className="text-gray-400 text-xs">未知</span>
          )}
        </div>
      </div>
    </div>
  </div>
);

const renderAgentMetadata = (
  router: AppRouterInstance,
  metadata?: AgentMetadata,
) => {
  if (!metadata) return null;
  const {
    agent_name,
    agent_config_version,
    agent_config_id,
    agent_config_version_id,
  } = metadata;
  if (!agent_name && agent_config_version === undefined) return null;
  const handleClick = () => {
    const id = agent_config_version_id || agent_config_id;
    if (id && agent_config_id) {
      router.push(
        `/agents/version/detail?id=${id}&config_id=${agent_config_id}`,
      );
    }
  };
  return renderInfoItem(
    "Agent:",
    <div
      className="flex items-center gap-1.5 cursor-pointer hover:text-blue-500 transition-colors"
      onClick={handleClick}
    >
      {agent_name && (
        <span className="font-medium text-gray-900">{agent_name}</span>
      )}
      {agent_config_version !== undefined && (
        <span className="text-xs px-1.5 py-0.5 rounded bg-gray-100 text-gray-500 border border-gray-200">
          v{agent_config_version}
        </span>
      )}
    </div>,
  );
};

export const SessionInfoCard = ({
  id,
  title,
  creator,
  status,
  created_at,
  updated_at,
  log_id,
  agent_metadata,
  onCopyId,
  onCopyLogId,
  showCopiedId,
  showCopiedLogId,
}: SessionInfoCardProps) => {
  const router = useRouter();
  const handleLogIdClick = (logId: string) => {
    const argosBaseUrl = isBoe()
      ? ARGOS_BOE_URL
      : isI18N()
      ? ARGOS_I18N_URL
      : ARGOS_ONLINE_URL;
    window.open(
      `${argosBaseUrl}/trace/retrieve/logIdRetrieve?curTimeShift=600&drawerSelected=-1&drawerTab=1&logId=${logId}&log_search=true&psm=flow.agentsphere.nextserver&rpcDrawerSelected=-1&selectedSpan=-1&trace_search=true&transactionDrawerSelected=-1&type=__logid&x-bc-region-id=bytedance&x-resource-account=public`,
      "_blank",
    );
  };

  const handleBoeLogIdClick = (logId: string) => {
    window.open(
      `https://cloud-boe.bytedance.net/argos/trace/retrieve/logIdRetrieve?psm=flow.agentsphere.nextserver&region=boe&x-bc-region-id=bytedance&x-resource-account=boe&logId=${logId}&log_search=true`,
      "_blank",
    );
  };

  const handleSessionIdClick = (sessionId: string) => {
    window.open(`/chat/${sessionId}`, "_blank");
  };

  const renderLogIdLinks = (logId: string) => {
    if (!isBoe()) return null;

    return (
      <button
        onClick={() => handleBoeLogIdClick(logId)}
        className="text-blue-500 hover:text-blue-600 flex items-center gap-1 text-xs"
      >
        <span>nextserver(boe)</span>
        <ExternalLink className="h-3 w-3" />
      </button>
    );
  };

  return (
    <div className="rounded-lg overflow-hidden border border-gray-200 shadow-sm">
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <div className="relative pl-3 text-sm font-medium text-gray-700">
          <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-4 bg-blue-500 rounded-r"></div>
          会话信息
        </div>
      </div>
      <div className="bg-white px-4">
        <StableHeightIdItem
          label="会话 ID:"
          id={id}
          showCopied={showCopiedId}
          onCopy={() => id && onCopyId(id)}
          onClickId={() => id && handleSessionIdClick(id)}
        />

        <StableHeightIdItem
          label="Log ID:"
          id={log_id}
          showCopied={showCopiedLogId}
          onCopy={() => log_id && onCopyLogId(log_id)}
          onClickId={() => log_id && handleLogIdClick(log_id)}
          formatId={formatLogId}
          extraLinks={log_id ? renderLogIdLinks(log_id) : undefined}
        />

        {renderInfoItem(
          "标题:",
          <span className="font-medium">{title || "未命名会话"}</span>,
        )}

        {renderInfoItem("创建者:", <span>{creator || "未知"}</span>)}

        {renderInfoItem(
          "会话状态:",
          <Badge
            variant={getStatusColor(status)}
            className="flex items-center gap-1 px-2 py-0.5 font-medium text-xs"
          >
            {getStatusIcon(status)}
            <span>
              {status
                ? (() => {
                    const statusKey = Object.keys(SessionStatus).find(
                      key =>
                        SessionStatus[key as keyof typeof SessionStatus] ===
                        status,
                    );
                    if (!statusKey) return "未知状态";
                    return statusKey.replace(/^SessionStatus/, "");
                  })()
                : "未知状态"}
            </span>
          </Badge>,
        )}

        {renderInfoItem("创建时间:", <span>{formatDateTime(created_at)}</span>)}

        {renderInfoItem("更新时间:", <span>{formatDateTime(updated_at)}</span>)}

        {renderAgentMetadata(router, agent_metadata)}
      </div>
    </div>
  );
};
