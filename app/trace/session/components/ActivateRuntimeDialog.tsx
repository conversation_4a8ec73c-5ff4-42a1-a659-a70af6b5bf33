import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { RefreshCw } from "lucide-react";

interface ActivateRuntimeDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onActivate: () => void;
  isActivating: boolean;
}

export const ActivateRuntimeDialog = ({
  isOpen,
  onOpenChange,
  onActivate,
  isActivating,
}: ActivateRuntimeDialogProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md p-0 overflow-hidden">
        <div className="px-6 pt-5 pb-4">
          <DialogHeader className="pb-2">
            <DialogTitle className="flex items-center text-base font-semibold">
              <RefreshCw className="h-4 w-4 mr-2 text-amber-500" />
              激活容器
            </DialogTitle>
          </DialogHeader>

          <div className="mt-2">
            <p className="text-sm text-gray-600 leading-relaxed">
              确认激活此容器？容器将在激活后
              <span className="font-medium text-amber-600">30分钟</span>
              内可用。
            </p>
          </div>
        </div>

        <div className="bg-gray-50 px-6 py-4 flex justify-end gap-2">
          <DialogClose asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 text-sm font-normal"
            >
              取消
            </Button>
          </DialogClose>
          <Button
            onClick={onActivate}
            disabled={isActivating}
            size="sm"
            className="h-8 bg-amber-500 hover:bg-amber-600 text-white text-sm font-normal"
          >
            {isActivating ? (
              <>
                <RefreshCw className="h-3.5 w-3.5 mr-1.5 animate-spin" />
                激活中...
              </>
            ) : (
              "确认激活"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
