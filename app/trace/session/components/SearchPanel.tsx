import { KeyboardEvent } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X, Search, History, FileSearch } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { useStore } from "@nanostores/react";

// 导入状态管理相关内容
import {
  searchStore,
  recentSearchesStore,
  addToRecentSearches,
  setSearchType,
  setSearchId,
  SearchType,
} from "../store/searchStore";

// 扩展SearchParams接口以支持onSuccess回调
interface ExtendedSearchParams {
  type: SearchType;
  id: string;
  onSuccess?: () => void;
  updateUrl?: boolean;
}

interface SearchPanelProps {
  handleSearch: (params?: ExtendedSearchParams) => void;
}

const SearchPanel = ({ handleSearch }: SearchPanelProps) => {
  // 使用nanostores获取状态
  const { searchType, searchId, isLoading, error } = useStore(searchStore);
  const recentSearches = useStore(recentSearchesStore);

  // 更新类型时调用setSearchType存储函数
  const handleTypeChange = (value: string) => {
    setSearchType(value as SearchType);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch({
        type: searchType,
        id: searchId,
        onSuccess: () => addToRecentSearches(searchId, searchType),
      });
    }
  };

  const clearInput = () => {
    setSearchId("");
  };

  const handleSelectRecentSearch = (id: string) => {
    // 解析ID类型和搜索类型
    const isReplayId = id.startsWith("replay_");
    const isSessionId = id.startsWith("sess_");
    const newSearchType = isReplayId ? SearchType.REPLAY : SearchType.SESSION;

    // 去除前缀，统一展示格式
    let displayId = id;
    if (isReplayId) {
      displayId = id.replace(/^replay_/, "");
    } else if (isSessionId) {
      displayId = id.replace(/^sess_/, "");
    }

    // 设置ID和类型
    setSearchId(displayId);
    setSearchType(newSearchType);

    // 直接用正确的参数调用搜索，但不更新URL，防止页面刷新
    handleSearch({
      type: newSearchType,
      id: displayId, // 使用处理后的ID
      updateUrl: false,
    });
  };

  const handleSearchClick = () => {
    handleSearch({
      type: searchType,
      id: searchId,
      onSuccess: () => addToRecentSearches(searchId, searchType),
    });
  };

  return (
    <div className="flex flex-col gap-2 bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
      <div className="flex justify-between items-center mb-1">
        <div className="flex items-center gap-1">
          <FileSearch className="h-5 w-5 text-blue-500" />
          <h2 className="text-lg font-medium">会话查询</h2>
        </div>
        <div className="text-xs text-gray-500">
          {searchType === SearchType.SESSION
            ? "输入聊天页面 URL 中的 ID"
            : "输入分享页面 URL 中的 ID"}
        </div>
      </div>

      <Tabs
        defaultValue={SearchType.SESSION}
        value={searchType}
        onValueChange={handleTypeChange}
        className="w-full"
      >
        <TabsList className="grid w-56 grid-cols-2">
          <TabsTrigger value={SearchType.SESSION}>会话 ID</TabsTrigger>
          <TabsTrigger value={SearchType.REPLAY}>分享 ID</TabsTrigger>
        </TabsList>

        <div className="flex gap-2 items-center">
          <div className="flex-1">
            <TabsContent value={SearchType.SESSION} className="m-0 pt-0">
              <div className="relative">
                <Input
                  placeholder="请输入会话 ID"
                  value={searchId}
                  onChange={e => setSearchId(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="h-9 pr-8 pl-9 border-gray-300 focus:border-blue-500"
                />
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                {searchId && (
                  <button
                    className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-sm hover:bg-gray-100 transition-colors"
                    onClick={clearInput}
                    type="button"
                  >
                    <X className="h-4 w-4 text-gray-500" />
                  </button>
                )}
              </div>
            </TabsContent>

            <TabsContent value={SearchType.REPLAY} className="m-0 pt-0">
              <div className="relative">
                <Input
                  placeholder="请输入分享 ID"
                  value={searchId}
                  onChange={e => setSearchId(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="h-9 pr-8 pl-9 border-gray-300 focus:border-blue-500"
                />
                <History className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                {searchId && (
                  <button
                    className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-sm hover:bg-gray-100 transition-colors"
                    onClick={clearInput}
                    type="button"
                  >
                    <X className="h-4 w-4 text-gray-500" />
                  </button>
                )}
              </div>
            </TabsContent>
          </div>

          <Button
            onClick={handleSearchClick}
            disabled={isLoading || !searchId.trim()}
            className={cn(
              "px-3 h-9 transition-all duration-300 whitespace-nowrap",
              !searchId.trim()
                ? "opacity-50 cursor-not-allowed"
                : "hover:scale-105",
            )}
          >
            {isLoading ? (
              <div className="flex items-center gap-1">
                <span className="h-3.5 w-3.5 animate-spin rounded-full border-2 border-gray-300 border-t-white"></span>
                <span>搜索中</span>
              </div>
            ) : (
              <div className="flex items-center gap-1">
                <Search className="h-3.5 w-3.5" />
                <span>搜索</span>
              </div>
            )}
          </Button>
        </div>

        {error && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md text-xs text-red-600 flex items-start">
            <X className="h-3.5 w-3.5 mt-0.5 mr-1.5 flex-shrink-0" />
            <p>{error}</p>
          </div>
        )}

        {/* 只在有最近搜索记录时显示 */}
        {recentSearches.length > 0 && (
          <div className="mt-2 min-h-[28px] border-t pt-2">
            <div className="flex items-start gap-1 text-gray-500">
              <div className="flex items-center flex-nowrap">
                <History className="h-3.5 w-3.5 text-gray-400 flex-shrink-0" />
                <span className="text-xs ml-1 whitespace-nowrap">最近:</span>
              </div>

              <div className="flex flex-wrap gap-1 min-h-[24px]">
                {recentSearches
                  .filter(id => {
                    // 检查ID的前缀类型
                    const isReplayId = id.startsWith("replay_");
                    const isSessionId = id.startsWith("sess_");

                    // 对于没有前缀的旧ID，默认认为是会话ID
                    const isLegacySessionId = !isReplayId && !isSessionId;

                    // 根据当前搜索类型过滤
                    if (searchType === SearchType.SESSION) {
                      return isSessionId || isLegacySessionId; // 会话搜索时显示sess_开头或没有前缀的ID
                    } else {
                      return isReplayId; // 分享搜索时只显示replay_开头的ID
                    }
                  })
                  .slice(0, 3)
                  .map(id => {
                    // 展示时去除所有前缀
                    let displayId = id;
                    if (id.startsWith("replay_")) {
                      displayId = id.replace(/^replay_/, "");
                    } else if (id.startsWith("sess_")) {
                      displayId = id.replace(/^sess_/, "");
                    }

                    return (
                      <button
                        key={id}
                        onClick={() => handleSelectRecentSearch(id)}
                        className={cn(
                          "px-2 py-0.5 text-xs rounded-full transition-colors",
                          searchId === displayId
                            ? "bg-blue-100 text-blue-700 border border-blue-300"
                            : "bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200",
                        )}
                      >
                        {displayId}
                      </button>
                    );
                  })}
              </div>
            </div>
          </div>
        )}
      </Tabs>
    </div>
  );
};

export default SearchPanel;
