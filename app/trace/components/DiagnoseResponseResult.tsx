import { MessageSquareIcon, RefreshCwIcon } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import ReactMarkdown from "react-markdown";
import "@/app/playground/chat2/components/History/chat-markdown.css";

interface DiagnoseResponseResultProps {
  response?: string;
  isLoading?: boolean;
  markdown?: boolean;
}

export function DiagnoseResponseResult({ 
  response = "", 
  isLoading = false, 
  markdown = false 
}: DiagnoseResponseResultProps) {
  return (
    <div className="rounded-xl border border-border/60 shadow-sm overflow-hidden bg-card">
      <div className="flex items-center justify-between px-4 py-3 bg-muted/30 border-b">
        <div className="flex items-center">
          <MessageSquareIcon className="h-5 w-5 mr-2 text-primary" />
          <h2 className="font-semibold">响应结果</h2>
        </div>
        {isLoading && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <RefreshCwIcon className="h-4 w-4 animate-spin" />
            正在生成响应...
          </div>
        )}
      </div>
      <ScrollArea className={`h-[400px] px-[10px] py-[10px] text-sm ${!markdown ? 'whitespace-pre-wrap' : ''}`}>
        {isLoading ? (
          <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
            <RefreshCwIcon className="h-8 w-8 animate-spin mb-4" />
            <p className="text-sm">正在分析 Agent 轨迹并生成诊断报告...</p>
            <p className="text-xs mt-2">请稍候，这可能需要几秒钟时间</p>
          </div>
        ) : response ? (
          markdown ? (
            <div className="chat-markdown">
              <ReactMarkdown>{response}</ReactMarkdown>
            </div>
          ) : (
            response
          )
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
            <MessageSquareIcon className="h-12 w-12 mb-4 opacity-20" />
            <p className="text-sm">请发送请求以查看响应结果</p>
            <p className="text-xs text-muted-foreground mt-2">
              诊断报告将在此显示
            </p>
          </div>
        )}
      </ScrollArea>
    </div>
  );
} 