"use client";

import { useState } from "react";
import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { PageHeader } from "@/app/components/PageHeader";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { apiClient } from "@/app/api/request";
import { ChatProvider } from "@/app/playground/chat2/components/Provider";
import { ChatGroupItem } from "@/app/playground/chat2/components/Chat";
import { v4 as uuid } from "uuid";
import { ChatCompletionMessage } from "@/app/bam/aime/namespaces/trace";
import { DiagnoseResponseResult } from "@/app/trace/components/DiagnoseResponseResult";
import { processAgentTraceCompressed } from "@/app/experience/extract/compress";
import { HelpTooltip } from "@/app/components/ui/help-tooltip";
import { COMPRESSION_HELP_CONTENT } from "@/app/constants/compression-help";

const SYSTEM_PROMPTS = {
  diagnose: `You are a senior Applied AI engineer.

# Objective
Your Task: Generate a Diagnosis Report
Users may question the agent's results or workflow. You should respond by analyzing Agent's Trajectory and generate a report with the following structure using Markdown.

# Structure
1. 诊断摘要 (Diagnosis Summary):
简要描述 Agent 的原始目标，用户问题最终的失败原因以及你认为的关键失败点发生在第几轮(Round)
概述 Agent 用户视角可能的解决方案,比如说如何修改 Prompt

2. 根本原因分析 (Root Cause Analysis):
详细解释为什么你认为这是失败的根本原因
引用 <agents_trace> 中的具体 ProgressPlan, Think, Action, 或 Reference 等内容作为证据
清晰地说明失败是如何从一个步骤传导到下一个步骤的

3. 失败分类 (Failure Classification):
明确指出这属于哪一种主要失败类型。常见类型：
- Agent 通信层面
	- 上下文丢失
	- 信息隐瞒
- 基础模型固有缺陷
	- 大模型幻觉
	- 输出不稳定
	- 知识缺陷
- 规划与理解层面
	- 用户 Prompt 不清晰
	- 任务偏离
- 执行与工具使用层面
	- Tool代码/脚本逻辑错误
...

4. 开发者排查建议 (Next Steps for Developer):
提供 2-3 个具体、可操作的排查建议。
建议应直接与分析出的根本原因挂钩。例如：
If Planning Failure: "建议优化 Planner Agent 的 Prompt，使其在生成计划时考虑更全面的场景..."
If Tool Misuse: "建议检查 SearchAgent 的 Prompt，确保它能正确地从用户输入中提取并格式化 date 参数..."
If Misalignment: "建议在 BookingAgent 的 Prompt 中强化指令，强制它必须使用 SearchAgent 在上一步输出的 flight_id..."
If Observation Parsing Failure: "建议检查 SearchAgent 的输出解析逻辑。工具返回的JSON可能存在非预期的嵌套结构。同时考虑为工具API增加更严格的输出模式（Output Schema）"

# Examples
<CommonFailureExample>
1. 用户以为讲清楚了，但实际模型没理解。
  1.1. 典型的是指标计算的case，用户要计算定容率指标，给了个公式，模型根据获取到的Meego字段来推断实现。如果meego字段与给的公式字段不完全一致，就依赖模型自己推理，有时候就推理错了，导致计算不对。
  1.2. 没澄清就开始干活了，干着干着觉得不对。这时模型一般有两种处理逻辑，1. 开始瞎编内容 或 直接跳过这部分内容；2.让用户进行澄清，一旦涉及多轮对话，特别是已经完成了一部分工作后，如果澄清的内容和之前的工作有冲突，上下文可能会有干扰（也会存在上下文丢失的情况），效果无法保证。
2. 任务没完成，但Aime认为完成了，导致最终结果不对。
  2.1. 模型写代码出现问题，代码写错后但自己没发觉，导致后续输出的内容不完整。比如：在处理各章节内容汇总时，使用了 replace 替换的方式，但实际替换的关键字是不匹配的，导致替换失败。但代码执行未报错，模型以为替换成功了，导致最后生成的报告有部分章节的内容丢失
3. 过程之间有依赖，上一步结果质量、传递影响了后续步骤。
  3.1. 上一步处理了所有的数据，后面各环节基于处理好的数据完成工作。但实际上，上一步处理的时候，漏了一部分数据，导致后续处理不了。模型开始编造数据（幻觉），或直接表示数据不完整，任务无法继续，流程终止。
  3.2. 上一次处理了所有的数据，但到下一步时，模型不知道数据都处理好了（或者没找到处理好的数据），开始重新计算，导致出现了无效的步骤，同时也可能出现计算逻辑不一致的问题（因为这一步给到的任务已经是生成内容了，数据处理所需的上下文不一定全）
</CommonFailureExample>`,
  chat: `# Role
You are an experienced AI Agent product support specialist. Your primary role is to analyze the behavior of AI agents and explain their performance to a non-technical audience, such as product users and operations staff.

# Objective
Anwser the user's question based on the Agent's Trajectory.
The answer must be easy for non-technical team members to understand, focusing on "what happened" and "why," rather than technical details.
Finally and most importantly: Provide actionable solutions by refining the user's prompt.
If the problem cannot be solved and is clearly caused by the agent, inform the user to escalate it to the technical team for further handling.

# Personality
Default answer with Chinese.
`,
  summary: `你是一个诊断汇总专家，负责汇总多个诊断agent的分析结果。

## 任务要求

你将收到多个诊断agent的分析结果，需要将它们合并成一个统一的诊断报告。

### 汇总规则

1. **诊断摘要汇总**：
   - 合并所有分析结果中的诊断摘要
   - 识别共同的关键失败点和解决方案
   - 综合所有agent的视角形成更全面的诊断摘要

2. **根本原因分析汇总**：
   - 合并所有分析结果中的根本原因分析
   - 去重：相同或相似的根本原因只保留一个
   - 整合不同agent发现的证据，形成更完整的分析

3. **失败分类汇总**：
   - 合并所有分析结果中的失败分类
   - 识别主要的失败类型
   - 确保分类的完整性和准确性

4. **开发者排查建议汇总**：
   - 合并所有分析结果的排查建议
   - 去重并整理成连贯的建议内容
   - 按优先级排序建议

## 输出格式

请以Markdown格式输出合并后的诊断报告，保持原有的结构：

# 诊断摘要 (Diagnosis Summary)
[合并后的诊断摘要]

# 根本原因分析 (Root Cause Analysis)
[合并后的根本原因分析]

# 失败分类 (Failure Classification)
[合并后的失败分类]

# 开发者排查建议 (Next Steps for Developer)
[合并后的排查建议]

请确保输出内容连贯、完整，避免重复。`,
};


export default function DiagnosePage() {
  const [sessionId, setSessionId] = useState("");
  const [userQuestion, setUserQuestion] = useState(""); // 新增用户问题输入
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [chatType, setChatType] = useState<"diagnose" | "chat" | null>(null);
  const [group, setGroup] = useState<any | null>(null);
  const [autoSend, setAutoSend] = useState(false); // 新增 autoSend 状态
  const [responseLoading, setResponseLoading] = useState(false); // 新增响应加载状态
  const [isCompressionEnabled, setIsCompressionEnabled] = useState(false); // 上下文压缩开关
  const [isMultiAgentEnabled, setIsMultiAgentEnabled] = useState(false); // 多Agent诊断开关
  const [agentCount, setAgentCount] = useState(2); // Agent数量
  const [analysisProgress, setAnalysisProgress] = useState<{
    step: 'idle' | 'analyzing' | 'summarizing' | 'completed' | 'error';
    currentAgent: number;
    totalAgents: number;
    message: string;
    error?: string;
  }>({
    step: 'idle',
    currentAgent: 0,
    totalAgents: 0,
    message: ''
  });

  const searchParams = useSearchParams();
  useEffect(() => {
    const urlSessionId = searchParams.get("session_id");
    if (urlSessionId) {
      setSessionId(urlSessionId);
    }
  }, [searchParams]);

  // 分割trace数据的函数 - 按照data列表项数量分割为N个部分
  const splitTraceData = (traceData: string, count: number = agentCount): { success: boolean; data?: string[]; error?: string } => {
    try {
      if (isCompressionEnabled) {
        // 对于压缩的YAML格式，需要解析后按data列表项分割
        const parsed = JSON.parse(traceData);
        if (parsed && parsed.data && Array.isArray(parsed.data)) {
          const dataItems = parsed.data;
          if (dataItems.length === 0) {
            return {
              success: false,
              error: 'trace数据中的data数组为空，无法进行分割'
            };
          }
          
          if (dataItems.length < count) {
            return {
              success: false,
              error: `trace数据中的data数组长度(${dataItems.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          // 计算每个Agent处理的数据项数量
          const itemsPerAgent = Math.floor(dataItems.length / count);
          const remainder = dataItems.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            // 前remainder个Agent多分配一个数据项
            const currentItemsCount = itemsPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentItemsCount;
            
            const agentData = {
              ...parsed,
              data: dataItems.slice(startIndex, endIndex)
            };
            
            result.push(JSON.stringify(agentData, null, 2));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        } else {
          // 如果不是标准格式，按行分割作为备选
          const lines = traceData.split('\n');
          if (lines.length === 0) {
            return {
              success: false,
              error: 'trace数据为空，无法进行分割'
            };
          }
          
          if (lines.length < count) {
            return {
              success: false,
              error: `trace数据行数(${lines.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          const linesPerAgent = Math.floor(lines.length / count);
          const remainder = lines.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            const currentLinesCount = linesPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentLinesCount;
            result.push(lines.slice(startIndex, endIndex).join('\n'));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        }
      } else {
        // 对于JSON格式，按照data列表项分割
        const parsed = JSON.parse(traceData);
        if (parsed && parsed.data && Array.isArray(parsed.data)) {
          const dataItems = parsed.data;
          if (dataItems.length === 0) {
            return {
              success: false,
              error: 'trace数据中的data数组为空，无法进行分割'
            };
          }
          
          if (dataItems.length < count) {
            return {
              success: false,
              error: `trace数据中的data数组长度(${dataItems.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          // 计算每个Agent处理的数据项数量
          const itemsPerAgent = Math.floor(dataItems.length / count);
          const remainder = dataItems.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            const currentItemsCount = itemsPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentItemsCount;
            
            const agentData = {
              ...parsed,
              data: dataItems.slice(startIndex, endIndex)
            };
            
            result.push(JSON.stringify(agentData, null, 2));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        } else if (Array.isArray(parsed)) {
          // 如果直接是数组，按数组项分割
          if (parsed.length === 0) {
            return {
              success: false,
              error: 'trace数据为空数组，无法进行分割'
            };
          }
          
          if (parsed.length < count) {
            return {
              success: false,
              error: `trace数组长度(${parsed.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          const itemsPerAgent = Math.floor(parsed.length / count);
          const remainder = parsed.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            const currentItemsCount = itemsPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentItemsCount;
            result.push(JSON.stringify(parsed.slice(startIndex, endIndex), null, 2));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        } else {
          // 如果不是标准格式，按属性分割作为备选
          const keys = Object.keys(parsed);
          if (keys.length === 0) {
            return {
              success: false,
              error: 'trace数据为空对象，无法进行分割'
            };
          }
          
          if (keys.length < count) {
            return {
              success: false,
              error: `trace对象属性数量(${keys.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          const keysPerAgent = Math.floor(keys.length / count);
          const remainder = keys.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            const currentKeysCount = keysPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentKeysCount;
            
            const agentData = keys.slice(startIndex, endIndex).reduce((acc, key) => {
              acc[key] = parsed[key];
              return acc;
            }, {} as any);
            
            result.push(JSON.stringify(agentData, null, 2));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        }
      }
    } catch (e) {
      console.error('分割trace数据失败:', e);
      return {
        success: false,
        error: `分割trace数据失败: ${(e as Error).message}`
      };
    }
  };

  // 多agent诊断函数
  const runMultiAgentDiagnosis = async (traceData: string) => {
    const startTime = Date.now();
    setLoading(true);
    setError("");
    setChatType("diagnose");
    setResponseLoading(true);
    setAnalysisProgress({
      step: 'analyzing',
      currentAgent: 0,
      totalAgents: agentCount,
      message: `正在启动${agentCount}个诊断agent...`
    });

    console.log(`开始多Agent诊断，Agent数量: ${agentCount}，开始时间: ${new Date().toISOString()}`);

    try {
      // 分割trace数据
      const splitResult = splitTraceData(traceData, agentCount);
      if (!splitResult.success) {
        throw new Error(splitResult.error || 'trace数据分割失败');
      }
      const traceParts = splitResult.data!;

      // 并行调用N个诊断agent
      setAnalysisProgress(prev => ({
        ...prev,
        message: `正在并行启动${agentCount}个诊断agent...`
      }));

      // 创建所有诊断agent的并行调用
      const diagnosisAgentPromises = traceParts.map((tracePart, index) => {
        console.log(`启动诊断Agent ${index + 1}/${agentCount}`);
        
        const systemPrompt: ChatCompletionMessage = {
          role: "system",
          content: SYSTEM_PROMPTS.diagnose,
        };
        const assistantMsg: ChatCompletionMessage = {
          role: "assistant",
          content: "# Agent's Trajectory: \n" + tracePart,
        };
        const userMsg: ChatCompletionMessage = {
          role: "user",
          content: userQuestion || "",
        };

        return apiClient.ChatStream({
          model: "gemini-2.5-pro",
          messages: [systemPrompt, assistantMsg, userMsg],
          max_tokens: 16000,
          temperature: 0.1,
        }).then(res => {
          const content = (res as any)?.choices?.[0]?.message?.content ?? '';
          console.log(`诊断Agent ${index + 1} 完成`);
          
          // 更新进度：显示完成的agent数量
          setAnalysisProgress(prev => ({
            ...prev,
            currentAgent: prev.currentAgent + 1,
            message: `诊断Agent ${index + 1} 完成 (${prev.currentAgent + 1}/${agentCount})`
          }));
          
          return {
            index,
            agentId: index + 1,
            content,
            timestamp: new Date().toISOString()
          };
        }).catch(error => {
          console.error(`诊断Agent ${index + 1} 失败:`, error);
          throw new Error(`诊断Agent ${index + 1} 调用失败: ${error.message}`);
        });
      });

      // 等待所有诊断agent完成
      setAnalysisProgress(prev => ({
        ...prev,
        message: `等待${agentCount}个诊断agent完成...`
      }));

      const agentResults = await Promise.all(diagnosisAgentPromises);
      
      // 所有诊断agent完成后，显示汇总阶段
      setAnalysisProgress(prev => ({
        ...prev,
        message: `所有${agentCount}个诊断agent已完成，开始汇总...`
      }));

      // 所有诊断agent完成后，调用总结agent
      setAnalysisProgress({
        step: 'summarizing',
        currentAgent: agentCount,
        totalAgents: agentCount,
        message: '正在调用总结Agent汇总诊断结果...'
      });

      console.log(`开始汇总${agentCount}个诊断Agent的结果`);

      // 构建汇总查询
      const resultsText = agentResults.map(result => 
        `## 第${result.index + 1}个诊断结果：\n${result.content}`
      ).join('\n\n');

      const summaryQuery = `以下是${agentCount}个诊断agent的分析结果，请按照要求汇总：

${resultsText}

请将以上${agentCount}个诊断结果汇总成一个统一的诊断报告。`;

      const summaryResult = await apiClient.ChatStream({
        model: "gemini-2.5-pro",
        messages: [
          { role: 'system', content: SYSTEM_PROMPTS.summary },
          { role: 'user', content: summaryQuery },
        ],
        temperature: 0.1,
        max_tokens: 16000,
      }).then(res => {
        console.log('总结Agent完成');
        return res;
      }).catch(error => {
        console.error('总结Agent失败:', error);
        throw new Error(`总结Agent调用失败: ${error.message}`);
      });

      const summaryContent = (summaryResult as any)?.choices?.[0]?.message?.content ?? '';

      // 构建最终结果
      const finalResult = {
        groupName: "multi-diagnose",
        groupId: uuid(),
        data: {
          model: "gemini-2.5-pro",
          messages: [
            { role: "system", content: SYSTEM_PROMPTS.summary },
            { role: "user", content: summaryQuery },
            { role: "assistant", content: summaryContent }
          ],
          max_tokens: 16000,
          temperature: 0.1,
        },
        response: summaryContent,
        multiAgentDetails: agentResults.map(result => ({
          agentId: result.agentId,
          content: result.content,
          timestamp: result.timestamp
        }))
      };

      setGroup(finalResult);
      setAutoSend(true);

      const endTime = Date.now();
      const totalTime = ((endTime - startTime) / 1000).toFixed(2);

      console.log(`多Agent诊断完成，总耗时: ${totalTime}秒，Agent数量: ${agentCount}`);

      setAnalysisProgress({
        step: 'completed',
        currentAgent: agentCount,
        totalAgents: agentCount,
        message: '诊断完成！'
      });

    } catch (error) {
      console.error('多agent诊断失败:', error);
      toast.error(`多Agent诊断失败: ${(error as Error).message}`);
      setError((error as Error).message);
      setResponseLoading(false);
      
      setAnalysisProgress({
        step: 'error',
        currentAgent: 0,
        totalAgents: 0,
        message: '诊断失败',
        error: (error as Error).message
      });
    } finally {
      setLoading(false);
      setTimeout(() => {
        setAnalysisProgress({
          step: 'idle',
          currentAgent: 0,
          totalAgents: 0,
          message: ''
        });
      }, 2000);
    }
  };

  const handleSearch = async (type: "diagnose" | "chat") => {
    if (!sessionId.trim()) {
      toast.error("请输入 Session ID");
      return;
    }
    if (!userQuestion.trim()) {
      toast.error("请输入您的问题");
      return;
    }
    setLoading(true);
    setError("");
    setChatType(type);
    setResponseLoading(true); // 确保在开始搜索时设置加载状态
    try {
      const res = await apiClient.GetSessionTrajectory(
        { session_id: sessionId },
      );
      // 构造初始消息
      const systemPrompt: ChatCompletionMessage = {
        role: "system",
        content: SYSTEM_PROMPTS[type],
      };
      // 处理轨迹数据，根据压缩开关决定是否压缩
      let trajectoryData: string;
      if (isCompressionEnabled && res.data) {
        // 需要将 res.data 转换为 OriginalTrace 格式
        const originalTrace = {
          data: typeof res.data === "string" ? JSON.parse(res.data) : res.data
        };
        trajectoryData = processAgentTraceCompressed(originalTrace);
      } else {
        trajectoryData = res.data ? (typeof res.data === "string" ? res.data : JSON.stringify(res.data, null, 2)) : "";
      }

      // 如果是多agent诊断模式，直接调用多agent诊断函数
      if (isMultiAgentEnabled && type === "diagnose") {
        await runMultiAgentDiagnosis(trajectoryData);
        return;
      }

      const assistantMsg: ChatCompletionMessage = {
        role: "assistant",
        content: "# Agent's Trajectory: \n" + trajectoryData,
      };
      const userMsg: ChatCompletionMessage = {
        role: "user",
        content: userQuestion || "", // 使用用户输入的问题
      };
      setGroup((prev: any) => {
        const currentModel = prev?.data?.model || "gemini-2.5-pro";
        return {
          groupName: "diagnose",
          groupId: uuid(),
          data: {
            model: currentModel,
            messages: [systemPrompt, assistantMsg, userMsg],
            max_tokens: 16000,
            temperature: 0.1,
          },
        };
      });
      // loading 结束后自动发送
      if (userQuestion.trim()) {
        setAutoSend(true);
      }
    } catch (e: any) {
      toast.error(e?.message || "诊断接口请求失败");
      setError(e?.message || "诊断接口请求失败");
      setResponseLoading(false); // 出错时停止响应加载状态
    } finally {
      setLoading(false);
    }
  };

  // 监听 group 变化，当有响应时停止加载状态
  useEffect(() => {
    if (group?.response && responseLoading) {
      setResponseLoading(false);
    }
  }, [group?.response, responseLoading]);


  // 监听 group 的 loading 状态变化
  useEffect(() => {
    if (group?.loading !== undefined) {
      setResponseLoading(group.loading);
    }
  }, [group?.loading]);

  return (
    <div className="flex flex-col gap-6 max-w-3xl mx-auto py-8">
      <PageHeader title="诊断" description="通过 Session ID 进行诊断和对话" />
      <div className="flex flex-col gap-2 bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
        <Input
          placeholder="请输入 Session ID"
          value={sessionId}
          onChange={e => setSessionId(e.target.value)}
          className="flex-1"
          onKeyDown={e => {
            if (e.key === "Enter") handleSearch("diagnose");
          }}
          disabled={loading}
        />
        <div className="flex items-center gap-2">
          <Input
            placeholder="请输入您的问题"
            value={userQuestion}
            onChange={e => setUserQuestion(e.target.value)}
            className="flex-1"
            onKeyDown={e => {
              if (e.key === "Enter") {
                if (!userQuestion.trim()) {
                  toast.error("请输入您的问题进行诊断/对话");
                  return;
                }
                setGroup(null); // 清空上下文
                handleSearch("diagnose");
              }
            }}
            disabled={loading}
            required
          />
          <Button
            onClick={() => {
              setGroup(null); // 清空上下文
              handleSearch("diagnose");
            }}
            disabled={loading}
          >
            {loading && chatType === "diagnose" ? <span className="animate-spin mr-2">⏳</span> : null}
            {isMultiAgentEnabled ? `多Agent诊断 (${agentCount}个)` : "诊断"}
          </Button>
          <Button
            onClick={() => {
              if (!userQuestion.trim()) {
                toast.error("请输入您的问题");
                return;
              }
              if (chatType === "chat" && group) {
                // 直接新增一条用户消息并自动发送
                const newMessages = [
                  ...group.data.messages,
                  { role: "user", content: userQuestion }
                ];
                setGroup({
                  ...group,
                  data: {
                    ...group.data,
                    messages: newMessages,
                  },
                });
                setAutoSend(true);
              } else {
                setGroup(null); // 清空上下文
                handleSearch("chat");
              }
              setResponseLoading(true); // 开始响应加载状态
            }}
            disabled={loading}
          >
            {loading && chatType === "chat" ? <span className="animate-spin mr-2">⏳</span> : null}
            对话
          </Button>
          {/* loading 结束后，显示当前模式 Emoji */}
          {!loading && chatType === "diagnose" && <span title="诊断模式" className="ml-2">🩺</span>}
          {!loading && chatType === "chat" && <span title="对话模式" className="ml-2">💬</span>}
        </div>
        <div className="flex items-center gap-4 mt-2 flex-wrap">
          <div className="flex items-center gap-2">
            <Switch 
              checked={isCompressionEnabled} 
              onCheckedChange={setIsCompressionEnabled}
              id="compression-switch"
            />
            <label 
              htmlFor="compression-switch" 
              className="text-sm font-medium cursor-pointer select-none"
            >
              上下文压缩
            </label>
            <HelpTooltip content={COMPRESSION_HELP_CONTENT} />
          </div>
          
          <div className="flex items-center gap-2">
            <Switch 
              checked={isMultiAgentEnabled} 
              onCheckedChange={setIsMultiAgentEnabled}
              id="multi-agent-switch"
            />
            <label 
              htmlFor="multi-agent-switch" 
              className="text-sm font-medium cursor-pointer select-none"
            >
              多Agent诊断
            </label>
            <HelpTooltip content="启用多Agent诊断模式，通过多个诊断Agent并行分析trace的不同部分，然后由总结Agent汇总结果，提供更全面的诊断" />
          </div>
          
          {isMultiAgentEnabled && (
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium cursor-pointer select-none">Agent数量</label>
              <Input
                type="number"
                min="2"
                max="10"
                value={String(agentCount)}
                onChange={(e) => setAgentCount(Math.max(2, Math.min(10, Number(e.target.value) || 2)))}
                className="w-16 h-8"
              />
              <HelpTooltip content="设置并行诊断Agent的数量，范围2-10个。数量越多，分析越细致，但耗时更长" />
            </div>
          )}
        </div>
      </div>
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* 多Agent诊断进度显示 */}
      {isMultiAgentEnabled && analysisProgress.step !== 'idle' && (
        <div className={`p-4 rounded-lg border ${
          analysisProgress.step === 'error' 
            ? 'bg-red-50 border-red-200' 
            : 'bg-blue-50 border-blue-200'
        }`}>
          <div className="flex items-center justify-between mb-2">
            <span className={`font-medium ${
              analysisProgress.step === 'error' ? 'text-red-800' : 'text-blue-800'
            }`}>
              {analysisProgress.step === 'analyzing' && '诊断阶段'}
              {analysisProgress.step === 'summarizing' && '汇总阶段'}
              {analysisProgress.step === 'completed' && '诊断完成'}
              {analysisProgress.step === 'error' && '诊断失败'}
            </span>
            {analysisProgress.step !== 'error' && (
              <span className="text-sm text-blue-600">
                {analysisProgress.currentAgent}/{analysisProgress.totalAgents}
              </span>
            )}
          </div>
          {analysisProgress.step !== 'error' && (
            <div className="w-full bg-blue-200 rounded-full h-2 mb-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ 
                  width: `${(analysisProgress.currentAgent / analysisProgress.totalAgents) * 100}%` 
                }}
              ></div>
            </div>
          )}
          {analysisProgress.step === 'analyzing' && (
            <div className="text-xs text-blue-600 mt-1">
              💡 所有诊断Agent正在并行处理中...
            </div>
          )}
          <p className={`text-sm ${
            analysisProgress.step === 'error' ? 'text-red-700' : 'text-blue-700'
          }`}>
            {analysisProgress.message}
          </p>
          {analysisProgress.step === 'error' && analysisProgress.error && (
            <div className="mt-2 p-2 bg-red-100 rounded border border-red-300">
              <p className="text-xs text-red-800 font-medium">错误详情：</p>
              <p className="text-xs text-red-700 mt-1">{analysisProgress.error}</p>
            </div>
          )}
        </div>
      )}
      
      {group && (
        <DiagnoseResponseResult 
          response={group?.response} 
          isLoading={responseLoading}
          markdown={true}
        />
      )}
      
      {/* 配置模块 */}
      {group && (
        <ChatProvider>
          <ChatGroupItem
            info={group}
            onChange={setGroup}
            onDel={() => {
              setGroup(null);
              setResponseLoading(false); // 重置加载状态
            }}
            index={0}
            autoInsertAssistantMessage={chatType === 'chat'}
            autoSend={autoSend}
            onAutoSendComplete={() => setAutoSend(false)}
            hideResponse={true}
          />
        </ChatProvider>
      )}
      {/* 多Agent模式下的详细结果显示 */}
      {isMultiAgentEnabled && group?.multiAgentDetails && (
        <div className="mt-6">
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <p className="text-sm text-green-700 mb-2">
              ✓ 已通过{agentCount}个诊断Agent和总结Agent完成全面诊断
            </p>
            <p className="text-xs text-green-600">
              使用{agentCount}个诊断Agent并行处理trace的不同部分，总结Agent汇总所有结果，提供更全面的诊断分析
            </p>
          </div>
        </div>
      )}
      {/* 这里可以放后续的配置模块 */}
    </div>
  );
} 