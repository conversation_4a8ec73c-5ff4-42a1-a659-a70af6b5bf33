/**
 * 上下文压缩功能说明常量
 * 统一管理所有页面中 HelpTooltip 的 content 值
 */

// 统一的压缩功能说明
const COMPRESSION_HELP_TEXT = `上下文压缩功能说明：

目的：
• 压缩轨迹数据大小，避免超过模型上下文窗口

压缩原理：
1. 去重优化：将重复的 ExecutionTrace 内容转换为增量引用
2. 知识库提取：将完整的知识条目提取到顶层，用ID引用
3. 空值清理：自动去除 null、""、[]、{} 等空值字段
4. 格式转换：将 JSON 转换为更紧凑的 YAML 格式

压缩效果：
• 通常可减少 50-80% 的数据大小
• 保持数据完整性和可读性
• 特别适合大型轨迹数据的处理`;

// 统一的压缩功能说明常量
export const COMPRESSION_HELP_CONTENT = COMPRESSION_HELP_TEXT;

// 类型定义
export type CompressionHelpType = keyof typeof COMPRESSION_HELP_CONTENT;