// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as activity from "./namespaces/activity";
import * as agent from "./namespaces/agent";
import * as artifact from "./namespaces/artifact";
import * as common from "./namespaces/common";
import * as debug from "./namespaces/debug";
import * as deploy from "./namespaces/deploy";
import * as deployment from "./namespaces/deployment";
import * as dev_resource from "./namespaces/dev_resource";
import * as event from "./namespaces/event";
import * as feature from "./namespaces/feature";
import * as knowledgebase from "./namespaces/knowledgebase";
import * as knowledgeset from "./namespaces/knowledgeset";
import * as lark from "./namespaces/lark";
import * as mcp from "./namespaces/mcp";
import * as mention from "./namespaces/mention";
import * as notification_message from "./namespaces/notification_message";
import * as ops from "./namespaces/ops";
import * as permission from "./namespaces/permission";
import * as prompt from "./namespaces/prompt";
import * as replay from "./namespaces/replay";
import * as session from "./namespaces/session";
import * as session_collection from "./namespaces/session_collection";
import * as share from "./namespaces/share";
import * as showcase from "./namespaces/showcase";
import * as space from "./namespaces/space";
import * as template from "./namespaces/template";
import * as trace from "./namespaces/trace";
import * as types from "./namespaces/types";
import * as user from "./namespaces/user";

export {
  activity,
  agent,
  artifact,
  common,
  debug,
  deploy,
  deployment,
  dev_resource,
  event,
  feature,
  knowledgebase,
  knowledgeset,
  lark,
  mcp,
  mention,
  notification_message,
  ops,
  permission,
  prompt,
  replay,
  session,
  session_collection,
  share,
  showcase,
  space,
  template,
  trace,
  types,
  user,
};

export type Int64 = string | number;

export default class AimeService<T> {
  private request: any = () => {
    throw new Error("AimeService.request is undefined");
  };
  private baseURL: string | ((path: string) => string) = "";

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: "GET" | "DELETE" | "POST" | "PUT" | "PATCH";
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || "";
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === "string"
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * GET /api/agents/v2/showcases
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2895334)
   */
  ListShowcase(
    req?: replay.ListShowcaseRequest,
    options?: T
  ): Promise<replay.ListShowcaseResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/showcases");
    const method = "GET";
    const params = { locale: _req["locale"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/replay/:replay_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2896354)
   */
  GetReplay(
    req: replay.GetReplayRequest,
    options?: T
  ): Promise<replay.GetReplayResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/agents/v2/replay/${_req["replay_id"]}`);
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/sessions
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2896355)
   *
   * session 会话相关
   */
  CreateSession(
    req?: session.CreateSessionRequest,
    options?: T
  ): Promise<session.CreateSessionResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/sessions");
    const method = "POST";
    const data = {
      role: _req["role"],
      use_internal_tool: _req["use_internal_tool"],
      space_id: _req["space_id"],
      excluded_mcps: _req["excluded_mcps"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/sessions
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2896356)
   */
  ListSessions(
    req: session.ListSessionsRequest,
    options?: T
  ): Promise<session.ListSessionsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/sessions");
    const method = "GET";
    const params = {
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      space_id: _req["space_id"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/sessions/:session_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2896357)
   */
  GetSession(
    req: session.GetSessionRequest,
    options?: T
  ): Promise<session.GetSessionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/sessions/${_req["session_id"]}`
    );
    const method = "GET";
    const params = { space_id: _req["space_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /api/agents/v2/sessions/:session_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2896358)
   */
  DeleteSession(
    req: session.DeleteSessionRequest,
    options?: T
  ): Promise<session.DeleteSessionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/sessions/${_req["session_id"]}`
    );
    const method = "DELETE";
    const params = { space_id: _req["space_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/sessions/:session_id/events
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2896509)
   */
  GetSessionStreamEvents(
    req: session.GetSessionStreamEventsRequest,
    options?: T
  ): Promise<common.SSEResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/sessions/${_req["session_id"]}/events`
    );
    const method = "POST";
    const params = {
      event_offset: _req["event_offset"],
      space_id: _req["space_id"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/sessions/:session_id/message
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2897894)
   */
  CreateMessage(
    req: session.CreateMessageRequest,
    options?: T
  ): Promise<session.CreateMessageResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/sessions/${_req["session_id"]}/message`
    );
    const method = "POST";
    const data = {
      content: _req["content"],
      attachments: _req["attachments"],
      tool_calls: _req["tool_calls"],
      event_offset: _req["event_offset"],
      options: _req["options"],
      mentions: _req["mentions"],
      space_id: _req["space_id"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/agents/v2/artifacts/:artifact_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2898797)
   */
  UpdateArtifact(
    req: artifact.UpdateArtifactRequest,
    options?: T
  ): Promise<artifact.UpdateArtifactResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/artifacts/${_req["artifact_id"]}`
    );
    const method = "PUT";
    const data = {
      status: _req["status"],
      metadata: _req["metadata"],
      session_id: _req["session_id"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/artifacts/:artifact_id/files
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2898799)
   */
  RetrieveArtifactFiles(
    req: artifact.RetrieveArtifactFilesRequest,
    options?: T
  ): Promise<artifact.RetrieveArtifactFilesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/artifacts/${_req["artifact_id"]}/files`
    );
    const method = "POST";
    const data = { files: _req["files"], preview: _req["preview"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/artifacts/:artifact_id/upload/stream
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2898801)
   */
  UploadArtifactStream(
    req: artifact.UploadArtifactStreamRequest,
    options?: T
  ): Promise<artifact.UploadArtifactResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/artifacts/${_req["artifact_id"]}/upload/stream`
    );
    const method = "POST";
    const params = { path: _req["path"], size: _req["size"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/artifacts/:artifact_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2898802)
   */
  GetArtifact(
    req: artifact.GetArtifactRequest,
    options?: T
  ): Promise<artifact.GetArtifactResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/artifacts/${_req["artifact_id"]}`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/artifacts/:artifact_id/upload
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2898803)
   */
  UploadArtifact(
    req: artifact.UploadArtifactRequest,
    options?: T
  ): Promise<artifact.UploadArtifactResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/artifacts/${_req["artifact_id"]}/upload`
    );
    const method = "POST";
    const data = { path: _req["path"], content: _req["content"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/artifacts
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2898804)
   *
   * artifact 相关
   */
  CreateArtifact(
    req: artifact.CreateArtifactRequest,
    options?: T
  ): Promise<artifact.CreateArtifactResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/artifacts");
    const method = "POST";
    const data = {
      session_id: _req["session_id"],
      type: _req["type"],
      key: _req["key"],
      metadata: _req["metadata"],
      version: _req["version"],
      file_metas: _req["file_metas"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/artifacts/list
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2898805)
   */
  ListArtifacts(
    req?: artifact.ListArtifactsRequest,
    options?: T
  ): Promise<artifact.ListArtifactsResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/artifacts/list");
    const method = "GET";
    const params = {
      session_id: _req["session_id"],
      replay_id: _req["replay_id"],
      display: _req["display"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/debug
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2899793)
   *
   * only for debug.
   */
  UnUsedStruct(
    req?: event.EventStruct,
    options?: T
  ): Promise<event.EventStruct> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/debug");
    const method = "GET";
    const params = {
      message_create_event: _req["message_create_event"],
      plan_update_event: _req["plan_update_event"],
      step_update_event: _req["step_update_event"],
      use_tool_event: _req["use_tool_event"],
      progress_notice_event: _req["progress_notice_event"],
      tool_call_required_event: _req["tool_call_required_event"],
      session_completed: _req["session_completed"],
      done_event: _req["done_event"],
      error_event: _req["error_event"],
      error_code: _req["error_code"],
      reference_event: _req["reference_event"],
      tool_call_confirmed_event: _req["tool_call_confirmed_event"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/sessions/:session_id/old_events
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2902005)
   */
  GetOldSessionEvents(
    req: session.GetOldSessionEventsRequest,
    options?: T
  ): Promise<session.GetOldSessionEventsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/sessions/${_req["session_id"]}/old_events`
    );
    const method = "GET";
    const params = { space_id: _req["space_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/lark/auth/check
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2902430)
   */
  CheckLarkAuth(
    req: lark.CheckLarkAuthRequest,
    options?: T
  ): Promise<lark.CheckLarkAuthResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/lark/auth/check");
    const method = "GET";
    const params = { url: _req["url"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/lark/auth
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2902431)
   *
   * lark 相关
   */
  LarkAuth(
    req: lark.LarkAuthRequest,
    options?: T
  ): Promise<lark.LarkAuthResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/lark/auth");
    const method = "GET";
    const params = {
      code: _req["code"],
      state: _req["state"],
      error: _req["error"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/replay
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2908069)
   */
  CreateReplay(
    req: replay.CreateReplayRequest,
    options?: T
  ): Promise<replay.CreateReplayResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/replay");
    const method = "POST";
    const data = {
      session_id: _req["session_id"],
      event_offset_start: _req["event_offset_start"],
      event_offset_end: _req["event_offset_end"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/agents/v2/artifacts/:artifact_id/files/:path
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2908149)
   */
  UpdateArtifactFile(
    req: artifact.UpdateArtifactFileRequest,
    options?: T
  ): Promise<artifact.UpdateArtifactFileResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/artifacts/${_req["artifact_id"]}/files/${_req["path"]}`
    );
    const method = "PUT";
    const data = {
      upload_lark: _req["upload_lark"],
      grant_permission: _req["grant_permission"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/artifacts/:artifact_id/raw/:path
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2908695)
   */
  DownloadArtifact(
    req: artifact.DownloadArtifactFileRequest,
    options?: T
  ): Promise<Blob> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/artifacts/${_req["artifact_id"]}/raw/${_req["path"]}`
    );
    const method = "GET";
    const params = { raw: _req["raw"], stream: _req["stream"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/share/replay/call_back
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2908852)
   *
   * share
   */
  ShareReplayPreviewCallback(
    req?: share.SharePreviewCallbackRequest,
    options?: T
  ): Promise<share.SharePreviewCallbackResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/share/replay/call_back");
    const method = "POST";
    const data = {
      challenge: _req["challenge"],
      type: _req["type"],
      event: _req["event"],
      header: _req["header"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/features
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2909436)
   *
   * feature
   */
  GetUserFeatures(
    req?: feature.GetUserFeaturesRequest,
    options?: T
  ): Promise<feature.GetUserFeaturesResponse> {
    const url = this.genBaseURL("/api/agents/v2/features");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/deployments
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2911010)
   *
   * deployment
   */
  GetMainDeploymentArtifact(
    req?: deployment.GetDeploymentArtifactRequest,
    options?: T
  ): Promise<deployment.GetDeploymentArtifactResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/deployments");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/deployments/*path
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2911011)
   */
  GetMDeploymentArtifact(
    req?: deployment.GetDeploymentArtifactRequest,
    options?: T
  ): Promise<deployment.GetDeploymentArtifactResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/deployments/*path");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/deployments
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2911012)
   */
  CreateDeployment(
    req: deployment.CreateDeploymentRequest,
    options?: T
  ): Promise<deployment.CreateDeploymentResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/deployments");
    const method = "POST";
    const data = {
      session_id: _req["session_id"],
      artifact_id: _req["artifact_id"],
      category: _req["category"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/sessions/collection
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2911013)
   *
   * 会话任务采集相关
   */
  SessionsCollection(
    req: session_collection.SessionsCollectionRequest,
    options?: T
  ): Promise<session_collection.SessionsCollectionResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/sessions/collection");
    const method = "POST";
    const data = {
      content: _req["content"],
      attachments: _req["attachments"],
      agree_home_display: _req["agree_home_display"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/sessions/collection/list
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2921790)
   */
  ListSessionCollections(
    req: session_collection.ListSessionCollectionsRequest,
    options?: T
  ): Promise<session_collection.ListSessionCollectionsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/sessions/collection/list");
    const method = "GET";
    const params = {
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      status: _req["status"],
      creator: _req["creator"],
      content: _req["content"],
      order_by_created_at: _req["order_by_created_at"],
      order_by_updated_at: _req["order_by_updated_at"],
      order: _req["order"],
      session_collection_ids: _req["session_collection_ids"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/sessions/collection/run
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2921791)
   */
  RunSessionCollections(
    req: session_collection.RunSessionCollectionsRequest,
    options?: T
  ): Promise<session_collection.RunSessionCollectionsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/sessions/collection/run");
    const method = "POST";
    const data = {
      session_collection_ids: _req["session_collection_ids"],
      role: _req["role"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/lark/send/replay_link
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2922736)
   */
  SendLarkReplayLinkMessage(
    req: lark.SendLarkReplayLinkMessageRequest,
    options?: T
  ): Promise<lark.SendLarkReplayLinkMessageResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/lark/send/replay_link");
    const method = "POST";
    const data = {
      username: _req["username"],
      replay_link: _req["replay_link"],
      task_name: _req["task_name"],
      to_type: _req["to_type"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/sessions/collection/run/notification
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2923325)
   */
  SendSessionCollectionRunNotification(
    req?: session_collection.SendSessionCollectionRunNotificationRequest,
    options?: T
  ): Promise<session_collection.SendSessionCollectionRunNotificationResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      "/api/agents/v2/sessions/collection/run/notification"
    );
    const method = "POST";
    const data = { notifications: _req["notifications"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/roles
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2926173)
   */
  GetUserRoles(
    req?: session.GetUserRolesRequest,
    options?: T
  ): Promise<session.GetUserRolesResponse> {
    const url = this.genBaseURL("/api/agents/v2/roles");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/sessions/check
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2935799)
   */
  CheckCreateSession(
    req?: session.CheckCreateSessionRequest,
    options?: T
  ): Promise<session.CheckCreateSessionResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/sessions/check");
    const method = "GET";
    const params = { roles: _req["roles"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PATCH /api/agents/v2/sessions/:session_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2938517)
   */
  UpdateSession(
    req: session.UpdateSessionRequest,
    options?: T
  ): Promise<session.UpdateSessionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/sessions/${_req["session_id"]}`
    );
    const method = "PATCH";
    const data = {
      title: _req["title"],
      status: _req["status"],
      scope: _req["scope"],
      space_id: _req["space_id"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/trace/session
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2950965)
   *
   * trace
   */
  GetTraceSession(
    req?: trace.GetTraceSessionRequest,
    options?: T
  ): Promise<trace.GetTraceSessionResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/trace/session");
    const method = "GET";
    const params = {
      session_id: _req["session_id"],
      replay_id: _req["replay_id"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/save_event_key
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2955830)
   */
  SaveEventKey(
    req: event.SaveEventKeyRequest,
    options?: T
  ): Promise<event.SaveEventKeyResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/save_event_key");
    const method = "POST";
    const data = {
      offset: _req["offset"],
      limit: _req["limit"],
      event_ids: _req["event_ids"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/trace/events
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2956120)
   */
  GetTraceEvents(
    req?: trace.GetTraceEventsRequest,
    options?: T
  ): Promise<common.SSEResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/trace/events");
    const method = "POST";
    const params = {
      run_id: _req["run_id"],
      session_id: _req["session_id"],
      container_id: _req["container_id"],
      uri: _req["uri"],
      provider: _req["provider"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/user/:user_name
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2956359)
   *
   * user
   */
  GetUserInfo(
    req: user.GetUserInfoRequest,
    options?: T
  ): Promise<user.GetUserInfoResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/agents/v2/user/${_req["user_name"]}`);
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/trace/actions/resume
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2956722)
   */
  ResumeRuntime(
    req: trace.ResumeRuntimeRequest,
    options?: T
  ): Promise<trace.ResumeRuntimeResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/trace/actions/resume");
    const method = "POST";
    const params = { run_id: _req["run_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/trace/session/chat
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2972509)
   */
  GetTraceSessionChat(
    req: trace.GetTraceSessionChatRequest,
    options?: T
  ): Promise<trace.GetTraceSessionChatResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/trace/session/chat");
    const method = "GET";
    const params = {
      session_id: _req["session_id"],
      status: _req["status"],
      type: _req["type"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      trace_id: _req["trace_id"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/message_with_template
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2975816)
   *
   * 快捷任务&模板相关
   */
  CreateMessageWithTemplate(
    req: session.CreateMessageWithTemplateRequest,
    options?: T
  ): Promise<session.CreateMessageWithTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/message_with_template");
    const method = "POST";
    const data = {
      role: _req["role"],
      use_internal_tool: _req["use_internal_tool"],
      template_id: _req["template_id"],
      content: _req["content"],
      form_value: _req["form_value"],
      options: _req["options"],
      mcps: _req["mcps"],
      space_id: _req["space_id"],
      excluded_mcps: _req["excluded_mcps"],
      from_app: _req["from_app"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/templates/list
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2975817)
   */
  ListTemplates(
    req?: session.ListTemplatesRequest,
    options?: T
  ): Promise<session.ListTemplatesResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/templates/list");
    const method = "GET";
    const params = {
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      category: _req["category"],
      search: _req["search"],
      source: _req["source"],
      label: _req["label"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/trace/session/log
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2977139)
   */
  DownloadSessionLog(
    req: trace.DownloadSessionLogRequest,
    options?: T
  ): Promise<Blob> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/trace/session/log");
    const method = "GET";
    const params = { session_id: _req["session_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/trace/session/agent_steps
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2977388)
   */
  ListSessionAgentStep(
    req: trace.ListSessionAgentStepRequest,
    options?: T
  ): Promise<trace.ListSessionAgentStepResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/trace/session/agent_steps");
    const method = "GET";
    const params = { session_id: _req["session_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/templates/:template_id/history_variables
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2977397)
   */
  GetHistoryTemplateVariables(
    req: session.GetHistoryTemplateVariablesRequest,
    options?: T
  ): Promise<session.GetHistoryTemplateVariablesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/${_req["template_id"]}/history_variables`
    );
    const method = "GET";
    const params = { space_id: _req["space_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/agent
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982661)
   *
   * Agent Manage
   */
  CreateAgent(
    req: agent.CreateAgentRequest,
    options?: T
  ): Promise<agent.CreateAgentResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/agent");
    const method = "POST";
    const data = { name: _req["name"], description: _req["description"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/agent/config/version/:agent_config_version_id/deploy
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982662)
   */
  DeployAgentConfigVersion(
    req: agent.DeployAgentConfigVersionRequest,
    options?: T
  ): Promise<agent.DeployAgentConfigVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/agent/config/version/${_req["agent_config_version_id"]}/deploy`
    );
    const method = "POST";
    const data = { status: _req["status"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/agents/v2/prompt/:prompt_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982663)
   */
  DeletePrompt(
    req: prompt.DeletePromptRequest,
    options?: T
  ): Promise<prompt.DeletePromptResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/agents/v2/prompt/${_req["prompt_id"]}`);
    const method = "DELETE";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/agent/config/version
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982664)
   */
  CreateAgentConfigVersion(
    req: agent.CreateAgentConfigVersionRequest,
    options?: T
  ): Promise<agent.CreateAgentConfigVersionResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/agent/config/version");
    const method = "POST";
    const data = {
      agent_config_id: _req["agent_config_id"],
      description: _req["description"],
      enabled: _req["enabled"],
      runtime_config: _req["runtime_config"],
      custom_config: _req["custom_config"],
      prompt_config: _req["prompt_config"],
      knowledge_set_config: _req["knowledge_set_config"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/prompt/version
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982665)
   */
  CreatePromptVersion(
    req: prompt.CreatePromptVersionRequest,
    options?: T
  ): Promise<prompt.CreatePromptVersionResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/prompt/version");
    const method = "POST";
    const data = {
      prompt_id: _req["prompt_id"],
      description: _req["description"],
      enabled: _req["enabled"],
      content: _req["content"],
      variable: _req["variable"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/agents/v2/agent/:agent_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982666)
   */
  DeleteAgent(
    req: agent.DeleteAgentRequest,
    options?: T
  ): Promise<agent.DeleteAgentResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/agents/v2/agent/${_req["agent_id"]}`);
    const method = "DELETE";
    return this.request({ url, method }, options);
  }

  /**
   * PUT /api/agents/v2/prompt/version/:prompt_version_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982667)
   */
  UpdatePromptVersion(
    req: prompt.UpdatePromptVersionRequest,
    options?: T
  ): Promise<prompt.UpdatePromptVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/prompt/version/${_req["prompt_version_id"]}`
    );
    const method = "PUT";
    const data = {
      enabled: _req["enabled"],
      description: _req["description"],
      content: _req["content"],
      variable: _req["variable"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/agent
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982669)
   */
  ListAgents(
    req: agent.ListAgentsRequest,
    options?: T
  ): Promise<agent.ListAgentsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/agent");
    const method = "GET";
    const params = {
      creator: _req["creator"],
      name: _req["name"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/agents/v2/prompt/:prompt_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982670)
   */
  UpdatePrompt(
    req: prompt.UpdatePromptRequest,
    options?: T
  ): Promise<prompt.UpdatePromptResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/agents/v2/prompt/${_req["prompt_id"]}`);
    const method = "PUT";
    const data = { name: _req["name"], description: _req["description"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/agents/v2/agent/:agent_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982671)
   */
  UpdateAgent(
    req: agent.UpdateAgentRequest,
    options?: T
  ): Promise<agent.UpdateAgentResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/agents/v2/agent/${_req["agent_id"]}`);
    const method = "PUT";
    const data = { name: _req["name"], description: _req["description"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/agent/:agent_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982672)
   */
  GetAgent(
    req: agent.GetAgentRequest,
    options?: T
  ): Promise<agent.GetAgentResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/agents/v2/agent/${_req["agent_id"]}`);
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/prompt/:prompt_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982673)
   */
  GetPrompt(
    req: prompt.GetPromptRequest,
    options?: T
  ): Promise<prompt.GetPromptResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/agents/v2/prompt/${_req["prompt_id"]}`);
    const method = "GET";
    const params = { version: _req["version"], latest: _req["latest"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/agents/v2/agent/config/version/:agent_config_version_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982674)
   */
  UpdateAgentConfigVersion(
    req: agent.UpdateAgentConfigVersionRequest,
    options?: T
  ): Promise<agent.UpdateAgentConfigVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/agent/config/version/${_req["agent_config_version_id"]}`
    );
    const method = "PUT";
    const data = {
      enabled: _req["enabled"],
      description: _req["description"],
      runtime_config: _req["runtime_config"],
      custom_config: _req["custom_config"],
      prompt_config: _req["prompt_config"],
      knowledge_set_config: _req["knowledge_set_config"],
      updated_at: _req["updated_at"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/agent/config/:agent_config_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982675)
   */
  GetAgentConfig(
    req: agent.GetAgentConfigRequest,
    options?: T
  ): Promise<agent.GetAgentConfigResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/agent/config/${_req["agent_config_id"]}`
    );
    const method = "GET";
    const params = {
      version: _req["version"],
      latest: _req["latest"],
      online: _req["online"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/agent/config
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982676)
   */
  ListAgentConfigs(
    req: agent.ListAgentConfigsRequest,
    options?: T
  ): Promise<agent.ListAgentConfigsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/agent/config");
    const method = "GET";
    const params = {
      agent_id: _req["agent_id"],
      type: _req["type"],
      name: _req["name"],
      creator: _req["creator"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/agents/v2/agent/config/:agent_config_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982677)
   */
  UpdateAgentConfig(
    req: agent.UpdateAgentConfigRequest,
    options?: T
  ): Promise<agent.UpdateAgentConfigResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/agent/config/${_req["agent_config_id"]}`
    );
    const method = "PUT";
    const data = { name: _req["name"], description: _req["description"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/agents/v2/agent/config/:agent_config_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982678)
   */
  DeleteAgentConfig(
    req: agent.DeleteAgentConfigRequest,
    options?: T
  ): Promise<agent.DeleteAgentConfigResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/agent/config/${_req["agent_config_id"]}`
    );
    const method = "DELETE";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/agent/config/version
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982679)
   */
  ListAgentConfigVersions(
    req: agent.ListAgentConfigVersionsRequest,
    options?: T
  ): Promise<agent.ListAgentConfigVersionsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/agent/config/version");
    const method = "GET";
    const params = {
      agent_config_id: _req["agent_config_id"],
      creator: _req["creator"],
      enabled: _req["enabled"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      status: _req["status"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/prompt
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982680)
   *
   * Prompt Manage
   */
  CreatePrompt(
    req: prompt.CreatePromptRequest,
    options?: T
  ): Promise<prompt.CreatePromptResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/prompt");
    const method = "POST";
    const data = {
      name: _req["name"],
      description: _req["description"],
      agent_id: _req["agent_id"],
      type: _req["type"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/agent/config
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982681)
   */
  CreateAgentConfig(
    req: agent.CreateAgentConfigRequest,
    options?: T
  ): Promise<agent.CreateAgentConfigResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/agent/config");
    const method = "POST";
    const data = {
      agent_id: _req["agent_id"],
      type: _req["type"],
      name: _req["name"],
      description: _req["description"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/prompt/version
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2982682)
   */
  ListPromptVersion(
    req: prompt.ListPromptVersionRequest,
    options?: T
  ): Promise<prompt.ListPromptVersionResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/prompt/version");
    const method = "GET";
    const params = {
      prompt_id: _req["prompt_id"],
      creator: _req["creator"],
      enabled: _req["enabled"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/prompt
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2984117)
   */
  ListPrompt(
    req: prompt.ListPromptRequest,
    options?: T
  ): Promise<prompt.ListPromptResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/prompt");
    const method = "GET";
    const params = {
      agent_id: _req["agent_id"],
      creator: _req["creator"],
      name: _req["name"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      type: _req["type"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/prompt/version/download
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2991876)
   */
  DownloadPromptVersion(
    req?: prompt.DownloadPromptVersionRequest,
    options?: T
  ): Promise<prompt.DownloadPromptVersionResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/prompt/version/download");
    const method = "GET";
    const params = {
      prompt_version_id: _req["prompt_version_id"],
      prompt_id: _req["prompt_id"],
      version: _req["version"],
      inject_variable: _req["inject_variable"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/agent/config/version/:agent_config_version_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2996994)
   */
  GetAgentConfigVersion(
    req: agent.GetAgentConfigVersionRequest,
    options?: T
  ): Promise<agent.GetAgentConfigVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/agent/config/version/${_req["agent_config_version_id"]}`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/prompt/version/:prompt_version_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=2996995)
   */
  GetPromptVersion(
    req: prompt.GetPromptVersionRequest,
    options?: T
  ): Promise<prompt.GetPromptVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/prompt/version/${_req["prompt_version_id"]}`
    );
    const method = "GET";
    const params = { content: _req["content"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/sessions/partial/list
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3003428)
   *
   * 获取所有未执行完成的会话
   */
  ListSessionPartial(
    req?: session.ListSessionPartialRequest,
    options?: T
  ): Promise<session.ListSessionPartialResponse> {
    const url = this.genBaseURL("/api/agents/v2/sessions/partial/list");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/lark/auth/ticket
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3011182)
   */
  GetLarkTicket(
    req?: lark.GetLarkTicketRequest,
    options?: T
  ): Promise<lark.GetLarkTicketResponse> {
    const url = this.genBaseURL("/api/agents/v2/lark/auth/ticket");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/artifacts/download/batch
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3016611)
   */
  DownloadArtifactBatch(
    req: artifact.DownloadArtifactBatchRequest,
    options?: T
  ): Promise<Blob> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/artifacts/download/batch");
    const method = "POST";
    const data = {
      artifacts: _req["artifacts"],
      session_id: _req["session_id"],
      replay_id: _req["replay_id"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/lark/get/lark_url
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3019083)
   */
  GetUserLarkURL(
    req: lark.GetUserLarkURLRequest,
    options?: T
  ): Promise<lark.GetUserLarkURLResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/lark/get/lark_url");
    const method = "GET";
    const params = {
      lark_url: _req["lark_url"],
      session_id: _req["session_id"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/activity/verify
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3029806)
   */
  VerifyActivityAward(
    req: activity.VerifyActivityAwardRequest,
    options?: T
  ): Promise<activity.VerifyActivityAwardResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/activity/verify");
    const method = "POST";
    const data = {
      replay_id: _req["replay_id"],
      activity_id: _req["activity_id"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/activity/progress
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3029807)
   *
   * avtivity
   */
  GetUserActivityProgress(
    req: activity.GetUserActivityProgressRequest,
    options?: T
  ): Promise<activity.GetUserActivityProgressResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/activity/progress");
    const method = "GET";
    const params = { activity_id: _req["activity_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/agents/v2/user/invitation_code/:invitation_code
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3029808)
   */
  BindInvitationCode(
    req: user.BindInvitationCodeRequest,
    options?: T
  ): Promise<user.BindInvitationCodeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/user/invitation_code/${_req["invitation_code"]}`
    );
    const method = "PUT";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/features/global
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3040415)
   */
  GetGlobalFeature(
    req?: feature.GetGlobalFeaturesRequest,
    options?: T
  ): Promise<feature.GetGlobalFeaturesResponse> {
    const url = this.genBaseURL("/api/agents/v2/features/global");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/sessions/collection/close
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3045516)
   */
  CloseSessionCollections(
    req: session_collection.CloseSessionCollectionsRequest,
    options?: T
  ): Promise<session_collection.CloseSessionCollectionsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/sessions/collection/close");
    const method = "POST";
    const data = { session_collection_ids: _req["session_collection_ids"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/sessions/collection/download
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3045517)
   */
  DownloadSessionCollections(
    req?: session_collection.DownloadSessionCollectionsRequest,
    options?: T
  ): Promise<session_collection.DownloadSessionCollectionsResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/sessions/collection/download");
    const method = "POST";
    const data = {
      status: _req["status"],
      creator: _req["creator"],
      content: _req["content"],
      order_by_created_at: _req["order_by_created_at"],
      order_by_updated_at: _req["order_by_updated_at"],
      order: _req["order"],
      session_collection_ids: _req["session_collection_ids"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/sessions/collection/notification_templates
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3045518)
   */
  ListNotificationTemplates(
    req?: session_collection.ListNotificationTemplatesRequest,
    options?: T
  ): Promise<session_collection.ListNotificationTemplatesResponse> {
    const url = this.genBaseURL(
      "/api/agents/v2/sessions/collection/notification_templates"
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/sessions/collection/list
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3048292)
   */
  FilterListSessionCollections(
    req: session_collection.FilterSessionCollectionsRequest,
    options?: T
  ): Promise<session_collection.FilterSessionCollectionsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/sessions/collection/list");
    const method = "POST";
    const data = {
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      status: _req["status"],
      creator: _req["creator"],
      content: _req["content"],
      order_by_created_at: _req["order_by_created_at"],
      order_by_updated_at: _req["order_by_updated_at"],
      order: _req["order"],
      session_collection_ids: _req["session_collection_ids"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/trace/models
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3064358)
   */
  ListModels(
    req?: trace.ListModelsRequest,
    options?: T
  ): Promise<trace.ListModelsResponse> {
    const url = this.genBaseURL("/api/agents/v2/trace/models");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/trace/:model/chat/completions
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3064359)
   */
  ChatStream(
    req: trace.ChatStreamRequest,
    options?: T
  ): Promise<common.SSEResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/trace/:model/chat/completions");
    const method = "POST";
    const data = {
      model: _req["model"],
      messages: _req["messages"],
      max_tokens: _req["max_tokens"],
      max_completion_tokens: _req["max_completion_tokens"],
      temperature: _req["temperature"],
      top_p: _req["top_p"],
      n: _req["n"],
      stream: _req["stream"],
      stop: _req["stop"],
      presence_penalty: _req["presence_penalty"],
      frequency_penalty: _req["frequency_penalty"],
      logit_bias: _req["logit_bias"],
      logprobs: _req["logprobs"],
      top_logprobs: _req["top_logprobs"],
      user: _req["user"],
      tools: _req["tools"],
      tool_choice: _req["tool_choice"],
      store: _req["store"],
      reasoning_effort: _req["reasoning_effort"],
      metadata: _req["metadata"],
      thinking: _req["thinking"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/mcp
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3071468)
   *
   * MCP 相关
   */
  CreateMCP(
    req: mcp.CreateMCPRequest,
    options?: T
  ): Promise<mcp.CreateMCPResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/mcp");
    const method = "POST";
    const data = {
      name: _req["name"],
      description: _req["description"],
      icon_url: _req["icon_url"],
      config: _req["config"],
      source: _req["source"],
      type: _req["type"],
      force_active: _req["force_active"],
      uid: _req["uid"],
      en_name: _req["en_name"],
      en_description: _req["en_description"],
      session_roles: _req["session_roles"],
      space_id: _req["space_id"],
      scope: _req["scope"],
      default_active: _req["default_active"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/sessions/collection/notification
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3071501)
   */
  SendSessionCollectionNotification(
    req?: session_collection.SendSessionCollectionNotificationRequest,
    options?: T
  ): Promise<session_collection.SendSessionCollectionNotificationResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      "/api/agents/v2/sessions/collection/notification"
    );
    const method = "POST";
    const data = {
      session_collection_ids: _req["session_collection_ids"],
      notification_template: _req["notification_template"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/mcp/activation
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3077996)
   */
  ModifyMCPActivation(
    req: mcp.ModifyMCPActivationRequest,
    options?: T
  ): Promise<mcp.ModifyMCPActivationResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/mcp/activation");
    const method = "POST";
    const data = {
      id: _req["id"],
      source: _req["source"],
      status: _req["status"],
      space_id: _req["space_id"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/agents/v2/mcp/update
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3077997)
   */
  UpdateMCP(
    req: mcp.UpdateMCPRequest,
    options?: T
  ): Promise<mcp.UpdateMCPResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/mcp/update");
    const method = "PUT";
    const data = {
      id: _req["id"],
      source: _req["source"],
      description: _req["description"],
      icon_url: _req["icon_url"],
      config: _req["config"],
      name: _req["name"],
      type: _req["type"],
      force_active: _req["force_active"],
      en_name: _req["en_name"],
      en_description: _req["en_description"],
      session_roles: _req["session_roles"],
      scope: _req["scope"],
      default_active: _req["default_active"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/mcp/list
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3078811)
   *
   * 因bam array query生成代码问题，改post
   */
  ListMCP(req?: mcp.ListMCPRequest, options?: T): Promise<mcp.ListMCPResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/mcp/list");
    const method = "POST";
    const data = {
      name: _req["name"],
      sources: _req["sources"],
      is_active: _req["is_active"],
      types: _req["types"],
      session_role: _req["session_role"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/mcp/validate
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3082971)
   */
  ValidateMCP(
    req: mcp.ValidateMCPRequest,
    options?: T
  ): Promise<mcp.ValidateMCPResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/mcp/validate");
    const method = "POST";
    const data = {
      config: _req["config"],
      source: _req["source"],
      type: _req["type"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/trace/actions/suspend
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3092874)
   */
  SuspendRuntime(
    req: trace.SuspendRuntimeRequest,
    options?: T
  ): Promise<trace.SuspendRuntimeResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/trace/actions/suspend");
    const method = "POST";
    const params = { run_id: _req["run_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/sessions/:session_id/agent_run
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3095164)
   */
  GetSessionAgentRun(
    req: session.GetSessionAgentRunRequest,
    options?: T
  ): Promise<session.GetSessionAgentRunResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/sessions/${_req["session_id"]}/agent_run`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * PUT /api/agents/v2/build_in_mcp/update
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3099851)
   */
  UpdateBuildInMCP(
    req: mcp.UpdateMCPRequest,
    options?: T
  ): Promise<mcp.UpdateMCPResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/build_in_mcp/update");
    const method = "PUT";
    const data = {
      id: _req["id"],
      source: _req["source"],
      description: _req["description"],
      icon_url: _req["icon_url"],
      config: _req["config"],
      name: _req["name"],
      type: _req["type"],
      force_active: _req["force_active"],
      en_name: _req["en_name"],
      en_description: _req["en_description"],
      session_roles: _req["session_roles"],
      scope: _req["scope"],
      default_active: _req["default_active"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/build_in_mcp/create
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3100017)
   *
   * 内置MCP管理相关
   */
  CreateBuildInMCP(
    req: mcp.CreateMCPRequest,
    options?: T
  ): Promise<mcp.CreateMCPResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/build_in_mcp/create");
    const method = "POST";
    const data = {
      name: _req["name"],
      description: _req["description"],
      icon_url: _req["icon_url"],
      config: _req["config"],
      source: _req["source"],
      type: _req["type"],
      force_active: _req["force_active"],
      uid: _req["uid"],
      en_name: _req["en_name"],
      en_description: _req["en_description"],
      session_roles: _req["session_roles"],
      space_id: _req["space_id"],
      scope: _req["scope"],
      default_active: _req["default_active"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/templates
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3108881)
   */
  CreateTemplate(
    req?: template.CreateTemplateRequest,
    options?: T
  ): Promise<template.CreateTemplateResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/templates");
    const method = "POST";
    const data = {
      session_id: _req["session_id"],
      draft_template_id: _req["draft_template_id"],
      from_template_id: _req["from_template_id"],
      template: _req["template"],
      space_id: _req["space_id"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/agents/v2/templates/:template_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3108883)
   */
  DeleteTemplate(
    req: template.DeleteTemplateRequest,
    options?: T
  ): Promise<template.DeleteTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/${_req["template_id"]}`
    );
    const method = "DELETE";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/templates/:template_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3108884)
   */
  GetTemplate(
    req: template.GetTemplateRequest,
    options?: T
  ): Promise<template.GetTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/${_req["template_id"]}`
    );
    const method = "GET";
    const params = { space_id: _req["space_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/templates/:template_id/upload/stream
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3108887)
   */
  UploadTemplateExperienceFileStream(
    req: template.UploadTemplateExperienceFileStreamRequest,
    options?: T
  ): Promise<template.UploadTemplateExperienceFileStreamResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/${_req["template_id"]}/upload/stream`
    );
    const method = "POST";
    const params = { path: _req["path"], size: _req["size"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/templates/draft
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3108888)
   */
  CreateTemplateDraft(
    req: template.CreateTemplateDraftRequest,
    options?: T
  ): Promise<template.CreateTemplateDraftResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/templates/draft");
    const method = "POST";
    const data = {
      template_key: _req["template_key"],
      space_id: _req["space_id"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/agents/v2/templates/:template_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3108889)
   */
  UpdateTemplate(
    req: template.UpdateTemplateRequest,
    options?: T
  ): Promise<template.UpdateTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/${_req["template_id"]}`
    );
    const method = "PUT";
    const data = {
      need_generate_experience: _req["need_generate_experience"],
      template_key: _req["template_key"],
      template: _req["template"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/templates/file/:file_id/raw
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3117447)
   */
  DownloadTemplateExperienceFile(
    req: template.DownloadTemplateExperienceFileStreamRequest,
    options?: T
  ): Promise<Blob> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/file/${_req["file_id"]}/raw`
    );
    const method = "GET";
    const params = { stream: _req["stream"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/agents/v2/templates/:template_id/experience
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3118357)
   */
  UpdateTemplateExperience(
    req: template.UpdateTemplateExperienceRequest,
    options?: T
  ): Promise<template.UpdateTemplateExperienceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/${_req["template_id"]}/experience`
    );
    const method = "PUT";
    const data = {
      progress_plan: _req["progress_plan"],
      exp_sop: _req["exp_sop"],
      status: _req["status"],
      error: _req["error"],
      force_active: _req["force_active"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/templates/:template_id/draft
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3127281)
   */
  GetTemplateDraft(
    req?: template.GetTemplateDraftRequest,
    options?: T
  ): Promise<template.GetTemplateDraftResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/agents/v2/templates/${_req["template_id"]}/draft`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/trace/actions/delete
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3129078)
   */
  DeleteRuntime(
    req: trace.DeleteRuntimeRequest,
    options?: T
  ): Promise<trace.DeleteRuntimeResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/trace/actions/delete");
    const method = "POST";
    const params = { run_id: _req["run_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/lark/documents/:document_id/blocks
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3135853)
   */
  GetLarkDocxBlocks(
    req: lark.GetLarkDocxBlocksRequest,
    options?: T
  ): Promise<Blob> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/lark/documents/${_req["document_id"]}/blocks`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/templates/count
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3146626)
   */
  CountTemplates(
    req?: template.CountTemplatesRequest,
    options?: T
  ): Promise<template.CountTemplatesResponse> {
    const url = this.genBaseURL("/api/agents/v2/templates/count");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/sessions/:session_id/tool_call
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3163404)
   */
  SubmitToolCall(
    req: session.SubmitToolCallRequest,
    options?: T
  ): Promise<session.SubmitToolCallResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/sessions/${_req["session_id"]}/tool_call`
    );
    const method = "POST";
    const data = { tool_calls: _req["tool_calls"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/trace/session/documents
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3172653)
   */
  ListSessionDocuments(
    req: trace.ListSessionDocumentsRequest,
    options?: T
  ): Promise<trace.ListSessionDocumentsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/trace/session/documents");
    const method = "GET";
    const params = { session_id: _req["session_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/trace/session/documents/convert_to_lark
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3177758)
   */
  ConvertSessionDocumentToLark(
    req: trace.ConvertSessionDocumentToLarkRequest,
    options?: T
  ): Promise<trace.ConvertSessionDocumentToLarkResponse> {
    const _req = req;
    const url = this.genBaseURL(
      "/api/agents/v2/trace/session/documents/convert_to_lark"
    );
    const method = "POST";
    const params = {
      session_id: _req["session_id"],
      file_path: _req["file_path"],
      artifact_id: _req["artifact_id"],
      force_regenerate: _req["force_regenerate"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/templates/:template_id/star
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3178918)
   */
  CreateTemplateStar(
    req: template.CreateTemplateStarRequest,
    options?: T
  ): Promise<template.CreateTemplateStarResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/${_req["template_id"]}/star`
    );
    const method = "POST";
    const data = { space_id: _req["space_id"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/templates/:template_id/share
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3178919)
   */
  CreateShareTemplate(
    req: template.CreateShareTemplateRequest,
    options?: T
  ): Promise<template.CreateShareTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/${_req["template_id"]}/share`
    );
    const method = "POST";
    return this.request({ url, method }, options);
  }

  /**
   * DELETE /api/agents/v2/templates/:template_id/star
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3178920)
   */
  DeleteTemplateStar(
    req: template.DeleteTemplateStarRequest,
    options?: T
  ): Promise<template.DeleteTemplateStarResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/${_req["template_id"]}/star`
    );
    const method = "DELETE";
    const params = { space_id: _req["space_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /api/agents/v2/templates/shares/:share_id/user
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3193435)
   */
  DeleteUserShareTemplate(
    req: template.DeleteUserShareTemplateRequest,
    options?: T
  ): Promise<template.DeleteUserShareTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/shares/${_req["share_id"]}/user`
    );
    const method = "DELETE";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/templates/shares/:share_id/user
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3193436)
   */
  CreateUserShareTemplate(
    req: template.CreateUserShareTemplateRequest,
    options?: T
  ): Promise<template.CreateUserShareTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/shares/${_req["share_id"]}/user`
    );
    const method = "POST";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/user/grant_access
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3203863)
   */
  GrantAccess(
    req?: user.GrantAccessRequest,
    options?: T
  ): Promise<user.GrantAccessResponse> {
    const url = this.genBaseURL("/api/agents/v2/user/grant_access");
    const method = "POST";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/resource/user/permission
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3234928)
   *
   * 权限相关
   */
  GetUserResourcePermission(
    req?: permission.GetUserResourcePermissionRequest,
    options?: T
  ): Promise<permission.GetUserResourcePermissionResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/resource/user/permission");
    const method = "GET";
    const params = {
      resource_id: _req["resource_id"],
      external_id: _req["external_id"],
      type: _req["type"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/trace/events/mcp
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3236304)
   */
  TraceMCPEvents(
    req: trace.TraceMCPEventsRequest,
    options?: T
  ): Promise<common.SSEResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/trace/events/mcp");
    const method = "POST";
    const params = { session_id: _req["session_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PATCH /api/agents/v2/ops/templates/:template_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3236307)
   */
  OpsEditTemplate(
    req: ops.OpsEditTemplateRequest,
    options?: T
  ): Promise<ops.OpsEditTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/ops/templates/${_req["template_id"]}`
    );
    const method = "PATCH";
    const data = {
      name: _req["name"],
      progress_plan: _req["progress_plan"],
      exp_sop: _req["exp_sop"],
      expired: _req["expired"],
      edited: _req["edited"],
      query_template: _req["query_template"],
      query_template_placeholders: _req["query_template_placeholders"],
      scope: _req["scope"],
      support_mcps: _req["support_mcps"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/ops/templates
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3236308)
   *
   * 管理运维接口
   */
  OpsListTemplates(
    req?: ops.OpsListTemplatesRequest,
    options?: T
  ): Promise<ops.OpsListTemplatesResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/ops/templates");
    const method = "GET";
    const params = {
      template_id: _req["template_id"],
      creator: _req["creator"],
      name: _req["name"],
      share_id: _req["share_id"],
      session_id: _req["session_id"],
      run_session_id: _req["run_session_id"],
      page: _req["page"],
      page_size: _req["page_size"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/space
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3242687)
   *
   * space项目空间
   */
  CreateSpace(
    req: space.CreateSpaceRequest,
    options?: T
  ): Promise<space.CreateSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/space");
    const method = "POST";
    const data = {
      name: _req["name"],
      name_en: _req["name_en"],
      description: _req["description"],
      space_config: _req["space_config"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/user/list/space
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3242688)
   */
  ListUserSpaces(
    req?: space.ListUserSpacesRequest,
    options?: T
  ): Promise<space.ListUserSpacesResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/user/list/space");
    const method = "GET";
    const params = {
      start_id: _req["start_id"],
      limit: _req["limit"],
      type: _req["type"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/space
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3242689)
   */
  GetSpace(
    req?: space.GetSpaceRequest,
    options?: T
  ): Promise<space.GetSpaceResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/space");
    const method = "GET";
    const params = {
      space_id: _req["space_id"],
      type: _req["type"],
      external_id: _req["external_id"],
      need_members: _req["need_members"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/space/list
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3242690)
   */
  ListAllSpaces(
    req: space.ListAllSpacesRequest,
    options?: T
  ): Promise<space.ListAllSpacesResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/space/list");
    const method = "GET";
    const params = { page_num: _req["page_num"], page_size: _req["page_size"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/agents/v2/space
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3242691)
   */
  UpdateSpace(
    req: space.UpdateSpaceRequest,
    options?: T
  ): Promise<space.UpdateSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/space");
    const method = "PUT";
    const data = {
      space_id: _req["space_id"],
      name: _req["name"],
      name_en: _req["name_en"],
      description: _req["description"],
      space_config: _req["space_config"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/space/members
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3242692)
   */
  ListSpaceMembers(
    req: space.ListSpaceMembersRequest,
    options?: T
  ): Promise<space.ListSpaceMembersResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/space/members");
    const method = "GET";
    const params = {
      space_id: _req["space_id"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      role: _req["role"],
      type: _req["type"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /api/agents/v2/space
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3242693)
   */
  DeleteSpace(
    req: space.DeleteSpaceRequest,
    options?: T
  ): Promise<space.DeleteSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/space");
    const method = "DELETE";
    const data = { space_id: _req["space_id"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/space/members
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3242694)
   */
  AddSpaceMember(
    req: space.AddSpaceMemberRequest,
    options?: T
  ): Promise<space.AddSpaceMemberResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/space/members");
    const method = "POST";
    const data = { space_id: _req["space_id"], members: _req["members"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/agents/v2/space/members
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3242695)
   */
  RemoveSpaceMember(
    req: space.RemoveSpaceMemberRequest,
    options?: T
  ): Promise<space.RemoveSpaceMemberResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/space/members");
    const method = "DELETE";
    const data = { space_id: _req["space_id"], members: _req["members"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/datasets/:dataset_id/documents
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3253925)
   */
  ListDocuments(
    req: knowledgebase.ListDocumentsRequest,
    options?: T
  ): Promise<knowledgebase.ListDocumentsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/datasets/${_req["dataset_id"]}/documents`
    );
    const method = "POST";
    const data = {
      query: _req["query"],
      creators: _req["creators"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      desc_order_by: _req["desc_order_by"],
      process_status: _req["process_status"],
      lte_created_at: _req["lte_created_at"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/agents/v2/datasets/:dataset_id/documents/:document_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3254644)
   */
  DeleteDocument(
    req: knowledgebase.DeleteDocumentRequest,
    options?: T
  ): Promise<knowledgebase.DeleteDocumentResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/datasets/${_req["dataset_id"]}/documents/${_req["document_id"]}`
    );
    const method = "DELETE";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/datasets/:dataset_id/documents/:document_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3255175)
   */
  GetDocument(
    req: knowledgebase.GetDocumentRequest,
    options?: T
  ): Promise<knowledgebase.GetDocumentResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/datasets/${_req["dataset_id"]}/documents/${_req["document_id"]}`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * PUT /api/agents/v2/datasets/:dataset_id/documents/:document_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3255176)
   */
  UpdateDocument(
    req: knowledgebase.UpdateDocumentRequest,
    options?: T
  ): Promise<knowledgebase.UpdateDocumentResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/datasets/${_req["dataset_id"]}/documents/${_req["document_id"]}`
    );
    const method = "PUT";
    return this.request({ url, method }, options);
  }

  /**
   * PUT /api/agents/v2/datasets/:dataset_id/documents
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3255177)
   *
   * knowledgebase
   */
  UploadDocuments(
    req: knowledgebase.UploadDocumentsRequest,
    options?: T
  ): Promise<knowledgebase.UploadDocumentsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/datasets/${_req["dataset_id"]}/documents`
    );
    const method = "PUT";
    const data = {
      document_urls: _req["document_urls"],
      wiki_list: _req["wiki_list"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/mentions
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3293955)
   */
  SearchMentions(
    req?: mention.SearchMentionsRequest,
    options?: T
  ): Promise<mention.SearchMentionsResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/mentions");
    const method = "GET";
    const params = {
      space_id: _req["space_id"],
      type: _req["type"],
      repository_id: _req["repository_id"],
      repository_branch: _req["repository_branch"],
      query: _req["query"],
      limit: _req["limit"],
      next_id: _req["next_id"],
      order_by: _req["order_by"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/templates/shares/form_data
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3305447)
   */
  SaveTemplateShareFormData(
    req: template.SaveTemplateShareFormDataRequest,
    options?: T
  ): Promise<template.SaveTemplateShareFormDataResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/templates/shares/form_data");
    const method = "POST";
    const data = { form_data: _req["form_data"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/templates/shares/form_data/:id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3305448)
   */
  GetTemplateShareFormData(
    req: template.GetTemplateShareFormDataRequest,
    options?: T
  ): Promise<template.GetTemplateShareFormDataResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/templates/shares/form_data/${_req["id"]}`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v3/templates/list
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3309875)
   */
  ListSpaceTemplates(
    req?: template.ListSpaceTemplatesRequest,
    options?: T
  ): Promise<template.ListSpaceTemplatesResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v3/templates/list");
    const method = "GET";
    const params = {
      next_id: _req["next_id"],
      limit: _req["limit"],
      category: _req["category"],
      search: _req["search"],
      source: _req["source"],
      label: _req["label"],
      space_id: _req["space_id"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v3/mcp/list
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3309876)
   *
   * 因bam array query生成代码问题，改post
   */
  ListSpaceMCP(
    req: mcp.ListSpaceMCPRequest,
    options?: T
  ): Promise<mcp.ListSpaceMCPResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v3/mcp/list");
    const method = "POST";
    const data = {
      name: _req["name"],
      sources: _req["sources"],
      is_active: _req["is_active"],
      types: _req["types"],
      session_role: _req["session_role"],
      space_id: _req["space_id"],
      next_id: _req["next_id"],
      limit: _req["limit"],
      tabs: _req["tabs"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v3/sessions
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3309877)
   *
   * v3 相关接口
   */
  ListSpaceSessions(
    req: session.ListSpaceSessionsRequest,
    options?: T
  ): Promise<session.ListSpaceSessionsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v3/sessions");
    const method = "GET";
    const params = {
      space_id: _req["space_id"],
      limit: _req["limit"],
      next_id: _req["next_id"],
      type: _req["type"],
      search: _req["search"],
      tab: _req["tab"],
      creators: _req["creators"],
      created_time: _req["created_time"],
      updated_time: _req["updated_time"],
      statuses: _req["statuses"],
      scopes: _req["scopes"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v3/templates/count
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3309878)
   */
  CountSpaceTemplates(
    req?: template.CountSpaceTemplatesRequest,
    options?: T
  ): Promise<template.CountSpaceTemplatesResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v3/templates/count");
    const method = "GET";
    const params = { space_id: _req["space_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/datasets/:dataset_id/lark_documents
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3315709)
   */
  SearchLarkDocuments(
    req: knowledgebase.SearchLarkDocumentsRequest,
    options?: T
  ): Promise<knowledgebase.SearchLarkDocumentsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/datasets/${_req["dataset_id"]}/lark_documents`
    );
    const method = "GET";
    const params = { query: _req["query"], import_type: _req["import_type"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/datasets/:dataset_id/recommend_documents
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3315710)
   */
  RecommendDocuments(
    req: knowledgebase.RecommendDocumentsRequest,
    options?: T
  ): Promise<knowledgebase.RecommendDocumentsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/datasets/${_req["dataset_id"]}/recommend_documents`
    );
    const method = "POST";
    const data = { reference_documents: _req["reference_documents"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/agents/v2/user/settings
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3325055)
   */
  UpdateUserSettings(
    req?: user.UpdateUserSettingsRequest,
    options?: T
  ): Promise<user.UpdateUserSettingsResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/user/settings");
    const method = "PUT";
    const data = {
      keep_login: _req["keep_login"],
      locale: _req["locale"],
      keyboard_shortcut: _req["keyboard_shortcut"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/user/settings
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3325056)
   */
  GetUserSettings(
    req?: user.GetUserSettingsRequest,
    options?: T
  ): Promise<user.GetUserSettingsResponse> {
    const url = this.genBaseURL("/api/agents/v2/user/settings");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/datasets/:dataset_id/documents/count
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3325811)
   */
  CountDocuments(
    req: knowledgebase.CountDocumentsRequest,
    options?: T
  ): Promise<knowledgebase.CountDocumentsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/datasets/${_req["dataset_id"]}/documents/count`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * DELETE /api/agents/v2/knowledgeset/:knowledge_set_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3331871)
   */
  DeleteKnowledgeset(
    req: knowledgeset.DeleteKnowledgesetRequest,
    options?: T
  ): Promise<knowledgeset.DeleteKnowledgesetResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/knowledgeset/${_req["knowledge_set_id"]}`
    );
    const method = "DELETE";
    return this.request({ url, method }, options);
  }

  /**
   * DELETE /api/agents/v2/knowledge/:knowledge_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3331873)
   */
  DeleteKnowledge(
    req: knowledgeset.DeleteKnowledgeRequest,
    options?: T
  ): Promise<knowledgeset.DeleteKnowledgeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/knowledge/${_req["knowledge_id"]}`
    );
    const method = "DELETE";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/knowledgeset/:knowledge_set_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3331874)
   */
  GetKnowledgeset(
    req: knowledgeset.GetKnowledgesetRequest,
    options?: T
  ): Promise<knowledgeset.GetKnowledgesetResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/knowledgeset/${_req["knowledge_set_id"]}`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/knowledgeset
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3331875)
   *
   * knowledgeset
   */
  CreateKnowledgeset(
    req: knowledgeset.CreateKnowledgesetRequest,
    options?: T
  ): Promise<knowledgeset.CreateKnowledgesetResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/knowledgeset");
    const method = "POST";
    const data = {
      name: _req["name"],
      description: _req["description"],
      type: _req["type"],
      key: _req["key"],
      tags: _req["tags"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/knowledgeset
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3331877)
   */
  ListKnowledgesets(
    req: knowledgeset.ListKnowledgesetRequest,
    options?: T
  ): Promise<knowledgeset.ListKnowledgesetResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/knowledgeset");
    const method = "GET";
    const params = {
      id: _req["id"],
      type: _req["type"],
      name: _req["name"],
      creator: _req["creator"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/knowledge
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3331881)
   *
   * knowledge
   */
  CreateKnowledge(
    req: knowledgeset.CreateKnowledgeRequest,
    options?: T
  ): Promise<knowledgeset.CreateKnowledgeResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/knowledge");
    const method = "POST";
    const data = {
      knowledge_set_id: _req["knowledge_set_id"],
      knowledge_version_id: _req["knowledge_version_id"],
      key: _req["key"],
      title: _req["title"],
      content: _req["content"],
      enable_if: _req["enable_if"],
      use_when: _req["use_when"],
      tags: _req["tags"],
      recall_method: _req["recall_method"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/knowledge/:knowledge_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3331882)
   */
  GetKnowledge(
    req: knowledgeset.GetKnowledgeRequest,
    options?: T
  ): Promise<knowledgeset.GetKnowledgeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/knowledge/${_req["knowledge_id"]}`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * PUT /api/agents/v2/knowledgeset/:knowledge_set_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3331883)
   */
  UpdateKnowledgeset(
    req: knowledgeset.UpdateKnowledgesetRequest,
    options?: T
  ): Promise<knowledgeset.UpdateKnowledgesetResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/knowledgeset/${_req["knowledge_set_id"]}`
    );
    const method = "PUT";
    const data = {
      name: _req["name"],
      description: _req["description"],
      type: _req["type"],
      key: _req["key"],
      tags: _req["tags"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/knowledgeset/metadata/conf
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3331884)
   *
   * knowledge Metadata Conf
   */
  GetKnowledgesetMetadataConf(
    req?: knowledgeset.GetKnowledgesetMetadataConfRequest,
    options?: T
  ): Promise<knowledgeset.GetKnowledgesetMetadataConfResponse> {
    const url = this.genBaseURL("/api/agents/v2/knowledgeset/metadata/conf");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * PUT /api/agents/v2/knowledge/:knowledge_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3331885)
   */
  UpdateKnowledge(
    req: knowledgeset.UpdateKnowledgeRequest,
    options?: T
  ): Promise<knowledgeset.UpdateKnowledgeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/knowledge/${_req["knowledge_id"]}`
    );
    const method = "PUT";
    const data = {
      key: _req["key"],
      title: _req["title"],
      content: _req["content"],
      enable_if: _req["enable_if"],
      use_when: _req["use_when"],
      tags: _req["tags"],
      recall_method: _req["recall_method"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/knowledgeset/version
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3334024)
   *
   * knowledgeset version
   */
  CreateKnowledgesetVersion(
    req: knowledgeset.CreateKnowledgesetVersionRequest,
    options?: T
  ): Promise<knowledgeset.CreateKnowledgesetVersionResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/knowledgeset/version");
    const method = "POST";
    const data = {
      knowledge_set_id: _req["knowledge_set_id"],
      description: _req["description"],
      use_when: _req["use_when"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/agents/v2/knowledgeset/version/:knowledge_set_version_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3334025)
   */
  DeleteKnowledgesetVersion(
    req: knowledgeset.DeleteKnowledgesetVersionRequest,
    options?: T
  ): Promise<knowledgeset.DeleteKnowledgesetVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/knowledgeset/version/${_req["knowledge_set_version_id"]}`
    );
    const method = "DELETE";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/knowledgeset/version/copy
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3334026)
   */
  CopyKnowledgesetVersion(
    req: knowledgeset.CopyKnowledgesetVersionRequest,
    options?: T
  ): Promise<knowledgeset.CopyKnowledgesetVersionResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/knowledgeset/version/copy");
    const method = "POST";
    const data = { source_id: _req["source_id"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/agents/v2/knowledgeset/version/:knowledge_set_version_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3334027)
   */
  UpdateKnowledgesetVersion(
    req: knowledgeset.UpdateKnowledgesetVersionRequest,
    options?: T
  ): Promise<knowledgeset.UpdateKnowledgesetVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/knowledgeset/version/${_req["knowledge_set_version_id"]}`
    );
    const method = "PUT";
    const data = {
      knowledge_set_id: _req["knowledge_set_id"],
      description: _req["description"],
      status: _req["status"],
      knowledges: _req["knowledges"],
      use_when: _req["use_when"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/knowledgeset/version/:knowledge_set_version_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3334028)
   */
  GetKnowledgesetVersion(
    req: knowledgeset.GetKnowledgesetVersionRequest,
    options?: T
  ): Promise<knowledgeset.GetKnowledgesetVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/knowledgeset/version/${_req["knowledge_set_version_id"]}`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/knowledgeset/version
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3334029)
   */
  ListKnowledgesetVersions(
    req: knowledgeset.ListKnowledgesetVersionRequest,
    options?: T
  ): Promise<knowledgeset.ListKnowledgesetVersionResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/knowledgeset/version");
    const method = "GET";
    const params = {
      knowledge_set_id: _req["knowledge_set_id"],
      status: _req["status"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/internal/knowledge
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3339855)
   */
  ListKnowledges(
    req: knowledgeset.ListKnowledgeRequest,
    options?: T
  ): Promise<knowledgeset.ListKnowledgeResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/internal/knowledge");
    const method = "GET";
    const params = {
      knowledge_set_version_id: _req["knowledge_set_version_id"],
      tag: _req["tag"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v3/mcp/pre_validate
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3341734)
   */
  ValidateActiveMCPs(
    req?: mcp.ValidateActiveMCPsRequest,
    options?: T
  ): Promise<mcp.ValidateActiveMCPsResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v3/mcp/pre_validate");
    const method = "POST";
    const data = { space_id: _req["space_id"], role: _req["role"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v3/templates/mcp/validate
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3343918)
   */
  ValidateTemplateMCPs(
    req: mcp.ValidateTemplateMCPsRequest,
    options?: T
  ): Promise<mcp.ValidateTemplateMCPsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v3/templates/mcp/validate");
    const method = "POST";
    const data = {
      template_id: _req["template_id"],
      space_id: _req["space_id"],
      role: _req["role"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/knowledge/enableif/check
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3347372)
   */
  CheckEnableIf(
    req: knowledgeset.CheckEnableIfRequest,
    options?: T
  ): Promise<knowledgeset.CheckEnableIfResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/knowledge/enableif/check");
    const method = "GET";
    const params = { enable_if_content: _req["enable_if_content"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/agent/config/version/copy
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3347373)
   */
  CopyAgentConfigVersion(
    req: agent.CopyAgentConfigVersionRequest,
    options?: T
  ): Promise<agent.CopyAgentConfigVersionResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/agent/config/version/copy");
    const method = "POST";
    const data = {
      source_id: _req["source_id"],
      agent_config_id: _req["agent_config_id"],
      description: _req["description"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/devops/session
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3352732)
   *
   * debug
   */
  GetDebugSession(
    req: debug.GetDebugSessionRequest,
    options?: T
  ): Promise<debug.GetDebugSessionResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/devops/session");
    const method = "GET";
    const params = { session_id: _req["session_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/devops/session/events
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3352733)
   */
  GetSessionDebugEvents(
    req: debug.GetDebugSessionEventsRequest,
    options?: T
  ): Promise<debug.GetDebugSessionEventsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/devops/session/events");
    const method = "GET";
    const params = {
      session_id: _req["session_id"],
      event_id: _req["event_id"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/devops/session/debug
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3352734)
   */
  DebugSessionEvent(
    req: debug.DebugSessionEventRequest,
    options?: T
  ): Promise<debug.DebugSessionEventResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/devops/session/debug");
    const method = "POST";
    const data = {
      session_id: _req["session_id"],
      event_id: _req["event_id"],
      context: _req["context"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/devops/session/events_stream
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3352735)
   */
  GetSessionDebugEventsStream(
    req: debug.GetSessionDebugEventsStreamRequest,
    options?: T
  ): Promise<common.SSEResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/devops/session/events_stream");
    const method = "POST";
    const params = {
      session_id: _req["session_id"],
      event_id: _req["event_id"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/internal/knowledgeset
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3353001)
   */
  GetKnowledgesetByIDs(
    req: knowledgeset.GetKnowledgesetByIDsRequest,
    options?: T
  ): Promise<knowledgeset.ListKnowledgesetResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/internal/knowledgeset");
    const method = "GET";
    const params = { ids: _req["ids"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/sessions/:session_id/mcp_details
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3356643)
   */
  GetSessionMCPDetails(
    req: session.GetSessionMCPDetailsRequest,
    options?: T
  ): Promise<session.GetSessionMCPDetailsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/sessions/${_req["session_id"]}/mcp_details`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v3/mcp/cloud_psm_auth
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3359961)
   */
  CreateCloudSDKAuthTicket(
    req: mcp.CreateCloudSDKAuthTicketRequest,
    options?: T
  ): Promise<mcp.CreateCloudSDKAuthTicketResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v3/mcp/cloud_psm_auth");
    const method = "POST";
    const data = { psm: _req["psm"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/mcp/user_config
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3360987)
   */
  ModifyMCPUserConfig(
    req: mcp.ModifyMCPUserConfigRequest,
    options?: T
  ): Promise<mcp.ModifyMCPUserConfigResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/mcp/user_config");
    const method = "POST";
    const data = {
      id: _req["id"],
      source: _req["source"],
      space_id: _req["space_id"],
      user_config: _req["user_config"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/project_artifacts/:artifact_key/versions/:version/files_content
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3362234)
   */
  GetProjectArtifactFilesContent(
    req: artifact.ProjectArtifactFilesContentRequest,
    options?: T
  ): Promise<artifact.ProjectArtifactFilesContentResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/project_artifacts/${_req["artifact_key"]}/versions/${_req["version"]}/files_content`
    );
    const method = "GET";
    const params = {
      session_id: _req["session_id"],
      replay_id: _req["replay_id"],
      file_path_list: _req["file_path_list"],
      is_preview_binary: _req["is_preview_binary"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/project_artifacts/:artifact_key/versions/:version/files
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3362235)
   */
  GetProjectArtifactFiles(
    req: artifact.ProjectArtifactFilesRequest,
    options?: T
  ): Promise<artifact.ProjectArtifactFilesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/project_artifacts/${_req["artifact_key"]}/versions/${_req["version"]}/files`
    );
    const method = "GET";
    const params = {
      session_id: _req["session_id"],
      replay_id: _req["replay_id"],
      file_path: _req["file_path"],
      depth: _req["depth"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/project_artifacts/:artifact_key/revisions/:revision/diff
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3362236)
   */
  GetProjectArtifactDiff(
    req: artifact.ProjectArtifactDiffRequest,
    options?: T
  ): Promise<artifact.ProjectArtifactDiffResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/project_artifacts/${_req["artifact_key"]}/revisions/${_req["revision"]}/diff`
    );
    const method = "GET";
    const params = {
      session_id: _req["session_id"],
      replay_id: _req["replay_id"],
      detail: _req["detail"],
      file_path: _req["file_path"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/debug/mock_event_data
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3364049)
   */
  MockDebugEventData(
    req?: debug.MockDebugEventData,
    options?: T
  ): Promise<debug.MockDebugEventData> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/debug/mock_event_data");
    const method = "POST";
    const data = {
      step: _req["step"],
      thinking: _req["thinking"],
      tool_call: _req["tool_call"],
      query: _req["query"],
      response: _req["response"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/ops/sessions/:session_id/trajectory
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3384501)
   */
  GetSessionTrajectory(
    req: ops.GetTraceSessionTrajectoryRequest,
    options?: T
  ): Promise<ops.GetTraceSessionTrajectoryResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/ops/sessions/${_req["session_id"]}/trajectory`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/trace/markdown/convert_to_lark
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3388781)
   */
  ConvertMarkdownToLark(
    req: trace.ConvertMarkdownToLarkRequest,
    options?: T
  ): Promise<trace.ConvertMarkdownToLarkResponse> {
    const _req = req;
    const url = this.genBaseURL(
      "/api/agents/v2/trace/markdown/convert_to_lark"
    );
    const method = "POST";
    const data = { Content: _req["Content"], SessionID: _req["SessionID"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/deploy/bpm/online
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3388878)
   */
  BPMOnlineCallback(
    req: deploy.BPMOnlineCallbackRequest,
    options?: T
  ): Promise<deploy.BPMOnlineCallbackResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/deploy/bpm/online");
    const method = "POST";
    const data = {
      id: _req["id"],
      deploy_id: _req["deploy_id"],
      agent_config_id: _req["agent_config_id"],
      agent_config_version_id: _req["agent_config_version_id"],
      skip_canary_comment: _req["skip_canary_comment"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/deploy/bpm/canary
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3388879)
   */
  BPMCanaryCallback(
    req: deploy.BPMCanaryCallbackRequest,
    options?: T
  ): Promise<deploy.BPMCanaryCallbackResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/deploy/bpm/canary");
    const method = "POST";
    const data = {
      id: _req["id"],
      deploy_id: _req["deploy_id"],
      agent_config_id: _req["agent_config_id"],
      agent_config_version_id: _req["agent_config_version_id"],
      status: _req["status"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/deploy/bpm/cancel/canary
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3388880)
   */
  BPMCancelCanaryCallback(
    req: deploy.BPMCancelCanaryCallbackRequest,
    options?: T
  ): Promise<deploy.BPMCancelCanaryCallbackResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/deploy/bpm/cancel/canary");
    const method = "POST";
    const data = {
      id: _req["id"],
      deploy_id: _req["deploy_id"],
      agent_config_id: _req["agent_config_id"],
      agent_config_version_id: _req["agent_config_version_id"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/deploy/bpm/auth
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3388881)
   */
  BPMAuthCallback(
    req: deploy.BPMAuthCallbackRequest,
    options?: T
  ): Promise<deploy.BPMAuthCallbackResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/deploy/bpm/auth");
    const method = "POST";
    const data = {
      id: _req["id"],
      workflow_config_id: _req["workflow_config_id"],
      agent_config_id: _req["agent_config_id"],
      agent_config_version_id: _req["agent_config_version_id"],
      creator: _req["creator"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/deploy/bpm/close
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3388882)
   */
  BPMCloseCallback(
    req: deploy.BPMCloseCallbackRequest,
    options?: T
  ): Promise<deploy.BPMCloseCallbackResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/deploy/bpm/close");
    const method = "POST";
    const data = {
      id: _req["id"],
      deploy_id: _req["deploy_id"],
      agent_config_id: _req["agent_config_id"],
      agent_config_version_id: _req["agent_config_version_id"],
      comment: _req["comment"],
      audit_reject_comment: _req["audit_reject_comment"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/deploy/process/info
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3388883)
   */
  GetDeployProcessInfo(
    req: deploy.GetDeployProcessInfoRequest,
    options?: T
  ): Promise<deploy.GetDeployProcessInfoResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/deploy/process/info");
    const method = "GET";
    const params = { deploy_id: _req["deploy_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/deploy
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3388884)
   *
   * deploy review
   */
  CreateDeploy(
    req: deploy.CreateDeployRequest,
    options?: T
  ): Promise<deploy.CreateDeployResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/deploy");
    const method = "POST";
    const data = {
      agent_config_version_id: _req["agent_config_version_id"],
      reviewer: _req["reviewer"],
      is_enable_ab: _req["is_enable_ab"],
      ab_comment: _req["ab_comment"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/deploy/:deploy_id
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3393114)
   */
  GetDeploy(
    req: deploy.GetDeployRequest,
    options?: T
  ): Promise<deploy.GetDeployResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/agents/v2/deploy/${_req["deploy_id"]}`);
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/scm/version
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3394854)
   */
  GetScmVersion(
    req?: deploy.GetScmVersionRequest,
    options?: T
  ): Promise<deploy.GetScmVersionResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/scm/version");
    const method = "GET";
    const params = {
      branch: _req["branch"],
      type_list: _req["type_list"],
      version: _req["version"],
      commit: _req["commit"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/icm/version
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3394855)
   */
  GetIcmVersion(
    req?: deploy.GetIcmVersionRequest,
    options?: T
  ): Promise<deploy.GetIcmVersionResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/api/agents/v2/icm/version");
    const method = "GET";
    const params = {
      version: _req["version"],
      region: _req["region"],
      specific_tag: _req["specific_tag"],
      config_type: _req["config_type"],
      image_type: _req["image_type"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agents/v2/sessions/:session_id/star
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3397106)
   *
   * 会话收藏相关
   */
  CreateSessionStar(
    req: session.CreateSessionStarRequest,
    options?: T
  ): Promise<session.CreateSessionStarResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/sessions/${_req["session_id"]}/star`
    );
    const method = "POST";
    return this.request({ url, method }, options);
  }

  /**
   * DELETE /api/agents/v2/sessions/:session_id/star
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3397107)
   */
  DeleteSessionStar(
    req: session.DeleteSessionStarRequest,
    options?: T
  ): Promise<session.DeleteSessionStarResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/sessions/${_req["session_id"]}/star`
    );
    const method = "DELETE";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/artifacts/images
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3397108)
   */
  UploadImageByURL(
    req: artifact.UploadImageByURLRequest,
    options?: T
  ): Promise<artifact.UploadImageByURLResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/artifacts/images");
    const method = "POST";
    const data = { urls: _req["urls"], session_id: _req["session_id"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/deploy/review/user
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3401551)
   */
  GetDeployReviewUser(
    req?: deploy.GetDeployReviewUserRequest,
    options?: T
  ): Promise<deploy.GetDeployReviewUserResponse> {
    const url = this.genBaseURL("/api/agents/v2/deploy/review/user");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agents/v2/deploy/list
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3402222)
   */
  GetAgentDeployList(
    req: deploy.GetAgentDeployListRequest,
    options?: T
  ): Promise<deploy.GetAgentDeployListResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/deploy/list");
    const method = "GET";
    const params = {
      agent_config_id: _req["agent_config_id"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      status: _req["status"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/agents/v2/code_repos
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3415622)
   */
  UploadCodeRepo(
    req: dev_resource.UploadCodeRepoRequest,
    options?: T
  ): Promise<dev_resource.UploadCodeRepoResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/code_repos");
    const method = "PUT";
    const data = { space_id: _req["space_id"], code_repos: _req["code_repos"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/space/init
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3415626)
   */
  InitSpace(
    req: space.InitSpaceRequest,
    options?: T
  ): Promise<space.InitSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/space/init");
    const method = "POST";
    const data = {
      space_id: _req["space_id"],
      basic: _req["basic"],
      members: _req["members"],
      lark_doc_config: _req["lark_doc_config"],
      repos: _req["repos"],
      services: _req["services"],
      platform_config: _req["platform_config"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/agents/v2/services
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3415627)
   */
  UploadService(
    req: dev_resource.UploadServiceRequest,
    options?: T
  ): Promise<dev_resource.UploadServiceResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/services");
    const method = "PUT";
    const data = { space_id: _req["space_id"], services: _req["services"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/code_repos
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3415628)
   */
  ListCodeRepo(
    req: dev_resource.ListCodeRepoRequest,
    options?: T
  ): Promise<dev_resource.ListCodeRepoResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/code_repos");
    const method = "POST";
    const data = {
      space_id: _req["space_id"],
      query: _req["query"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      creators: _req["creators"],
      process_status: _req["process_status"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/services
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3415631)
   */
  ListService(
    req: dev_resource.ListServiceRequest,
    options?: T
  ): Promise<dev_resource.ListServiceResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/services");
    const method = "POST";
    const data = {
      space_id: _req["space_id"],
      query: _req["query"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      creators: _req["creators"],
      process_status: _req["process_status"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/agents/v2/datasets/:dataset_id/documents/batch
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3419205)
   */
  BatchDeleteDocument(
    req: knowledgebase.BatchDeleteDocumentRequest,
    options?: T
  ): Promise<knowledgebase.BatchDeleteDocumentResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/datasets/${_req["dataset_id"]}/documents/batch`
    );
    const method = "DELETE";
    const data = { document_ids: _req["document_ids"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/agents/v2/services
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3419206)
   */
  DeleteServiceRepo(
    req: dev_resource.DeleteServiceRequest,
    options?: T
  ): Promise<dev_resource.DeleteServiceResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/services");
    const method = "DELETE";
    const data = { space_id: _req["space_id"], services: _req["services"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agents/v2/notification_message
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3419207)
   *
   * notification message
   *
   * 创建消息
   */
  CreateNotificationMessage(
    req: notification_message.CreateNotificationMessageRequest,
    options?: T
  ): Promise<notification_message.CreateNotificationMessageResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/notification_message");
    const method = "POST";
    const data = {
      title: _req["title"],
      content: _req["content"],
      receive_config: _req["receive_config"],
      is_top: _req["is_top"],
      send_lark: _req["send_lark"],
      link_info: _req["link_info"],
      type: _req["type"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/agents/v2/code_repos
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3419208)
   */
  DeleteCodeRepo(
    req: dev_resource.DeleteCodeRepoRequest,
    options?: T
  ): Promise<dev_resource.DeleteCodeRepoResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/code_repos");
    const method = "DELETE";
    const data = { space_id: _req["space_id"], code_repos: _req["code_repos"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/agents/v2/datasets/:dataset_id/documents/batch
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3419209)
   */
  BatchUpdateDocument(
    req: knowledgebase.BatchUpdateDocumentRequest,
    options?: T
  ): Promise<knowledgebase.BatchUpdateDocumentResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/datasets/${_req["dataset_id"]}/documents/batch`
    );
    const method = "PUT";
    const data = { document_ids: _req["document_ids"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/meego_space
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3419211)
   *
   * platform config
   */
  SearchMeegoSpace(
    req: dev_resource.SearchMeegoSpaceRequest,
    options?: T
  ): Promise<dev_resource.SearchMeegoSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/meego_space");
    const method = "GET";
    const params = { query: _req["query"], space_id: _req["space_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/platform_config
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3419212)
   */
  GetPlatformConfig(
    req: dev_resource.GetPlatformConfigRequest,
    options?: T
  ): Promise<dev_resource.GetPlatformConfigResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/platform_config");
    const method = "GET";
    const params = { space_id: _req["space_id"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/agents/v2/platform_config
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3419213)
   */
  UpdatePlatformConfig(
    req: dev_resource.UpdatePlatformConfigRequest,
    options?: T
  ): Promise<dev_resource.UpdatePlatformConfigResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/platform_config");
    const method = "PUT";
    const data = {
      space_id: _req["space_id"],
      meego_spaces: _req["meego_spaces"],
      operate_type: _req["operate_type"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/agents/v2/notification_message_status
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3419214)
   *
   * 更新消息状态
   */
  UpdateNotificationMessageStatus(
    req: notification_message.UpdateNotificationMessageStatusRequest,
    options?: T
  ): Promise<notification_message.UpdateNotificationMessageStatusResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/notification_message_status");
    const method = "PUT";
    const data = { messageid: _req["messageid"], status: _req["status"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/code_repo
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3419215)
   *
   * dev resource
   *
   * code repo
   */
  SearchCodeRepo(
    req: dev_resource.SearchCodeRepoRequest,
    options?: T
  ): Promise<dev_resource.SearchCodeRepoResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/code_repo");
    const method = "GET";
    const params = {
      space_id: _req["space_id"],
      query: _req["query"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/notification_messages
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3419216)
   *
   * 获取特定用户消息列表
   */
  ListNotificationMessage(
    req?: notification_message.ListNotificationMessagesRequest,
    options?: T
  ): Promise<notification_message.ListNotificationMessagesResponse> {
    const url = this.genBaseURL("/api/agents/v2/notification_messages");
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * DELETE /api/agents/v2/sessions/batch
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3430396)
   */
  BatchDeleteSession(
    req: session.BatchDeleteSessionRequest,
    options?: T
  ): Promise<session.BatchDeleteSessionResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/sessions/batch");
    const method = "DELETE";
    const data = { session_ids: _req["session_ids"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agents/v2/service
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3435450)
   *
   * service
   */
  SearchService(
    req: dev_resource.SearchServiceRequest,
    options?: T
  ): Promise<dev_resource.SearchServiceResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/service");
    const method = "GET";
    const params = {
      space_id: _req["space_id"],
      query: _req["query"],
      service_type: _req["service_type"],
      page_num: _req["page_num"],
      page_size: _req["page_size"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/agent/user/sessions
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3451258)
   */
  ListUserSessions(
    req: session.ListUserSessionsRequest,
    options?: T
  ): Promise<session.ListUserSessionsResponse> {
    const _req = req;
    const url = this.genBaseURL("/api/agents/v2/agent/user/sessions");
    const method = "GET";
    const params = {
      page_num: _req["page_num"],
      page_size: _req["page_size"],
      session_id: _req["session_id"],
      agent_config_version_id: _req["agent_config_version_id"],
      start_time: _req["start_time"],
      end_time: _req["end_time"],
      status: _req["status"],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agents/v2/datasets/:dataset_id/task_status
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3458343)
   */
  GetKnowledgeTaskStatus(
    req: knowledgebase.GetKnowledgeTaskStatusRequest,
    options?: T
  ): Promise<knowledgebase.GetKnowledgeTaskStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agents/v2/datasets/${_req["dataset_id"]}/task_status`
    );
    const method = "GET";
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/agents/v2/deploy/bits/upsert/agent/version
   *
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/flow.agentsphere.nextserver/api_doc/show_doc?version=1.0.726&endpoint_id=3467133)
   */
  BitsUpsertAgentConfigVersion(
    req: deploy.BitsUpsertAgentVersionRequest,
    options?: T
  ): Promise<deploy.BitsUpsertAgentVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      "/api/agents/v2/deploy/bits/upsert/agent/version"
    );
    const method = "POST";
    const data = {
      agent_config_id: _req["agent_config_id"],
      bits_build_id: _req["bits_build_id"],
      scm_version: _req["scm_version"],
      user: _req["user"],
      description: _req["description"],
      icm_version: _req["icm_version"],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
