// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface GetTraceSessionTrajectoryRequest {
  session_id: string;
}

export interface GetTraceSessionTrajectoryResponse {
  message?: string;
  data?: Record<string, string>;
}

export interface OpsEditTemplateRequest {
  template_id: string;
  name?: string;
  progress_plan?: string;
  exp_sop?: string;
  expired?: boolean;
  edited?: boolean;
  query_template?: string;
  query_template_placeholders?: string;
  scope?: string;
  support_mcps?: string;
}

export interface OpsEditTemplateResponse {
  template: TemplateVersion;
}

export interface OpsListTemplatesRequest {
  template_id?: string;
  creator?: string;
  name?: string;
  share_id?: string;
  session_id?: string;
  run_session_id?: string;
  page?: number;
  page_size?: Int64;
}

export interface OpsListTemplatesResponse {
  templates: Array<TemplateVersion>;
}

export interface TemplateVersion {
  template_id: string;
  name: string;
  status: string;
  creator: string;
  session_id: string;
  prompt_content: string;
  prompt_variables: string;
  plan: string;
  plan_steps: string;
  exp_sop: string;
  support_mcps: string;
  label: string;
  expired: boolean;
  edited: boolean;
  created_at: string;
  updated_at: string;
}
/* eslint-enable */
