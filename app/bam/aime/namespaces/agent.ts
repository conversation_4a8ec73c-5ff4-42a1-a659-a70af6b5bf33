// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as knowledgeset from "./knowledgeset";

export type Int64 = string | number;

export enum AgentConfigStatus {
  AgentConfigStatusCanary = "canary",
  AgentConfigStatusCreated = "created",
  AgentConfigStatusOnline = "online",
  AgentConfigStatusUnknown = "unknown",
}

export enum AgentConfigType {
  AgentConfigTypeAbTest = "abtest",
  AgentConfigTypeBase = "base",
  AgentConfigTypeFeature = "feature",
  AgentConfigTypeUnknown = "unknown",
  AgentConfigTypeUser = "user",
}

export enum RuntimeConfigType {
  RuntimeConfigTypeDocker = "docker",
  RuntimeConfigTypeStratoCube = "stratocube",
  RuntimeConfigTypeUnknown = "unknown",
}

export interface Agent {
  id: string;
  name: string;
  description: string;
  creator: string;
  created_at: string;
  updated_at: string;
}

export interface AgentConfig {
  id: string;
  agent_id: string;
  type: AgentConfigType;
  name: string;
  description: string;
  creator: string;
  created_at: string;
  updated_at: string;
}

export interface AgentConfigVersion {
  id: string;
  agent_config_id: string;
  description: string;
  creator: string;
  version: number;
  enabled: boolean;
  status: AgentConfigStatus;
  runtime_config: RuntimeConfig;
  custom_config: string;
  prompt_config: PromptConfig;
  created_at: string;
  updated_at: string;
  knowledgeset_config?: KnowledgesetConfig;
  knowledge_set?: Array<knowledgeset.Knowledgeset>;
  knowledge_set_version?: Array<knowledgeset.KnowledgesetVersion>;
  deploy_id?: string;
}

export interface CopyAgentConfigVersionRequest {
  source_id: string;
  agent_config_id: string;
  description?: string;
}

export interface CopyAgentConfigVersionResponse {
  agent_config_version_id: string;
}

export interface CreateAgentConfigRequest {
  agent_id: string;
  type: AgentConfigType;
  name: string;
  description: string;
}

export interface CreateAgentConfigResponse {
  AgentConfig: AgentConfig;
}

export interface CreateAgentConfigVersionRequest {
  agent_config_id: string;
  description: string;
  enabled: boolean;
  runtime_config: RuntimeConfig;
  custom_config: string;
  prompt_config: PromptConfig;
  knowledge_set_config?: KnowledgesetConfig;
}

export interface CreateAgentConfigVersionResponse {
  agent_config_version: AgentConfigVersion;
}

export interface CreateAgentRequest {
  name: string;
  description: string;
}

export interface CreateAgentResponse {
  agent: Agent;
}

export interface DeleteAgentConfigRequest {
  agent_config_id: string;
}

export interface DeleteAgentConfigResponse {}

export interface DeleteAgentRequest {
  agent_id: string;
}

export interface DeleteAgentResponse {}

export interface DeployAgentConfigVersionRequest {
  agent_config_version_id: string;
  status: AgentConfigStatus;
}

export interface DeployAgentConfigVersionResponse {
  agent_config_version: AgentConfigVersion;
}

export interface GetAgentConfigRequest {
  agent_config_id: string;
  /** 获取指定版本的具体配置内容 */
  version?: number;
  /** 获取最新版本，如果 Version 不为空则以 Version 为准, 如果都为空，则不返回具体配置 */
  latest?: boolean;
  /** 获取 Online 状态的具体配置内容 */
  online?: boolean;
}

export interface GetAgentConfigResponse {
  agent_config: AgentConfig;
  agent_config_version?: AgentConfigVersion;
}

export interface GetAgentConfigVersionRequest {
  agent_config_version_id: string;
}

export interface GetAgentConfigVersionResponse {
  agent_config_version: AgentConfigVersion;
}

export interface GetAgentRequest {
  agent_id: string;
}

export interface GetAgentResponse {
  agent: Agent;
}

export interface KnowledgesetConfig {
  knowledge_sets: Array<KnowledgesetMetadata>;
}

export interface KnowledgesetMetadata {
  knowledge_set_id: string;
  knowledge_set_version_id: string;
  knowledge_set_type: string;
}

export interface ListAgentConfigsRequest {
  agent_id?: string;
  /** 支持一组 Type 用逗号间隔 */
  type?: string;
  name?: string;
  creator?: string;
  page_num: Int64;
  page_size: Int64;
}

export interface ListAgentConfigsResponse {
  agent: Agent;
  agent_configs: Array<AgentConfig>;
  total: Int64;
}

export interface ListAgentConfigVersionsRequest {
  agent_config_id: string;
  creator?: string;
  enabled?: boolean;
  page_num: Int64;
  page_size: Int64;
  /** 支持一组 Status 用逗号间隔 */
  status?: string;
}

export interface ListAgentConfigVersionsResponse {
  agent_config: AgentConfig;
  agent_config_versions: Array<AgentConfigVersion>;
  total: Int64;
}

export interface ListAgentsRequest {
  creator?: string;
  /** 根据名字进行匹配 */
  name?: string;
  page_num: Int64;
  page_size: Int64;
}

export interface ListAgentsResponse {
  agents: Array<Agent>;
  total: Int64;
}

export interface OrchestrationConfig {
  /** 0 不限制 */
  max_concurrency: number;
  max_poolsize: number;
  runtime_stop_timeout: string;
  runtime_delete_timeout: string;
}

export interface PromptConfig {
  prompts: Array<PromptConfigMetadata>;
}

export interface PromptConfigMetadata {
  key: string;
  id: string;
  version: number;
}

export interface ResourceQuantity {
  requests: string;
  limits?: string;
}

export interface RuntimeConfig {
  id: string;
  psm: string;
  type: RuntimeConfigType;
  inject_openai_token: boolean;
  image?: string;
  envs?: Record<string, string>;
  port?: number;
  runtime_resource_quota?: RuntimeResourceQuota;
  binary_source?: string;
  orchestration_config?: OrchestrationConfig;
  bash_image?: string;
}

export interface RuntimeResourceQuota {
  cpu?: ResourceQuantity;
  mem?: ResourceQuantity;
  persist_workspace?: ResourceQuantity;
  storage_class?: string;
}

export interface UpdateAgentConfigRequest {
  agent_config_id: string;
  name?: string;
  description?: string;
}

export interface UpdateAgentConfigResponse {
  AgentConfig: AgentConfig;
}

export interface UpdateAgentConfigVersionRequest {
  agent_config_version_id: string;
  enabled?: boolean;
  description?: string;
  runtime_config?: RuntimeConfig;
  custom_config?: string;
  prompt_config?: PromptConfig;
  knowledge_set_config?: KnowledgesetConfig;
  updated_at?: string;
}

export interface UpdateAgentConfigVersionResponse {
  agent_config_version: AgentConfigVersion;
}

export interface UpdateAgentRequest {
  agent_id: string;
  name?: string;
  description?: string;
}

export interface UpdateAgentResponse {
  agent: Agent;
}
/* eslint-enable */
