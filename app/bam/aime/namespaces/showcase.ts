// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as artifact from "./artifact";

export type Int64 = string | number;

export enum ShowcaseCategory {
  Study = 0,
  Code = 1,
  Life = 2,
  Analysis = 3,
}

export interface Showcase {
  id: string;
  title: string;
  description: string;
  image_url: string;
  total_steps: Int64;
  /** in seconds */
  duration: Int64;
  artifacts: Array<ShowcaseArtifact>;
  category: ShowcaseCategory;
}

export interface ShowcaseArtifact {
  type: artifact.ArtifactType;
  name: string;
}
/* eslint-enable */
