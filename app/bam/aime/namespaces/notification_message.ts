// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum MessageStatus {
  MessageStatusRead = "read",
  MessageStatusRecalled = "recalled",
  MessageStatusUnread = "unread",
}

export enum MessageType {
  MessageTypeInvitation = "invitation",
  MessageTypeNotification = "notification",
}

export enum ReceiveType {
  ReceiveTypeAll = "all",
  ReceiveTypeDepartment = "department",
  ReceiveTypePersonal = "personal",
}

export interface CreateNotificationMessageRequest {
  title: string;
  /** 通知内容 */
  content: string;
  receive_config: Array<ReceiveConfig>;
  /** 是否置顶 */
  is_top?: boolean;
  /** 是否发送 lark 通知 */
  send_lark?: boolean;
  link_info?: LinkInfo;
  type: MessageType;
}

export interface CreateNotificationMessageResponse {
  message_id: string;
}

export interface LinkInfo {
  link: string;
  name: string;
}

export interface ListNotificationMessagesRequest {}

export interface ListNotificationMessagesResponse {
  message?: Array<NotificationMessage>;
}

/** 消息 */
export interface NotificationMessage {
  message_id: string;
  title: string;
  /** 通知内容 */
  content: string;
  /** 跳转链接信息 */
  link_info?: LinkInfo;
  /** 消息状态 */
  status: MessageStatus;
  is_top: boolean;
  created_at: Int64;
  creator: string;
  type: MessageType;
}

export interface ReceiveConfig {
  /** 接收类型：个人，全体等 */
  receive_type: ReceiveType;
  receivers?: Array<string>;
}

export interface UpdateNotificationMessageStatusRequest {
  messageid: string;
  status: MessageStatus;
}

export interface UpdateNotificationMessageStatusResponse {}
/* eslint-enable */
