// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as types from "./types";
import * as common from "./common";
import * as permission from "./permission";

export type Int64 = string | number;

/** 激活状态枚举 */
export enum ActiveStatus {
  /** 激活 */
  ACTIVATE = 1,
  /** 取消激活 */
  DEACTIVATE = 2,
}

export enum MCPScope {
  /** 个人 */
  Private = 0,
  /** 公司内公开 */
  Public = 1,
  /** 项目内公开 */
  ProjectPublic = 2,
}

/** MCP工具来源枚举 */
export enum MCPSource {
  /** AIME平台 */
  AIME = 1,
  /** 用户自定义 */
  UserDefine = 2,
  /** 字节云平台 */
  Cloud = 3,
}

export enum MCPSourceTab {
  /** 用户自定义MCP：个人创建+项目内公开 */
  Custom = 0,
  /** 内置MCP：公司内公开 */
  Builtin = 1,
}

/** MCP工具类型枚举 */
export enum MCPType {
  /** STDIO类型 */
  STDIO = 1,
  /** SSE类型 */
  SSE = 2,
  /** StreamableHTTP类型 (二期新增) */
  StreamableHTTP = 3,
  /** CloudSDK类型 (二期新增) */
  CloudSDK = 4,
}

/** 用户配置类型枚举 */
export enum UserConfigType {
  /** 环境变量 */
  ENV = 1,
  /** 请求头 */
  HEADER = 2,
}

export interface CreateCloudSDKAuthTicketRequest {
  /** 目标PSM */
  psm: string;
}

export interface CreateCloudSDKAuthTicketResponse {
  /** 工单链接 */
  ticket_url?: string;
}

export interface CreateMCPRequest {
  /** MCP工具名称 */
  name: string;
  /** MCP工具描述 */
  description: string;
  /** MCP工具图标URL */
  icon_url?: string;
  /** MCP工具参数结构 */
  config: MCPConfig;
  /** 来源 */
  source: MCPSource;
  /** MCP工具类型 */
  type: MCPType;
  /** 是否强制激活 */
  force_active?: boolean;
  /** mcp的唯一id，runtime使用需要 */
  uid?: string;
  /** mcp工具英文名称 */
  en_name?: string;
  /** mcp 工具英文描述 */
  en_description?: string;
  /** 支持的SessionRole列表 (二期新增) */
  session_roles?: Array<types.SessionRole>;
  /** 空间ID */
  space_id?: string;
  /** 可见范围 */
  scope?: MCPScope;
  /** 内置MCP是否默认添加 */
  default_active?: boolean;
}

export interface CreateMCPResponse {
  BaseResp: common.BaseResp;
  mcp: MCP;
}

export interface ListMCPCount {
  /** 公司内公开总数 */
  public_count: Int64;
  /** 个人创建总数 */
  custom_count: Int64;
  /** 已激活总数 */
  activate_count: Int64;
  /** 激活MCP上限 */
  activate_limit: Int64;
}

export interface ListMCPRequest {
  /** 按名称搜索 */
  name?: string;
  /** 按来源列表搜索 */
  sources?: Array<MCPSource>;
  /** 按添加状态搜索 */
  is_active?: boolean;
  /** 按类型列表搜索 */
  types?: Array<MCPType>;
  /** 按SessionRole搜索 (二期新增) */
  session_role?: types.SessionRole;
}

export interface ListMCPResponse {
  mcps: Array<MCP>;
  /** 总数 */
  total: number;
}

export interface ListSpaceMCPRequest {
  /** 按名称搜索 */
  name?: string;
  /** 按来源列表搜索 */
  sources?: Array<MCPSource>;
  /** 按添加状态搜索 */
  is_active?: boolean;
  /** 按类型列表搜索 */
  types?: Array<MCPType>;
  /** 按SessionRole搜索 (二期新增) */
  session_role?: types.SessionRole;
  /** 空间ID */
  space_id?: string;
  /** 起始ID */
  next_id: string;
  /** 限制数量 */
  limit: Int64;
  /** 来源tab */
  tabs: Array<MCPSourceTab>;
}

export interface ListSpaceMCPResponse {
  mcps: Array<MCP>;
  /** 是否还有更多 */
  has_more: boolean;
  next_id: string;
  count: ListMCPCount;
}

/** MCP (Multi-Cloud Platform) 工具定义 */
export interface MCP {
  /** 来源不同的话，ID可能会重复 */
  id: string;
  name: string;
  description: string;
  icon_url: string;
  config: MCPConfig;
  creator: string;
  source: MCPSource;
  created_at: string;
  updated_at: string;
  /** 是否已添加到个人工具 */
  is_active: boolean;
  /** MCP工具类型 */
  type: MCPType;
  /** 是否强制激活 */
  force_active: boolean;
  /** mcp工具英文名称 */
  en_name?: string;
  /** mcp 工具英文描述 */
  en_description?: string;
  /** 支持的SessionRole列表，为null表示支持所有Role (二期新增) */
  session_roles?: Array<types.SessionRole>;
  /** 可见范围 */
  scope?: MCPScope;
  /** 权限列表 */
  permissions?: Array<permission.PermissionAction>;
  /** 用户配置，配置优先级 user_config > config， 当前仅支持env/header，其余后期服务端拓展 */
  user_config?: MCPConfig;
  /** 内置MCP是否默认添加 */
  default_active?: boolean;
}

/** MCP工具参数结构 */
export interface MCPConfig {
  /** 命令 */
  command?: string;
  /** 参数列表 */
  args?: Array<string>;
  /** 环境变量 */
  env?: Record<string, string>;
  /** SSE基础URL */
  base_url?: string;
  /** PSM参数 (二期新增) */
  psm?: string;
  /** 不传字节云Token */
  skip_cloud_jwt_auth?: boolean;
  header?: Record<string, string>;
  /** 需要用户配置的类型列表 */
  required_user_configs?: Array<UserConfigType>;
}

export interface MCPKey {
  id: string;
  source: MCPSource;
}

/** MCP工具信息 */
export interface MCPTool {
  /** 工具名称 */
  name: string;
  /** 工具描述 */
  description: string;
  /** agent看到的工具名称 */
  name_for_agent: string;
}

export interface ModifyMCPActivationRequest {
  /** MCP工具ID */
  id: string;
  /** MCP 工具来源 */
  source: MCPSource;
  /** 激活状态：1-激活 2-取消激活 */
  status: ActiveStatus;
  /** 空间ID */
  space_id?: string;
}

export interface ModifyMCPActivationResponse {
  mcp: MCP;
}

export interface ModifyMCPUserConfigRequest {
  /** MCP工具ID */
  id: string;
  /** MCP 工具来源 */
  source: MCPSource;
  /** 空间ID */
  space_id?: string;
  /** 用户配置，配置优先级 user_config > config， 当前仅支持env/header，其余后期服务端拓展 */
  user_config?: MCPConfig;
}

export interface ModifyMCPUserConfigResponse {
  mcp: MCP;
}

export interface UpdateMCPRequest {
  /** MCP工具ID */
  id: string;
  /** 来源 */
  source: MCPSource;
  /** MCP工具描述 */
  description?: string;
  /** MCP工具图标URL */
  icon_url?: string;
  /** MCP工具参数结构 */
  config?: MCPConfig;
  /** MCP工具名称 */
  name?: string;
  /** MCP工具类型 */
  type?: MCPType;
  /** 是否强制激活 */
  force_active?: boolean;
  /** mcp工具英文名称 */
  en_name?: string;
  /** mcp 工具英文描述 */
  en_description?: string;
  /** 支持的SessionRole列表 (二期新增) */
  session_roles?: Array<types.SessionRole>;
  /** 可见范围 */
  scope?: MCPScope;
  /** 内置MCP是否默认添加 */
  default_active?: boolean;
}

export interface UpdateMCPResponse {
  BaseResp: common.BaseResp;
  mcp: MCP;
}

export interface ValidateActiveMCPsRequest {
  /** 空间ID */
  space_id?: string;
  role?: types.SessionRole;
}

export interface ValidateActiveMCPsResponse {
  /** 是否所有MCP都有效 */
  all_valid: boolean;
  results?: Array<ValidateMCPResult>;
}

export interface ValidateMCPRequest {
  /** MCP工具参数结构 */
  config: MCPConfig;
  /** 来源 */
  source: MCPSource;
  /** MCP工具类型 */
  type: MCPType;
}

export interface ValidateMCPResponse {
  valid: boolean;
}

export interface ValidateMCPResult {
  mcp: MCP;
  valid: boolean;
  code?: Int64;
  msg?: string;
}

export interface ValidateTemplateMCPsRequest {
  /** 模板ID */
  template_id: string;
  /** 空间ID */
  space_id?: string;
  role?: types.SessionRole;
}

export interface ValidateTemplateMCPsResponse {
  /** 是否所有MCP都有效 */
  all_valid: boolean;
  /** 校验结果列表 */
  results?: Array<ValidateMCPResult>;
}
/* eslint-enable */
