// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum BindStatus {
  BindStatusAlreadyBind = "already_bind",
  BindStatusInvalid = "invalid_code",
  BindStatusSuccess = "success",
}

export enum GrantStatus {
  GrantStatusFinish = "finish",
  GrantStatusSuccess = "success",
}

export interface BindInvitationCodeRequest {
  invitation_code: string;
}

export interface BindInvitationCodeResponse {
  status: BindStatus;
}

export interface GetUserInfoRequest {
  user_name: string;
}

export interface GetUserInfoResponse {
  user: User;
}

export interface GetUserSettingsRequest {}

export interface GetUserSettingsResponse {
  settings: UserSettings;
}

export interface GrantAccessRequest {}

export interface GrantAccessResponse {
  status: GrantStatus;
}

/** 邀请码 */
export interface InvitationCode {
  /** 活动id */
  activity_id: string;
  code: string;
  activited: boolean;
  /** 绑定的用户 (激活的人) */
  bind_user?: string;
  /** 所属用户 （拥有这个邀请码的人） */
  owner?: string;
}

export interface KeyboardShortcut {
  mac?: Record<string, Shortcut>;
  windows?: Record<string, Shortcut>;
}

export interface Shortcut {
  keys: Array<string>;
}

export interface UpdateUserSettingsRequest {
  keep_login?: boolean;
  locale?: string;
  keyboard_shortcut?: KeyboardShortcut;
}

export interface UpdateUserSettingsResponse {
  settings: UserSettings;
}

export interface User {
  id?: number;
  name: string;
  username: string;
  en_name?: string;
  avatar_url?: string;
  terminated?: boolean;
}

export interface UserSettings {
  id: string;
  username: string;
  locale: string;
  keep_login: boolean;
  keyboard_shortcut: KeyboardShortcut;
  created_at: string;
  updated_at: string;
}
/* eslint-enable */
