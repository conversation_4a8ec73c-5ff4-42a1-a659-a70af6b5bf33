// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ErrorCode {
  ErrUndefined = 0,
  ErrNoAuth = 1001,
  ErrCheckAuth = 1002,
  ErrInternal = 2001,
  ErrResourceProcessing = 2002,
  ErrModelReqFail = 3003,
  ErrEmbeddingModelNotExist = 3004,
  ErrParamInvalid = 4001,
  ErrExist = 4002,
  ErrOperating = 4003,
  ErrContentTooLong = 4006,
  ErrRateLimitExceeded = 4007,
  ErrQuotaExceeded = 4008,
  ErrLogOut = 4010,
  ErrAccessReject = 4011,
  ErrDowngrade = 4012,
  ErrCountryBlocked = 4013,
  ErrDeregister = 4014,
  ErrNewRiskControl = 4019,
  ErrRecordNotFound = 4020,
  ErrUsageReachLimit = 4021,
  ErrPromptTokensOverLimit = 4022,
  ErrUnknownModel = 4023,
  ErrAgentRunLoopExceeded = 5001,
  ErrAgentRunTimeExceeded = 5002,
  ErrAgentRunQuotaExceeded = 5003,
  ErrAgentRunBusy = 5004,
  ErrContainerStartupTimeout = 5005,
  ErrContainerIsDeleted = 5006,
  ErrInsufficientStock = 6001,
  ErrBindInvitation = 6002,
  ErrNotEnoughPoints = 6003,
  ErrNotInEventTime = 6004,
  ErrReachedUpperLimit = 6005,
  ErrAccountAbnormality = 6006,
  ErrDecryption = 6007,
  ErrNotGetPrizeTime = 6008,
  ErrReviewResultNotReleased = 6009,
  ErrNoQualification = 6010,
  ErrRedemptionLimitReached = 6011,
  ErrSecurityCheckFail = 7001,
  ErrCompetetionInvalid = 7002,
  ErrDuplicateCompetitionRegistration = 7003,
  ErrTeamFull = 7004,
  ErrDuplicateTeamRegistration = 7005,
  ErrTeamNoAuth = 7006,
  ErrInvalidTeam = 7007,
  ErrDuplicateTeamName = 7008,
  ErrInvalidTeamApplication = 7009,
  ErrInvalidTeamMember = 7010,
  ErrExistedEntitlement = 7011,
  ErrCodePreCheckFail = 7012,
  ErrSandboxCreateTimeExceeded = 8001,
  ErrSandboxRunTimeExceeded = 8002,
  ErrSandboxStillRunning = 8003,
  ErrImageReviewUnpass = 8004,
  ErrImageReviewDownload = 8005,
  ErrImageReviewImageRead = 8006,
  ErrImageReviewUpload = 8007,
  ErrImageReviewGetUrl = 8008,
  ErrImageReviewDecode = 8009,
  ErrImageReviewInternal = 8010,
  ErrTextReviewUnpass = 8011,
  ErrSandboxInternalError = 8012,
  ErrTraePayRisk = 9001,
  ErrTraePayUnexpiredOrder = 9002,
  ErrTraePayRegionNotAllowed = 9003,
  ErrTraePayParamInvalid = 9004,
  ErrTraePayEntitlementExists = 9005,
  ErrTraePayInvalidPI = 9006,
  ErrNextAgentReachedUpperLimit = 10001,
  ErrNextSessionStopped = 10002,
  ErrNextAgentNotAllowedUseInternalTool = 10003,
  ErrInternalFatal = 10004,
  ErrNextAgentReachedGlobalLimit = 10005,
  ErrNextAgentGrantAccessEnd = 10006,
  /** MCP 激活数量限制 */
  ErrMCPActiveLimitExceeded = 10007,
  /** 文档不存在 */
  ErrDocumentNotExist = 10008,
  /** 文档类型不支持 */
  ErrDocumentTypeNotSupport = 10009,
  /** MCP配置为空 */
  ErrCodeMCPConfigEmpty = 10010,
  /** MCP调用失败 */
  ErrCodeMCPConnectFailed = 10011,
  /** MCP鉴权失败 */
  ErrCodeMCPPermissionVerificationFailed = 10012,
  /** MCP配置的PSM有误 */
  ErrCodeMCPPSMNotFound = 10013,
}

export interface BaseResp {
  code?: Int64;
  message?: string;
}

export interface SSEResponse {
  event?: string;
  data?: string;
}
/* eslint-enable */
