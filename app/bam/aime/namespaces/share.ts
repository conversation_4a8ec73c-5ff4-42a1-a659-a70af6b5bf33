// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface CallbackAction {
  value?: string;
  tag?: string;
  timezone?: string;
  form_value?: Record<string, string>;
  name?: string;
  input_value?: string;
}

export interface CallbackHeader {
  event_id?: string;
  token?: string;
  create_time?: string;
  event_type?: string;
  tenant_key?: string;
  app_id?: string;
}

export interface PreviewCard {
  type: string;
  data: PreviewCardData;
}

export interface PreviewCardData {
  template_id: string;
  template_variable: PreviewCardTemplateVariable;
}

export interface PreviewCardTemplateVariable {
  title: string;
  content: string;
  url: string;
  creator: string;
}

export interface PreviewInline {
  i18n_title: PreviewTitle;
  image_key?: string;
}

export interface PreviewTitle {
  zh_cn: string;
  en_us: string;
}

export interface ShareContext {
  url: string;
  preview_token: string;
  open_message_id?: string;
}

export interface ShareEvent {
  operator?: ShareOperator;
  context: ShareContext;
  action?: CallbackAction;
}

export interface ShareOperator {
  user_id: string;
  open_id: string;
}

export interface SharePreviewCallbackRequest {
  /** 绑定回调时候需验证：https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/event-subscription-guide/callback-subscription/configure-callback-request-address */
  challenge?: string;
  type?: string;
  event?: ShareEvent;
  header?: CallbackHeader;
}

export interface SharePreviewCallbackResponse {
  challenge?: string;
  inline: PreviewInline;
  card: PreviewCard;
}
/* eslint-enable */
