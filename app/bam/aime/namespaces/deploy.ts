// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface AgentConfigVersionInfo {
  id: string;
  version: number;
  description: string;
}

export interface AgentDeploy {
  id: string;
  agent_config_id: string;
  agent_config_version_info: AgentConfigVersionInfo;
  workflow_id: Int64;
  extra_info?: DeployExtraInfo;
  created_at: string;
  status: string;
  agent_config_version_online_info?: AgentConfigVersionInfo;
  actor: string;
}

export interface BitsUpsertAgentVersionRequest {
  agent_config_id: string;
  bits_build_id: string;
  scm_version: string;
  user: string;
  description: string;
  icm_version: string;
}

export interface BitsUpsertAgentVersionResponse {
  agent_config_version_id: string;
}

export interface BPMAuthCallbackRequest {
  id: string;
  workflow_config_id: string;
  agent_config_id: string;
  agent_config_version_id: string;
  creator: string;
}

export interface BPMAuthCallbackResponse {
  id: number;
  data: BPMAuthCallbackResponseData;
}

export interface BPMAuthCallbackResponseData {
  auth_result: boolean;
}

export interface BPMCanaryCallbackRequest {
  id: Int64;
  deploy_id: string;
  agent_config_id: string;
  agent_config_version_id: string;
  status: string;
}

export interface BPMCanaryCallbackResponse {
  code: number;
}

export interface BPMCancelCanaryCallbackRequest {
  id: Int64;
  deploy_id: string;
  agent_config_id: string;
  agent_config_version_id: string;
}

export interface BPMCancelCanaryCallbackResponse {
  code: number;
}

export interface BPMCloseCallbackRequest {
  id: Int64;
  deploy_id: string;
  agent_config_id: string;
  agent_config_version_id: string;
  comment?: string;
  audit_reject_comment?: string;
}

export interface BPMCloseCallbackResponse {
  code: number;
}

export interface BPMOnlineCallbackRequest {
  id: Int64;
  deploy_id: string;
  agent_config_id: string;
  agent_config_version_id: string;
  skip_canary_comment?: string;
}

export interface BPMOnlineCallbackResponse {
  code: number;
  data: BPMOnlineCallbackResponseData;
}

export interface BPMOnlineCallbackResponseData {
  online_result: boolean;
}

export interface CreateDeployRequest {
  agent_config_version_id: string;
  reviewer: string;
  is_enable_ab: boolean;
  ab_comment: string;
}

export interface CreateDeployResponse {
  deploy_id: string;
  workflow_id: Int64;
}

export interface DeployExtraInfo {
  reviewer?: string;
  enable_ab?: boolean;
  ab_comment?: string;
  skip_canary?: boolean;
  skip_canary_comment?: string;
  close_reason?: string;
  audit_reject_comment?: string;
}

export interface GetAgentDeployListRequest {
  agent_config_id: string;
  page_num: Int64;
  page_size: Int64;
  status?: string;
}

export interface GetAgentDeployListResponse {
  agent_deploys: Array<AgentDeploy>;
  total: Int64;
}

export interface GetDeployProcessInfoRequest {
  deploy_id: string;
}

export interface GetDeployProcessInfoResponse {
  canary_ratio: number;
  success_count: Int64;
  fail_count: Int64;
  running_count: Int64;
  deploy_status: string;
}

export interface GetDeployRequest {
  deploy_id: string;
}

export interface GetDeployResponse {
  deploy: AgentDeploy;
}

export interface GetDeployReviewUserRequest {}

export interface GetDeployReviewUserResponse {
  users: Array<string>;
}

export interface GetIcmVersionRequest {
  version?: string;
  region?: string;
  specific_tag?: string;
  /** agent config type, base/abtest/feature */
  config_type?: string;
  image_type?: string;
}

export interface GetIcmVersionResponse {
  icm_versions: Array<IcmVersion>;
}

export interface GetScmVersionRequest {
  branch?: string;
  /** 逗号分隔, online/offline/test */
  type_list?: string;
  version?: string;
  commit?: string;
}

export interface GetScmVersionResponse {
  scm_versions: Array<ScmVersion>;
}

export interface IcmVersion {
  describe: string;
  image_size: string;
  builder: string;
  image_name: string;
  image_version: string;
  specific_tag: string;
  specific_image_name: string;
  create_time: string;
}

export interface ScmVersion {
  version: string;
  arch: Array<string>;
  type: string;
  create_user: string;
  git_url: string;
  desc: string;
  branch_name: string;
  base_commit_hash: string;
  local_date: string;
  commit_url: string;
  status: string;
  status_aarch64: string;
  create_date: string;
  status_display: string;
  status_display_aarch64: string;
  id: number;
  git_tag?: string;
  tags: Array<string>;
}
/* eslint-enable */
