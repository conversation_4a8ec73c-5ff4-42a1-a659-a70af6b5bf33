// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as permission from "./permission";
import * as dev_resource from "./dev_resource";

export type Int64 = string | number;

export enum SpaceStatus {
  SpaceStatusActive = "active",
  SpaceStatusDeleted = "deleted",
  SpaceStatusInactive = "inactive",
  SpaceStatusUninit = "uninit",
}

export enum SpaceType {
  SpaceTypePersonal = "personal",
  SpaceTypeProject = "project",
}

export interface AddSpaceMemberRequest {
  space_id: string;
  /** 成员列表 */
  members: Array<SpaceMember>;
}

export interface AddSpaceMemberResponse {
  success: boolean;
}

export interface BasicInfo {
  name: StringInMultiLang;
  description?: StringInMultiLang;
}

export interface CreateSpaceRequest {
  name: string;
  name_en?: string;
  description?: string;
  space_config?: SpaceConfig;
}

export interface CreateSpaceResponse {
  space: Space;
}

export interface DeleteSpaceRequest {
  space_id: string;
}

export interface DeleteSpaceResponse {
  success: boolean;
}

export interface GetSpaceRequest {
  space_id?: string;
  type?: permission.ResourceType;
  external_id?: string;
  need_members?: boolean;
}

export interface GetSpaceResponse {
  space: Space;
  have_permission?: boolean;
  is_not_found?: boolean;
}

export interface InitSpaceRequest {
  /** 项目空间 ID */
  space_id: string;
  /** 空间基本信息 */
  basic: BasicInfo;
  /** 空间成员 */
  members?: Array<SpaceMember>;
  /** 文档信息 */
  lark_doc_config?: dev_resource.LarkDocConfig;
  /** 仓库信息 */
  repos?: Array<dev_resource.CodeRepo>;
  /** 项目信息 */
  services?: Array<dev_resource.Service>;
  /** 其余空间配置信息 */
  platform_config?: dev_resource.PlatformConfig;
}

export interface InitSpaceResponse {}

export interface ListAllSpacesRequest {
  page_num: Int64;
  page_size: Int64;
}

export interface ListAllSpacesResponse {
  spaces: Array<Space>;
  total: Int64;
}

export interface ListSpaceMembersRequest {
  space_id: string;
  page_num: Int64;
  page_size: Int64;
  /** 过滤角色 */
  role?: permission.PermissionRole;
  /** 过滤类型 */
  type?: permission.PermissionType;
}

export interface ListSpaceMembersResponse {
  members: Array<SpaceMember>;
  total: Int64;
}

export interface ListUserSpacesRequest {
  start_id?: string;
  limit?: Int64;
  type?: SpaceType;
}

export interface ListUserSpacesResponse {
  spaces: Array<Space>;
  next_id: string;
}

export interface RemoveSpaceMemberRequest {
  space_id: string;
  /** 成员列表 */
  members: Array<SpaceMember>;
}

export interface RemoveSpaceMemberResponse {
  success: boolean;
}

/** 数据结构定义 */
export interface Space {
  id: string;
  name: string;
  name_en?: string;
  description?: string;
  creator: string;
  /** 空间类型 */
  type: SpaceType;
  status: SpaceStatus;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  /** 空间用户列表(可选) */
  members?: Array<SpaceMember>;
  dataset_id: string;
  space_config?: SpaceConfig;
  /** 权限列表 */
  permission_actions?: Array<permission.PermissionAction>;
}

export interface SpaceBaseConfig {
  /** 空间 session 默认可见性，默认 false 不可见，调整后只对后续新创建的 session 生效，之前创建的按照之前的配置决定 */
  session_visibility?: boolean;
}

export interface SpaceConfig {
  base_config?: SpaceBaseConfig;
}

export interface SpaceMember {
  /** member类型 */
  type: permission.PermissionType;
  name: string;
  role: permission.PermissionRole;
}

export interface StringInMultiLang {
  cn?: string;
  en?: string;
}

export interface UpdateSpaceRequest {
  space_id: string;
  name?: string;
  name_en?: string;
  description?: string;
  space_config?: SpaceConfig;
}

export interface UpdateSpaceResponse {
  space: Space;
}
/* eslint-enable */
