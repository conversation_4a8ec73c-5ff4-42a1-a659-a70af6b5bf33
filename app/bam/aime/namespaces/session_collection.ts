// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as types from "./types";
import * as session from "./session";

export type Int64 = string | number;

export enum Order {
  OrderAsc = "asc",
  OrderDesc = "desc",
}

export enum SessionsCollectionFeedbackType {
  SessionsCollectionFeedbackTypeUseful = "useful",
  SessionsCollectionFeedbackTypeUseless = "useless",
}

export enum SessionsCollectionStatus {
  SessionsCollectionStatusClosed = "closed",
  SessionsCollectionStatusCreated = "created",
  SessionsCollectionStatusFinished = "completed",
  SessionsCollectionStatusNoticed = "noticed",
  SessionsCollectionStatusRunning = "running",
}

export interface CloseSessionCollectionsRequest {
  session_collection_ids: Array<string>;
}

export interface CloseSessionCollectionsResponse {}

export interface DownloadSessionCollectionsRequest {
  status?: SessionsCollectionStatus;
  creator?: string;
  content?: string;
  order_by_created_at?: boolean;
  order_by_updated_at?: boolean;
  order?: Order;
  session_collection_ids?: Array<string>;
}

export interface DownloadSessionCollectionsResponse {
  lark_url: string;
}

export interface FilterSessionCollectionsRequest {
  page_num: Int64;
  page_size: Int64;
  status?: SessionsCollectionStatus;
  creator?: string;
  content?: string;
  order_by_created_at?: boolean;
  order_by_updated_at?: boolean;
  order?: Order;
  session_collection_ids?: Array<string>;
}

export interface FilterSessionCollectionsResponse {
  session_collections?: Array<SessionCollection>;
  total: Int64;
}

export interface ListNotificationTemplatesRequest {}

export interface ListNotificationTemplatesResponse {
  templates?: Array<NotificationTemplate>;
}

export interface ListSessionCollectionsRequest {
  page_num: Int64;
  page_size: Int64;
  status?: SessionsCollectionStatus;
  creator?: string;
  content?: string;
  order_by_created_at?: boolean;
  order_by_updated_at?: boolean;
  order?: Order;
  session_collection_ids?: Array<string>;
}

export interface ListSessionCollectionsResponse {
  session_collections?: Array<SessionCollection>;
  total: Int64;
}

export interface NotificationTemplate {
  id?: string;
  title?: string;
  content?: string;
}

export interface RunSessionCollectionsRequest {
  session_collection_ids: Array<string>;
  role?: types.SessionRole;
}

export interface RunSessionCollectionsResponse {}

export interface SendSessionCollectionNotificationRequest {
  session_collection_ids?: Array<string>;
  notification_template?: NotificationTemplate;
}

export interface SendSessionCollectionNotificationResponse {}

export interface SendSessionCollectionRunNotificationRequest {
  notifications?: Array<SessionCollectionRunNotification>;
}

export interface SendSessionCollectionRunNotificationResponse {}

/** Session需求采集 */
export interface SessionCollection {
  id?: string;
  status?: SessionsCollectionStatus;
  creator?: string;
  content?: string;
  attachments?: Array<session.AttachmentRequired>;
  agree_home_display?: boolean;
  run_list?: Array<SessionCollectionRun>;
  created_at: string;
  updated_at: string;
}

export interface SessionCollectionFeedback {
  type?: SessionsCollectionFeedbackType;
  content?: string;
}

export interface SessionCollectionRun {
  id?: string;
  session_id?: string;
  creator?: string;
  replay_id?: string;
  noticed?: boolean;
  /** personal/group */
  notice_way?: string;
  session_collection_id?: string;
  session_role?: types.SessionRole;
  feedback?: SessionCollectionFeedback;
}

export interface SessionCollectionRunNotification {
  session_collection_run_id?: string;
  /** user/group */
  notice_way?: string;
}

export interface SessionsCollectionRequest {
  content: string;
  attachments?: Array<session.AttachmentRequired>;
  agree_home_display?: boolean;
}

export interface SessionsCollectionResponse {}
/* eslint-enable */
