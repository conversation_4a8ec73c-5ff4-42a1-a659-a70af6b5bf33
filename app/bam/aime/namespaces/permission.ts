// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum PermissionAction {
  /** Agent模块 */
  PermissionActionAgentCreate = "agent.create",
  PermissionActionAgentDelete = "agent.delete",
  PermissionActionAgentRead = "agent.read",
  PermissionActionAgentUpdate = "agent.update",
  PermissionActionKnowledgebaseCreate = "knowledgebase.create",
  PermissionActionKnowledgebaseDelete = "knowledgebase.delete",
  /** Knowledgebase 模块 */
  PermissionActionKnowledgebaseRead = "knowledgebase.read",
  PermissionActionKnowledgebaseUpdate = "knowledgebase.update",
  /** MCP 模块 */
  PermissionActionMCPCreate = "mcp.create",
  PermissionActionMCPDelete = "mcp.delete",
  PermissionActionMCPRead = "mcp.read",
  PermissionActionMCPUpdate = "mcp.update",
  PermissionActionSessionAllFile = "session.all_file",
  PermissionActionSessionChat = "session.chat",
  /** Session 模块 */
  PermissionActionSessionCreate = "session.create",
  PermissionActionSessionDelete = "session.delete",
  PermissionActionSessionDownload = "session.download",
  PermissionActionSessionFeedback = "session.feedback",
  PermissionActionSessionMCPTraceRead = "session.mcp_trace.read",
  PermissionActionSessionRead = "session.read",
  PermissionActionSessionShareCreate = "session.share.create",
  PermissionActionSessionTemplateCreate = "session.template.create",
  PermissionActionSessionTraceRead = "session.trace.read",
  PermissionActionSessionUpdate = "session.update",
  PermissionActionSessionVSCodeOpen = "session.vscode.open",
  PermissionActionSessionVisualization = "session.visualization",
  /** Space 模块 */
  PermissionActionSpaceCreate = "space.create",
  PermissionActionSpaceDelete = "space.delete",
  PermissionActionSpaceDevResourceUpdate = "space.dev_resource_update",
  PermissionActionSpaceRead = "space.read",
  PermissionActionSpaceUpdate = "space.update",
  PermissionActionTemplateCreate = "template.create",
  PermissionActionTemplateDelete = "template.delete",
  /** Template 模块 */
  PermissionActionTemplateRead = "template.read",
  PermissionActionTemplateUpdate = "template.update",
  PermissionActionUnknown = "unknown",
}

export enum PermissionRole {
  PermissionRoleUnknown = 0,
  PermissionRoleVisitor = 1000,
  PermissionRoleMember = 2000,
  PermissionRoleMananger = 3000,
}

export enum PermissionType {
  PermissionTypeDepartment = "department",
  PermissionTypeServiceAccount = "service_accout",
  PermissionTypeSpace = "space",
  PermissionTypeUnknown = "unknown",
  PermissionTypeUser = "user",
}

export enum ResourceStatus {
  ResourceStatusPrivate = "private",
  ResourceStatusPublic = "public",
  ResourceStatusUnknown = "unknown",
}

export enum ResourceType {
  ResourceTypeArtifact = "artifact",
  ResourceTypeKnowledgebase = "knowledgebase",
  ResourceTypeMCP = "mcp",
  ResourceTypeSession = "session",
  ResourceTypeSpace = "space",
  ResourceTypeTemplate = "template",
  ResourceTypeUnknown = "unknown",
}

export interface GetUserResourcePermissionRequest {
  /** User 取当前登陆的用户
ID 不存在时，使用 ExternalID + Type 的组合查询资源, 比如查当前用户所在空间的权限，external_id 为 space_id,type 为 space, 也可以直接使用 resource id */
  resource_id?: string;
  external_id?: string;
  type?: ResourceType;
}

export interface GetUserResourcePermissionResponse {
  resource: Resource;
}

export interface Group {
  resource_id: string;
  group_id: string;
}

export interface Resource {
  id: string;
  type: ResourceType;
  external_id: string;
  owner: string;
  status: ResourceStatus;
  created_at: string;
  updated_at: string;
  /** 关联数据，按需返回 */
  groups?: Array<Group>;
  /** 一个用户可能存在多个不同的角色，不同的角色有不同的权限点取并集 */
  permissions?: Array<ResourcePermission>;
}

export interface ResourcePermission {
  id: string;
  resource_id: string;
  resource_type: ResourceType;
  type: PermissionType;
  /** Usernme / DepartmentName / SpaceID */
  external_id: string;
  role: PermissionRole;
  created_at: string;
  updated_at: string;
  actions?: Array<PermissionAction>;
}
/* eslint-enable */
