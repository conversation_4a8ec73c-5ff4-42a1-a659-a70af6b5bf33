// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum KnowledgesetVersionStatus {
  KnowledgeVersionStatusCreated = "created",
  KnowledgeVersionStatusDisable = "disable",
  KnowledgeVersionStatusOnline = "online",
}

export interface CheckEnableIfRequest {
  enable_if_content: string;
}

export interface CheckEnableIfResponse {
  is_valid: boolean;
  err_message?: string;
}

export interface CopyKnowledgesetVersionRequest {
  source_id: string;
}

export interface CopyKnowledgesetVersionResponse {
  knowledge_version_id: string;
}

export interface CreateKnowledgeRequest {
  knowledge_set_id: string;
  knowledge_version_id: string;
  key: string;
  title: string;
  content: string;
  enable_if: string;
  use_when?: string;
  tags: Array<string>;
  recall_method: Array<string>;
}

export interface CreateKnowledgeResponse {
  knowledge_id: string;
}

export interface CreateKnowledgesetRequest {
  name: string;
  description: string;
  type: string;
  key: string;
  tags: Array<string>;
}

export interface CreateKnowledgesetResponse {
  knowledge_set_id: string;
}

export interface CreateKnowledgesetVersionRequest {
  knowledge_set_id: string;
  description: string;
  use_when?: string;
}

export interface CreateKnowledgesetVersionResponse {
  knowledge_set_version_id: string;
}

export interface DeleteKnowledgeRequest {
  knowledge_id: string;
}

export interface DeleteKnowledgeResponse {
  knowledge_id: string;
}

export interface DeleteKnowledgesetRequest {
  knowledge_set_id: string;
}

export interface DeleteKnowledgesetResponse {
  knowledge_set_id: string;
}

export interface DeleteKnowledgesetVersionRequest {
  knowledge_set_version_id: string;
}

export interface DeleteKnowledgesetVersionResponse {
  knowledge_set_version_id: string;
}

export interface GetKnowledgeRequest {
  knowledge_id: string;
}

export interface GetKnowledgeResponse {
  knowledge: Knowledge;
}

export interface GetKnowledgesetByIDsRequest {
  ids: string;
}

export interface GetKnowledgesetMetadataConfRequest {}

export interface GetKnowledgesetMetadataConfResponse {
  knowledge_set_types?: Array<KnowledgetsetTypesMeta>;
  knowledge_tags?: Record<string, Array<string>>;
  knowledge_set_tags?: Record<string, Array<string>>;
  knowledge_recall_method?: Array<KnowledgeRecallMethod>;
}

export interface GetKnowledgesetRequest {
  knowledge_set_id: string;
}

export interface GetKnowledgesetResponse {
  knowledge_set: Knowledgeset;
}

export interface GetKnowledgesetVersionRequest {
  knowledge_set_version_id: string;
}

export interface GetKnowledgesetVersionResponse {
  knowledge_set_version: KnowledgesetVersion;
}

export interface Knowledge {
  id: string;
  knowledge_set_id: string;
  knowledge_version_id: string;
  key: string;
  title: string;
  content: string;
  enable_if: string;
  use_when: string;
  tags: Array<string>;
  creator: string;
  created_at: string;
  updated_at: string;
  recall_method?: Array<string>;
}

export interface KnowledgeRecallMethod {
  name: string;
  value: string;
}

export interface Knowledgeset {
  id: string;
  name: string;
  description: string;
  creator: string;
  last_updater: string;
  type: string;
  created_at: string;
  updated_at: string;
  key: string;
  type_name?: string;
  tags?: Array<string>;
}

export interface KnowledgesetVersion {
  id: string;
  knowledge_set_id: string;
  version: string;
  description: string;
  status: KnowledgesetVersionStatus;
  creator: string;
  last_updater: string;
  created_at: string;
  updated_at: string;
  relation_agents?: Array<RelationAgent>;
  knowledges?: Array<Knowledge>;
  use_when?: string;
}

export interface KnowledgetsetTypesMeta {
  name: string;
  value: string;
}

export interface ListKnowledgeRequest {
  knowledge_set_version_id: string;
  tag?: string;
}

export interface ListKnowledgeResponse {
  knowledges: Array<Knowledge>;
}

export interface ListKnowledgesetRequest {
  id?: string;
  type?: string;
  name?: string;
  creator?: string;
  page_num: Int64;
  page_size: Int64;
}

export interface ListKnowledgesetResponse {
  knowledge_sets: Array<Knowledgeset>;
  total: Int64;
}

export interface ListKnowledgesetVersionRequest {
  knowledge_set_id: string;
  status?: string;
  page_num: Int64;
  page_size: Int64;
}

export interface ListKnowledgesetVersionResponse {
  knowledge_set_versions: Array<KnowledgesetVersion>;
  total: Int64;
}

export interface RelationAgent {
  agent_id: string;
  agent_name: string;
  agent_config_id: string;
  agent_config_type: string;
  agent_config_version_id: string;
  agent_config_version_status: string;
}

export interface UpdateKnowledgeRequest {
  knowledge_id: string;
  key: string;
  title: string;
  content: string;
  enable_if: string;
  use_when?: string;
  tags: Array<string>;
  recall_method: Array<string>;
}

export interface UpdateKnowledgeResponse {
  knowledge_id: string;
}

export interface UpdateKnowledgesetRequest {
  knowledge_set_id: string;
  name: string;
  description: string;
  type: string;
  key: string;
  tags: Array<string>;
}

export interface UpdateKnowledgesetResponse {
  knowledge_set_id: string;
}

export interface UpdateKnowledgesetVersionRequest {
  knowledge_set_version_id: string;
  knowledge_set_id: string;
  description?: string;
  status?: string;
  knowledges?: Array<UpdateKnowledgeWitVersion>;
  use_when?: string;
}

export interface UpdateKnowledgesetVersionResponse {
  knowledge_set_version_id: string;
}

export interface UpdateKnowledgeWitVersion {
  knowledge_id?: string;
  key: string;
  title: string;
  content: string;
  enable_if: string;
  use_when?: string;
  tags: Array<string>;
  recall_method?: Array<string>;
}
/* eslint-enable */
