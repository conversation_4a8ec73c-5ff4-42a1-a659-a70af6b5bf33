// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as event from "./event";

export type Int64 = string | number;

export enum ArtifactSource {
  ArtifactSourceAgent = "agent",
  ArtifactSourceUser = "user",
}

export enum ArtifactStatus {
  ArtifactStatusCompleted = "completed",
  ArtifactStatusCreating = "creating",
  ArtifactStatusDraft = "draft",
}

export enum ArtifactType {
  ArtifactTypeCode = "code",
  ArtifactTypeFile = "file",
  ArtifactTypeImage = "image",
  ArtifactTypeLink = "link",
  ArtifactTypeLogs = "logs",
  ArtifactTypeProject = "project",
  ArtifactTypeResult = "result",
}

export enum GitChangeType {
  ChangeTypeAdded = "added",
  ChangeTypeDeleted = "deleted",
  ChangeTypeModified = "modified",
  ChangeTypeRenamed = "renamed",
}

export interface Artifact {
  id: string;
  type: ArtifactType;
  status: ArtifactStatus;
  source: ArtifactSource;
  key: string;
  version: number;
  file_metas: Array<FileMeta>;
  created_at: string;
  metadata: string;
  history_version_artifacts: Array<HistoryVersionArtifact>;
}

export interface ArtifactFiles {
  artifact_id: string;
  paths: Array<string>;
}

export interface CreateArtifactRequest {
  session_id: string;
  /** 目前对前端支持 file, code, image 类型 */
  type: ArtifactType;
  key: string;
  metadata: string;
  version?: number;
  file_metas?: string;
}

export interface CreateArtifactResponse {
  artifact: Artifact;
}

export interface DiffFileContent {
  path: string;
  /** Enum values: `added` / `modified` / `deleted` / `renamed`. */
  change_type: GitChangeType;
  from_path: string;
  /** Deprecated. Use FromId instead. */
  from_commit: string;
  from_mode: number;
  to_path: string;
  /** Deprecated. Use ToId instead. */
  to_commit: string;
  to_mode: number;
  is_binary: boolean;
  raw_patch: string;
  from_id?: string;
  to_id?: string;
  /** Indicates the patch was pruned since it surpassed a hard limit. */
  too_large?: boolean;
}

export interface DiffFileMeta {
  path: string;
  /** Enum values: `added` / `modified` / `deleted` / `renamed`. */
  change_type: GitChangeType;
  from_path: string;
  /** Deprecated. Use FromId instead. */
  from_commit: string;
  from_mode: number;
  to_path: string;
  /** Deprecated. Use ToId instead. */
  to_commit: string;
  to_mode: number;
  is_binary: boolean;
  lines_inserted: Int64;
  lines_deleted: Int64;
  from_id?: string;
  to_id?: string;
}

export interface DownloadArtifactBatchRequest {
  artifacts: Array<ArtifactFiles>;
  session_id?: string;
  replay_id?: string;
}

export interface DownloadArtifactFileRequest {
  artifact_id: string;
  path: string;
  /** 是否返回原始文件内容，默认会替换文件内部分内容，如 ::cite 标签 */
  raw?: boolean;
  /** 流式返回 */
  stream?: boolean;
}

/** 文件内容 */
export interface FileContent {
  path?: string;
  content?: string;
  size?: Int64;
  is_binary?: boolean;
}

export interface FileMeta {
  name: string;
  size: Int64;
  content: string;
  lark_token: string;
  imagex_uri: string;
  /** 目前一个 Artifact 可能会包含不同的业务类型的文件, 以该类型为准 */
  type: ArtifactType;
  /** 子类型，如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是Link，则对应的是 link 类型（参考 ArtifactLinkSubType） */
  sub_type: string;
  link_content?: LinkContent;
  patch_file_result?: Array<event.FilePatch>;
  /** 默认缩放比例的图片链接 */
  imagex_resize_url?: string;
}

/** 版本下的文件树节点 */
export interface FileNode {
  path?: string;
  name?: string;
  is_dir?: boolean;
  is_leaf?: boolean;
  size?: Int64;
  /** 目录才有 */
  children?: Array<FileNode>;
}

export interface GetArtifactRequest {
  artifact_id: string;
}

export interface GetArtifactResponse {
  artifact: Artifact;
}

export interface HistoryVersionArtifact {
  id: string;
  version: number;
  file_metas: Array<FileMeta>;
  metadata: string;
}

export interface LinkContent {
  url?: string;
  title?: string;
  description?: string;
}

export interface ListArtifactsRequest {
  session_id?: string;
  replay_id?: string;
  display?: boolean;
}

export interface ListArtifactsResponse {
  artifacts: Array<Artifact>;
}

export interface ProjectArtifactDiffRequest {
  session_id?: string;
  replay_id?: string;
  artifact_key: string;
  /** "2" 或 "2..5" */
  revision: string;
  /** true: 返回 DiffFilesContent，false: 返回 DiffFilesMeta */
  detail?: boolean;
  file_path?: string;
}

export interface ProjectArtifactDiffResponse {
  diff_files_meta: Array<DiffFileMeta>;
  diff_files_content: Array<DiffFileContent>;
}

export interface ProjectArtifactFilesContentRequest {
  session_id?: string;
  replay_id?: string;
  artifact_key: string;
  /** "2" 或 "2..5" */
  version: string;
  file_path_list: Array<string>;
  is_preview_binary?: boolean;
}

export interface ProjectArtifactFilesContentResponse {
  files_content: Array<FileContent>;
}

export interface ProjectArtifactFilesRequest {
  session_id?: string;
  replay_id?: string;
  artifact_key: string;
  /** "2" 或 "2..5" */
  version: string;
  file_path: string;
  depth?: number;
}

export interface ProjectArtifactFilesResponse {
  files: Array<FileNode>;
}

export interface RetrieveArtifactFilesRequest {
  artifact_id: string;
  files: Array<string>;
  preview?: boolean;
}

export interface RetrieveArtifactFilesResponse {
  files: Array<FileMeta>;
}

export interface UpdateArtifactFileRequest {
  artifact_id: string;
  path: string;
  /** UploadLark 上传 Lark 会自动给 Session Owner 授权 */
  upload_lark?: boolean;
  /** GrantPermission 会给该 Artifact 所属 Session 的 Owner 授权 */
  grant_permission?: boolean;
}

export interface UpdateArtifactFileResponse {
  artifact: Artifact;
}

export interface UpdateArtifactRequest {
  artifact_id: string;
  status?: ArtifactStatus;
  metadata?: string;
  session_id?: string;
}

export interface UpdateArtifactResponse {
  artifact: Artifact;
}

export interface UploadArtifactRequest {
  artifact_id: string;
  path: string;
  content: Blob;
}

export interface UploadArtifactResponse {
  artifact: Artifact;
}

export interface UploadArtifactStreamRequest {
  artifact_id: string;
  path: string;
  size: Int64;
}

export interface UploadImageByURLRequest {
  urls: Array<string>;
  session_id: string;
}

export interface UploadImageByURLResponse {
  artifacts: Array<Artifact>;
}
/* eslint-enable */
