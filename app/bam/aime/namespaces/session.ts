// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as mention from "./mention";
import * as event from "./event";
import * as types from "./types";
import * as mcp from "./mcp";
import * as permission from "./permission";
import * as showcase from "./showcase";

export type Int64 = string | number;

export enum CanNotResumeReason {
  CanNotResumeReasonDeleted = "deleted",
  CanNotResumeReasonExpired = "expired",
  CanNotResumeReasonNotAllowed = "not_allowed",
  CanNotResumeReasonUnknown = "unknown",
}

export enum ListFilterType {
  /** 空间内自己创建的 */
  SelfCreated = 0,
  /** 空间内公开的 */
  SpacePublic = 1,
}

export enum SessionScope {
  Unknown = 0,
  /** 个人 */
  Private = 1,
  /** 公司内公开 */
  Public = 2,
  /** 项目内公开 */
  ProjectPublic = 3,
}

export enum SessionStatus {
  SessionStatusAbnormal = "abnormal",
  SessionStatusCanceled = "canceled",
  SessionStatusClosed = "closed",
  SessionStatusCreated = "created",
  SessionStatusError = "error",
  SessionStatusIdle = "idle",
  SessionStatusRunning = "running",
  SessionStatusStopped = "stopped",
  SessionStatusWaiting = "waiting",
}

export enum SessionTab {
  /** 全部会话 */
  All = 0,
  /** 收藏会话 */
  Starred = 1,
  /** 未收藏会话 */
  UnStarred = 2,
}

export enum TemplateCategory {
  TemplateCategoryAnalysis = "Analysis",
  TemplateCategoryCode = "Code",
  TemplateCategoryProjectAsk = "ProjectAsk",
  TemplateCategoryQuality = "Quality",
  TemplateCategoryStudy = "Study",
  TemplateCategoryTroubleshooting = "Troubleshooting",
  TemplateCategoryWrite = "Write",
}

export enum TemplateLabel {
  TemplateLabelAlgorithm = "Algorithm",
  TemplateLabelClient = "Client",
  TemplateLabelData = "Data",
  TemplateLabelDesign = "Design",
  TemplateLabelFrontend = "Frontend",
  TemplateLabelGeneral = "General",
  TemplateLabelOperations = "Operations",
  TemplateLabelOther = "Other",
  TemplateLabelPMO = "PMO",
  TemplateLabelProduct = "Product",
  TemplateLabelQuality = "Quality",
  TemplateLabelServer = "Server",
}

export enum TemplatePlanStepStatus {
  /** 未生成执行规划 */
  Default = 0,
  /** 当前模板无需生成执行规划（比如因为用户编辑过模板） */
  None = 1,
  /** 当前模板正在生成执行规划 */
  Generating = 2,
  /** 生成结束 */
  Generated = 3,
}

export enum TemplateScope {
  /** 私有模板 */
  Private = 0,
  /** 公司内公开模板 */
  Public = 1,
  /** 共享模板 */
  Shared = 2,
  /** 官方模板 */
  Official = 3,
  /** 项目内公开模板 */
  ProjectPublic = 4,
}

export enum TemplateSource {
  /** 全部模板 */
  All = 0,
  /** 我的模板 */
  My = 1,
  /** 收藏模板 */
  Star = 2,
  /** 项目内模板 */
  Project = 3,
}

export enum TemplateStatus {
  /** 草稿状态，此时对用户不可见 */
  Draft = 0,
  /** 生成经验中，此时用户可见，但是不可执行 */
  Generating = 1,
  /** 可用状态，此时用户可见，且可执行 */
  Available = 2,
}

export enum TemplateVariableType {
  TemplateVariableTypeAutoComplete = "AutoComplete",
  TemplateVariableTypeUpload = "Upload",
  TemplateVariableTypeUploadWithText = "UploadWithText",
}

export enum UserRole {
  UserRoleAimoAdmin = "AimoAdmin",
  UserRoleAimoAgentDeveloper = "AimoAgentDeveloper",
  UserRoleAimoInternalDev = "AimoInternalDev",
  UserRoleAimoMCPDeveloper = "AimoMCPDeveloper",
  UserRoleAimoMCPPlayground = "AimoMCPPlayground",
  UserRoleAimoTraceAll = "AimoTraceAll",
  UserRoleAimoTraceDiagnose = "AimoTraceDiagnose",
  UserRoleAimoTraceSelf = "AimoTraceSelf",
  UserRoleCollectionOperator = "CollectionOperator",
  UserRoleViewOthers = "ViewOthers",
}

export interface AttachmentRequired {
  id: string;
  file_name: string;
}

export interface BatchDeleteSessionRequest {
  session_ids: Array<string>;
}

export interface BatchDeleteSessionResponse {}

export interface CheckCreateSessionRequest {
  /** query 通过逗号切分，不支持多个 roles, example 1,2,3 */
  roles?: string;
}

export interface CheckCreateSessionResponse {
  allowed: boolean;
  roles?: Array<SessionRoleAllowed>;
}

export interface CreateMessageRequest {
  session_id: string;
  /** Content 用户发送的消息内容 */
  content: string;
  attachments?: Array<AttachmentRequired>;
  /** ToolCalls Agent 发送的工具调用，用户需要回复Agent时填写，例如二次确认、授权等
Content和ToolCalls互斥，ToolCalls仅在有 tool_call_required event 时使用 */
  tool_calls?: Array<ToolCall>;
  /** 指定创建消息的事件的 offset，通常用于打断或者澄清的场景 */
  event_offset?: Int64;
  options?: string;
  mentions?: Array<mention.Mention>;
  space_id?: string;
}

export interface CreateMessageResponse {
  message: event.Message;
}

export interface CreateMessageWithTemplateRequest {
  role?: types.SessionRole;
  use_internal_tool?: boolean;
  template_id: string;
  content: string;
  form_value?: TemplateFormValue;
  options?: string;
  mcps?: Array<mcp.MCPKey>;
  space_id?: string;
  excluded_mcps?: Array<mcp.MCPKey>;
  from_app?: string;
}

export interface CreateMessageWithTemplateResponse {
  message: event.Message;
  session: Session;
}

export interface CreateSessionRequest {
  role?: types.SessionRole;
  use_internal_tool?: boolean;
  space_id?: string;
  excluded_mcps?: Array<mcp.MCPKey>;
}

export interface CreateSessionResponse {
  session: Session;
}

export interface CreateSessionStarRequest {
  session_id: string;
}

export interface CreateSessionStarResponse {}

export interface DeleteSessionRequest {
  session_id: string;
  space_id?: string;
}

export interface DeleteSessionResponse {
  message: string;
}

export interface DeleteSessionStarRequest {
  session_id: string;
}

export interface DeleteSessionStarResponse {}

export interface EstimatedMinutes {
  min: Int64;
  max: Int64;
}

export interface GetHistoryTemplateVariablesRequest {
  template_id: string;
  space_id?: string;
}

export interface GetHistoryTemplateVariablesResponse {
  form_value: Array<TemplateFormValueDetail>;
}

export interface GetOldSessionEventsRequest {
  session_id: string;
  space_id?: string;
}

export interface GetOldSessionEventsResponse {
  events: Array<event.Event>;
  role?: types.SessionRole;
  session?: Session;
}

export interface GetSessionAgentRunRequest {
  session_id: string;
}

export interface GetSessionAgentRunResponse {
  is_available: boolean;
  runtime_meta: RuntimeMeta;
  created_at: string;
  updated_at: string;
}

export interface GetSessionMCPDetailsRequest {
  /** 会话ID */
  session_id: string;
}

export interface GetSessionMCPDetailsResponse {
  /** MCP详细信息列表 */
  mcps?: Array<SessionMCPDetail>;
}

export interface GetSessionRequest {
  session_id: string;
  space_id?: string;
}

export interface GetSessionResponse {
  session: Session;
  messages?: Array<event.Message>;
}

export interface GetSessionStreamEventsRequest {
  session_id: string;
  event_offset?: Int64;
  space_id?: string;
}

export interface GetUserRolesRequest {}

export interface GetUserRolesResponse {
  roles: Array<UserRole>;
}

export interface ListSessionPartialRequest {}

export interface ListSessionPartialResponse {
  sessions: Array<SessionPartial>;
}

export interface ListSessionsRequest {
  page_num: Int64;
  page_size: Int64;
  space_id?: string;
}

export interface ListSessionsResponse {
  sessions: Array<Session>;
  total: Int64;
}

export interface ListSpaceSessionsCount {
  space_public: Int64;
  self_created: Int64;
}

export interface ListSpaceSessionsRequest {
  space_id?: string;
  limit: Int64;
  /** 时间戳起始位置 */
  next_id?: string;
  type?: ListFilterType;
  /** 搜索关键词 */
  search?: string;
  /** 会话列表页签 */
  tab?: SessionTab;
  /** 创建者 */
  creators?: Array<string>;
  /** 创建时间 */
  created_time?: TimeDuration;
  /** 更新时间 */
  updated_time?: TimeDuration;
  /** 状态 */
  statuses?: Array<SessionStatus>;
  /** 作用域 */
  scopes?: Array<SessionScope>;
}

export interface ListSpaceSessionsResponse {
  sessions: Array<Session>;
  /** 是否还有更多 */
  has_more: boolean;
  next_id?: string;
  count: ListSpaceSessionsCount;
}

export interface ListTemplatesRequest {
  page_num?: Int64;
  page_size?: Int64;
  category?: TemplateCategory;
  search?: string;
  source?: TemplateSource;
  label?: TemplateLabel;
}

export interface ListTemplatesResponse {
  templates: Array<Template>;
  total: Int64;
}

export interface ListUserSessionsRequest {
  page_num: Int64;
  page_size: Int64;
  session_id?: string;
  agent_config_version_id?: string;
  start_time?: string;
  end_time?: string;
  status?: SessionStatus;
}

export interface ListUserSessionsResponse {
  sessions: Array<UserSession>;
  total: Int64;
}

/** ModifyTemplate 中可选字段根据用户的编辑操作来传，如果用户编辑了则传该字段，否则不传 */
export interface ModifyTemplate {
  category: TemplateCategory;
  name?: string;
  prompt?: string;
  variables?: Array<TemplateVariable>;
  /** 快捷模板的作用域，public、private */
  scope: TemplateScope;
  /** 快捷模板的标签 */
  label: TemplateLabel;
}

export interface RuntimeMeta {
  provider: string;
  container_id: string;
  container_host: string;
  wildcard_domain: string;
}

export interface Session {
  id: string;
  status: SessionStatus;
  title: string;
  context: SessionContext;
  created_at: string;
  updated_at: string;
  role?: types.SessionRole;
  creator?: string;
  metadata?: SessionRuntimeMetadata;
  last_message_at?: string;
  can_resume?: boolean;
  can_not_resume_reason?: CanNotResumeReason;
  template_id?: string;
  source_space_id?: string;
  scope?: SessionScope;
  /** 权限列表 */
  permission_actions?: Array<permission.PermissionAction>;
  /** 会话是否被收藏 */
  starred?: boolean;
  /** 会话的第一个用户提问 */
  first_user_query?: string;
}

export interface SessionContext {
  use_internal_tool?: boolean;
  /** MCP集 */
  mcps?: Array<mcp.MCP>;
}

/** 会话MCP详细信息 */
export interface SessionMCPDetail {
  /** MCP ID */
  id: string;
  /** MCP 名称 */
  name: string;
  /** MCP 描述 */
  description: string;
  /** MCP 来源 */
  source: mcp.MCPSource;
  /** 工具列表 */
  tools?: Array<mcp.MCPTool>;
  /** Agent看到的MCP名称 */
  NameForAgent: string;
  /** 获取不到工具情况下的错误信息 */
  err_message?: string;
}

export interface SessionPartial {
  id: string;
  status: SessionStatus;
  title: string;
}

export interface SessionRoleAllowed {
  role: types.SessionRole;
  allowed: boolean;
  remaining_times?: number;
}

export interface SessionRoleConfig {
  role: types.SessionRole;
  use_internal_tool: boolean;
  session_limit?: Int64;
  message_limit?: Int64;
  message_warning?: Int64;
}

export interface SessionRuntimeMetadata {
  log_id?: string;
  agent_config_version_id?: string;
  agent_config_id?: string;
}

export interface SubmitToolCallRequest {
  session_id: string;
  /** ToolCalls Agent 发送工具调用，用户需要回复 Agent 时填写，例如二次确认、授权等 */
  tool_calls?: Array<ToolCall>;
}

export interface SubmitToolCallResponse {}

export interface Template {
  id: string;
  category: TemplateCategory;
  name: string;
  prompt: string;
  variables: Array<TemplateVariable>;
  showcase?: showcase.Showcase;
  estimated_minutes?: EstimatedMinutes;
  /** 快捷模板支持的角色列表 */
  support_roles?: Array<types.SessionRole>;
  /** 快捷模板的作用域，public、private、shared */
  scope: TemplateScope;
  /** 快捷模板的创建者 */
  creator: string;
  /** 快捷模板的收藏数 */
  star_count?: Int64;
  /** 快捷模板支持的MCP列表 */
  support_mcps?: Array<mcp.MCP>;
  /** 快捷模板的状态 */
  status: TemplateStatus;
  /** 快捷模板的版本号 */
  version: string;
  /** 快捷模板的步骤 */
  steps?: Array<string>;
  /** 快捷模板的执行规划状态 */
  plan_step_status: TemplatePlanStepStatus;
  /** 快捷模板的标签 */
  label: TemplateLabel;
  /** 快捷模板是否已收藏 */
  starred?: boolean;
  /** 快捷模板的分享ID */
  share_id?: string;
}

export interface TemplateFormValue {
  variables: Record<string, TemplateVariableValue>;
}

export interface TemplateFormValueDetail {
  variables: Record<string, TemplateVariableValueDetail>;
}

export interface TemplateVariable {
  name: string;
  required?: boolean;
  description?: string;
  type: TemplateVariableType;
  placeholder?: string;
  default_value?: string;
  select_content?: string;
  id: string;
}

export interface TemplateVariableValue {
  content?: string;
  attachments?: Array<AttachmentRequired>;
}

export interface TemplateVariableValueDetail {
  content?: string;
  attachments?: Array<event.Attachment>;
}

export interface TimeDuration {
  /** 开始时间，毫秒级时间戳 */
  start_time?: Int64;
  /** 结束时间，毫秒级时间戳 */
  end_time?: Int64;
}

/** ToolCall 是用户对工具调用的回复，例如二次确认、授权等 */
export interface ToolCall {
  id: string;
  name: string;
  /** 用户回复的内容 */
  content: string;
  /** 用户的操作 */
  action: event.ToolCallAction;
  /** 是否需要保持登录状态 */
  need_keep_login?: boolean;
  /** 填写后的表单数据 */
  form_data?: string;
}

export interface UpdateSessionRequest {
  session_id: string;
  title?: string;
  status?: SessionStatus;
  scope?: SessionScope;
  space_id?: string;
}

export interface UpdateSessionResponse {
  session: Session;
}

export interface UserSession {
  id: string;
  status: SessionStatus;
  created_at: string;
  updated_at: string;
  role?: types.SessionRole;
  metadata?: SessionRuntimeMetadata;
  last_message_at?: string;
  can_resume?: boolean;
  can_not_resume_reason?: CanNotResumeReason;
  template_id?: string;
  source_space_id?: string;
  scope?: SessionScope;
  /** 权限列表 */
  permission_actions?: Array<permission.PermissionAction>;
  /** 会话是否被收藏 */
  starred?: boolean;
}
/* eslint-enable */
