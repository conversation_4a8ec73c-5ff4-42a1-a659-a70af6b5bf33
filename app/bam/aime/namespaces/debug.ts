// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as session from "./session";
import * as mcp from "./mcp";

export type Int64 = string | number;

export enum DebugEventStepType {
  DebugEventExecute = "execute",
  DebugEventPlan = "plan",
  DebugEventQuery = "query",
  DebugEventResponse = "response",
  DebugEventThinking = "thinking",
  DebugEventToolCall = "tool_call",
}

export enum DebugSessionEventStatus {
  Success = 1,
  Failed = 2,
}

export interface DebugEventDataQuery {
  id: string;
  parent_id?: string;
  content: string;
  attachments?: Array<string>;
}

export interface DebugEventDataResponse {
  id: string;
  parent_id?: string;
  content: string;
  attachments?: Array<string>;
}

export interface DebugEventDataStep {
  id: string;
  parent_id?: string;
  status: string;
  executor: string;
  error?: string;
  inputs?: string;
  outputs?: string;
  duration_ms?: Int64;
}

export interface DebugEventDataThinking {
  id: string;
  parent_id?: string;
  agent: string;
  outputs: string;
  duration_ms?: Int64;
}

export interface DebugEventDataToolCall {
  id: string;
  parent_id?: string;
  tool: string;
  description: string;
  status: string;
  error?: string;
  inputs?: string;
  outputs?: string;
  duration_ms?: Int64;
}

export interface DebugSessionContext {
  mcp: Array<MCPDetail>;
}

export interface DebugSessionEvent {
  id: string;
  type: DebugEventStepType;
  timestamp: string;
  data: string;
  parsed_data?: Array<ParsedData>;
  debuggable: boolean;
}

export interface DebugSessionEventMetadata {
  executed_at: string;
  execution_time_ms?: Int64;
}

export interface DebugSessionEventRequest {
  session_id: string;
  event_id: string;
  context?: DebugSessionContext;
}

export interface DebugSessionEventResponse {
  session_id: string;
  event_id: string;
  status: DebugSessionEventStatus;
  data?: string;
  error_message?: string;
  metadata?: DebugSessionEventMetadata;
}

export interface GetDebugSessionEventsRequest {
  session_id: string;
  event_id?: string;
}

export interface GetDebugSessionEventsResponse {
  events: Array<DebugSessionEvent>;
}

export interface GetDebugSessionRequest {
  session_id: string;
}

export interface GetDebugSessionResponse {
  id: string;
  title: string;
  context: DebugSessionContext;
  created_at: string;
  updated_at: string;
  status: session.SessionStatus;
}

export interface GetSessionDebugEventsStreamRequest {
  session_id: string;
  event_id?: string;
}

export interface MCPDetail {
  id: string;
  name: string;
  description: string;
  source: mcp.MCPSource;
  tools: Array<mcp.MCPTool>;
  name_for_agent: string;
}

export interface MockDebugEventData {
  step?: DebugEventDataStep;
  thinking?: DebugEventDataThinking;
  tool_call?: DebugEventDataToolCall;
  query?: DebugEventDataQuery;
  response?: DebugEventDataResponse;
}

export interface ParsedData {
  name: string;
  value: string;
  name_en?: string;
  key?: string;
  default_expanded?: boolean;
}
/* eslint-enable */
