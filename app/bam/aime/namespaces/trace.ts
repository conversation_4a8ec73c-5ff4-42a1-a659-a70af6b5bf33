// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as session from "./session";

export type Int64 = string | number;

export enum AgentStepStatus {
  AgentStepCreated = "created",
  AgentStepFailed = "failed",
  AgentStepRunning = "running",
  AgentStepSuccess = "success",
}

export enum ChatCompletionStatus {
  Fail = "fail",
  Success = "success",
}

export enum ChatMessagePartType {
  ImageURL = "image_url",
  Text = "text",
}

export enum RunDebugStatus {
  Recoverable = "recoverable",
  Running = "running",
  UnRecoverable = "unrecoverable",
}

export enum RuntimeProvider {
  Bytesuite = "bytesuite",
  Docker = "docker",
  Local = "local",
  StratoCube = "stratocube",
}

export interface AgentMetadata {
  agent_name?: string;
  agent_config_id?: string;
  agent_config_version?: number;
  agent_config_version_id?: string;
}

export interface AgentStep {
  action: string;
  executor: string;
  mcp_tool: string;
  start_time: Int64;
  end_time: Int64;
  status: AgentStepStatus;
  step_id: string;
}

export interface ChatCompletion {
  id: string;
  created_at: string;
  updated_at: string;
  prompt: string;
  response: string;
  type: string;
  metadata: string;
  status: ChatCompletionStatus;
  model_name: string;
}

export interface ChatCompletionMessage {
  role: string;
  content?: string;
  refusal?: string;
  multi_content?: Array<ChatMessagePart>;
  name?: string;
  function_call?: FunctionCall;
  tool_calls?: Array<ChatToolCall>;
  tool_call_id?: string;
}

export interface ChatMessageImageURL {
  url?: string;
  detail?: string;
}

export interface ChatMessagePart {
  type?: ChatMessagePartType;
  text?: string;
  image_url?: ChatMessageImageURL;
}

export interface ChatStreamRequest {
  model: string;
  messages: Array<ChatCompletionMessage>;
  max_tokens?: number;
  max_completion_tokens?: number;
  temperature?: number;
  top_p?: number;
  n?: number;
  stream?: boolean;
  stop?: Array<string>;
  presence_penalty?: number;
  frequency_penalty?: number;
  logit_bias?: Record<string, number>;
  logprobs?: boolean;
  top_logprobs?: number;
  user?: string;
  tools?: Array<Tool>;
  tool_choice?: ToolChoiceType;
  store?: boolean;
  reasoning_effort?: string;
  metadata?: Record<string, string>;
  thinking?: ThinkingConfig;
}

export interface ChatToolCall {
  index?: number;
  id?: string;
  type: string;
  function: FunctionCall;
}

export interface ConvertMarkdownToLarkRequest {
  Content: string;
  SessionID?: string;
}

export interface ConvertMarkdownToLarkResponse {
  lark_url: string;
}

export interface ConvertSessionDocumentToLarkRequest {
  session_id: string;
  file_path: string;
  artifact_id?: string;
  force_regenerate?: boolean;
}

export interface ConvertSessionDocumentToLarkResponse {
  lark_url: string;
}

export interface DeleteRuntimeRequest {
  run_id: string;
}

export interface DeleteRuntimeResponse {
  message: string;
}

export interface Document {
  name: string;
  file_path: string;
  type: string;
  content: string;
  size: Int64;
  artifact_id?: string;
  related_files: Array<string>;
  created_at: string;
}

export interface DownloadSessionLogRequest {
  session_id: string;
}

export interface FunctionCall {
  name?: string;
  arguments?: string;
}

export interface FunctionDefinition {
  name: string;
  description?: string;
  strict?: boolean;
  parameters: string;
}

export interface GetTraceEventsRequest {
  run_id?: string;
  session_id?: string;
  container_id?: string;
  uri?: string;
  provider?: string;
}

export interface GetTraceSessionChatRequest {
  session_id: string;
  status?: ChatCompletionStatus;
  type?: string;
  page_num: Int64;
  page_size: Int64;
  trace_id?: string;
}

export interface GetTraceSessionChatResponse {
  chat_completions: Array<ChatCompletion>;
  total: Int64;
}

export interface GetTraceSessionRequest {
  session_id?: string;
  replay_id?: string;
}

export interface GetTraceSessionResponse {
  session: TraceSession;
}

export interface ListModelsRequest {}

export interface ListModelsResponse {
  models: Array<Model>;
}

export interface ListSessionAgentStepRequest {
  session_id: string;
}

export interface ListSessionAgentStepResponse {
  steps: Array<AgentStep>;
  total: Int64;
}

export interface ListSessionDocumentsRequest {
  session_id: string;
}

export interface ListSessionDocumentsResponse {
  documents: Array<Document>;
}

export interface Model {
  type?: string;
  models?: Array<string>;
}

export interface ResumeRuntimeRequest {
  run_id: string;
}

export interface ResumeRuntimeResponse {
  message: string;
}

export interface SuspendRuntimeRequest {
  run_id: string;
}

export interface SuspendRuntimeResponse {
  message: string;
}

export interface ThinkingConfig {
  type?: string;
  budget_tokens?: number;
}

export interface Tool {
  type: string;
  function?: FunctionDefinition;
}

export interface ToolChoiceObject {
  type: string;
  function: ToolFunction;
}

export interface ToolChoiceType {
  StringChoice?: string;
  ObjectChoice?: ToolChoiceObject;
}

export interface ToolFunction {
  name: string;
}

export interface TraceMCPEventsRequest {
  session_id: string;
}

export interface TraceSession {
  id?: string;
  creator?: string;
  created_at?: string;
  updated_at?: string;
  title?: string;
  status?: session.SessionStatus;
  run_id?: string;
  run_provider?: RuntimeProvider;
  run_status?: string;
  run_debug_status?: RunDebugStatus;
  webshell_url?: string;
  file_server_url?: string;
  events_url?: string;
  agent_metadata?: AgentMetadata;
  log_id?: string;
  browser_url?: string;
}
/* eslint-enable */
