// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface CheckLarkAuthRequest {
  /** 请求url */
  url: string;
}

export interface CheckLarkAuthResponse {
  /** 是否授权 */
  authorization: boolean;
  /** 重定向url */
  redirect_url?: string;
  /** 是否拒绝授权 */
  authorization_denied?: boolean;
}

export interface GetLarkDocxBlocksRequest {
  document_id: string;
}

export interface GetLarkTicketRequest {}

export interface GetLarkTicketResponse {
  /** 是否授权 */
  authorization: boolean;
  /** jsapi_ticket */
  jsapi_ticket: JSApiTicket;
  /** app_id */
  app_id: string;
  /** open_id */
  open_id: string;
  /** signature, 预留 */
  signature?: string;
}

export interface GetUserLarkURLRequest {
  lark_url: string;
  session_id: string;
}

export interface GetUserLarkURLResponse {
  /** 移动到用户空间的lark_url */
  lark_url: string;
}

export interface JSApiTicket {
  ticket: string;
  expire_in: number;
}

export interface LarkAuthRequest {
  /** 授权码 */
  code: string;
  /** 包含重定向url和用户名 */
  state: string;
  /** 错误信息 */
  error?: string;
}

export interface LarkAuthResponse {}

export interface SendLarkReplayLinkMessageRequest {
  /** 用户名 */
  username: string;
  /** 链接 */
  replay_link: string;
  /** 任务名称 */
  task_name: string;
  /** 发送对象类型 */
  to_type: string;
}

export interface SendLarkReplayLinkMessageResponse {}
/* eslint-enable */
