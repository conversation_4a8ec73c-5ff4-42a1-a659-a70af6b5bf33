/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

// Scene interfaces
export interface Scene {
  id: string;
  scene: string;
  desc: string;
  create_time: string;
  update_time: string;
  user_email: string;
  sessionRole: string;
}

export interface CreateSceneRequest {
  scene: string;
  desc: string;
  user_email: string;
  sessionRole: string;
}

export interface CreateSceneResponse {
  code: number;
  msg?: string;
  data?: Scene;
}

export interface UpdateSceneRequest {
  scene: string;
  desc: string;
  user_email: string;
  sessionRole: string;
}

export interface UpdateSceneResponse {
  code: number;
  msg?: string;
  data?: Scene;
}

export interface GetScenesRequest {}

export interface GetScenesResponse {
  code: number;
  msg?: string;
  data?: Array<Scene>;
}

export interface DeleteSceneRequest {
  id: string;
}

export interface DeleteSceneResponse {
  code: number;
  msg?: string;
}

// Knowledge Item interface
export interface KnowledgeItem {
  scene: string;
  knowledge: string;
}

// Dynamic Knowledge interfaces
export interface DynamicKnowledge {
  id: string;
  scene_id: string;
  system: string;
  isOverwriteSystemPrompt: boolean;
  recallMode: string;
  knowledge: Array<KnowledgeItem>;
  create_time: string;
  update_time: string;
  user_email: string;
}

export interface CreateDynamicKnowledgeRequest {
  scene_id: string;
  system: string;
  isOverwriteSystemPrompt: boolean;
  recallMode: string;
  knowledge: Array<KnowledgeItem>;
  user_email: string;
}

export interface CreateDynamicKnowledgeResponse {
  code: number;
  msg?: string;
  data?: DynamicKnowledge;
}

export interface UpdateDynamicKnowledgeRequest {
  scene_id: string;
  system: string;
  isOverwriteSystemPrompt: boolean;
  recallMode: string;
  knowledge: Array<KnowledgeItem>;
  user_email: string;
}

export interface UpdateDynamicKnowledgeResponse {
  code: number;
  msg?: string;
  data?: DynamicKnowledge;
}

export interface GetDynamicKnowledgeRequest {}

export interface GetDynamicKnowledgeResponse {
  code: number;
  msg?: string;
  data?: Array<DynamicKnowledge>;
}

export interface DeleteDynamicKnowledgeRequest {
  id: string;
}

export interface DeleteDynamicKnowledgeResponse {
  code: number;
  msg?: string;
}

// Planner Knowledge interfaces
export interface PlannerKnowledge {
  id: string;
  scene_id: string;
  system: string;
  isOverwriteSystemPrompt: boolean;
  recallMode: string;
  tools: Array<string>;
  knowledge: Array<KnowledgeItem>;
  create_time: string;
  update_time: string;
  user_email: string;
}

export interface CreatePlannerKnowledgeRequest {
  scene_id: string;
  system: string;
  isOverwriteSystemPrompt: boolean;
  recallMode: string;
  tools: Array<string>;
  knowledge: Array<KnowledgeItem>;
  user_email: string;
}

export interface CreatePlannerKnowledgeResponse {
  code: number;
  msg?: string;
  data?: PlannerKnowledge;
}

export interface UpdatePlannerKnowledgeRequest {
  scene_id: string;
  system: string;
  isOverwriteSystemPrompt: boolean;
  recallMode: string;
  tools: Array<string>;
  knowledge: Array<KnowledgeItem>;
  user_email: string;
}

export interface UpdatePlannerKnowledgeResponse {
  code: number;
  msg?: string;
  data?: PlannerKnowledge;
}

export interface GetPlannerKnowledgeRequest {}

export interface GetPlannerKnowledgeResponse {
  code: number;
  msg?: string;
  data?: Array<PlannerKnowledge>;
}

export interface DeletePlannerKnowledgeRequest {
  id: string;
}

export interface DeletePlannerKnowledgeResponse {
  code: number;
  msg?: string;
}

// Experience interfaces - 经验管理相关接口
export interface ExtractionMetadata {
  taskName: string;         // 任务名称
  taskCategory: string;     // 任务类别
  executionContext: string; // 执行上下文
  overallComplexity: string; // 整体复杂度
  taskOutcome: string;      // 任务结果
  type: string;             // 经验提取类型，如 "planner"
}

export interface ExperienceSummary {
  totalExperiencesFound: string; // 发现的经验总数
  extractionRationale: string;   // 提取理由
}

export interface PracticalGuidance {
  name: string;                          // 指导名称
  applicationScenarios: Array<string>;   // 应用场景
  implementationSteps: Array<string>;    // 实施步骤
  keyConsiderations: Array<string>;      // 关键考虑事项
  commonPitfalls: Array<string>;         // 常见陷阱
}

export interface ExperienceItem {
  type: string;                        // 经验类型
  title: string;                       // 经验标题
  ragMatch: Array<string>;             // RAG匹配关键词
  practicalGuidance: PracticalGuidance; // 实践指导
}

export interface Experience {
  id?: string;                           // 经验ID
  extractionMetadata: ExtractionMetadata; // 提取元数据
  experienceSummary: ExperienceSummary;   // 经验摘要
  experiences: Array<ExperienceItem>;    // 具体经验列表
  createTime?: string;                   // 创建时间
  updateTime?: string;                   // 更新时间
  createUser: string;                    // 创建用户
}

// Experience API Request/Response interfaces
export interface CreateExperienceRequest {
  extractionMetadata: ExtractionMetadata;
  experienceSummary: ExperienceSummary;
  experiences: Array<ExperienceItem>;
  createUser: string;
}

export interface CreateExperienceResponse {
  code: number;
  msg?: string;
  data?: Experience;
}

export interface UpdateExperienceRequest {
  extractionMetadata: ExtractionMetadata;
  experienceSummary: ExperienceSummary;
  experiences: Array<ExperienceItem>;
  createUser: string;
}

export interface UpdateExperienceResponse {
  code: number;
  msg?: string;
  data?: boolean;
}

export interface GetExperienceRequest {
  id: string;
}

export interface GetExperienceResponse {
  code: number;
  msg?: string;
  data?: Experience;
}

export interface GetAllExperiencesRequest {}

export interface GetAllExperiencesResponse {
  code: number;
  msg?: string;
  data?: Array<Experience>;
}

export interface GetExperiencesByUserRequest {
  createUser: string;
}

export interface GetExperiencesByUserResponse {
  code: number;
  msg?: string;
  data?: Array<Experience>;
}

export interface GetExperiencesByCategoryRequest {
  taskCategory: string;
}

export interface GetExperiencesByCategoryResponse {
  code: number;
  msg?: string;
  data?: Array<Experience>;
}

export interface GetExperiencesByTypeRequest {
  experienceType: string;
}

export interface GetExperiencesByTypeResponse {
  code: number;
  msg?: string;
  data?: Array<Experience>;
}

export interface GetExperiencesByExtractionTypeRequest {
  type: string;
}

export interface GetExperiencesByExtractionTypeResponse {
  code: number;
  msg?: string;
  data?: Array<Experience>;
}

export interface SearchExperiencesRequest {
  keyword: string;
}

export interface SearchExperiencesResponse {
  code: number;
  msg?: string;
  data?: Array<Experience>;
}

export interface GetExperiencesByComplexityRequest {
  minComplexity: number;
  maxComplexity: number;
}

export interface GetExperiencesByComplexityResponse {
  code: number;
  msg?: string;
  data?: Array<Experience>;
}

export interface GetExperiencesByDateRangeRequest {
  startDate: string;
  endDate: string;
}

export interface GetExperiencesByDateRangeResponse {
  code: number;
  msg?: string;
  data?: Array<Experience>;
}

export interface GetExperienceStatsRequest {}

export interface ExperienceStats {
  totalCount: number;
  categoryStats: { [key: string]: number };
  experienceTypeStats: { [key: string]: number };
  extractionTypeStats: { [key: string]: number };
  complexityStats: { [key: string]: number };
}

export interface GetExperienceStatsResponse {
  code: number;
  msg?: string;
  data?: ExperienceStats;
}

export interface RecommendExperiencesRequest {
  keywords: Array<string>;
  limit?: number;
}

export interface RecommendExperiencesResponse {
  code: number;
  msg?: string;
  data?: Array<Experience>;
}

export interface DeleteExperienceRequest {
  id: string;
}

export interface DeleteExperienceResponse {
  code: number;
  msg?: string;
  data?: boolean;
}

export interface BatchDeleteExperiencesRequest {
  ids: Array<string>;
}

export interface BatchDeleteExperiencesResponse {
  code: number;
  msg?: string;
  data?: { [key: string]: boolean };
}