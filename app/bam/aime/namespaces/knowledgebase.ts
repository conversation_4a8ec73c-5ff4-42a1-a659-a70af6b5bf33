// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as dev_resource from "./dev_resource";

export type Int64 = string | number;

export enum DocumentContentType {
  DocumentContentTypeBitable = "bitable",
  DocumentContentTypeDoc = "doc",
  DocumentContentTypeSheet = "sheet",
}

export enum ImportType {
  ImportTypeSingle = "single",
  ImportTypeWikiTree = "wiki_tree",
}

export enum KnowledgeTaskStatus {
  KnowledgeTaskStatusFinished = "finished",
  KnowledgeTaskStatusProcessing = "processing",
}

export enum ListDocumentsDescOrderBy {
  ListDocumentsDescOrderByHeat = "heat",
  ListDocumentsDescOrderByLastUpdatedAt = "last_updated_at",
  ListDocumentsDescOrderByUpdatedAt = "updated_at",
}

export interface BatchDeleteDocumentRequest {
  dataset_id: string;
  document_ids: Array<string>;
}

export interface BatchDeleteDocumentResponse {}

export interface BatchUpdateDocumentRequest {
  dataset_id: string;
  document_ids: Array<string>;
}

export interface BatchUpdateDocumentResponse {}

export interface CountDocumentsRequest {
  dataset_id: string;
}

export interface CountDocumentsResponse {
  all_total?: Int64;
  my_total?: Int64;
}

export interface DeleteDocumentRequest {
  dataset_id: string;
  document_id: string;
}

export interface DeleteDocumentResponse {}

export interface GetDocumentRequest {
  dataset_id: string;
  document_id: string;
}

export interface GetDocumentResponse {
  document?: KnowledgeBaseDocument;
}

export interface GetKnowledgeTaskStatusRequest {
  dataset_id: string;
}

export interface GetKnowledgeTaskStatusResponse {
  status?: KnowledgeTaskStatus;
}

export interface KnowledgeBaseDocument {
  id?: string;
  title?: string;
  creator?: string;
  url?: string;
  updated_at?: string;
  owner?: string;
  created_at?: string;
  content?: string;
  heat?: Int64;
  dataset_id?: string;
  content_type?: DocumentContentType;
  import_type?: ImportType;
  process_status?: dev_resource.ProcessStatus;
  wiki_space_name?: string;
  failed_reason?: string;
}

export interface LarkDocument {
  title?: string;
  content?: string;
  url?: string;
  is_uploaded?: boolean;
  content_type?: DocumentContentType;
  owner_name?: string;
  has_child?: boolean;
  is_root?: boolean;
}

export interface ListDocumentsRequest {
  dataset_id: string;
  query?: string;
  creators?: Array<string>;
  page_num: Int64;
  page_size: Int64;
  desc_order_by?: ListDocumentsDescOrderBy;
  process_status?: Array<dev_resource.ProcessStatus>;
  lte_created_at?: Int64;
}

export interface ListDocumentsResponse {
  total?: Int64;
  documents?: Array<KnowledgeBaseDocument>;
}

export interface RecommendDocumentsRequest {
  reference_documents?: Array<ReferenceDocument>;
  dataset_id: string;
}

export interface RecommendDocumentsResponse {
  documents?: Array<LarkDocument>;
}

export interface ReferenceDocument {
  title?: string;
  url?: string;
}

export interface SearchLarkDocumentsRequest {
  query: string;
  dataset_id: string;
  import_type?: ImportType;
}

export interface SearchLarkDocumentsResponse {
  documents?: Array<LarkDocument>;
  /** 从知识库导入时返回 */
  wiki_space?: dev_resource.WikiSpace;
}

export interface UpdateDocumentRequest {
  dataset_id: string;
  document_id: string;
}

export interface UpdateDocumentResponse {}

export interface UploadDocumentsRequest {
  dataset_id: string;
  document_urls: Array<string>;
  /** 导入的知识库列表 */
  wiki_list?: Array<dev_resource.WikiDocConfig>;
}

export interface UploadDocumentsResponse {
  Documents?: Array<KnowledgeBaseDocument>;
}
/* eslint-enable */
