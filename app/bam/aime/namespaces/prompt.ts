// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as agent from "./agent";

export type Int64 = string | number;

export enum PromptType {
  PromptTypeAgent = "agent",
  PromptTypePublic = "public",
  PromptTypeUnknown = "unknown",
  PromptTypeUser = "user",
}

export enum VariableType {
  VariableTypeFloat64 = "float64",
  VariableTypeInt64 = "int64",
  VariableTypeList = "list",
  VariableTypeMap = "map",
  VariableTypeString = "string",
  VariableTypeUnknown = "unknown",
}

export interface CreatePromptRequest {
  name: string;
  description: string;
  agent_id?: string;
  /** 不填，如果有 agent id 默认是 agent 类型，否则是 public 类型 */
  type?: PromptType;
}

export interface CreatePromptResponse {
  prompt: Prompt;
}

export interface CreatePromptVersionRequest {
  prompt_id: string;
  description: string;
  enabled: boolean;
  /** 也支持 base64 格式，防止解析问题 */
  content: string;
  variable: Record<string, VariableMetadata>;
}

export interface CreatePromptVersionResponse {
  prompt_version: PromptVersion;
}

export interface DeletePromptRequest {
  prompt_id: string;
}

export interface DeletePromptResponse {}

export interface DownloadPromptVersionRequest {
  /** 如果指定该参数，则直接通过该参数查询 */
  prompt_version_id?: string;
  /** 通过 prompt id + version 查询 */
  prompt_id?: string;
  version?: number;
  /** 确定是不是注入变量 */
  inject_variable?: boolean;
}

export interface DownloadPromptVersionResponse {
  prompt_version: PromptVersion;
}

export interface GetPromptRequest {
  prompt_id: string;
  version?: number;
  latest?: boolean;
}

export interface GetPromptResponse {
  prompt: Prompt;
  prompt_version?: PromptVersion;
}

export interface GetPromptVersionRequest {
  prompt_version_id: string;
  content?: boolean;
}

export interface GetPromptVersionResponse {
  prompt_version: PromptVersion;
}

export interface ListPromptRequest {
  agent_id?: string;
  creator?: string;
  name?: string;
  page_num: Int64;
  page_size: Int64;
  type?: PromptType;
}

export interface ListPromptResponse {
  agent?: agent.Agent;
  prompts: Array<Prompt>;
  total: Int64;
}

export interface ListPromptVersionRequest {
  prompt_id: string;
  creator?: string;
  enabled?: boolean;
  page_num: Int64;
  page_size: Int64;
}

export interface ListPromptVersionResponse {
  prompt: Prompt;
  prompt_versions: Array<PromptVersion>;
  total: Int64;
}

export interface Prompt {
  id: string;
  name: string;
  description: string;
  agent_id?: string;
  creator: string;
  created_at: string;
  updated_at: string;
  type: PromptType;
}

export interface PromptVersion {
  id: string;
  prompt_id: string;
  version: number;
  description: string;
  enabled: boolean;
  content?: string;
  variable: Record<string, VariableMetadata>;
  creator: string;
  created_at: string;
  updated_at: string;
}

export interface UpdatePromptRequest {
  prompt_id: string;
  name?: string;
  description?: string;
}

export interface UpdatePromptResponse {
  prompt: Prompt;
}

export interface UpdatePromptVersionRequest {
  prompt_version_id: string;
  enabled?: boolean;
  description?: string;
  content?: string;
  variable?: Record<string, VariableMetadata>;
}

export interface UpdatePromptVersionResponse {
  prompt_version: PromptVersion;
}

export interface VariableMetadata {
  type: VariableType;
  list_value?: Array<string>;
  string_value?: string;
  i64_value?: Int64;
  map_value?: Record<string, string>;
  float64_value?: number;
}
/* eslint-enable */
