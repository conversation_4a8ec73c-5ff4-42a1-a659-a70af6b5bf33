// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as knowledgebase from "./knowledgebase";

export type Int64 = string | number;

export enum MentionOrderBy {
  MentionOrderByHeat = "heat",
  MentionOrderByUnknown = "unknown",
}

export enum MentionType {
  MentionTypeAttachment = "attachment",
  MentionTypeKnowledgeBase = "knowledgebase",
  MentionTypeRepository = "repository",
  MentionTypeRepositoryBranch = "repository_branch",
  MentionTypeRepositoryDir = "repository_dir",
  MentionTypeRepositoryFile = "repository_file",
  MentionTypeSnippet = "snippet",
  MentionTypeUnknown = "unknown",
}

export interface AttachmentMention {
  artifact_id: string;
  path: string;
  /** 附件是否从用户 query 转换而来 */
  is_from_query: boolean;
  /** 附件内容摘要，主要是超长 query 转附件时使用 */
  summary?: string;
}

export interface BelongsToArtifact {
  id: string;
  type: string;
  key: string;
  version: number;
}

export interface Branch {
  name: string;
  commit_sha: string;
}

export interface CodebaseMention {
  repo_name: string;
  branch?: string;
  tag?: string;
  file_path?: string;
  directory?: string;
}

export interface Dir {
  path: string;
}

export interface File {
  path: string;
}

export interface KnowledgeMention {
  knowledge_base_id: string;
  /** 文档ID */
  document_id: string;
  title?: string;
  url?: string;
}

export interface Mention {
  id: string;
  type: MentionType;
  codebase_mention?: CodebaseMention;
  knowledge_mention?: KnowledgeMention;
  attachment_mention?: AttachmentMention;
  snippet_mention?: SnippetMention;
}

export interface Range {
  start: number;
  end: number;
  start_column?: number;
  end_column?: number;
}

export interface Repository {
  id: Int64;
  name: string;
}

export interface SearchMentionsRequest {
  /** 搜索知识库必须传 */
  space_id?: string;
  /** 不填的情况下，服务端返回一个默认的数据 */
  type?: MentionType;
  /** 支持 id or path 两种 */
  repository_id?: string;
  repository_branch?: string;
  query?: string;
  limit?: number;
  next_id?: string;
  order_by?: MentionOrderBy;
}

export interface SearchMentionsResponse {
  type: MentionType;
  next_id?: string;
  has_more?: boolean;
  knowledgebase_documents?: Array<knowledgebase.KnowledgeBaseDocument>;
  repositories?: Array<Repository>;
  branches?: Array<Branch>;
  dirs?: Array<Dir>;
  files?: Array<File>;
}

export interface SnippetMention {
  path: string;
  range: Range;
  content: string;
  belongs_to_artifact?: BelongsToArtifact;
}
/* eslint-enable */
