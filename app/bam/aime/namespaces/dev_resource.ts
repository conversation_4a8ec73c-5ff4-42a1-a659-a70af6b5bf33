// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ControlPlane {
  /** UNSPECIFIED 在阶段模板配置里，可以额外用于未指定控制面（自由流水线） */
  CONTROL_PLANE_UNSPECIFIED = 0,
  CONTROL_PLANE_CN = 1,
  CONTROL_PLANE_I18N = 2,
  /** deprecated 沿用 ByteCycle US-TTP 方案使用的枚举值 */
  CONTROL_PLANE_TTP = 3,
  CONTROL_PLANE_EU_TTP = 4,
  CONTROL_PLANE_US_TTP = 5,
  /** I18N non TT */
  CONTROL_PLANE_I18N_BD = 6,
}

export enum DisabledReasonType {
  DisabledReasonOnlyBOE = "only_boe",
}

export enum OperateType {
  OperateTypeAdd = "add",
  OperateTypeDelete = "delete",
}

export enum ProcessStatus {
  ProcessStatusFailed = "failed",
  ProcessStatusPending = "pending",
  ProcessStatusProcessing = "processing",
  ProcessStatusSuccess = "success",
}

export enum ServiceType {
  SERVICE_TYPE_UNSPECIFIED = 0,
  /** TCE */
  SERVICE_TYPE_TCE = 1,
  /** ByteFaaS */
  SERVICE_TYPE_FAAS = 2,
  /** Cronjob */
  SERVICE_TYPE_CRONJOB = 3,
  /** 新版 Web */
  SERVICE_TYPE_WEB = 4,
  /** 跨端 */
  SERVICE_TYPE_HYBRID = 5,
  /** 组件/模块 */
  SERVICE_TYPE_LIBRARY = 6,
  /** 新版 NodeJS */
  SERVICE_TYPE_NODEJS = 7,
  /** 新版 Monorepo */
  SERVICE_TYPE_MONOREPO = 8,
  /** sidecar */
  SERVICE_TYPE_SIDECAR = 9,
  /** webapp */
  SERVICE_TYPE_WEB_APP = 10,
  /** 旧版web */
  SERVICE_TYPE_OLD_WEB = 11,
  /** TCC */
  SERVICE_TYPE_TCC = 12,
  /** 自定义项目类型 */
  SERVICE_TYPE_CUSTOM = 100,
}

export interface CodeRepo {
  repo_id: string;
  repo_name: string;
  is_uploaded?: boolean;
  desc?: string;
  avatar_url?: string;
  url: string;
  process_status?: ProcessStatus;
}

export interface DeleteCodeRepoRequest {
  /** 项目空间 ID */
  space_id: string;
  /** 代码仓库 */
  code_repos: Array<CodeRepo>;
}

export interface DeleteCodeRepoResponse {}

export interface DeleteServiceRequest {
  /** 项目空间 ID */
  space_id: string;
  /** 服务 */
  services: Array<Service>;
}

export interface DeleteServiceResponse {}

export interface GetPlatformConfigRequest {
  space_id: string;
}

export interface GetPlatformConfigResponse {
  platform_config?: PlatformConfig;
}

export interface LarkDocConfig {
  /** 单篇文档信息 */
  single_docs?: Array<string>;
  /** 导入的知识库列表 */
  wiki_list?: Array<WikiDocConfig>;
}

export interface ListCodeRepoRequest {
  /** 项目空间 ID */
  space_id: string;
  /** 查询条件 */
  query?: string;
  page_num?: Int64;
  page_size?: Int64;
  creators?: Array<string>;
  process_status?: Array<ProcessStatus>;
}

export interface ListCodeRepoResponse {
  code_repos?: Array<CodeRepo>;
  total?: Int64;
}

export interface ListServiceRequest {
  /** 项目空间 ID */
  space_id: string;
  /** 查询条件 */
  query?: string;
  page_num: Int64;
  page_size: Int64;
  creators?: Array<string>;
  process_status?: Array<ProcessStatus>;
}

export interface ListServiceResponse {
  services?: Array<Service>;
  total?: Int64;
}

export interface MeegoSpace {
  space_id?: string;
  space_name?: string;
  single_name?: string;
  url?: string;
  /** 是否上传 */
  is_uploaded?: boolean;
}

export interface PlatformConfig {
  meego_space_list?: Array<MeegoSpace>;
}

export interface SearchCodeRepoRequest {
  /** 项目空间 ID */
  space_id: string;
  /** 查询条件 */
  query?: string;
  page_num: Int64;
  page_size: Int64;
}

export interface SearchCodeRepoResponse {
  code_repos?: Array<CodeRepo>;
  total?: Int64;
}

export interface SearchMeegoSpaceRequest {
  query: string;
  space_id: string;
}

export interface SearchMeegoSpaceResponse {
  meego_spaces?: Array<MeegoSpace>;
}

export interface SearchServiceRequest {
  space_id: string;
  query?: string;
  service_type?: ServiceType;
  page_num: Int64;
  page_size: Int64;
}

export interface SearchServiceResponse {
  services?: Array<Service>;
  total?: Int64;
}

export interface Service {
  /** 项目唯一标识 */
  unique_id: string;
  /** 项目类型 */
  type: ServiceType;
  /** 项目名称 */
  name: string;
  /** 控制面 */
  control_plane: ControlPlane;
  /** 是否上传 */
  is_uploaded?: boolean;
  process_status?: ProcessStatus;
  url: string;
  /** 不可选原因 */
  disabled_reason?: DisabledReasonType;
}

export interface UpdatePlatformConfigRequest {
  space_id: string;
  meego_spaces: Array<MeegoSpace>;
  operate_type: OperateType;
}

export interface UpdatePlatformConfigResponse {}

export interface UploadCodeRepoRequest {
  /** 项目空间 ID */
  space_id: string;
  code_repos: Array<CodeRepo>;
}

export interface UploadCodeRepoResponse {}

export interface UploadServiceRequest {
  /** 项目空间 ID */
  space_id: string;
  services: Array<Service>;
}

export interface UploadServiceResponse {}

export interface WikiDoc {
  /** 是否全选 */
  select_all?: boolean;
  /** 链接 */
  url: string;
}

export interface WikiDocConfig {
  /** 知识空间信息 */
  wiki_space: WikiSpace;
  /** 文档信息 */
  docs: Array<WikiDoc>;
}

export interface WikiSpace {
  space_id: string;
  space_name: string;
}
/* eslint-enable */
