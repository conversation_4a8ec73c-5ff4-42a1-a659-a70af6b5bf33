// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as event from "./event";
import * as types from "./types";
import * as showcase from "./showcase";

export type Int64 = string | number;

export interface CreateReplayRequest {
  session_id: string;
  event_offset_start?: Int64;
  event_offset_end?: Int64;
}

export interface CreateReplayResponse {
  replay: Replay;
}

export interface GetReplayRequest {
  replay_id: string;
}

export interface GetReplayResponse {
  replay: Replay;
  events: Array<event.Event>;
  role?: types.SessionRole;
}

export interface ListShowcaseRequest {
  locale?: string;
}

export interface ListShowcaseResponse {
  showcases: Array<showcase.Showcase>;
}

export interface Replay {
  id: string;
  session_id: string;
  title: string;
  created_at: string;
  updated_at: string;
  creator: string;
}
/* eslint-enable */
