// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from "./common";
import * as mention from "./mention";

export type Int64 = string | number;

export enum BrowserContentType {
  BrowserTypeScreenshot = 1,
  BrowserTypeDeploy = 2,
  BrowserTypeLogin = 3,
  /** 自定义的网页卡片 */
  BrowserTypeCard = 4,
}

export enum DoneStatus {
  Success = 0,
  Failed = 1,
}

export enum LoginStatus {
  LoginStatusUnknown = 0,
  LoginStatusWaiting = 1,
  LoginStatusSuccess = 4,
  LoginStatusFailed = 5,
}

export enum ToolCallAction {
  Reject = 0,
  Confirm = 1,
  Timeout = 2,
}

export enum ToolCallRequiredType {
  ToolCallRequiredTypeAsk = "ask",
  ToolCallRequiredTypeTakeControl = "take_control",
}

export interface AppendFile {
  /** 文档名称/ Title */
  file_path?: string;
  /** 文档内容 */
  content?: string;
  /** 如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是 Link，则对应的是 link 类型（参考 ArtifactLinkSubType） */
  sub_type?: string;
  /** 文档链接，与 Content 二选一 */
  url?: string;
}

export interface Attachment {
  id: string;
  file_name: string;
  path: string;
  type: string;
  url: string;
  content_type: string;
  content_length: Int64;
  parent_step_ids?: Array<string>;
  lark_token: string;
  need_preview?: boolean;
  /** 子类型，如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是Link，则对应的是 link 类型（参考 ArtifactLinkSubType） */
  sub_type?: string;
  /** 是否是直接跳转的链接 */
  need_jump?: boolean;
  version?: number;
}

export interface Browser {
  /** 截图url */
  screenshot_url?: string;
  /** 访问页面url 或者部署的页面url */
  url?: string;
  content_type?: BrowserContentType;
  /** 部署id */
  deploy_id?: string;
  login_info?: LoginInfo;
  /** 只有 card 类型的时候才有这个字段 */
  card_body?: string;
}

export interface CodeEditor {
  create_file?: CreateFile;
  patch_file?: PatchFile;
  append_file?: AppendFile;
}

export interface CreateFile {
  /** 文档名称/ Title */
  file_path?: string;
  /** 文档内容 */
  content?: string;
  /** 如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是 Link，则对应的是 link 类型（参考 ArtifactLinkSubType） */
  sub_type?: string;
  /** 文档链接，与 Content 二选一 */
  url?: string;
}

export interface DoneEvent {
  event_id: string;
  status: DoneStatus;
  message: string;
  timestamp: Int64;
  error_code?: common.ErrorCode;
}

export interface ErrorEvent {
  event_id: string;
  session_id: string;
  error_code: common.ErrorCode;
  message: string;
  timestamp?: Int64;
  event_offset?: Int64;
  event_key?: string;
}

export interface Event {
  event: string;
  data: string;
}

export interface EventStruct {
  message_create_event?: MessageCreateEvent;
  plan_update_event?: PlanUpdateEvent;
  step_update_event?: StepUpdateEvent;
  use_tool_event?: UseToolEvent;
  progress_notice_event?: ProgressNoticeEvent;
  tool_call_required_event?: ToolCallRequiredEvent;
  session_completed?: SessionCompletedEvent;
  done_event?: DoneEvent;
  error_event?: ErrorEvent;
  error_code?: common.ErrorCode;
  reference_event?: ReferenceEvent;
  tool_call_confirmed_event?: ToolCallConfirmedEvent;
}

export interface FilePatch {
  file_path?: string;
  patch?: string;
}

export interface FormParams {
  /** 表单标题或解释 */
  title: string;
  /** 待填写的表单内容，序列化后的 json 字符串 */
  body: string;
  /** 原始 query */
  message: string;
  /** 超时时间，单位秒 */
  timeout?: Int64;
}

export interface GenerateVisualPage {
  /** 显示生成可视化页面按钮 */
  display_generate: boolean;
  /** 点击生成可视化页面的默认 query */
  generate_page_query: string;
}

export interface LoginInfo {
  login_status: LoginStatus;
  login_timeout?: Int64;
}

export interface Message {
  message_id: string;
  session_id: string;
  task_id: string;
  role: string;
  content: string;
  creator: string;
  attachments?: Array<Attachment>;
  created_at: string;
  updated_at: string;
  mentions?: Array<mention.Mention>;
}

export interface MessageCreateEvent {
  event_id: string;
  message: Message;
  timestamp?: Int64;
  event_offset?: Int64;
  reply_message_id?: string;
  event_key?: string;
  generate_visual_page?: GenerateVisualPage;
}

export interface PatchFile {
  /** 文档名称/ Title */
  file_path?: string;
  diff?: string;
  patch_file_result?: Array<FilePatch>;
  /** 如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是 Link，则对应的是 link 类型（参考 ArtifactLinkSubType） */
  sub_type?: string;
  /** 文档链接 */
  url?: string;
}

export interface PlanUpdateEvent {
  event_id: string;
  task_id: string;
  plan_id: string;
  steps: Array<Step>;
  timestamp?: Int64;
  event_offset?: Int64;
  status: string;
  session_id: string;
  event_key?: string;
}

export interface ProgressNoticeEvent {
  event_id: string;
  session_id: string;
  status?: string;
  timestamp?: Int64;
  event_offset?: Int64;
  event_key?: string;
}

export interface ReferenceEvent {
  event_id: string;
  session_id: string;
  task_id: string;
  references: Array<ReferenceItem>;
  timestamp?: Int64;
  event_offset?: Int64;
  event_key?: string;
}

export interface ReferenceItem {
  id: number;
  title: string;
  uri: string;
}

export interface SaveEventKeyRequest {
  offset: Int64;
  limit: Int64;
  event_ids?: Array<string>;
}

export interface SaveEventKeyResponse {
  success_event_ids: Array<string>;
  failed_event_ids: Array<string>;
}

export interface SearchList {
  query: string;
  results: Array<SearchListItem>;
}

export interface SearchListItem {
  title: string;
  url: string;
  description: string;
  favicon: string;
}

export interface SessionCompletedEvent {
  event_id: string;
  session_id: string;
  status?: string;
  timestamp?: Int64;
  event_offset?: Int64;
  event_key?: string;
}

export interface Step {
  step_id: string;
  task_id: string;
  title: string;
  status: string;
  start_time: Int64;
  end_time: Int64;
  parent_message_ids: Array<string>;
}

export interface StepUpdateEvent {
  event_id: string;
  step_id: string;
  plan_id: string;
  status: string;
  summary: string;
  description: string;
  timestamp?: Int64;
  event_offset?: Int64;
  session_id: string;
  event_key?: string;
}

export interface TakeBrowserParams {
  /** 接管浏览器参数 */
  stream_url: string;
  reason: string;
  /** 超时时间，单位秒 */
  timeout: Int64;
  /** 是否需要用户确认是否保持登录状态 */
  ask_keep_login?: boolean;
}

export interface Terminal {
  command: string;
  output: Array<string>;
}

export interface TextEditor {
  create_file?: CreateFile;
  patch_file?: PatchFile;
  append_file?: AppendFile;
}

export interface ToolCallConfirmedEvent {
  event_id: string;
  session_id: string;
  tool_call_id: string;
  step_id: string;
  action: ToolCallAction;
  reason?: string;
  timestamp?: Int64;
  event_offset?: Int64;
  event_key?: string;
  browser?: Browser;
  /** 填写后的表单数据 */
  form_data?: string;
}

export interface ToolCallRequiredEvent {
  event_id: string;
  session_id: string;
  tool?: string;
  question?: string;
  type?: ToolCallRequiredType;
  timestamp?: Int64;
  event_offset?: Int64;
  tool_call_id: string;
  event_key?: string;
  take_browser_params?: TakeBrowserParams;
  step_id?: string;
  form_params?: FormParams;
}

export interface UseToolEvent {
  event_id: string;
  step_id: string;
  plan_id: string;
  tool_name: string;
  summary: string;
  description: string;
  status: string;
  terminal?: Terminal;
  text_editor?: TextEditor;
  code_editor?: CodeEditor;
  browser?: Browser;
  search_list?: SearchList;
  timestamp?: Int64;
  event_offset?: Int64;
  session_id: string;
  agent_step_id: string;
  event_key?: string;
}
/* eslint-enable */
