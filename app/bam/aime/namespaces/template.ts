// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as session from "./session";
import * as permission from "./permission";

export type Int64 = string | number;

export enum TemplateExperienceStatus {
  Success = 0,
  Failed = 1,
}

export interface CountSpaceTemplatesRequest {
  space_id?: string;
}

export interface CountSpaceTemplatesResponse {
  /** 我的模板总数，不随筛选条件变化 */
  my_total: Int64;
  /** 收藏模板总数，不随筛选条件变化 */
  star_total: Int64;
  /** 所有公开模板总数，不随筛选条件变化 */
  all_total: Int64;
  /** 项目内模板总数，不随筛选条件变化 */
  project_total: Int64;
}

export interface CountTemplatesRequest {}

export interface CountTemplatesResponse {
  /** 我的模板总数，不随筛选条件变化 */
  my_total: Int64;
  /** 收藏模板总数，不随筛选条件变化 */
  star_total: Int64;
  /** 所有公开模板总数，不随筛选条件变化 */
  all_total: Int64;
}

export interface CreateShareTemplateRequest {
  template_id: string;
}

export interface CreateShareTemplateResponse {
  share_id: string;
}

export interface CreateTemplateDraftRequest {
  template_key: TemplateKey;
  space_id?: string;
}

export interface CreateTemplateDraftResponse {
  template: session.Template;
}

export interface CreateTemplateRequest {
  session_id?: string;
  draft_template_id?: string;
  from_template_id?: string;
  template?: session.ModifyTemplate;
  space_id?: string;
}

export interface CreateTemplateResponse {
  template: session.Template;
  /** 用户对该模板的权限 */
  permissions: Array<permission.PermissionAction>;
}

export interface CreateTemplateStarRequest {
  template_id: string;
  space_id?: string;
}

export interface CreateTemplateStarResponse {}

export interface CreateUserShareTemplateRequest {
  share_id: string;
}

export interface CreateUserShareTemplateResponse {
  template: session.Template;
  /** 用户对该模板的权限 */
  permissions: Array<permission.PermissionAction>;
}

export interface DeleteTemplateRequest {
  template_id: string;
}

export interface DeleteTemplateResponse {}

export interface DeleteTemplateStarRequest {
  template_id: string;
  space_id?: string;
}

export interface DeleteTemplateStarResponse {}

export interface DeleteUserShareTemplateRequest {
  share_id: string;
}

export interface DeleteUserShareTemplateResponse {}

export interface DownloadTemplateExperienceFileStreamRequest {
  file_id: string;
  /** 流式返回 */
  stream?: boolean;
}

export interface GetTemplateDraftRequest {
  template_id?: string;
}

export interface GetTemplateDraftResponse {
  template: session.Template;
}

export interface GetTemplateRequest {
  template_id: string;
  space_id?: string;
}

export interface GetTemplateResponse {
  template: session.Template;
  /** 用户对该模板的权限 */
  permissions: Array<permission.PermissionAction>;
}

export interface GetTemplateShareFormDataRequest {
  id: string;
}

export interface GetTemplateShareFormDataResponse {
  form_data: session.TemplateFormValueDetail;
}

export interface ListSpaceTemplatesRequest {
  next_id?: string;
  limit?: Int64;
  category?: session.TemplateCategory;
  search?: string;
  source?: session.TemplateSource;
  label?: session.TemplateLabel;
  space_id?: string;
}

export interface ListSpaceTemplatesResponse {
  templates: Array<session.Template>;
  has_more: boolean;
  next_id: string;
}

export interface SaveTemplateShareFormDataRequest {
  form_data: session.TemplateFormValueDetail;
}

export interface SaveTemplateShareFormDataResponse {
  id: string;
}

export interface TemplateFile {
  file_id: string;
  template_id: string;
  name: string;
  type: string;
}

export interface TemplateKey {
  session_id: string;
  latest_event_timestamp: Int64;
}

export interface UpdateTemplateExperienceRequest {
  template_id: string;
  /** 粗粒度的经验 */
  progress_plan?: string;
  /** 细粒度的经验 */
  exp_sop?: string;
  /** 经验生成结果状态 */
  status: TemplateExperienceStatus;
  /** 经验生成错误信息 */
  error?: string;
  /** 经验是否强制生效，默认会根据用户对模板的编辑操作来确定 */
  force_active?: boolean;
}

export interface UpdateTemplateExperienceResponse {}

export interface UpdateTemplateRequest {
  template_id: string;
  need_generate_experience?: boolean;
  template_key?: TemplateKey;
  template?: session.ModifyTemplate;
}

export interface UpdateTemplateResponse {
  template: session.Template;
}

export interface UploadTemplateExperienceFileStreamRequest {
  template_id: string;
  path: string;
  size: Int64;
}

export interface UploadTemplateExperienceFileStreamResponse {
  file: TemplateFile;
}
/* eslint-enable */
