// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum DeploymentCategory {
  DeploymentCategoryBackend = "backend",
  DeploymentCategoryFrontend = "frontend",
}

export interface CreateDeploymentRequest {
  session_id: string;
  artifact_id: string;
  category?: DeploymentCategory;
}

export interface CreateDeploymentResponse {
  deployment_id: string;
  url: string;
}

export interface GetDeploymentArtifactRequest {
  path?: string;
}

export interface GetDeploymentArtifactResponse {}
/* eslint-enable */
