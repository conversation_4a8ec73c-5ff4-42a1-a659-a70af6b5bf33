// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as user from "./user";

export type Int64 = string | number;

export interface Activity {
  id: string;
  /** 活动处于开启状态，前端需要展示活动页面 */
  activited: boolean;
  /** 活动处于开启状态，但是邀请码已经发放完了 */
  have_no_prize: boolean;
}

export interface GetUserActivityProgressRequest {
  activity_id: string;
}

export interface GetUserActivityProgressResponse {
  /** 用户活动进度 */
  progress: string;
  /** 该期活动获取的邀请码信息 */
  invitation_codes: Array<user.InvitationCode>;
}

export interface VerifyActivityAwardRequest {
  replay_id: string;
  activity_id: string;
}

export interface VerifyActivityAwardResponse {
  hit: boolean;
}
/* eslint-enable */
