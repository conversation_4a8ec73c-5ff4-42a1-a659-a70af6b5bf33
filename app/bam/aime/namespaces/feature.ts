// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as activity from "./activity";
import * as session from "./session";
import * as types from "./types";

export type Int64 = string | number;

export interface GetGlobalFeaturesRequest {}

export interface GetGlobalFeaturesResponse {
  current_activity?: activity.Activity;
  role_features?: Array<RoleFeature>;
}

export interface GetUserFeaturesRequest {}

export interface GetUserFeaturesResponse {
  invited: boolean;
  /** 没有返回该字段表示不限制 */
  session_limit?: Int64;
  message_limit?: Int64;
  message_warning?: Int64;
  role_configs?: Array<session.SessionRoleConfig>;
  /** 新版空间灰度字段 */
  use_space?: boolean;
}

export interface RoleFeature {
  role: types.SessionRole;
  /** 休眠后最长可唤醒的天数 */
  max_resume_days: number;
  /** 事件不更新后多久时间进入休眠 */
  sleeping_time_hours: number;
}
/* eslint-enable */
