import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "@radix-ui/themes/styles.css";
import { LayoutWithoutSidebar } from "./components/LayoutWithoutSidebar";
import { Providers } from "./components/Providers";
import "@arco-design/theme-aime-admin/css/arco.css";
import "./tod.css";
import "./globals.css";
import "./tod.css";
import { Toaster } from "@/components/ui/sonner";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "AIMO Lab",
  description: "AI Model Optimization Lab",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh" suppressHydrationWarning>
      <body className={inter.className} style={{ margin: 0, padding: 0 }}>
        <Providers>
          <LayoutWithoutSidebar>{children}</LayoutWithoutSidebar>
        </Providers>
         <Toaster  />
      </body>
    </html>
  );
}
