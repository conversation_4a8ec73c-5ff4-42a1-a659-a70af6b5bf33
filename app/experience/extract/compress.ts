/**
 * @file traceProcessor.ts
 * @description 包含用于处理和优化AI Agent执行路径（Trace）的工具函数。
 * 该模块执行四项核心优化：
 * 1. `Context.Knowledges`: 将完整的知识库文章提取到顶层，用ID引用。
 * 2. `Tools`: 将工具描述提取到顶层，用名称引用，仅保留Name、Description和Parameters字段。
 * 3. `ActorDescription`: 将Actor描述提取到顶层，用名称引用，仅保留Name、Description和Parameters字段。
 * 4. `Action.Results`: 移除`general_data_file_preview`结果中的`sample_values`字段，并优化`progress_think`中的`knowledge_renders`。
 */

import * as yaml from 'js-yaml';
import { v4 as uuidv4 } from 'uuid';

// =================================================================
// 工具函数
// =================================================================

/**
 * 检查值是否为空值（null、""、[]、{}）
 * @param value - 要检查的值
 * @returns 是否为空值
 */
function isEmptyValue(value: any): boolean {
  if (value === null || value === undefined) return true;
  if (value === '') return true;
  if (Array.isArray(value) && value.length === 0) return true;
  if (typeof value === 'object' && Object.keys(value).length === 0) return true;
  return false;
}

/**
 * 递归去除对象中的空值字段
 * @param obj - 要处理的对象
 * @returns 去除空值后的对象
 */
function removeEmptyValues(obj: any): any {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj
      .map(item => removeEmptyValues(item))
      .filter(item => !isEmptyValue(item));
  }
  
  if (typeof obj === 'object') {
    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const cleanedValue = removeEmptyValues(value);
      if (!isEmptyValue(cleanedValue)) {
        result[key] = cleanedValue;
      }
    }
    return result;
  }
  
  return obj;
}

// =================================================================
// 1. 类型定义 (Interfaces)
// =================================================================
export interface Knowledge { id: string; key: string; title: string; content: string; [key: string]: any; }
export interface KnowledgeBase { [knowledgeId: string]: Knowledge; }
export interface TraceBlock { previous_trace_id: string | null; content: string; }
export interface TracesObject { [traceId: string]: TraceBlock; }
export interface ToolParameter { Name: string; Description: string; [key: string]: any; }
export interface Tool { Name: string; Description: string; Parameters: ToolParameter[]; [key: string]: any; }
export interface ToolBase { [toolName: string]: Tool; }
export interface ActorParameter { Name: string; Description: string; [key: string]: any; }
export interface ActorDescription { Name: string; Description: string; Parameters: ActorParameter[]; [key: string]: any; }
export interface ActorBase { [actorName: string]: ActorDescription; }
interface OptimizedKnowledgeRender { knowledge_id: string; step_id: string; }
interface OriginalKnowledgeRender { knowledge: Knowledge; step_id: string; }
interface OriginalProgressThinkResults { knowledge_renders: OriginalKnowledgeRender[]; [key: string]: any; }
interface OriginalAction { Tool: string; Results: OriginalProgressThinkResults | any; [key: string]: any; }
interface OriginalInnerRound { Action: OriginalAction; [key: string]: any; }
interface OriginalContext { Knowledges: Knowledge[]; FormerOutput: string; }
interface OriginalExecutionDetail { ExecutionTrace: string; Context: OriginalContext; Rounds?: OriginalInnerRound[]; [key: string]: any; }
interface OriginalExecution { Detail: OriginalExecutionDetail; [key: string]: any; }
interface OriginalRound { Execution: OriginalExecution; [key: string]: any; }
interface OriginalDataItem { Round: OriginalRound | null; [key: string]: any; }
export interface OriginalTrace { data: OriginalDataItem[]; }
interface OptimizedContext { UsedKnowledgeIDs: string[]; FormerOutput: string; }
interface OptimizedProgressThinkResults { knowledge_renders: OptimizedKnowledgeRender[]; [key: string]: any; }
interface OptimizedAction extends Omit<OriginalAction, 'Results'> { Results: OptimizedProgressThinkResults | any; }
interface OptimizedInnerRound extends Omit<OriginalInnerRound, 'Action'> { Action: OptimizedAction; }
interface OptimizedExecutionDetail { Context: OptimizedContext; Rounds?: OptimizedInnerRound[]; [key: string]: any; }
interface OptimizedExecution extends Omit<OriginalExecution, 'Detail'> { Detail: OptimizedExecutionDetail; }
interface OptimizedRound extends Omit<OriginalRound, 'Execution'> { Execution: OptimizedExecution; }
interface OptimizedDataItem extends Omit<OriginalDataItem, 'Round'> { Round: OptimizedRound | null; }
export interface OptimizedTrace { data: OptimizedDataItem[]; Traces: TracesObject; KnowledgeBase: KnowledgeBase; ToolBase: ToolBase; ActorBase: ActorBase; }

// =================================================================
// 2. 核心处理函数
// =================================================================

/**
 * 升级版：处理AI Agent的执行路径记录，同时优化Context.Knowledges、Tools、ActorDescription和Action.Results。
 * @param originalTrace - 原始的、包含大量重复内容的JSON对象。
 * @returns {OptimizedTrace} 优化后的、包含KnowledgeBase、ToolBase和ActorBase对象以及ID引用的JSON对象。
 */
export function processAgentTrace(originalTrace: OriginalTrace): OptimizedTrace {
    const knowledgeBase: KnowledgeBase = {};
    const toolBase: ToolBase = {};
    const actorBase: ActorBase = {};
    const traces: TracesObject = {};
    let previousTraceContent = '';
    let previousTraceId: string | null = null;

    const newData = originalTrace.data.map((item, index) => {
        if (!item.Round?.Execution?.Detail) {
            return item;
        }

        const detail = item.Round.Execution.Detail;

        // --- 步骤1: 优化 Context.Knowledges ---
        const usedKnowledgeIDs: string[] = [];
        if (detail.Context?.Knowledges?.length) {
            detail.Context.Knowledges.forEach((knowledge: Knowledge) => {
                const knowledgeId = knowledge.id || knowledge.key;
                if (!knowledgeBase[knowledgeId]) {
                    knowledgeBase[knowledgeId] = knowledge;
                }
                usedKnowledgeIDs.push(knowledgeId);
            });
        }
        const optimizedContext = { ...detail.Context, UsedKnowledgeIDs: usedKnowledgeIDs };
        delete (optimizedContext as any).Knowledges;

        // --- 步骤2: 优化 Tools ---
        const usedToolNames: string[] = [];
        if (detail.Tools?.length) {
            detail.Tools.forEach((tool: Tool) => {
                const toolName = tool.Name;
                if (!toolBase[toolName]) {
                    // 只保留 Name, Description 和 Parameters 字段
                    toolBase[toolName] = {
                        Name: tool.Name,
                        Description: tool.Description,
                        Parameters: tool.Parameters || []
                    };
                }
                usedToolNames.push(toolName);
            });
        }

        // --- 步骤3: 优化 ActorDescription ---
        let usedActorName: string | null = null;
        if (item.Round?.Plan?.ActorDescription) {
            const actor = item.Round.Plan.ActorDescription;
            const actorName = actor.Name;
            if (!actorBase[actorName]) {
                // 只保留 Name, Description 和 Parameters 字段
                actorBase[actorName] = {
                    Name: actor.Name,
                    Description: actor.Description,
                    Parameters: actor.Parameters || []
                };
            }
            usedActorName = actorName;
        }

        // --- 步骤4: 优化 ExecutionTrace ---
        let currentTraceContent = detail.ExecutionTrace || "";
        if (currentTraceContent.endsWith("</execution_trace>")) {
            currentTraceContent = currentTraceContent.slice(0, -"</execution_trace>".length);
        }
        if (currentTraceContent.startsWith("<execution_trace>")) {
            currentTraceContent = currentTraceContent.slice("<execution_trace>".length);
        }

        const newContent = currentTraceContent.startsWith(previousTraceContent)
          ? currentTraceContent.substring(previousTraceContent.length).trim()
          : currentTraceContent.trim();

        if (index > 0 && !currentTraceContent.startsWith(previousTraceContent)) {
            console.warn(`Warning: Trace at data index ${index} is not continuous.`);
        }

        const newTraceId = `trace-${index + 1}-${uuidv4().slice(0, 8)}`;
        traces[newTraceId] = { previous_trace_id: previousTraceId, content: newContent };
        previousTraceContent = currentTraceContent;
        previousTraceId = newTraceId;

        // --- 步骤5: 优化嵌套Rounds中的Action.Results ---
        const optimizedInnerRounds = detail.Rounds?.map(innerRound => {
            const newInnerRound = { ...innerRound }; // 创建副本以修改

            // 优化 progress_think 中的 knowledge_renders
            if (newInnerRound.Action?.Tool === 'progress_think' && newInnerRound.Action.Results?.knowledge_renders) {
                const originalRenders = newInnerRound.Action.Results.knowledge_renders;
                const optimizedRenders = originalRenders.map((render: OriginalKnowledgeRender) => {
                    const knowledge = render.knowledge;
                    const knowledgeId = knowledge.id || knowledge.key;
                    if (!knowledgeBase[knowledgeId]) {
                        knowledgeBase[knowledgeId] = knowledge;
                    }
                    return { knowledge_id: knowledgeId, step_id: render.step_id };
                });
                newInnerRound.Action.Results.knowledge_renders = optimizedRenders;
            }

            // +++ 新增逻辑: 优化 general_data_file_preview 中的 sample_values +++
            if (newInnerRound.Action?.Tool === 'general_data_file_preview' && newInnerRound.Action.Results?.preview?.columns) {
                const columns = newInnerRound.Action.Results.preview.columns;
                // 使用 map 创建新数组，并从每个列对象中移除 sample_values
                newInnerRound.Action.Results.preview.columns = columns.map((col: any) => {
                    const { sample_values, ...restOfCol } = col;
                    return restOfCol;
                });
            }
            // +++ 新增逻辑结束 +++

            return newInnerRound;
        });

        // --- 步骤6: 构建最终的优化数据项 ---
        const { ExecutionTrace, Context, Tools, ...restOfDetail } = detail;

        // 处理 Plan 中的 ActorDescription
        let optimizedPlan = item.Round?.Plan;
        if (optimizedPlan?.ActorDescription) {
            const { ActorDescription, ...restOfPlan } = optimizedPlan;
            optimizedPlan = {
                ...restOfPlan,
                UsedActorName: usedActorName,
            };
        }

        return {
            ...item,
            Round: {
                ...item.Round,
                Execution: {
                    ...item.Round.Execution,
                    Detail: {
                        ...restOfDetail,
                        Rounds: optimizedInnerRounds || [],
                        Context: optimizedContext,
                        UsedToolNames: usedToolNames,
                    },
                },
                Plan: optimizedPlan,
            },
        };
    });

    return {
        data: newData as OptimizedTrace['data'],
        Traces: traces,
        KnowledgeBase: knowledgeBase,
        ToolBase: toolBase,
        ActorBase: actorBase,
    };
}

/**
 * 将优化后的轨迹数据转换为 YAML 格式字符串
 * @param optimizedTrace - 优化后的轨迹数据
 * @returns YAML 格式的字符串
 */
export function convertToYaml(optimizedTrace: OptimizedTrace): string {
    try {
        return yaml.dump(optimizedTrace, {
            indent: 2,
            lineWidth: 120,
            noRefs: true,
            sortKeys: false,
            quotingType: '"',
            forceQuotes: false,
        });
    } catch (error) {
        console.error('Error converting to YAML:', error);
        throw new Error('Failed to convert trace data to YAML format');
    }
}

/**
 * 处理AI Agent的执行路径记录并转换为YAML格式
 * @param originalTrace - 原始的、包含大量重复内容的JSON对象
 * @returns YAML格式的字符串
 */
export function processAgentTraceToYaml(originalTrace: OriginalTrace): string {
    const optimizedTrace = processAgentTrace(originalTrace);
    // 去除空值字段以进一步压缩
    const cleanedTrace = removeEmptyValues(optimizedTrace);
    return convertToYaml(cleanedTrace);
}

/**
 * 处理AI Agent的执行路径记录，压缩后直接返回YAML格式字符串
 * @param originalTrace - 原始的、包含大量重复内容的JSON对象
 * @returns YAML格式的字符串（用于进一步压缩）
 */
export function processAgentTraceCompressed(originalTrace: OriginalTrace): string {
    const optimizedTrace = processAgentTrace(originalTrace);
    // 去除空值字段以进一步压缩
    const cleanedTrace = removeEmptyValues(optimizedTrace);
    return convertToYaml(cleanedTrace);
}