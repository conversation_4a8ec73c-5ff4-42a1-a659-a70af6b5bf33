import React, { useState, useEffect } from 'react';
import { Button, Card, Tag, Typography, Space, Modal, Message, Input, InputTag, Radio, Checkbox } from '@arco-design/web-react';
import { IconDelete, IconUpload, IconEdit } from '@arco-design/web-react/icon';
import { ExperienceManager } from '@/app/experience/extract/components/Extraction/parse/experience';
import { ExtractedExperienceResult } from '@/app/experience/extract/components/Extraction/parse/types';
import { useStore } from '@nanostores/react';
import { userAtom } from '@/app/store/auth';
import { variantOptions } from '@/app/experience/api/experience';

interface ResultPreviewProps {
  result: string;
  sessionId?: string;
  goodCaseSessionId?: string;
  badCaseSessionId?: string;
  experienceType?: string;
  reason?: string;
}

export function ResultPreview({ result, sessionId, goodCaseSessionId, badCaseSessionId, experienceType = 'Insights-BadCase', reason = "" }: ResultPreviewProps) {
  const user = useStore(userAtom);
  const [parsedResult, setParsedResult] = useState<ExtractedExperienceResult | null>(null);
  const [experiences, setExperiences] = useState<any[]>([]);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editedExperience, setEditedExperience] = useState<any>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadSucceeded, setUploadSucceeded] = useState(false);
  const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);
  const [diagnosisSummary, setDiagnosisSummary] = useState('');
   const [exp_reason, setExpReason] = useState('');
  const [rootCauseAnalysis, setRootCauseAnalysis] = useState('');
  const [isVariantModalVisible, setIsVariantModalVisible] = useState(false);
  const [selectedVariants, setSelectedVariants] = useState<string[]>(['newbie']);

  useEffect(() => {
    if (result) {
      try {
        let jsonToParse;
        const firstBraceIndex = result.indexOf('{');
        const lastBraceIndex = result.lastIndexOf('}');

        if (firstBraceIndex !== -1 && lastBraceIndex > firstBraceIndex) {
          jsonToParse = result.substring(firstBraceIndex, lastBraceIndex + 1);
        } else {
          jsonToParse = result
            .replace(/```json\s*/g, '')
            .replace(/```\s*/g, '')
            .trim();
        }
        const parsed = JSON.parse(jsonToParse);
        setParsedResult(parsed);
        const extractedResult = parsed as ExtractedExperienceResult;
        setExperiences(extractedResult.experiences || []);
        setDiagnosisSummary(extractedResult.diagnosis_summary || '');
        setRootCauseAnalysis(extractedResult.root_cause_analysis || '');
        setExpReason(extractedResult.exp_reason || '');
      } catch (error) {
        console.error('Error parsing result JSON:', error);
        Message.error('Failed to parse result JSON.');
        setParsedResult(null);
        setExperiences([]);
        setDiagnosisSummary('');
        setRootCauseAnalysis('');
        setExpReason('');
      }
    }
  }, [result]);

  const handleEdit = (index: number) => {
    setEditingIndex(index);
    setEditedExperience({ ...experiences[index] });
  };

  const handleSave = (index: number) => {
    const newExperiences = [...experiences];
    newExperiences[index] = editedExperience;
    setExperiences(newExperiences);
    setEditingIndex(null);
    setEditedExperience(null);
  };

  const handleCancel = () => {
    setEditingIndex(null);
    setEditedExperience(null);
  };

  const handleFieldChange = (field: string, value: any) => {
    setEditedExperience((prev: any) => ({ ...prev, [field]: value }));
  };

  const handleDeleteExperience = (index: number) => {
    setExperiences(experiences.filter((_, i) => i !== index));
  };



  const handleUpload = async () => {
    setIsVariantModalVisible(true);
  };

  const handleConfirmUpload = async () => {
    setIsVariantModalVisible(false);
    setUploading(true);
    const experienceManager = new ExperienceManager();
    const username = user?.username || "";
    try {
        // 遍历所有选中的 variant 进行多次上传
        for (const variant of selectedVariants) {
            if (sessionId) {
                const resultToUpload = {
                    ...(parsedResult as ExtractedExperienceResult),
                    experiences,
                };
                await experienceManager.parseAndStoreStandardExperience(JSON.stringify(resultToUpload), sessionId, username, variant, experienceType, reason);
            } else if (goodCaseSessionId && badCaseSessionId) {
                const resultToUpload = {
                    ...(parsedResult as ExtractedExperienceResult),
                    experiences,
                };
                await experienceManager.parseAndStoreStandardExperience(JSON.stringify(resultToUpload), `goodId_${goodCaseSessionId}_badId_${badCaseSessionId}`, username, variant, experienceType, reason);
            }
        }
        setUploadSucceeded(true);
        setIsSuccessModalVisible(true);
    } catch (error) {
        console.error('Upload failed:', error);
        Message.error('Upload failed.');
    } finally {
        setUploading(false);
    }
  };

  if (!parsedResult || experiences.length === 0) {
    return null;
  }

  return (
    <div className="mt-4">
      <div className="flex justify-between items-center mb-4">
        <Typography.Title heading={6}>Experience Preview</Typography.Title>
        {!uploadSucceeded && (
          <Button type="primary" icon={<IconUpload />} onClick={handleUpload} loading={uploading} disabled={experiences.length === 0}>
            Upload
          </Button>
        )}
      </div>

      <>
        <Card className="mb-4" title="Diagnosis Summary">
          <p>{diagnosisSummary ?? ""}</p>
        </Card>
        <Card className="mb-4" title="Root Cause Analysis">
          <pre className="whitespace-pre-wrap bg-gray-100 p-2 rounded mt-2">{rootCauseAnalysis ?? ""}</pre>
        </Card>
        <Card className="mb-4" title="提取经验原因">
          <p>{exp_reason ?? ""}</p>
        </Card>
      </>


      {experiences.map((exp, index) => (
        <Card key={index} className="mb-4" title={
          <div className="flex justify-between items-center">
            <span>{exp.title}</span>
            <Space>
              {editingIndex === index ? (
                <>
                  <Button size="small" type="primary" onClick={() => handleSave(index)}>Save</Button>
                  <Button size="small" onClick={handleCancel}>Cancel</Button>
                </>
              ) : (
                <Button size="small" icon={<IconEdit />} onClick={() => handleEdit(index)}>Edit</Button>
              )}
              <Button icon={<IconDelete />} status="danger" type="text" onClick={() => handleDeleteExperience(index)} />
            </Space>
          </div>
        }>
          {editingIndex === index && editedExperience ? (
            <div>
              <p><strong>Apply Type:</strong> {exp.apply_type}</p>
              <div className="mt-2">
                <p><strong>RAG Content</strong></p>
                <Input.TextArea
                  autoSize={{ minRows: 2 }}
                  value={editedExperience.rag_content}
                  onChange={(value) => handleFieldChange('rag_content', value)}
                />
              </div>
              {/* <div className="mt-2">
                <p><strong>Scenarios</strong></p>
                <Input.TextArea
                  autoSize={{ minRows: 2 }}
                  value={editedExperience.scenarios}
                  onChange={(value) => handleFieldChange('scenarios', value)}
                />
              </div>
              <div className="mt-2">
                <p><strong>Limit Scenarios</strong></p>
                <InputTag
                  allowClear
                  placeholder="Press Enter to add a tag"
                  value={editedExperience.limit_scenarios || []}
                  onChange={(value) => handleFieldChange('limit_scenarios', value)}
                  className="w-full"
                />
              </div> */}
              <div className="mt-2">
                <p><strong>Tag</strong></p>
                <Input
                  placeholder="Enter tag"
                  value={editedExperience.tag || ''}
                  onChange={(value) => handleFieldChange('tag', value)}
                  className="w-full"
                />
              </div>
              <Typography.Title heading={6} className="mt-2">Experience</Typography.Title>
              <Input.TextArea
                autoSize={{ minRows: 4 }}
                value={editedExperience.experience}
                onChange={(value) => handleFieldChange('experience', value)}
                className="whitespace-pre-wrap bg-gray-100 p-2 rounded mt-2"
              />
            </div>
          ) : (
            <div>
              <p><strong>Apply Type:</strong> {exp.apply_type}</p>
              <p><strong>RAG Content:</strong>{exp.rag_content}</p>
              {/* <p><strong>Scenarios:</strong> {exp.scenarios}</p> */}
              {/* <p><strong>Limit Scenarios:</strong> <Space size="medium" wrap>{(exp.limit_scenarios || []).map((tag: string, i: number) => tag.trim() && <Tag key={i}>{tag.trim()}</Tag>)}</Space></p> */}
              <p><strong>Tag:</strong> {exp.tag || ''}</p>
              <Typography.Title heading={6} className="mt-2">Experience</Typography.Title>
              <pre className="whitespace-pre-wrap bg-gray-100 p-2 rounded mt-2">{exp.experience}</pre>
            </div>
          )}
        </Card>
      ))}


      <Modal
        title="Success"
        visible={isSuccessModalVisible}
        onOk={() => setIsSuccessModalVisible(false)}
        onCancel={() => setIsSuccessModalVisible(false)}
      >
        <p>Upload successful!</p>
      </Modal>
      
      <Modal
        title="选择上传类型"
        visible={isVariantModalVisible}
        onOk={handleConfirmUpload}
        onCancel={() => setIsVariantModalVisible(false)}
        okText="确认上传"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16 }}>
          <p>请选择要上传的经验类型：</p>
          <Checkbox.Group
            value={selectedVariants}
            onChange={(value) => setSelectedVariants(value as string[])}
            style={{ marginTop: 8 }}
          >
            {variantOptions.map((option) => (
              <Checkbox key={option.value} value={option.value}>
                {option.label}
              </Checkbox>
            ))}
          </Checkbox.Group>
        </div>
      </Modal>
    </div>
  );
}