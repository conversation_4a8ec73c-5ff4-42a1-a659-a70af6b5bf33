import React, { useState, useEffect } from 'react';
import { Input, Button, Modal, Message } from '@arco-design/web-react';
import { apiClient } from '@/app/api/request';
import { ResultPreview } from './ResultPreview';
import { fetchTraceWithRetry } from '../../utils';

interface BatchExtractionProps {
  model: string;
  temperature: number;
}

interface ExtractionItem {
  id: string;
  sessionId: string;
  checkReason: string;
  result: string;
  isLoading: boolean;
  query: string;
}

interface IChatResponse {
    choices: {
      message: {
        content: string
      }
    }[]
  }

const initialExtractionItems: Omit<ExtractionItem, 'id' | 'result' | 'isLoading' | 'query'>[] = [
   {  sessionId: '', checkReason: '' }
];

export function BatchExtraction({ model, temperature }: BatchExtractionProps) {
  const [items, setItems] = useState<ExtractionItem[]>(() =>
    initialExtractionItems.map(item => ({
      ...item,
      id: String(Math.random()),
      result: '',
      isLoading: false,
      query: '',
    }))
  );
  const [systemPrompt, setSystemPrompt] = useState('');
  const [isPromptExpanded, setIsPromptExpanded] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [jsonInput, setJsonInput] = useState('');

  useEffect(() => {
    const fetchPrompt = async () => {
      try {
        const response = await fetch(`https://tosv-cn.byted.org/obj/ttclient-android/aime/single_system.md?_t=${Date.now()}`, {
          method: 'GET',
          cache: 'no-store'
        });;
        const text = await response.text();
        setSystemPrompt(text);
      } catch (error) {
        console.error('Error fetching system prompt:', error);
      }
    };
    fetchPrompt();
  }, []);

  const handleItemChange = (id: string, update: Partial<Omit<ExtractionItem, 'id'>>) => {
    setItems(prevItems =>
      prevItems.map(item => (item.id === id ? { ...item, ...update } : item))
    );
  };

  const handleAddItem = () => {
    setItems([...items, { id: String(Math.random()), sessionId: '', checkReason: '', result: '', isLoading: false, query: '' }]);
  };

  const handleRemoveItem = (id: string) => {
    setItems(items.filter(item => item.id !== id));
  };

  const handleModalOk = () => {
    if (!jsonInput.trim()) {
        Message.error('请输入内容');
        return;
    }
    try {
      const parsedData = JSON.parse(jsonInput);
      if (Array.isArray(parsedData)) {
        const newItems: ExtractionItem[] = parsedData.map((item: any) => ({
          sessionId: item.sessionId || '',
          checkReason: item.checkReason || '',
          id: String(Math.random()),
          result: '',
          isLoading: false,
          query: '',
        }));
        setItems(newItems);
        setIsModalVisible(false);
        setJsonInput('');
      } else {
        Message.error('JSON数据格式不正确，根节点应该是一个数组');
      }
    } catch (error) {
      Message.error('JSON解析失败，请检查格式');
      console.error('Error parsing JSON:', error);
    }
  };

  const handleRunAll = async () => {
    if (!model) return;

    const runSingle = async (item: ExtractionItem) => {
        if (!item.sessionId) return;

        handleItemChange(item.id, { isLoading: true, result: '' });

        let queryToUse = item.query;
        if (!queryToUse) {
            try {
                const res = await fetchTraceWithRetry(item.sessionId);
                //only check trace
                const traceData = JSON.stringify(res, null);
                queryToUse = `<trace>
${traceData}
</trace>`;
                handleItemChange(item.id, { query: queryToUse });
            } catch (e) {
                console.error(e);
                handleItemChange(item.id, { result: `获取 trace 失败: ${(e as Error).message}`, isLoading: false });
                return;
            }
        }

        const finalQuery = `<check>
${item.checkReason}
</check>
${queryToUse}`;

        try {
            const res = await apiClient.ChatStream({
              model,
              messages: [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: finalQuery },
              ],
              temperature,
              max_tokens: 64000
            });
            const content = (res as IChatResponse)?.choices?.[0]?.message?.content ?? '';
            handleItemChange(item.id, { result: content, isLoading: false });
          } catch (error) {
            console.error(`Error running query for session ${item.sessionId}:`, error);
            handleItemChange(item.id, { result: 'Error: Could not fetch result.', isLoading: false });
          }
    }

    const promises = items.map(item => runSingle(item));
    await Promise.all(promises);
  };

  return (
    <div>
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold">System Prompt</h2>
          <Button onClick={() => setIsModalVisible(true)}>从 JSON 导入</Button>
        </div>
        <textarea
          className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
          value={systemPrompt}
          onChange={(e) => setSystemPrompt(e.target.value)}
          rows={isPromptExpanded ? Math.max(10, systemPrompt.split('\n').length) : 3}
        />
        <button
          className="text-sm text-blue-500 mt-1"
          onClick={() => setIsPromptExpanded(!isPromptExpanded)}
        >
          {isPromptExpanded ? '收起' : '展开'}
        </button>
      </div>

      {items.map((item) => (
        <div key={item.id} className="mb-4 border p-4 rounded-md">
          <div className="flex items-center gap-4 mb-4">
            <Input
              placeholder="Enter Session ID"
              value={item.sessionId}
              onChange={value => handleItemChange(item.id, { sessionId: value })}
              className="w-1/2"
            />
            {items.length > 1 && (
                <Button status="danger" onClick={() => handleRemoveItem(item.id)}>
                    删除
                </Button>
            )}
          </div>
          <div className="mb-4">
            <p className="font-semibold">{'<check> 原因:'}</p>
            <Input.TextArea
              value={item.checkReason}
              onChange={value => handleItemChange(item.id, { checkReason: value })}
              rows={3}
            />
          </div>
          {item.result && (
            <ResultPreview result={item.result} sessionId={item.sessionId} reason={item.checkReason}/>
          )}
           {item.isLoading && (
            <div className="p-4 rounded-md mt-2">
              <p className="font-semibold">提取中...</p>
            </div>
          )}
        </div>
      ))}

      <div className="flex gap-4 mb-4">
        <Button type="primary" onClick={handleAddItem}>
          添加一项
        </Button>
        <Button type="primary" status="success" onClick={handleRunAll} loading={items.some(i => i.isLoading)}>
          全部运行
        </Button>
      </div>
      <Modal
        title="从JSON导入"
        visible={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => {
            setIsModalVisible(false);
            setJsonInput('');
        }}
        autoFocus={false}
        focusLock={true}
      >
        <Input.TextArea
          placeholder={`[
  {
    "sessionId": "xxxx",
    "checkReason": "xxxx"
  }
]`}
          value={jsonInput}
          onChange={setJsonInput}
          rows={15}
          style={{ height: 400 }}
        />
      </Modal>
    </div>
  );
}