import React from 'react';
import { Tabs } from '@arco-design/web-react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { SingleExtraction } from './SingleExtraction';
import { ComparisonExtraction } from './ComparisonExtraction';
import { ScriptExtraction } from './ScriptExtraction';
import { BatchExtraction } from './BatchExtraction';

const TabPane = Tabs.TabPane;

interface ExtractionTabsProps {
  model: string;
  temperature: number;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export function ExtractionTabs({ model, temperature, activeTab, onTabChange }: ExtractionTabsProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const handleTabChange = (key: string) => {
    onTabChange(key);

    // 创建新的URL参数
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', key);

    // 更新URL，不刷新页面
    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  };

  return (
    <Tabs activeTab={activeTab} onChange={handleTabChange}>
      <TabPane key="1" title="单个任务提取">
        <SingleExtraction model={model} temperature={temperature} />
      </TabPane>
      <TabPane key="2" title="对比提取">
        <ComparisonExtraction model={model} temperature={temperature} />
      </TabPane>
      <TabPane key="3" title="批量提取">
        <BatchExtraction model={model} temperature={temperature} />
      </TabPane>
      <TabPane key="4" title="模版提取">
        <ScriptExtraction model={model} temperature={temperature} />
      </TabPane>
    </Tabs>
  );
}