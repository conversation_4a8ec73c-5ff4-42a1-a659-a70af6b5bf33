export interface SessionInfo {
  session_id: string;

  [key: string]: any;
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ChatCompletion {
  id: string;
  created_at: string;
  updated_at: string;
  prompt: string;
  response: string;
  type: string;
  metadata: string;
  status: string;
  model_name: string;
}

export interface SessionChatData {
  chat_completions: ChatCompletion[];
  total: number;
}


/**
 * Agent 执行轨迹条目
 */
export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
  agent: string;
}

export interface Trajectory {
  messages: Message[];
  query: string;// 用户原始 query
}

// API接口相关类型定义
export interface PracticalGuidance {
  name: string;
  overall_strategy?: string;
  specific_steps?: string[];
  key_techniques?: string[];
  important_notes?: string[];
  flexible_application?: string[];
  applicationScenarios: string[];
  // 保持向后兼容性
  implementationSteps?: string[];
  keyConsiderations?: string[];
  commonPitfalls?: string[];
}

export interface ExperienceItem {
  type: string;
  title: string;
  ragMatch: string[];
  practicalGuidance: PracticalGuidance;
}

export interface ExtractionMetadata {
  taskName: string;
  taskCategory: string;
  executionContext: string;
  overallComplexity: string;
  taskOutcome: string;
  type: string;
}

export interface ExperienceSummary {
  totalExperiencesFound: string;
  extractionRationale: string;
}

export interface Experience {
  id?: string;
  extractionMetadata: ExtractionMetadata;
  experienceSummary: ExperienceSummary;
  experiences: ExperienceItem[];
  createTime?: Date;
  updateTime?: Date;
  createUser: string;
}

export interface ApiResponse<T = any> {
  code: number;
  data: T;
  msg: string;
}

// LLM返回的经验提取结果
export interface ExtractedExperienceResult {
  diagnosis_summary: string;
  root_cause_analysis: string;
  exp_reason: string;
  experiences: {
    title: string;
    apply_type: string;
    rag_content: string;
    // scenarios: string;
    experience: string;
    limit_scenarios: string[];
    tag: string;
  }[];
}

// 新的存储API接口类型定义
export interface ExperienceStoreItem {
  vector_content: string;
  title: string;
  type: string;
  experience_content: string;
  source_id: string;
  apply_type: 'planner' | 'mewtwo';
  publish_status: string;
  extract_type: string;
  limit: string;
  variant: string;
  ext: string;
}


// 脚本提取结果类型
export interface ScriptExtractionResult {
  scripts: {
    title: string;
    file_path: string;
    content: string;
    used_when: string;
    notice: string;
  }[];
}

