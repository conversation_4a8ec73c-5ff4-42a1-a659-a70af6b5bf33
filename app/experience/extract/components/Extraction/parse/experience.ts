import axios, { AxiosResponse } from 'axios';
import {
  ExperienceStoreItem,
  ExtractedExperienceResult,
} from './types';
import { BASE_URL } from '@/app/experience/api/experience';

export class ExperienceManager {
  private readonly baseUrl = BASE_URL;

  /**
   * 通用的经验存储接口
   */
  async storeExperienceItem(experienceItem: ExperienceStoreItem, username: string): Promise<any> {
    try {
      // 获取用户名
      experienceItem.extract_type = username;
      const response: AxiosResponse<any> = await axios.post(
        `${this.baseUrl}/api/experiences`,
        experienceItem,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.status === 200) {
        console.log(`✅ 成功存储经验: ${experienceItem.title}`);
        return response.data;
      } else {
        throw new Error(`存储失败，状态码: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ 存储经验失败:', error);
      throw error;
    }
  }

  /**
   * 解析标准经验提取结果并存储
   */
  async parseAndStoreStandardExperience(
    extractedExperienceJson: string,
    // experienceType: 'mewtwo' | 'planner',
    sessionId: string,
    username: string,
    variant: string = 'newbie',
    experienceType: string = 'Insights-BadCase',
    reason: string = '',
  ): Promise<void> {
    try {
      // 清理JSON字符串，移除可能的markdown标记
      const cleanedJson = extractedExperienceJson
        .replace(/```json\s*/g, '')
        .replace(/```\s*/g, '')
        .trim();

      const extractedResult: ExtractedExperienceResult = JSON.parse(cleanedJson);

      console.log(`📊 解析到 ${extractedResult.experiences.length} 个经验项，开始逐个存储...`);

      let successCount = 0;
      let failureCount = 0;

      // 循环存储每个经验项
      for (let i = 0; i < extractedResult.experiences.length; i++) {
        const experience = extractedResult.experiences[i];

        try {
          // 构建vector_content
          const vectorContent = experience.rag_content || "";

          // 从experience中提取标题
          const title = experience.title;

          // 构建experience_content
          let experienceContent = `# UsedWhen
${experience.rag_content}`;
          
          // 只有当 limit_scenarios 不为空时才添加 LimitScenarios 部分
          if (experience.limit_scenarios && experience.limit_scenarios.length > 0) {
            experienceContent += `

# LimitScenarios (同时满足以下场景时才适用)
${experience.limit_scenarios.join('; ')}`;
          }
          
          experienceContent += `

# Content
${experience.experience}`;

          // analysis
          const analysis =  `## 分析原因总结
${extractedResult.diagnosis_summary}

## 具体BadCase原因
${extractedResult.root_cause_analysis}

## 提取原因
${extractedResult.exp_reason}

## 提取时的人工 reason
${reason}
`;
          

          // 构建存储项，将tag解析到limit字段
          const storeItem: ExperienceStoreItem = {
            vector_content: vectorContent,
            title: title,
            type: experienceType,
            experience_content: experienceContent,
            source_id: sessionId,
            apply_type: experience.apply_type as 'planner' | 'mewtwo',
            publish_status: "pending",
            // extract_type: `llm | ${username}`
            extract_type: username,
            limit: experience.tag || '', // 将tag解析到limit字段
            variant: variant,
            ext: analysis,
          };

          // 存储到服务器
          await this.storeExperienceItem(storeItem, username);
          successCount++;

          console.log(`✅ 经验项 ${i + 1}/${extractedResult.experiences.length} 存储成功: ${title}`);

        } catch (error) {
          failureCount++;
          console.error(`❌ 经验项 ${i + 1}/${extractedResult.experiences.length} 存储失败:`, error);
        }

        // 添加延迟，避免请求过于频繁
        if (i < extractedResult.experiences.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      console.log(`\n📊 经验存储完成统计:`);
      console.log(`   ✅ 成功存储: ${successCount} 个经验项`);
      console.log(`   ❌ 存储失败: ${failureCount} 个经验项`);
      console.log(`   📋 总计处理: ${extractedResult.experiences.length} 个经验项`);

    } catch (error) {
      console.error('❌ 解析经验提取结果失败:', error);
      console.error('原始JSON:', extractedExperienceJson);
      throw new Error(`解析经验提取结果失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

}