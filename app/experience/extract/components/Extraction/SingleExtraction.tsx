import React, { useState, useEffect, useRef } from 'react';
import { Input, Button, Switch,Space, Select } from '@arco-design/web-react';
import { apiClient } from '@/app/api/request';
import { ResultPreview } from './ResultPreview';
import { classifyScene, getEvaluationCriteria, fetchTraceWithRetry } from '../../utils';
import { processAgentTrace, processAgentTraceCompressed } from "@/app/experience/extract/compress";
import { HelpTooltip } from "@/app/components/ui/help-tooltip";
import { COMPRESSION_HELP_CONTENT } from "@/app/constants/compression-help";
import Editor from "@monaco-editor/react";
import { Message } from './parse/types';
import { experienceTypeOptions } from '@/app/experience/api/experience';

interface SingleExtractionProps {
  model: string;
  temperature: number;
}

interface IChatResponse {
  choices: {
    message: {
      content: string
    }
  }[]
}

export function SingleExtraction({ model, temperature }: SingleExtractionProps) {
  const [sessionId, setSessionId] = useState('');
  const [query, setQuery] = useState('');
  const [result, setResult] = useState('');
  const [manyResult, setManyResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [isFetchingTrace, setIsFetchingTrace] = useState(false);
  const [systemPrompt, setSystemPrompt] = useState('');
  const [selectSystemPrompt, setSelectSystemPrompt] = useState('');
  const [classificationPrompt, setClassificationPrompt] = useState('');
  const [scene, setScene] = useState('');
  const [criteria, setCriteria] = useState('');
  const [checkContent, setCheckContent] = useState('');
  const [isPromptExpanded, setIsPromptExpanded] = useState(false);
  const [isResultExpanded, setIsResultExpanded] = useState(false);
  const [isContinueQuestion, setIsContinueQuestion] = useState(false);
  const isProgrammaticChange = useRef(false);
  const [useSceneClassification, setUseSceneClassification] = useState(false);
  const [isSingleRoundOnly, setIsSingleRoundOnly] = useState(false);
  const [isCompressionEnabled, setIsCompressionEnabled] = useState(false);
  const [isMultipleExtraction, setIsMultipleExtraction] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [followUpQuery, setFollowUpQuery] = useState('');
  const [experienceType, setExperienceType] = useState<string>('Insights-BadCase');

  useEffect(() => {
    if (isProgrammaticChange.current) {
        isProgrammaticChange.current = false;
        return;
    }
    setScene('');
    setCriteria('');
  }, [query]);

  useEffect(() => {
    const fetchPrompt = async () => {
      try {
        const promptUrlMap = {
          'Insights-BadCase': `https://tosv-cn.byted.org/obj/ttclient-android/aime/single_system_badcase.md?_t=${Date.now()}`,
          'Insights-Workflow': `https://tosv-cn.byted.org/obj/ttclient-android/aime/single_system_workflow.md?_t=${Date.now()}`,
          'Insights-Planner': `https://tosv-cn.byted.org/obj/ttclient-android/aime/single_system_planner.md?_t=${Date.now()}`,
          'Insights-Error': `https://tosv-cn.byted.org/obj/ttclient-android/aime/single_system_error.md?_t=${Date.now()}`
        };
        const promptUrl = experienceType in promptUrlMap ? promptUrlMap[experienceType as keyof typeof promptUrlMap] : promptUrlMap['Insights-Planner'];
        const response = await fetch(promptUrl, {
          method: 'GET',
          cache: 'no-store'
        });
        const text = await response.text();
        setSystemPrompt(text);
      } catch (error) {
        console.error('Error fetching system prompt:', error);
      }
    };

    const fetchSelectPrompt = async () => {
      try {
        const response = await fetch(`https://tosv-cn.byted.org/obj/ttclient-android/aime/check_exp_system.md?_t=${Date.now()}`, {
          method: 'GET',
          cache: 'no-store'
        });;
        const text = await response.text();
        setSelectSystemPrompt(text);
      } catch (error) {
        console.error('Error fetching system prompt:', error);
      }
    };

    const fetchClassificationPrompt = async () => {
        try {
          const response = await fetch(`https://tosv-cn.byted.org/obj/ttclient-android/aime/sence.md?_t=${Date.now()}`, {
          method: 'GET',
          cache: 'no-store'
        });;
          const text = await response.text();
          setClassificationPrompt(text);
        } catch (error) {
          console.error('Error fetching classification prompt:', error);
        }
    };

    fetchPrompt();
    fetchSelectPrompt();
    fetchClassificationPrompt();
  }, [experienceType]);

  const handleFetchTrace = async () => {
    if (!sessionId) {
      return null;
    }
    setIsFetchingTrace(true);
    try {
      let res = await fetchTraceWithRetry(sessionId);
      let traceData: string;
      
      if (isCompressionEnabled) {
        traceData = processAgentTraceCompressed(res);
      } else {
        traceData = JSON.stringify(res, null);
      }
      
      if (isSingleRoundOnly && res.data) {
        let userMessageCount = 0;
        const secondUserMessageIndex = res.data.findIndex((item: any) => {
          if (item.UserMessage) {
            userMessageCount++;
          }
          return userMessageCount === 2;
        });

        if (secondUserMessageIndex !== -1) {
          res.data = res.data.slice(0, secondUserMessageIndex);
          // 重新处理压缩后的数据
          if (isCompressionEnabled) {
            traceData = processAgentTraceCompressed(res);
          } else {
            traceData = JSON.stringify(res, null);
          }
        }
      }
      
      const newQuery = `<trace>
${traceData}
</trace>`;
      setQuery(newQuery);
      setIsFetchingTrace(false);
      return newQuery;
    } catch (e) {
      console.error(e);
      setQuery(`获取 trace 失败: ${(e as Error).message}`);
      setIsFetchingTrace(false);
      return null;
    }
  };

  const handleRun = async () => {
    if (isLoading || !model) return;
    const queryFromUI = query || await handleFetchTrace();
    if (!queryFromUI) {
        return;
    }
    setIsLoading(true);
    setResult('');

    let rawQuery = queryFromUI;
    if (queryFromUI.startsWith('<check>')) {
        const checkEndIndex = queryFromUI.indexOf('</check>');
        if (checkEndIndex !== -1) {
            rawQuery = queryFromUI.substring(checkEndIndex + '</check>'.length).trim();
        }
    }

    let finalQuery = rawQuery;
    let combinedCheckContent = checkContent;

    if (useSceneClassification) {
        let currentScene = scene;
        let currentCriteria = criteria;

        if (!currentScene) {
            setLoadingMessage('场景识别中...');
            try {
                let classificationQuery = rawQuery;
                if (rawQuery.startsWith('<trace>')) {
                    const traceJsonString = rawQuery.substring('<trace>'.length, rawQuery.lastIndexOf('</trace>')).trim();
                    try {
                        const traceData = JSON.parse(traceJsonString);
                        if (traceData?.data?.[0]?.UserMessage?.content) {
                            classificationQuery = traceData.data[0].UserMessage.content;
                        }
                    } catch (e) {
                        console.error("Failed to parse trace data", e);
                    }
                }
                currentScene = await classifyScene(classificationQuery, classificationPrompt, model, temperature);
                const criteriaData = await getEvaluationCriteria();
                const sceneCriteria = criteriaData?.[currentScene] ?? '暂无评价标准';
                currentCriteria = sceneCriteria;
                setScene(currentScene);
                setCriteria(currentCriteria);
            } catch (error) {
                console.error(`Error during classification:`, error);
                setResult('Error: Could not classify scene.');
                setIsLoading(false);
                setLoadingMessage('');
                return;
            }
        }
        
        if (currentCriteria) {
            if (combinedCheckContent) {
                combinedCheckContent += `\n${currentCriteria}`;
            } else {
                combinedCheckContent = currentCriteria;
            }
        }

    } else {
        setScene('');
        setCriteria('');
    }

    if (combinedCheckContent) {
        finalQuery = `<check>
${combinedCheckContent}
</check>
${rawQuery}`;
    }

    if (query !== finalQuery) {
        isProgrammaticChange.current = true;
        setQuery(finalQuery);
    }

    const runSingleExtraction = async () => {
      const initialMessages: Message[] = [
        { role: 'system', content: systemPrompt, agent: 'default' },
        { role: 'user', content: finalQuery, agent: 'default' },
      ];

      try {
        const res = await apiClient.ChatStream({
          model,
          messages: initialMessages,
          temperature,
          max_tokens: 64000
        });
        const content = (res as IChatResponse)?.choices?.[0]?.message?.content ?? '';
        return content;
      } catch (error) {
        console.error(`Error running query:`, error);
        throw error;
      }
    };

    try {
      if (isMultipleExtraction) {
        setLoadingMessage('多次提取中 (1/3)...');
        const results = [];
        
        for (let i = 0; i < 3; i++) {
          setLoadingMessage(`多次提取中 (${i + 1}/3)...`);
          const extractionResult = await runSingleExtraction();
          results.push(`## 第${i + 1}次提取结果:\n${extractionResult}`);
          const combinedResult = results.join('\n\n---\n\n');
          setManyResult(combinedResult);
        }
        
        // 使用 aws_sdk_claude4_sonnet 模型进行最终提取
        setLoadingMessage('最终提取中...');
        try {
          const finalRes = await apiClient.ChatStream({
            model: 'aws_sdk_claude4_sonnet',
            messages: [
              { role: 'system', content: selectSystemPrompt },
              { role: 'user', content: results.join('\n\n') },
            ],
            temperature,
            max_tokens: 64000
          });
          const finalContent = (finalRes as IChatResponse)?.choices?.[0]?.message?.content ?? '';
          setResult(finalContent);
        } catch (error) {
          console.error(`Error running final extraction:`, error);
          setResult('Error: Could not fetch final result.');
        }
      } else {
        setLoadingMessage('经验提取中...');
        const content = await runSingleExtraction();
        setResult(content);
        
        const initialMessages: Message[] = [
          { role: 'system', content: systemPrompt, agent: 'default' },
          { role: 'user', content: finalQuery, agent: 'default' },
        ];
        setMessages([...initialMessages, { role: 'assistant', content, agent: 'default' }]);
      }
    } catch (error) {
      console.error(`Error running query:`, error);
      setResult('Error: Could not fetch result.');
    }
    setIsLoading(false);
    setLoadingMessage('');
}

const handleFollowUp = async () => {
  if (isLoading || !model || !followUpQuery) return;
  setIsLoading(true);
  setLoadingMessage('多轮对话中...');

  const newMessages: Message[] = [...messages, { role: 'user', content: followUpQuery, agent: 'default' }];
  setMessages(newMessages);
  setFollowUpQuery('');

  try {
    const res = await apiClient.ChatStream({
      model,
      messages: newMessages,
      temperature,
      max_tokens: 64000
    });
    const content = (res as IChatResponse)?.choices?.[0]?.message?.content ?? '';
    setResult(content);
    setMessages([...newMessages, { role: 'assistant', content, agent: 'default' }]);
  } catch (error) {
    console.error(`Error running follow-up query:`, error);
    setResult('Error: Could not fetch result.');
  }
  setIsLoading(false);
  setLoadingMessage('');
};

  return (
    <div>
      <div className="mb-4">
        <h2 className="text-lg font-semibold mb-2">System Prompt</h2>
        <textarea
          className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
          value={systemPrompt}
          onChange={(e) => setSystemPrompt(e.target.value)}
          rows={isPromptExpanded ? Math.max(10, systemPrompt.split('\n').length) : 3}
        />
        <button
          className="text-sm text-blue-500 mt-1"
          onClick={() => setIsPromptExpanded(!isPromptExpanded)}
        >
          {isPromptExpanded ? '收起' : '展开'}
        </button>
      </div>
      <div className="flex items-center gap-4 mb-4">
        <Input
          placeholder="Enter Session ID"
          value={sessionId}
          onChange={setSessionId}
          className="w-1/2"
        />
        <Button type="primary" onClick={handleFetchTrace} loading={isFetchingTrace}>
          {isFetchingTrace ? '拉取中...' : '拉取 trace'}
        </Button>
      </div>
      <div className="flex items-center gap-4 mb-4">
        <Input.TextArea
          value={checkContent}
          placeholder={experienceType === 'Insights-BadCase' ? '请输入经验 badcase 的reason(或者不符合预期的地方)' : experienceType === 'Insights-Workflow' ? '请输入经验 workflow 的reason(或者不符合预期的地方)' : experienceType === 'Insights-Error' ? '请输入经验 error 的reason(或者不符合预期的地方)' : '请输入经验 planner 的reason(或者不符合预期的地方)'}
          onChange={setCheckContent}
          rows={1}
          className="flex-1"
        />
        <div className="flex items-center gap-2">
          <span>经验类型:</span>
          <Select
            value={experienceType}
            onChange={setExperienceType}
            style={{ width: 200 }}
            options={experienceTypeOptions.filter(option => 
              option.value === 'Insights-BadCase' || option.value === 'Insights-Planner' || option.value === 'Insights-Workflow' || option.value === 'Insights-Error'
            )}
          />
        </div>
      </div>
      <div className="mb-4">
        <p className="font-semibold">Query:</p>
        <div style={{ border: '1px solid var(--color-border-2)', borderRadius: 'var(--border-radius-medium)' }}>
          <Editor
            height="240px" // 对应原来 rows={10} 的大致高度
            language="plaintext"
            value={query}
            onChange={(value) => setQuery(value || '')}
            options={{
              wordWrap: 'on',
              minimap: { enabled: true },
              scrollBeyondLastLine: false,
              automaticLayout: true,
              unusualLineTerminators: 'off',
              unicodeHighlight: {
                ambiguousCharacters: false,
              },
            }}
          />
        </div>
      </div>
      {scene && (
        <div className="mb-4">
          <h2 className="text-lg font-semibold mb-2">识别场景</h2>
          <div className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm">
            {scene}
          </div>
        </div>
      )}
      {criteria && (
        <div className="mb-4">
          <h2 className="text-lg font-semibold mb-2">评价标准</h2>
          <pre className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm">
            {criteria}
          </pre>
        </div>
      )}
      <div className="flex items-center gap-2 mb-4">
        <Switch checked={useSceneClassification} onChange={setUseSceneClassification} />
        <label onClick={() => setUseSceneClassification(v => !v)} className="cursor-pointer select-none">
            走场景识别逻辑
        </label>
        <Switch checked={isSingleRoundOnly} onChange={setIsSingleRoundOnly} />
        <label onClick={() => setIsSingleRoundOnly(v => !v)} className="cursor-pointer select-none">
            是否只提取第一轮对话
        </label>
        <Switch checked={isCompressionEnabled} onChange={setIsCompressionEnabled} />
        <label onClick={() => setIsCompressionEnabled(v => !v)} className="cursor-pointer select-none">
          上下文压缩
        </label>
        <HelpTooltip content={COMPRESSION_HELP_CONTENT} />
        <Switch checked={isMultipleExtraction} onChange={setIsMultipleExtraction} />
        <label onClick={() => setIsMultipleExtraction(v => !v)} className="cursor-pointer select-none">
          是否多次提取(3次)
        </label>
      </div>
       <Space size='large'>
        <Button type="primary" onClick={handleRun} loading={isLoading} className="mb-4">
            {isLoading ? loadingMessage : 'Run Extraction'}
        </Button>
        {
          messages.length > 0 &&  ( <Button type="primary" onClick={() => setIsContinueQuestion(!isContinueQuestion)}>
           二次追问 {isContinueQuestion ? '收起' : '展开'}
          </Button>)
        }
      </Space>
      {isMultipleExtraction && (
        <div className="mb-4 mt-6">
          <h2 className="text-lg font-semibold mb-2">多次提取后经验判断 SystemPrompt</h2>
          <textarea
            className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
            value={selectSystemPrompt}
            onChange={(e) => setSelectSystemPrompt(e.target.value)}
            rows={isPromptExpanded ? Math.max(10, selectSystemPrompt.split('\n').length) : 3}
          />
        </div>
      )
      }
      {messages.length > 0 && (
        <div className="mt-4">
           {
            isContinueQuestion ?  <div>
            <div className="bg-gray-100 p-4 rounded-md w-full" style={{maxHeight: '400px', overflowY: 'auto'}}>
              {messages.map((msg, index) => {
                if (msg.role === 'system') return null;
                if (msg.role == 'user' && index == 1){
                  return (
                  <div key={index} className={`mb-2 p-2 rounded-md ${msg.role === 'user' ? 'bg-blue-100' : 'bg-gray-200'}`}>
                    <p className="font-semibold capitalize">{msg.role}</p>
                    <pre className="whitespace-pre-wrap font-mono text-sm">{"check/trace 内容省略...."}</pre>
                  </div>
                  )
                }
                return (
                  <div key={index} className={`mb-2 p-2 rounded-md ${msg.role === 'user' ? 'bg-blue-100' : 'bg-gray-200'}`}>
                    <p className="font-semibold capitalize">{msg.role}</p>
                    <pre className="whitespace-pre-wrap font-mono text-sm">{msg.content}</pre>
                  </div>
                )
              })}
            </div>
            <div className="mt-4 flex items-center gap-2">
              <Input.TextArea
                value={followUpQuery}
                onChange={setFollowUpQuery}
                placeholder="当前经验有问题，不满足，可以二次提问/修改经验，请输入你的问题..."
                rows={2}
                style={{ flex: 1 }}
              />
              <Button type="primary" onClick={handleFollowUp} loading={isLoading}>
                {isLoading ? '发送中...' : '发送'}
              </Button>
            </div>
          </div>: <></>
          }
        </div>
      )}

      {result && (
        <div className="bg-green-100 p-4 rounded-md mt-2">
          <p className="font-semibold cursor-pointer" onClick={() => setIsResultExpanded(!isResultExpanded)}>
            JSON Result (click to {isResultExpanded ? 'collapse' : 'expand'}):
          </p>
          {isResultExpanded && <pre className="whitespace-pre-wrap">{result}</pre>}
        </div>
      )}

       {manyResult && (
        <div className="bg-green-100 p-4 rounded-md mt-2">
          <p className="font-semibold cursor-pointer" onClick={() => setIsResultExpanded(!isResultExpanded)}>
            3次经验提取结果 (click to {isResultExpanded ? 'collapse' : 'expand'}):
          </p>
          {isResultExpanded && <pre className="whitespace-pre-wrap">{manyResult}</pre>}
        </div>
      )}

       {result && (
        <ResultPreview result={result} sessionId={sessionId} experienceType={experienceType} reason={checkContent} />
      )}
    </div>
  );
}