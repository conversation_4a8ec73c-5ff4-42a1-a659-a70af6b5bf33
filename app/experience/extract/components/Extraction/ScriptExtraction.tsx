import { apiClient } from '@/app/api/request';
import { userAtom } from '@/app/store/auth';
import { Button, Collapse, Input, Message, Switch, Modal, Radio, Checkbox, Card, Space, Typography, InputTag } from '@arco-design/web-react';
import { IconDelete, IconUpload, IconEdit } from '@arco-design/web-react/icon';
import { useStore } from '@nanostores/react';
import { useState } from 'react';
import { ExperienceManager } from './parse/experience';
import { toast } from 'sonner';
import { getEnv } from '@/app/api/env';
import { ensureContainerActive, fetchTraceWithRetry } from '../../utils';
import { processAgentTrace, processAgentTraceCompressed } from "@/app/experience/extract/compress";
import { HelpTooltip } from "@/app/components/ui/help-tooltip";
import { COMPRESSION_HELP_CONTENT } from "@/app/constants/compression-help";
import Editor from '@monaco-editor/react';
import { variantOptions } from '@/app/experience/api/experience';

interface ScriptExtractionProps {
  model: string;
  temperature: number;
}

interface IChatResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

interface ScriptItem {
  title: string;
  file_path?: string;
  content: string;
  used_when: string;
  notice?: string;
}

interface PlanItem {
  title: string;
  content: string;
  used_when: string;
  notice?: string;
}

interface ExtractionResult {
  doc_url?: string;
  plan?: PlanItem;
  scripts?: ScriptItem[];
}

const PLAN_CONTENT = `
执行轨迹中 Planner 执行与任务分配的原始内容。使用 Markdown 格式输出。例如：
1. 执行过程中分配的第一个 mewtwo 任务概述
  - **tools**: 给该任务分配的工具列表（例如 bash、lark 等）
  - **progress**: 该 mewtwo 任务具体的执行过程，需要重点包含使用哪些 tool、执行了哪些脚本等
  - **result**: 该 mewtwo 任务的最终结果
2. 执行过程中分配的第二个 mewtwo 任务概述
3. 执行过程中分配的第三个 mewtwo 任务概述
...
`

// JSON 格式定义
const JSON_FORMAT = `{
  "doc_url": "该任务轨迹用户原始请求中的飞书模版链接。需要从用户原始query中提取飞书文档链接，例如：用户请求'根据这个飞书模板：https://bytedance.larkoffice.com/docx/ToxydPv9ioa1pDxDEV0cPsQ0nWc，遵守格式和要求，生成对应的度量报告'，则doc_url为'https://bytedance.larkoffice.com/docx/ToxydPv9ioa1pDxDEV0cPsQ0nWc'。如果用户请求中没有飞书链接，则此字段为空字符串",
  "plan": {
    "title": "计划内容概述",
    "content": "${PLAN_CONTENT}",
    "used_when": "描述该计划的适用场景，要求：1) 场景描述必须从用户原始query提取具体问题，避免泛化；2) 确保只在极其相似场景下召回；3) 在2句话以内描述清楚，需要包含用户Query提及的关键特征，例如使用的工具（飞书、Excel 等），使用的平台（Meego、Tea、风神等），最终的目标（例如产出报表、数据打标等）",
    "notice": "使用该计划作为代码示例时的注意事项，尤其是其中硬编码的上下文信息或文件路径等需要根据实际环境进行调整，避免直接复用导致错误",
  },
  "scripts": [
  {
    "title": "脚本内容概述",
    "file_path": "脚本文件相较于 workspace 的相对路径（如有）",
    "content": "脚本原始内容（注意：轨迹执行过程中脚本内容可能被修改，请尽量还原最终内容）",
    "used_when": "描述该脚本的适用场景，要求：1) 场景描述必须从用户原始query提取具体问题，避免泛化；2) 确保只在极其相似场景下召回；3) 在2句话以内描述清楚，需要包含用户Query提及的关键特征，例如使用的工具（飞书、Excel 等），使用的平台（Meego、Tea、风神等），最终的目标（例如产出报表、数据打标等）",
    "notice": "使用该脚本作为代码示例时的注意事项，尤其是其中硬编码的上下文信息或文件路径等需要根据实际环境进行调整，避免直接复用导致错误",
  }]
  }`;

const DEFAULT_SYSTEM_PROMPT =
  `你是一个 agent 轨迹分析专家，你需要根据用户提供的 agent 执行轨迹，提取出用户原始请求中的飞书模版链接、计划内容、脚本内容(包括直接通过 \`python -c\` 执行的脚本)以及最终生成的 \`.lark.md\` 文件）。
  你需要以如下格式的 JSON 数据返回：
  ${JSON_FORMAT}
  请严格只输出 JSON，不要输出任何解释或多余内容，输出内容必须是有效的 JSON 数据。
  `;

const SUMMARY_SYSTEM_PROMPT = `你是一个脚本提取汇总专家，负责汇总多个分析agent的提取结果。

## 任务要求

你将收到多个分析agent的提取结果，需要将它们合并成一个统一的提取报告。

### 汇总规则

1. **doc_url 汇总**：
   - 如果任一分析结果中有 doc_url，则使用第一个非空的 doc_url
   - 如果所有结果都没有 doc_url，则 doc_url 为空字符串

2. **plan 内容汇总**：
   - 如果多个结果都有 plan，将其合并为一个 plan
   - 如果只有一个结果有 plan，则使用该 plan
   - 如果所有结果都没有 plan，则 plan 为 null

3. **scripts 内容汇总**：
   - 合并所有结果中的 scripts 数组
   - 去重：相同 title 和 file_path 的脚本只保留一个（选择内容更完整的）
   - 保持原有的详细信息结构

4. **内容质量保证**：
   - 确保所有字段都符合原始 JSON 格式要求
   - 保持脚本内容的完整性和准确性

## 输出格式

请以JSON格式输出合并后的提取结果：

\`\`\`json
{
  "doc_url": "合并后的文档链接",
  "plan": {
    "title": "计划标题",
    "content": "计划内容",
    "used_when": "适用场景",
    "notice": "注意事项"
  },
  "scripts": [
    {
      "title": "脚本标题",
      "file_path": "脚本路径",
      "content": "脚本内容",
      "used_when": "适用场景",
      "notice": "注意事项"
    }
  ]
}
\`\`\`

请严格只输出JSON，不要输出任何解释或多余内容。`;

const DEFAULT_QUERY_PREFIX =
  '有如下的 agent 执行轨迹：\n<trace>\n';

const QUERY_SUFFIX = `</trace>
# trace 附加说明

## trace 内容结构介绍

这是一个AI Agent执行任务的轨迹日志（Trace）。其核心架构由一个 **Planner（规划器）** 和多个 **Execution（执行器）**构成，通过多轮（Round）的"规划-执行"循环来完成复杂任务。

1. **输入** (\`UserMessage\`):
    - 流程的起点，封装了用户的原始需求、对话和输入附件

2. **规划** (\`Plan\`):
    - Planner的核心产物
    - 对用户任务进行理解和拆解，形成结构化执行蓝图
    - 关键要素包括：
        - \`Rationale\`: 阐述制定该计划的思考过程
        - \`ProgressPlan\`: 将任务分解为有序的、可执行的阶段性步骤
        - \`Actor\` & \`TaskDescription\`: 为每个步骤指派执行Agent并提供明确任务指令

3. **执行** (\`Execution\`):
    - Actor执行任务的具体记录
    - 包含最终的\`Output\`（成果摘要）和\`Reference\`（产出物链接）
    - \`Detail\`部分记录了由"思考(\`Think\`)-动作(\`Action\`)"对组成的执行链
    - 大多数由mewtwo这个Actor负责执行与完成
`;



// 新实现：通过 apiClient.GetTraceSession 获取 containerId
async function fetchContainerId(sessionId: string): Promise<string | null> {
  console.log('[fetchContainerId] start', { sessionId });
  try {
    const res = await apiClient.GetTraceSession({ session_id: sessionId });
    const webshellUrl = res.session?.webshell_url;
    if (!webshellUrl) {
      console.log('[fetchContainerId] end', { sessionId, containerId: null });
      return null;
    }
    const match = webshellUrl.match(/id=cube-([a-z0-9]+)&/);
    if (match) {
      console.log('[fetchContainerId] end', { sessionId, containerId: match[1] });
      return match[1];
    }
    console.log('[fetchContainerId] end', { sessionId, containerId: null });
    return null;
  } catch (e) {
    console.log('[fetchContainerId] error', { sessionId, error: e });
    return null;
  }
}



// 新增：下载 python 文件内容
async function fetchPythonFileContent(containerId: string, filePath: string, sessionId?: string): Promise<string | null> {
  console.log('[fetchPythonFileContent] start', { containerId, filePath, sessionId });
  try {
    // 兼容拼接 workspace 路径
    let fullPath = filePath;
    if (sessionId && !filePath.startsWith('workspace/iris_')) {
      fullPath = `workspace/iris_${sessionId}/${filePath.replace(/^\/+/, '')}`;
    }

    // 根据环境选择 stratocube 域名
    const env = getEnv();
    const stratocubeDomain = env === 'online' ? 'stratocube-online.byted.org' : 'stratocube-test.byted.org';
    const url = `https://${stratocubeDomain}/file/v1/download?id=cube-${containerId}&path=${encodeURIComponent(fullPath)}&container=runtime`;

    console.log('[fetchPythonFileContent] using stratocube domain', { env, stratocubeDomain, url });

    const res = await fetch(url);
    if (!res.ok) {
      console.log('[fetchPythonFileContent] end', { containerId, filePath, sessionId, content: null });
      return null;
    }
    const content = await res.text();
    console.log('[fetchPythonFileContent] use real file content', { containerId, filePath, sessionId, contentLength: content.length });
    return content;
  } catch (e) {
    console.log('[fetchPythonFileContent] error', { containerId, filePath, sessionId, error: e });
    return null;
  }
}

export function ScriptExtraction({ model, temperature }: ScriptExtractionProps) {
  const [sessionId, setSessionId] = useState('');
  const [query, setQuery] = useState('');
  const [result, setResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingTrace, setIsFetchingTrace] = useState(false);
  const [systemPrompt, setSystemPrompt] = useState(DEFAULT_SYSTEM_PROMPT);
  const [isPromptExpanded, setIsPromptExpanded] = useState(false);
  const [isResultExpanded, setIsResultExpanded] = useState(true);
  const [parsedScripts, setParsedScripts] = useState<any[] | null>(null);
  const [parsedPlan, setParsedPlan] = useState<PlanItem | null>(null);
  const [docUrl, setDocUrl] = useState<string>('');
  const [showContentMap, setShowContentMap] = useState<Record<number, boolean>>({});
  const [showAllMap, setShowAllMap] = useState<Record<number, boolean>>({});
  const user = useStore(userAtom);
  const [uploading, setUploading] = useState(false);
  const [isCompressionEnabled, setIsCompressionEnabled] = useState(false);
  const [isSingleRoundOnly, setIsSingleRoundOnly] = useState(false);
  const [isUploadConfirmVisible, setIsUploadConfirmVisible] = useState(false);
  const [selectedVariants, setSelectedVariants] = useState<string[]>(['newbie']);
  const [isMultiAgentEnabled, setIsMultiAgentEnabled] = useState(false);
  const [agentCount, setAgentCount] = useState(2);
  const [extractionProgress, setExtractionProgress] = useState<{
    step: 'idle' | 'extracting' | 'summarizing' | 'completed' | 'error';
    currentAgent: number;
    totalAgents: number;
    message: string;
    error?: string;
  }>({
    step: 'idle',
    currentAgent: 0,
    totalAgents: 0,
    message: ''
  });

  // 编辑相关状态
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editedScript, setEditedScript] = useState<ScriptItem | null>(null);
  const [editingPlan, setEditingPlan] = useState<boolean>(false);
  const [editedPlan, setEditedPlan] = useState<PlanItem | null>(null);
  const [uploadSucceeded, setUploadSucceeded] = useState(false);
  const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);

  // 新增：单个条目的上传状态管理
  const [uploadingItems, setUploadingItems] = useState<Record<string, boolean>>({});
  const [uploadedItems, setUploadedItems] = useState<Record<string, boolean>>({});

  // 新增：选择上传类型的弹窗状态
  const [showVariantSelector, setShowVariantSelector] = useState(false);
  const [pendingUploadItem, setPendingUploadItem] = useState<{ item: any, type: 'plan' | 'script', index?: number } | null>(null);

  // 分割trace数据的函数 - 按照data列表项数量分割为N个部分
  const splitTraceData = (traceData: string, count: number = agentCount): { success: boolean; data?: string[]; error?: string } => {
    try {
      if (isCompressionEnabled) {
        // 对于压缩的YAML格式，需要解析后按data列表项分割
        const parsed = JSON.parse(traceData);
        if (parsed && parsed.data && Array.isArray(parsed.data)) {
          const dataItems = parsed.data;
          if (dataItems.length === 0) {
            return {
              success: false,
              error: 'trace数据中的data数组为空，无法进行分割'
            };
          }
          
          if (dataItems.length < count) {
            return {
              success: false,
              error: `trace数据中的data数组长度(${dataItems.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          // 计算每个Agent处理的数据项数量
          const itemsPerAgent = Math.floor(dataItems.length / count);
          const remainder = dataItems.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            // 前remainder个Agent多分配一个数据项
            const currentItemsCount = itemsPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentItemsCount;
            
            const agentData = {
              ...parsed,
              data: dataItems.slice(startIndex, endIndex)
            };
            
            result.push(JSON.stringify(agentData, null, 2));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        } else {
          // 如果不是标准格式，按行分割作为备选
          const lines = traceData.split('\n');
          if (lines.length === 0) {
            return {
              success: false,
              error: 'trace数据为空，无法进行分割'
            };
          }
          
          if (lines.length < count) {
            return {
              success: false,
              error: `trace数据行数(${lines.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          const linesPerAgent = Math.floor(lines.length / count);
          const remainder = lines.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            const currentLinesCount = linesPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentLinesCount;
            result.push(lines.slice(startIndex, endIndex).join('\n'));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        }
      } else {
        // 对于JSON格式，按照data列表项分割
        const parsed = JSON.parse(traceData);
        if (parsed && parsed.data && Array.isArray(parsed.data)) {
          const dataItems = parsed.data;
          if (dataItems.length === 0) {
            return {
              success: false,
              error: 'trace数据中的data数组为空，无法进行分割'
            };
          }
          
          if (dataItems.length < count) {
            return {
              success: false,
              error: `trace数据中的data数组长度(${dataItems.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          // 计算每个Agent处理的数据项数量
          const itemsPerAgent = Math.floor(dataItems.length / count);
          const remainder = dataItems.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            const currentItemsCount = itemsPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentItemsCount;
            
            const agentData = {
              ...parsed,
              data: dataItems.slice(startIndex, endIndex)
            };
            
            result.push(JSON.stringify(agentData, null, 2));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        } else if (Array.isArray(parsed)) {
          // 如果直接是数组，按数组项分割
          if (parsed.length === 0) {
            return {
              success: false,
              error: 'trace数据为空数组，无法进行分割'
            };
          }
          
          if (parsed.length < count) {
            return {
              success: false,
              error: `trace数组长度(${parsed.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          const itemsPerAgent = Math.floor(parsed.length / count);
          const remainder = parsed.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            const currentItemsCount = itemsPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentItemsCount;
            result.push(JSON.stringify(parsed.slice(startIndex, endIndex), null, 2));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        } else {
          // 如果不是标准格式，按属性分割作为备选
          const keys = Object.keys(parsed);
          if (keys.length === 0) {
            return {
              success: false,
              error: 'trace数据为空对象，无法进行分割'
            };
          }
          
          if (keys.length < count) {
            return {
              success: false,
              error: `trace对象属性数量(${keys.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          const keysPerAgent = Math.floor(keys.length / count);
          const remainder = keys.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            const currentKeysCount = keysPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentKeysCount;
            
            const agentData = keys.slice(startIndex, endIndex).reduce((acc, key) => {
              acc[key] = parsed[key];
              return acc;
            }, {} as any);
            
            result.push(JSON.stringify(agentData, null, 2));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        }
      }
    } catch (e) {
      console.error('分割trace数据失败:', e);
      return {
        success: false,
        error: `分割trace数据失败: ${(e as Error).message}`
      };
    }
  };

  // 新增：公共上传逻辑函数
  const uploadExperienceItem = async (
    item: any,
    type: 'plan' | 'script',
    variant: string,
    containerId: string | null,
    username: string
  ) => {
    const experienceManager = new ExperienceManager();

    if (type === 'plan') {
      // 上传 plan 内容
      const vector_content = `${item.title} | ${item.used_when}${docUrl ? ` | ${docUrl}` : ''}`;

      // 构建 LimitScenarios 列表
      const limitScenarios: string[] = [];
      if (docUrl) {
        limitScenarios.push(`**用户消息中必须有链接（${docUrl}）时才可以选择。**`);
        limitScenarios.push(`**该计划仅在用户使用对应飞书模版时才可以使用。**`);
      }

      const limitScenariosSection = limitScenarios.length > 0
        ? `# LimitScenarios\n- ${limitScenarios.join('\n- ')}`
        : '';

      const experience_content = `# UsedWhen\n${item.used_when} 这个计划会召回一个历史任务分配和实际执行的内容，包含任务分配的工具列表、执行过程和最终结果。\n\n${limitScenariosSection}\n\n# Content\n${item.content}\n\n# ImportantNotes\n${item.notice || '使用该计划作为参考时，需要根据实际环境调整其中的具体参数和路径信息。'}`;

      await experienceManager.storeExperienceItem(
        {
          vector_content,
          title: "Agent Plan: " + item.title,
          type: 'Insights-Script',
          experience_content,
          source_id: sessionId,
          apply_type: 'planner',
          publish_status: 'draft',
          extract_type: username,
          limit: docUrl,
          variant: variant,
          ext: "",
        },
        username
      );
    } else {
      // 上传 script 内容
      let content = item.content;
      if (containerId && item.file_path) {
        // 优先尝试用 API 拉取
        const apiContent = await fetchPythonFileContent(containerId, item.file_path, sessionId);
        if (apiContent) {
          console.log('[uploadExperienceItem] use real file content for upload', {
            title: item.title,
            filePath: item.file_path,
            contentLength: apiContent.length
          });
          content = apiContent;
        }
      }

      const vector_content = `${item.title} | ${item.used_when}${docUrl ? ` | ${docUrl}` : ''}`;

      // 构建 LimitScenarios 列表
      const limitScenarios: string[] = [];
      if (docUrl) {
        limitScenarios.push(`**用户消息中必须有链接（${docUrl}）时才可以选择。**`);
      }

      const limitScenariosSection = limitScenarios.length > 0
        ? `# LimitScenarios\n- ${limitScenarios.join('\n- ')}`
        : '';

      // 根据文件后缀名生成相应的文案
      const getExperienceDescription = (filePath?: string) => {
        if (!filePath) return "这条经验为一个示例脚本代码";
        const fileExtension = filePath.split('.').pop()?.toLowerCase();
        if (fileExtension === 'md') {
          return "这条经验为一个用于格式示例的 markdown 文档";
        }
        return "这条经验为一个示例脚本代码";
      };

      // 根据文件类型生成 ImportantNotes 内容
      const getImportantNotes = (filePath?: string, notice?: string) => {
        const fileExtension = filePath?.split('.').pop()?.toLowerCase();

        // 定义不同文件类型的特定注意事项
        const fileTypeNotes: Record<string, string> = {
          'md': `- 参考该示例时，必须严格遵循其中的排版格式，包括但不限于段落标题、图表格式、高亮格式、字体颜色等。禁止在此基础上增删段落标题。
- 参考该示例时，必须严格遵循其中的排版格式，包括但不限于段落标题、图表格式、高亮格式、字体颜色、文本缩进等。禁止在此基础上增删段落、图表等关键元素。
- 参考格式时，需要严格遵循该示例中的文本缩进（空格数量），尤其是\`callout\` 中的缩进一定要严格遵循。当忽略了其中的空格数量时，会导致列表项 \`-\` 等元素排版混乱。
- 所有的图片需要根据实际数据生成，不可使用原始数据（例如 .json 文件）替代。
          `,
          'py': `- 参考该脚本时，需要严格遵循脚本中的核心逻辑和数据计算逻辑，仅根据上下文调整对应的输入输出`
        };

        const specificNotes = fileTypeNotes[fileExtension || ''];

        if (specificNotes) {
          return notice ? `- ${notice}\n${specificNotes}` : specificNotes;
        }

        return notice ? `- ${notice}` : '';
      };

      const experience_content = `# UsedWhen\n${item.used_when} ${getExperienceDescription(item.file_path)}\n\n${limitScenariosSection}\n\n# Content\n${content}\n\n# ImportantNotes\n${getImportantNotes(item.file_path, item.notice)}`;

      await experienceManager.storeExperienceItem(
        {
          vector_content,
          title: "Python Script: " + item.title,
          type: 'Insights-Script',
          experience_content,
          source_id: sessionId,
          apply_type: 'mewtwo',
          publish_status: 'draft',
          extract_type: username,
          limit: docUrl,
          variant: variant,
          ext: "",
        },
        username
      );
    }
  };

  // 恢复：全局上传处理函数
  const handleUpload = async () => {
    if ((!parsedScripts || parsedScripts.length === 0) && !parsedPlan) return;
    setIsUploadConfirmVisible(true);
  };

  // 恢复：全局确认上传处理函数
  const handleConfirmUpload = async () => {
    setIsUploadConfirmVisible(false);
    setUploading(true);
    const username = user?.username || '';
    try {
      // 获取 containerId
      const containerId = await fetchContainerId(sessionId);

      // 上传 plan 内容
      if (parsedPlan) {
        for (const variant of selectedVariants) {
          await uploadExperienceItem(parsedPlan, 'plan', variant, containerId, username);
        }
      }

      // 上传 scripts 内容
      if (parsedScripts) {
        for (const variant of selectedVariants) {
          for (const item of parsedScripts) {
            await uploadExperienceItem(item, 'script', variant, containerId, username);
          }
        }
      }

      toast.success('Upload successful!');
      setUploadSucceeded(true);
      setIsSuccessModalVisible(true);
    } catch (e) {
      toast.error('Upload failed.');
    } finally {
      setUploading(false);
    }
  };

  // 新增：单个条目上传处理函数
  const handleSingleItemUpload = async (item: any, type: 'plan' | 'script', index?: number) => {
    const itemKey = type === 'plan' ? 'plan' : `script_${index}`;

    if (uploadingItems[itemKey] || uploadedItems[itemKey]) return;

    setUploadingItems(prev => ({ ...prev, [itemKey]: true }));

    const username = user?.username || '';

    try {
      // 获取 containerId
      const containerId = await fetchContainerId(sessionId);

      // 使用公共上传函数
      for (const variant of selectedVariants) {
        await uploadExperienceItem(item, type, variant, containerId, username);
      }

      // 标记为已上传
      setUploadedItems(prev => ({ ...prev, [itemKey]: true }));
      toast.success(`${type === 'plan' ? '计划' : '脚本'}上传成功！`);

    } catch (e) {
      console.error('Upload failed:', e);
      toast.error(`${type === 'plan' ? '计划' : '脚本'}上传失败！`);
    } finally {
      setUploadingItems(prev => ({ ...prev, [itemKey]: false }));
    }
  };

  // 新增：选择上传类型的弹窗状态
  const handleSingleItemUploadWithVariant = (item: any, type: 'plan' | 'script', index?: number) => {
    setPendingUploadItem({ item, type, index });
    setShowVariantSelector(true);
  };

  // 新增：确认单个条目上传
  const handleConfirmSingleItemUpload = () => {
    if (pendingUploadItem) {
      handleSingleItemUpload(pendingUploadItem.item, pendingUploadItem.type, pendingUploadItem.index);
      setShowVariantSelector(false);
      setPendingUploadItem(null);
    }
  };

  const handleFetchTrace = async () => {
    if (!sessionId) {
      return null;
    }
    setIsFetchingTrace(true);
    try {
      let res = await fetchTraceWithRetry(sessionId);
      let traceData: string;
      
      if (isCompressionEnabled) {
        traceData = processAgentTraceCompressed(res);
      } else {
        traceData = JSON.stringify(res, null);
      }
      
      if (isSingleRoundOnly && res.data) {
        let userMessageCount = 0;
        const secondUserMessageIndex = res.data.findIndex((item: any) => {
          if (item.UserMessage) {
            userMessageCount++;
          }
          return userMessageCount === 2;
        });

        if (secondUserMessageIndex !== -1) {
          res.data = res.data.slice(0, secondUserMessageIndex);
          // 重新处理压缩后的数据
          if (isCompressionEnabled) {
            traceData = processAgentTraceCompressed(res);
          } else {
            traceData = JSON.stringify(res, null);
          }
        }
      }
      
      const newQuery = `${DEFAULT_QUERY_PREFIX}${traceData}${QUERY_SUFFIX}`;
      setQuery(newQuery);
      setIsFetchingTrace(false);
      return newQuery;
    } catch (e) {
      console.error(e);
      setQuery(`获取 trace 失败: ${(e as Error).message}`);
      setIsFetchingTrace(false);
      return null;
    }
  };

  // 解析JSON响应的辅助函数
  const parseJsonResponse = (content: string): any => {
    try {
      let cleaned = content.trim();
      // 去除 BOM
      cleaned = cleaned.replace(/^\uFEFF/, '');
      // 去掉开头的 ```json 或 ```
      if (cleaned.startsWith('```json')) {
        cleaned = cleaned.substring(7);
      } else if (cleaned.startsWith('```')) {
        cleaned = cleaned.substring(3);
      }
      // 去掉结尾的 ```
      if (cleaned.endsWith('```')) {
        cleaned = cleaned.substring(0, cleaned.length - 3);
      }
      // 去除首尾空白字符
      cleaned = cleaned.trim();
      return JSON.parse(cleaned);
    } catch (e) {
      console.error('JSON 解析失败:', e);
      console.error('原始内容:', content.substring(0, 500) + '...');
      return null;
    }
  };

  // 多agent提取函数
  const runMultiAgentExtraction = async (traceData: string) => {
    const startTime = Date.now();
    setIsLoading(true);
    setResult('');
    setParsedScripts(null);
    setParsedPlan(null);
    setDocUrl('');
    setExtractionProgress({
      step: 'extracting',
      currentAgent: 0,
      totalAgents: agentCount,
      message: `正在启动${agentCount}个提取agent...`
    });

    console.log(`开始多Agent提取，Agent数量: ${agentCount}，开始时间: ${new Date().toISOString()}`);

    try {
      // 分割trace数据
      const splitResult = splitTraceData(traceData, agentCount);
      if (!splitResult.success) {
        throw new Error(splitResult.error || 'trace数据分割失败');
      }
      const traceParts = splitResult.data!;

      // 并行调用N个提取agent
      setExtractionProgress(prev => ({
        ...prev,
        message: `正在并行启动${agentCount}个提取agent...`
      }));

      // 创建所有提取agent的并行调用
      const extractionAgentPromises = traceParts.map((tracePart, index) => {
        console.log(`启动提取Agent ${index + 1}/${agentCount}`);
        
        return apiClient.ChatStream({
          model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `${DEFAULT_QUERY_PREFIX}${tracePart}${QUERY_SUFFIX}` },
          ],
          temperature,
          max_tokens: 64000,
        }).then(res => {
          const content = (res as IChatResponse)?.choices?.[0]?.message?.content ?? '';
          console.log(`提取Agent ${index + 1} 完成`);
          
          // 更新进度：显示完成的agent数量
          setExtractionProgress(prev => ({
            ...prev,
            currentAgent: prev.currentAgent + 1,
            message: `提取Agent ${index + 1} 完成 (${prev.currentAgent + 1}/${agentCount})`
          }));
          
          return {
            index,
            agentId: index + 1,
            content,
            timestamp: new Date().toISOString()
          };
        }).catch(error => {
          console.error(`提取Agent ${index + 1} 失败:`, error);
          throw new Error(`提取Agent ${index + 1} 调用失败: ${error.message}`);
        });
      });

      // 等待所有提取agent完成
      setExtractionProgress(prev => ({
        ...prev,
        message: `等待${agentCount}个提取agent完成...`
      }));

      const agentResults = await Promise.all(extractionAgentPromises);
      
      // 所有提取agent完成后，显示汇总阶段
      setExtractionProgress(prev => ({
        ...prev,
        message: `所有${agentCount}个提取agent已完成，开始汇总...`
      }));

      // 解析所有提取结果
      const parsedResults = agentResults.map(result => {
        const parsed = parseJsonResponse(result.content);
        if (!parsed) {
          throw new Error(`第${result.index + 1}个提取agent返回结果解析失败`);
        }
        return { index: result.index, data: parsed };
      });

      // 所有提取agent完成后，调用总结agent
      setExtractionProgress({
        step: 'summarizing',
        currentAgent: agentCount,
        totalAgents: agentCount,
        message: '正在调用总结Agent汇总提取结果...'
      });

      console.log(`开始汇总${agentCount}个提取Agent的结果`);

      // 构建汇总查询
      const resultsText = parsedResults.map(result => 
        `## 第${result.index + 1}个提取结果：\n\`\`\`json\n${JSON.stringify(result.data, null, 2)}\n\`\`\``
      ).join('\n\n');

      const summaryQuery = `以下是${agentCount}个提取agent的提取结果，请按照要求汇总：

${resultsText}

请将以上${agentCount}个提取结果汇总成一个统一的提取报告。`;

      const summaryResult = await apiClient.ChatStream({
        model,
        messages: [
          { role: 'system', content: SUMMARY_SYSTEM_PROMPT },
          { role: 'user', content: summaryQuery },
        ],
        temperature,
        max_tokens: 64000,
      }).then(res => {
        console.log('总结Agent完成');
        return res;
      }).catch(error => {
        console.error('总结Agent失败:', error);
        throw new Error(`总结Agent调用失败: ${error.message}`);
      });

      const summaryContent = (summaryResult as IChatResponse)?.choices?.[0]?.message?.content ?? '';
      const finalResult = parseJsonResponse(summaryContent);

      if (finalResult) {
        // 从一级结构中获取 doc_url
        const extractedDocUrl = finalResult.doc_url || '';
        setDocUrl(extractedDocUrl);

        // 解析 plan 内容，并添加 doc_url
        if (finalResult.plan) {
          const planWithDocUrl = { ...finalResult.plan, doc_url: extractedDocUrl };
          setParsedPlan(planWithDocUrl);
        } else {
          setParsedPlan(null);
        }

        // 解析 scripts 内容，并为每个 script 添加 doc_url
        let scripts: any[] = [];
        if (Array.isArray(finalResult)) {
          scripts = finalResult;
        } else if (finalResult.scripts && Array.isArray(finalResult.scripts)) {
          scripts = finalResult.scripts;
        }

        if (scripts.length > 0) {
          // 为每个 script 添加 doc_url
          const scriptsWithDocUrl = scripts.map(item => ({ ...item, doc_url: extractedDocUrl }));

          // 拉取 containerId
          const containerId = await fetchContainerId(sessionId);
          // 并发下载所有 python 文件内容
          const scriptsWithRealContent = await Promise.all(
            scriptsWithDocUrl.map(async (item) => {
              if (containerId && item.file_path) {
                // 激活容器
                await ensureContainerActive(sessionId);
                const realContent = await fetchPythonFileContent(containerId, item.file_path, sessionId);
                if (realContent) {
                  console.log('[runMultiAgentExtraction] use real file content for script', {
                    title: item.title,
                    filePath: item.file_path,
                    contentLength: realContent.length
                  });
                  return { ...item, content: realContent };
                }
              }
              return item;
            })
          );
          setParsedScripts(scriptsWithRealContent);
        } else {
          setParsedScripts(null);
        }
        
        // 构建详细结果显示
        const detailedResults = parsedResults.map(result => {
          const agentResult = agentResults.find(r => r.index === result.index);
          return `提取Agent ${result.index + 1}结果 (完成时间: ${agentResult?.timestamp || 'N/A'})：
${JSON.stringify(result.data, null, 2)}`;
        }).join('\n\n');

        const endTime = Date.now();
        const totalTime = ((endTime - startTime) / 1000).toFixed(2);

        setResult(`${agentCount}个Agent并行提取完成！

## 并行处理详情：
- 提取Agent数量: ${agentCount}
- 总耗时: ${totalTime}秒
- 并行启动时间: ${new Date().toISOString()}
- 所有提取Agent并行处理完成

${detailedResults}

## 汇总结果：
${JSON.stringify(finalResult, null, 2)}`);

        console.log(`多Agent提取完成，总耗时: ${totalTime}秒，Agent数量: ${agentCount}`);
      } else {
        throw new Error('总结agent返回结果解析失败');
      }

      setExtractionProgress({
        step: 'completed',
        currentAgent: agentCount,
        totalAgents: agentCount,
        message: '提取完成！'
      });

    } catch (error) {
      console.error('多agent提取失败:', error);
      setResult(`多Agent提取失败: ${(error as Error).message}`);
      setParsedScripts(null);
      setParsedPlan(null);
      setDocUrl('');
      
      setExtractionProgress({
        step: 'error',
        currentAgent: 0,
        totalAgents: 0,
        message: '提取失败',
        error: (error as Error).message
      });
    } finally {
      setIsLoading(false);
      setTimeout(() => {
        setExtractionProgress({
          step: 'idle',
          currentAgent: 0,
          totalAgents: 0,
          message: ''
        });
      }, 2000);
    }
  };

  // 单agent提取函数（原有逻辑）
  const runSingleAgentExtraction = async (queryToRun: string) => {
    setIsLoading(true);
    setResult('');
    setParsedScripts(null);
    setParsedPlan(null);
    setDocUrl(''); // Reset docUrl before running
    try {
      const res = await apiClient.ChatStream({
        model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: queryToRun },
        ],
        temperature,
        max_tokens: 64000,
      });
      const content = (res as IChatResponse)?.choices?.[0]?.message?.content ?? '';
      setResult(content);
      // 解析 JSON
      try {
        let cleaned = content.trim();
        // 去除 BOM
        cleaned = cleaned.replace(/^\uFEFF/, '');
        // 去掉开头的 ```json 或 ```
        if (cleaned.startsWith('```json')) {
          cleaned = cleaned.substring(7);
        } else if (cleaned.startsWith('```')) {
          cleaned = cleaned.substring(3);
        }
        // 去掉结尾的 ```
        if (cleaned.endsWith('```')) {
          cleaned = cleaned.substring(0, cleaned.length - 3);
        }
        // 去除首尾空白字符
        cleaned = cleaned.trim();
        const json = JSON.parse(cleaned);

        // 从一级结构中获取 doc_url
        const extractedDocUrl = json.doc_url || '';
        setDocUrl(extractedDocUrl);

        // 解析 plan 内容，并添加 doc_url
        if (json.plan) {
          const planWithDocUrl = { ...json.plan, doc_url: extractedDocUrl };
          setParsedPlan(planWithDocUrl);
        } else {
          setParsedPlan(null);
        }

        // 解析 scripts 内容，并为每个 script 添加 doc_url
        let scripts: any[] = [];
        if (Array.isArray(json)) {
          scripts = json;
        } else if (json.scripts && Array.isArray(json.scripts)) {
          scripts = json.scripts;
        }

        if (scripts.length > 0) {
          // 为每个 script 添加 doc_url
          const scriptsWithDocUrl = scripts.map(item => ({ ...item, doc_url: extractedDocUrl }));

          // 新增：拉取 containerId
          const containerId = await fetchContainerId(sessionId);
          // 并发下载所有 python 文件内容
          const scriptsWithRealContent = await Promise.all(
            scriptsWithDocUrl.map(async (item) => {
              if (containerId && item.file_path) {
                // 激活容器
                await ensureContainerActive(sessionId);
                const realContent = await fetchPythonFileContent(containerId, item.file_path, sessionId);
                if (realContent) {
                  console.log('[runSingleAgentExtraction] use real file content for script', {
                    title: item.title,
                    filePath: item.file_path,
                    contentLength: realContent.length
                  });
                  return { ...item, content: realContent };
                }
              }
              return item;
            })
          );
          setParsedScripts(scriptsWithRealContent);
        } else {
          setParsedScripts(null);
        }
      } catch (e) {
        console.error('JSON 解析失败:', e);
        console.error('原始内容:', content.substring(0, 500) + '...');
        setParsedScripts(null);
        setParsedPlan(null);
        setDocUrl('');
      }
    } catch (error) {
      console.error(`Error running query:`, error);
      setResult('Error: Could not fetch result.');
      setParsedScripts(null);
      setParsedPlan(null);
      setDocUrl('');
    }
    setIsLoading(false);
  };

  const handleRun = async () => {
    if (isLoading || !model) return;
    
    if (isMultiAgentEnabled) {
      // 多agent模式
      const queryToRun = query || (await handleFetchTrace());
      if (!queryToRun) {
        return;
      }
      
      // 提取trace数据
      let traceData: string;
      
      // 尝试多种方式提取trace数据
      const traceMatch = queryToRun.match(/<trace>\n([\s\S]*?)\n<\/trace>/);
      if (traceMatch) {
        // 标准格式：<trace>...</trace>
        traceData = traceMatch[1];
      } else {
        // 尝试其他可能的格式
        const alternativeMatch = queryToRun.match(/<trace>([\s\S]*?)<\/trace>/);
        if (alternativeMatch) {
          traceData = alternativeMatch[1].trim();
        } else {
          // 如果都没有找到，检查是否是纯trace数据（没有<trace>标签）
          const lines = queryToRun.split('\n');
          const hasTraceTag = lines.some(line => line.includes('<trace>') || line.includes('</trace>'));
          
          if (!hasTraceTag) {
            // 可能是纯trace数据，尝试直接解析
            try {
              const parsed = JSON.parse(queryToRun);
              if (parsed && (parsed.data || Array.isArray(parsed))) {
                traceData = queryToRun;
              } else {
                throw new Error('不是有效的trace数据格式');
              }
            } catch (e) {
              console.error('无法提取trace数据，query内容：', queryToRun.substring(0, 500) + '...');
              toast.error('无法从query中提取trace数据。请确保：\n1. query中包含<trace>标签，或\n2. query是完整的trace JSON数据');
              return;
            }
          } else {
            console.error('无法提取trace数据，query内容：', queryToRun.substring(0, 500) + '...');
            toast.error('无法从query中提取trace数据，请确保<trace>标签格式正确');
            return;
          }
        }
      }
      await runMultiAgentExtraction(traceData);
    } else {
      // 单agent模式
      const queryToRun = query || (await handleFetchTrace());
      if (!queryToRun) {
        return;
      }
      await runSingleAgentExtraction(queryToRun);
    }
  };

  // 编辑相关处理函数
  const handleEdit = (index: number) => {
    if (!parsedScripts) return;
    setEditingIndex(index);
    setEditedScript({ ...parsedScripts[index] });
  };

  const handleEditPlan = () => {
    if (!parsedPlan) return;
    setEditingPlan(true);
    setEditedPlan({ ...parsedPlan });
  };

  const handleSavePlan = () => {
    if (!editedPlan) return;
    setParsedPlan(editedPlan);
    setEditingPlan(false);
    setEditedPlan(null);
  };

  const handleCancelPlan = () => {
    setEditingPlan(false);
    setEditedPlan(null);
  };

  const handlePlanFieldChange = (field: string, value: any) => {
    setEditedPlan((prev) => prev ? { ...prev, [field]: value } : null);
  };

  const handleSave = (index: number) => {
    if (!editedScript || !parsedScripts) return;
    const newScripts = [...parsedScripts];
    newScripts[index] = editedScript;
    setParsedScripts(newScripts);
    setEditingIndex(null);
    setEditedScript(null);
  };

  const handleCancel = () => {
    setEditingIndex(null);
    setEditedScript(null);
  };

  const handleFieldChange = (field: string, value: any) => {
    setEditedScript((prev) => prev ? { ...prev, [field]: value } : null);
  };

  const handleDeleteScript = (index: number) => {
    if (!parsedScripts) return;
    setParsedScripts(parsedScripts.filter((_, i) => i !== index));
  };



  return (
    <div>
      <div className="mb-4">
        <h2 className="text-lg font-semibold mb-2">System Prompt</h2>
        <textarea
          className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
          value={systemPrompt}
          onChange={(e) => setSystemPrompt(e.target.value)}
          rows={isPromptExpanded ? Math.max(10, systemPrompt.split('\n').length) : 3}
        />
        <button
          className="text-sm text-blue-500 mt-1"
          onClick={() => setIsPromptExpanded(!isPromptExpanded)}
        >
          {isPromptExpanded ? '收起' : '展开'}
        </button>
      </div>
      <div className="flex items-center gap-4 mb-4">
        <Input
          placeholder="Enter Session ID"
          value={sessionId}
          onChange={setSessionId}
          className="w-1/2"
        />
        <Button type="primary" onClick={handleFetchTrace} loading={isFetchingTrace}>
          {isFetchingTrace ? '拉取中...' : '拉取 trace'}
        </Button>
      </div>
      <div className="flex items-center gap-4 mb-4 flex-wrap">
        <div className="flex items-center gap-2 flex-shrink-0">
          <Switch checked={isCompressionEnabled} onChange={setIsCompressionEnabled} />
          <label onClick={() => setIsCompressionEnabled(v => !v)} className="cursor-pointer select-none whitespace-nowrap">
            上下文压缩
          </label>
          <HelpTooltip content={COMPRESSION_HELP_CONTENT} />
        </div>
        
        <div className="flex items-center gap-2 flex-shrink-0">
          <Switch checked={isSingleRoundOnly} onChange={setIsSingleRoundOnly} />
          <label onClick={() => setIsSingleRoundOnly(v => !v)} className="cursor-pointer select-none whitespace-nowrap">
            是否只提取第一轮对话
          </label>
        </div>
        
        <div className="flex items-center gap-2 flex-shrink-0">
          <Switch checked={isMultiAgentEnabled} onChange={setIsMultiAgentEnabled} />
          <label onClick={() => setIsMultiAgentEnabled(v => !v)} className="cursor-pointer select-none whitespace-nowrap">
            多Agent提取
          </label>
          <HelpTooltip content="启用多Agent提取模式，通过多个提取Agent并行分析trace的不同部分，然后由总结Agent汇总结果，提供更全面的提取" />
        </div>
        
        {isMultiAgentEnabled && (
          <div className="flex items-center gap-2 flex-shrink-0">
            <label className="cursor-pointer select-none whitespace-nowrap">Agent数量</label>
            <Input
              type="number"
              min="2"
              max="10"
              value={String(agentCount)}
              onChange={(value) => setAgentCount(Math.max(2, Math.min(10, Number(value) || 2)))}
              className="w-16"
              size="small"
            />
            <HelpTooltip content="设置并行提取Agent的数量，范围2-10个。数量越多，提取越细致，但耗时更长" />
          </div>
        )}
      </div>
      <div className="mb-4">
        <p className="font-semibold">Query:</p>
        <div style={{ border: '1px solid var(--color-border-2)', borderRadius: 'var(--border-radius-medium)' }}>
          <Editor
            height="240px" // 对应原来 rows={10} 的大致高度
            language={isCompressionEnabled ? "yaml" : "plaintext"}
            value={query}
            onChange={(value) => setQuery(value || '')}
            options={{
              wordWrap: 'on',
              minimap: { enabled: true },
              scrollBeyondLastLine: false,
              automaticLayout: true,
              unicodeHighlight: {
                ambiguousCharacters: false,
              },
            }}
          />
        </div>
      </div>
      <Button type="primary" onClick={handleRun} loading={isLoading} className="mb-4">
        {isLoading ? (isMultiAgentEnabled ? '多Agent提取中...' : '提取中...') : 'Run Extraction'}
      </Button>

      {/* 多Agent提取进度显示 */}
      {isMultiAgentEnabled && extractionProgress.step !== 'idle' && (
        <div className={`mb-4 p-4 rounded-lg border ${
          extractionProgress.step === 'error' 
            ? 'bg-red-50 border-red-200' 
            : 'bg-blue-50 border-blue-200'
        }`}>
          <div className="flex items-center justify-between mb-2">
            <span className={`font-medium ${
              extractionProgress.step === 'error' ? 'text-red-800' : 'text-blue-800'
            }`}>
              {extractionProgress.step === 'extracting' && '提取阶段'}
              {extractionProgress.step === 'summarizing' && '汇总阶段'}
              {extractionProgress.step === 'completed' && '提取完成'}
              {extractionProgress.step === 'error' && '提取失败'}
            </span>
            {extractionProgress.step !== 'error' && (
              <span className="text-sm text-blue-600">
                {extractionProgress.currentAgent}/{extractionProgress.totalAgents}
              </span>
            )}
          </div>
          {extractionProgress.step !== 'error' && (
            <div className="w-full bg-blue-200 rounded-full h-2 mb-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ 
                  width: `${(extractionProgress.currentAgent / extractionProgress.totalAgents) * 100}%` 
                }}
              ></div>
            </div>
          )}
          {extractionProgress.step === 'extracting' && (
            <div className="text-xs text-blue-600 mt-1">
              💡 所有提取Agent正在并行处理中...
            </div>
          )}
          <p className={`text-sm ${
            extractionProgress.step === 'error' ? 'text-red-700' : 'text-blue-700'
          }`}>
            {extractionProgress.message}
          </p>
          {extractionProgress.step === 'error' && extractionProgress.error && (
            <div className="mt-2 p-2 bg-red-100 rounded border border-red-300">
              <p className="text-xs text-red-800 font-medium">错误详情：</p>
              <p className="text-xs text-red-700 mt-1">{extractionProgress.error}</p>
            </div>
          )}
        </div>
      )}

      {/* 全局上传的变体选择弹窗 */}
      <Modal
        title="选择上传类型"
        visible={isUploadConfirmVisible}
        onOk={handleConfirmUpload}
        onCancel={() => setIsUploadConfirmVisible(false)}
        okText="确认上传"
        cancelText="取消"
      >
        <p>确定要上传 {parsedPlan ? 1 : 0} 个计划{(parsedScripts && parsedScripts.length > 0) ? ` 和 ${parsedScripts.length} 个脚本` : ''}吗？</p>
        <div style={{ marginTop: 16 }}>
          <p style={{ marginBottom: 8 }}>请选择上传类型：</p>
          <Checkbox.Group value={selectedVariants} onChange={(value) => setSelectedVariants(value as string[])}>
            {variantOptions.map((option) => (
              <Checkbox key={option.value} value={option.value}>
                {option.label}
              </Checkbox>
            ))}
          </Checkbox.Group>
        </div>
      </Modal>

      {/* 新增：单个条目上传的变体选择弹窗 */}
      <Modal
        title="选择上传类型"
        visible={showVariantSelector}
        onOk={handleConfirmSingleItemUpload}
        onCancel={() => {
          setShowVariantSelector(false);
          setPendingUploadItem(null);
        }}
        okText="确认上传"
        cancelText="取消"
      >
        <p>确定要上传这个{pendingUploadItem?.type === 'plan' ? '计划' : '脚本'}吗？</p>
        <div style={{ marginTop: 16 }}>
          <p style={{ marginBottom: 8 }}>请选择上传类型：</p>
          <Checkbox.Group value={selectedVariants} onChange={(value) => setSelectedVariants(value as string[])}>
            {variantOptions.map((option) => (
              <Checkbox key={option.value} value={option.value}>
                {option.label}
              </Checkbox>
            ))}
          </Checkbox.Group>
        </div>
      </Modal>
      {(parsedScripts || parsedPlan) ? (
        <div className="mt-4">
          <div className="flex justify-between items-center mb-4">
            <Typography.Title heading={6}>提取结果预览</Typography.Title>
            {!uploadSucceeded && (
              <Button type="primary" icon={<IconUpload />} onClick={handleUpload} loading={uploading} disabled={(!parsedScripts || parsedScripts.length === 0) && !parsedPlan}>
                上传
              </Button>
            )}
          </div>

          {/* Plan 内容展示 */}
          {parsedPlan && (
            <Card className="mb-4" title={
              <div className="flex justify-between items-center">
                <span>计划内容</span>
                <Space>
                  {editingPlan ? (
                    <>
                      <Button size="small" type="primary" onClick={handleSavePlan}>保存</Button>
                      <Button size="small" onClick={handleCancelPlan}>取消</Button>
                    </>
                  ) : (
                    <>
                      <Button size="small" icon={<IconEdit />} onClick={handleEditPlan}>编辑</Button>
                      {!uploadedItems['plan'] ? (
                        <Button
                          size="small"
                          type="primary"
                          icon={<IconUpload />}
                          onClick={() => handleSingleItemUploadWithVariant(parsedPlan, 'plan')}
                          loading={uploadingItems['plan']}
                        >
                          上传
                        </Button>
                      ) : (
                        <Button size="small" type="primary" status="success" disabled>
                          已上传
                        </Button>
                      )}
                    </>
                  )}
                </Space>
              </div>
            }>
              {editingPlan && editedPlan ? (
                <div>
                  <div className="mt-2">
                    <p><strong>标题:</strong></p>
                    <Input
                      value={editedPlan.title}
                      onChange={(value) => handlePlanFieldChange('title', value)}
                      className="mt-1"
                    />
                  </div>
                  <div className="mt-2">
                    <p><strong>适用场景:</strong></p>
                    <Input.TextArea
                      autoSize={{ minRows: 2 }}
                      value={editedPlan.used_when}
                      onChange={(value) => handlePlanFieldChange('used_when', value)}
                      className="mt-1"
                    />
                  </div>
                  <div className="mt-2">
                    <p><strong>计划内容:</strong></p>
                    <Input.TextArea
                      autoSize={{ minRows: 6 }}
                      value={editedPlan.content}
                      onChange={(value) => handlePlanFieldChange('content', value)}
                      className="mt-1"
                    />
                  </div>
                  <div className="mt-2">
                    <p><strong>注意事项:</strong></p>
                    <Input.TextArea
                      autoSize={{ minRows: 2 }}
                      value={editedPlan.notice || ''}
                      onChange={(value) => handlePlanFieldChange('notice', value)}
                      className="mt-1"
                    />
                  </div>
                  <div className="mt-2">
                    <p><strong>文档链接:</strong> {docUrl || '无'}</p>
                  </div>
                </div>
              ) : (
                <div>
                  <p><strong>适用场景:</strong> {parsedPlan.used_when}</p>
                  {docUrl && (
                    <p><strong>文档链接:</strong> {docUrl}</p>
                  )}
                  {parsedPlan.notice && (
                    <p><strong>注意事项:</strong> {parsedPlan.notice}</p>
                  )}
                  <div className="mt-2">
                    <p><strong>计划内容:</strong></p>
                    <div className="bg-gray-100 p-3 rounded-md mt-1">
                      <pre className="whitespace-pre-wrap text-sm">{parsedPlan.content}</pre>
                    </div>
                  </div>
                </div>
              )}
            </Card>
          )}

          {/* Scripts 内容展示 */}
          {parsedScripts && parsedScripts.map((item, idx) => {
            const vector_content = `${item.title} | ${item.used_when}${docUrl ? ` | ${docUrl}` : ''}`;

            return (
              <Card key={idx} className="mb-4" title={
                <div className="flex justify-between items-center">
                  <span>{item.title}</span>
                  <Space>
                    {editingIndex === idx ? (
                      <>
                        <Button size="small" type="primary" onClick={() => handleSave(idx)}>保存</Button>
                        <Button size="small" onClick={handleCancel}>取消</Button>
                      </>
                    ) : (
                      <Button size="small" icon={<IconEdit />} onClick={() => handleEdit(idx)}>编辑</Button>
                    )}
                    <Button icon={<IconDelete />} status="danger" type="text" onClick={() => handleDeleteScript(idx)} />
                    {!uploadedItems[`script_${idx}`] ? (
                      <Button
                        size="small"
                        type="primary"
                        icon={<IconUpload />}
                        onClick={() => handleSingleItemUploadWithVariant(item, 'script', idx)}
                        loading={uploadingItems[`script_${idx}`]}
                      >
                        上传
                      </Button>
                    ) : (
                      <Button size="small" type="primary" status="success" disabled>
                        已上传
                      </Button>
                    )}
                  </Space>
                </div>
              }>
                {editingIndex === idx && editedScript ? (
                  <div>
                    <div className="mt-2">
                      <p><strong>标题:</strong></p>
                      <Input
                        value={editedScript.title}
                        onChange={(value) => handleFieldChange('title', value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="mt-2">
                      <p><strong>适用场景:</strong></p>
                      <Input.TextArea
                        autoSize={{ minRows: 2 }}
                        value={editedScript.used_when}
                        onChange={(value) => handleFieldChange('used_when', value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="mt-2">
                      <p><strong>脚本内容:</strong></p>
                      <Input.TextArea
                        autoSize={{ minRows: 4 }}
                        value={editedScript.content}
                        onChange={(value) => handleFieldChange('content', value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="mt-2">
                      <p><strong>注意事项:</strong></p>
                      <Input.TextArea
                        autoSize={{ minRows: 2 }}
                        value={editedScript.notice || ''}
                        onChange={(value) => handleFieldChange('notice', value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="mt-2">
                      <p><strong>文档链接:</strong> {docUrl || '无'}</p>
                    </div>
                  </div>
                ) : (
                  <div>
                    <p><strong>适用场景:</strong> {item.used_when}</p>
                    {docUrl && (
                      <p><strong>文档链接:</strong> {docUrl}</p>
                    )}
                    {item.notice && (
                      <p><strong>注意事项:</strong> {item.notice}</p>
                    )}
                    <div className="mt-2">
                      <p><strong>脚本内容:</strong></p>
                      <div className="bg-gray-100 p-3 rounded-md mt-1">
                        <pre className="whitespace-pre-wrap text-sm">{item.content}</pre>
                      </div>
                    </div>
                  </div>
                )}
              </Card>
            );
          })}
        </div>
      ) : (
        result && (
          <div className="bg-green-100 p-4 rounded-md mt-2">
            <p className="font-semibold">模型原始内容：</p>
            <pre className="whitespace-pre-wrap">{result}</pre>
          </div>
        )
      )}

      {/* 成功上传提示 */}
      <Modal
        title="上传成功"
        visible={isSuccessModalVisible}
        onOk={() => {
          setIsSuccessModalVisible(false);
          setUploadSucceeded(false);
        }}
        onCancel={() => {
          setIsSuccessModalVisible(false);
          setUploadSucceeded(false);
        }}
        okText="确定"
        cancelText="取消"
      >
        <p>内容已成功上传！</p>
      </Modal>

      {/* 多Agent模式下的详细结果显示 */}
      {isMultiAgentEnabled && parsedScripts && (
        <div className="mt-6">
          <Typography.Title heading={5}>多Agent提取详情</Typography.Title>
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <p className="text-sm text-green-700 mb-2">
              ✓ 已通过{agentCount}个提取Agent和总结Agent完成全面提取
            </p>
            <p className="text-xs text-green-600">
              使用{agentCount}个提取Agent并行处理trace的不同部分，总结Agent汇总所有结果，提供更全面的脚本提取
            </p>
          </div>
        </div>
      )}
    </div>
  );
}