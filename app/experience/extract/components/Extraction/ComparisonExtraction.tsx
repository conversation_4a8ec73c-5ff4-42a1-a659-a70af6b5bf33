import React, { useState, useEffect } from 'react';
import { Input, Button, Switch, Space, Select } from '@arco-design/web-react';
import { apiClient } from '@/app/api/request';
import { ResultPreview } from './ResultPreview';
import { fetchTraceWithRetry } from '../../utils';
import { processAgentTrace, processAgentTraceCompressed } from "@/app/experience/extract/compress";
import { HelpTooltip } from "@/app/components/ui/help-tooltip";
import { COMPRESSION_HELP_CONTENT } from "@/app/constants/compression-help";
import { Message } from './parse/types';
import { experienceTypeOptions } from '@/app/experience/api/experience';

interface ComparisonExtractionProps {
  model: string;
  temperature: number;
}

interface IChatResponse {
  choices: {
    message: {
      content: string
    }
  }[]
}

export function ComparisonExtraction({ model, temperature }: ComparisonExtractionProps) {
  const [goodSessionId, setGoodSessionId] = useState('');
  const [badSessionId, setBadSessionId] = useState('');
  const [checkContent, setCheckContent] = useState('');
  const [query, setQuery] = useState('');
  const [result, setResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingTraces, setIsFetchingTraces] = useState(false);
  const [systemPrompt, setSystemPrompt] = useState('');
  const [isPromptExpanded, setIsPromptExpanded] = useState(false);
  const [isResultExpanded, setIsResultExpanded] = useState(true);
  const [isCompressionEnabled, setIsCompressionEnabled] = useState(false);
  const [isContinueQuestion, setIsContinueQuestion] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [followUpQuery, setFollowUpQuery] = useState('');
  const [experienceType, setExperienceType] = useState<string>('Insights-BadCase');

  useEffect(() => {
    const fetchPrompt = async () => {
      try {
        const promptUrlMap = {
          'Insights-BadCase': `https://tosv-cn.byted.org/obj/ttclient-android/aime/compare_system_badcase.md?_t=${Date.now()}`,
          'Insights-Workflow': `https://tosv-cn.byted.org/obj/ttclient-android/aime/compare_system_workflow.md?_t=${Date.now()}`,
          // 'Insights-Planner': `https://tosv-cn.byted.org/obj/ttclient-android/aime/compare_system_planner.md?_t=${Date.now()}`
        };
        const promptUrl = experienceType in promptUrlMap ? promptUrlMap[experienceType as keyof typeof promptUrlMap] : promptUrlMap['Insights-BadCase'];
        const response = await fetch(promptUrl, {
          method: 'GET',
          cache: 'no-store'
        });
        let text = await response.text();
        text = text.replace('{{.CurrentTime}}', new Date().toLocaleString());
        text = text.replace('{{.UserLanguage}}', '中文');
        setSystemPrompt(text);
      } catch (error) {
        console.error('Error fetching system prompt:', error);
      }
    };

    fetchPrompt();
  }, [experienceType]);

  const handleFetchTraces = async () => {
    if (!goodSessionId || !badSessionId) {
      return null;
    }
    setIsFetchingTraces(true);
    try {
      const [goodRes, badRes] = await Promise.all([
        fetchTraceWithRetry(goodSessionId),
        fetchTraceWithRetry(badSessionId),
      ]);

      let goodTraceData: string;
      let badTraceData: string;
      
      if (isCompressionEnabled) {
        goodTraceData = processAgentTraceCompressed(goodRes);
        badTraceData = processAgentTraceCompressed(badRes);
      } else {
        goodTraceData = JSON.stringify(goodRes, null);
        badTraceData = JSON.stringify(badRes, null);
      }

      let newQuery = '';
      if (checkContent.trim()) {
        newQuery += `<check>
${checkContent}
</check>
`;
      }
      newQuery += `<good_case>
${goodTraceData}
</good_case>
<bad_case>
${badTraceData}
</bad_case>`;
      setQuery(newQuery);
      setIsFetchingTraces(false);
      return newQuery;
    } catch (e) {
      console.error(e);
      setQuery(`获取 trace 失败: ${(e as Error).message}`);
      setIsFetchingTraces(false);
      return null;
    }
  };

  const handleRun = async () => {
      if (isLoading || !model) return;
      const queryToRun = query || await handleFetchTraces();
      if (!queryToRun) {
        return;
      }
      setIsLoading(true);
      setResult('');
      try {
        const res = await apiClient.ChatStream({
          model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: queryToRun },
          ],
          temperature,
          max_tokens: 64000
        });
        const content = (res as IChatResponse)?.choices?.[0]?.message?.content ?? '';
        setResult(content);
        
        const initialMessages: Message[] = [
          { role: 'system', content: systemPrompt, agent: 'default' },
          { role: 'user', content: queryToRun, agent: 'default' },
        ];
        setMessages([...initialMessages, { role: 'assistant', content, agent: 'default' }]);
      } catch (error) {
        console.error(`Error running query:`, error);
        setResult('Error: Could not fetch result.');
      }
      setIsLoading(false);
  }

  const handleFollowUp = async () => {
    if (isLoading || !model || !followUpQuery) return;
    setIsLoading(true);
    
    const newMessages: Message[] = [...messages, { role: 'user', content: followUpQuery, agent: 'default' }];
    setMessages(newMessages);
    setFollowUpQuery('');

    try {
      const res = await apiClient.ChatStream({
        model,
        messages: newMessages,
        temperature,
        max_tokens: 64000
      });
      const content = (res as IChatResponse)?.choices?.[0]?.message?.content ?? '';
      setResult(content);
      setMessages([...newMessages, { role: 'assistant', content, agent: 'default' }]);
    } catch (error) {
      console.error(`Error running follow-up query:`, error);
      setResult('Error: Could not fetch result.');
    }
    setIsLoading(false);
  };

  return (
    <div>
      <div className="mb-4">
        <h2 className="text-lg font-semibold mb-2">System Prompt</h2>
        <textarea
          className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
          value={systemPrompt}
          onChange={(e) => setSystemPrompt(e.target.value)}
          rows={isPromptExpanded ? Math.max(10, systemPrompt.split('\n').length) : 3}
        />
        <button
          className="text-sm text-blue-500 mt-1"
          onClick={() => setIsPromptExpanded(!isPromptExpanded)}
        >
          {isPromptExpanded ? '收起' : '展开'}
        </button>
      </div>
      <div className="flex items-center gap-4 mb-4">
        <Input
          placeholder="Enter Good Session ID"
          value={goodSessionId}
          onChange={setGoodSessionId}
          className="w-1/2"
        />
        <Input
          placeholder="Enter Bad Session ID"
          value={badSessionId}
          onChange={setBadSessionId}
          className="w-1/2"
        />
      </div>
      <div className="flex items-center gap-4 mb-4">
        <Input.TextArea
          value={checkContent}
          placeholder={experienceType === 'Insights-BadCase' ? '请输入经验 badcase 的reason(或者不符合预期的地方)' : experienceType === 'Insights-Workflow' ? '请输入经验 workflow 的reason(或者不符合预期的地方)' : '请输入经验 planner 的reason(或者不符合预期的地方)'}
          onChange={setCheckContent}
          rows={1}
          className="flex-1"
        />
        <div className="flex items-center gap-2">
          <span>经验类型:</span>
          <Select
            value={experienceType}
            onChange={setExperienceType}
            style={{ width: 200 }}
            options={experienceTypeOptions.filter(option => 
              option.value === 'Insights-BadCase' || option.value === 'Insights-Workflow'
            )}
          />
        </div>
        <Button type="primary" onClick={handleFetchTraces} loading={isFetchingTraces}>
          {isFetchingTraces ? '拉取中...' : '拉取 trace'}
        </Button>
      </div>
      <div className="mb-4">
        <p className="font-semibold">Query:</p>
        <Input.TextArea
          value={query}
          onChange={setQuery}
          rows={10}
        />
      </div>
      <div className="flex items-center gap-2 mb-4">
        <Switch checked={isCompressionEnabled} onChange={setIsCompressionEnabled} />
        <label onClick={() => setIsCompressionEnabled(v => !v)} className="cursor-pointer select-none">
          上下文压缩
        </label>
        <HelpTooltip content={COMPRESSION_HELP_CONTENT} />
      </div>
      <Space size='large'>
        <Button type="primary" onClick={handleRun} loading={isLoading} className="mb-4">
            {isLoading ? 'Running...' : 'Run Extraction'}
        </Button>
        {
          messages.length > 0 &&  ( <Button type="primary" onClick={() => setIsContinueQuestion(!isContinueQuestion)}>
           二次追问 {isContinueQuestion ? '收起' : '展开'}
          </Button>)
        }
      </Space>
      {messages.length > 0 && (
        <div className="mt-4">
           {
            isContinueQuestion ?  <div>
            <div className="bg-gray-100 p-4 rounded-md w-full" style={{maxHeight: '400px', overflowY: 'auto'}}>
              {messages.map((msg, index) => {
                if (msg.role === 'system') return null;
                if (msg.role == 'user' && index == 1){
                  return (
                  <div key={index} className={`mb-2 p-2 rounded-md ${msg.role === 'user' ? 'bg-blue-100' : 'bg-gray-200'}`}>
                    <p className="font-semibold capitalize">{msg.role}</p>
                    <pre className="whitespace-pre-wrap font-mono text-sm">{"check/good_case/bad_case 内容省略...."}</pre>
                  </div>
                  )
                }
                return (
                  <div key={index} className={`mb-2 p-2 rounded-md ${msg.role === 'user' ? 'bg-blue-100' : 'bg-gray-200'}`}>
                    <p className="font-semibold capitalize">{msg.role}</p>
                    <pre className="whitespace-pre-wrap font-mono text-sm">{msg.content}</pre>
                  </div>
                )
              })}
            </div>
            <div className="mt-4 flex items-center gap-2">
              <Input.TextArea
                value={followUpQuery}
                onChange={setFollowUpQuery}
                placeholder="当前经验有问题，不满足，可以二次提问/修改经验，请输入你的问题..."
                rows={2}
                style={{ flex: 1 }}
              />
              <Button type="primary" onClick={handleFollowUp} loading={isLoading}>
                {isLoading ? '发送中...' : '发送'}
              </Button>
            </div>
          </div>: <></>
          }
        </div>
      )}
      {result && (
        <ResultPreview
          result={result}
          goodCaseSessionId={goodSessionId}
          badCaseSessionId={badSessionId}
          experienceType={experienceType}
          reason={checkContent}
        />
      )}
      {result && (
        <div className="bg-green-100 p-4 rounded-md mt-2">
          <p className="font-semibold cursor-pointer" onClick={() => setIsResultExpanded(!isResultExpanded)}>
            Result (click to {isResultExpanded ? 'collapse' : 'expand'}):
          </p>
          {isResultExpanded && <pre className="whitespace-pre-wrap">{result}</pre>}
        </div>
      )}
    </div>
  );
}