import { useCallback } from "react";
import { Role } from "../../interface";
import { MessageItem } from "./item";
import { ChatCompletionMessage } from "@/app/bam/aime/namespaces/trace";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "lucide-react";
import { cloneDeep } from "lodash-es";
export function Messages(props: {
  messages?: ChatCompletionMessage[];
  onChange: (messages: ChatCompletionMessage[]) => void;
}) {
  const { messages = [], onChange } = props;

  const onMessageChange = useCallback(
    (message: string, index: number) => {
      const newMessages = cloneDeep(messages ?? []);
      newMessages[index].content = message;
      onChange(newMessages);
    },
    [messages, onChange],
  );
  const onRoleChange = useCallback(
    (role: Role, index: number) => {
      const newMessages = cloneDeep(messages ?? []);
      newMessages[index].role = role;
      onChange(newMessages);
    },
    [messages, onChange],
  );

  const onDelate = useCallback(
    (index: number) => {
      const newMessages = cloneDeep(messages ?? []);
      newMessages.splice(index, 1);
      onChange(newMessages);
    },
    [messages, onChange],
  );

  const handleCreate = useCallback(() => {
    const newMessages = cloneDeep(messages ?? []);
    newMessages.push({
      role: "user",
      content: "",
    });
    onChange(newMessages);
  }, [messages, onChange]);

  return (
    <div className="flex flex-col gap-2 overflow-y-auto bg-white rounded-xl px-3 py-5 h-[430px]">
      <h1 className="!font-bold !text-sm text-slate-600 rt-r-mb-3">对话消息</h1>
      {messages?.map((message, index) => (
        <MessageItem
          key={`${index}-${messages.length}`}
          message={message.content ?? ""}
          role={message.role as Role}
          onMessageChange={(message: string) => onMessageChange(message, index)}
          onRoleChange={(role: Role) => onRoleChange(role, index)}
          onDelate={() => onDelate(index)}
        />
      ))}
      <div className="mt-[10px]">
        <Button
          variant="outline"
          size="sm"
          className="text-xs h-8"
          onClick={handleCreate}
        >
          <PlusIcon className="mr-1 h-3.5 w-3.5" />
          添加消息
        </Button>
      </div>
    </div>
  );
}
