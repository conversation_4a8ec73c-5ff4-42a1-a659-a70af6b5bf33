import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { TrashIcon } from "lucide-react";
import { Role } from "../../interface";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export interface MessageItemProps {
  message: string;
  onMessageChange: (message: string) => void;
  role: Role;
  onRoleChange: (role: Role) => void;
  onDelate?: () => void;
}

export function MessageItem(props: MessageItemProps) {
  const { message, onMessageChange, role, onRoleChange, onDelate } = props;

  return (
    <div className="">
      <div className="relative p-4 rounded-lg transition-all group bg-muted/40 border border-border/50 hover:border-border">
        <div className="flex flex-col gap-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <Badge variant="default" className="font-medium text-xs">
                {role === "system"
                  ? "系统"
                  : role === "assistant"
                  ? "助手"
                  : "用户"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Select value={role} onValueChange={onRoleChange}>
                <SelectTrigger className="w-28 h-8 text-xs">
                  <SelectValue placeholder="选择角色" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="system">系统</SelectItem>
                  <SelectItem value="user">用户</SelectItem>
                  <SelectItem value="assistant">助手</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-muted-foreground hover:text-destructive opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => onDelate?.()}
              >
                <TrashIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <Textarea
            placeholder={`输入${
              role === "system"
                ? "系统提示"
                : role === "assistant"
                ? "助手回复"
                : "用户消息"
              }...`}
            defaultValue={message}
            onChange={(e) => onMessageChange(e.target.value)}
            className="min-h-[80px] resize-none transition-colors focus-visible:ring-1 focus-visible:ring-ring bg-background border-muted"
          />
        </div>
      </div>
    </div>
  );
}
