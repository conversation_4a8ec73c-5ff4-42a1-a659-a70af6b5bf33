import { apiClient } from '@/app/api/request';
import { RunDebugStatus } from '@/app/bam/aime/namespaces/trace';

interface IChatResponse {
  choices: {
    message: {
      content: string
    }
  }[]
}

async function doChat(systemPrompt: string, userPrompt: string, model: string, temperature: number, max_tokens: number) {
    const res = await apiClient.ChatStream({
        model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        temperature,
        max_tokens,
      });
    return (res as IChatResponse)?.choices?.[0]?.message?.content ?? '';
}

export async function classifyScene(query: string, classificationPrompt: string, model: string, temperature: number): Promise<string> {
  try {
    const userPrompt = `<query>
${query}
</query>
`
    const content = await doChat(classificationPrompt, userPrompt, model, temperature, 4096);
    // The output is expected to be XML, so we need to parse it.
    const match = content.match(/<PrimaryDomain>(.*?)<\/PrimaryDomain>/);
    return match ? match[1].trim() : 'Others'; // Default to 'Others' if parsing fails
  } catch (error) {
    console.error('Error classifying scene:', error);
    return 'Others'; // Default to 'Others' on error
  }
}

export async function getEvaluationCriteria() {
    try {
        const response = await fetch(`https://tosv-cn.byted.org/obj/ttclient-android/aime/evaluation_criteria.json?_t=${Date.now()}`, {
          method: 'GET',
          cache: 'no-store'
        });
        if (!response.ok) {
            return null;
        }
        const data = await response.json();
        if (Array.isArray(data)) {
            return data.reduce((acc: Record<string, string>, item: { name: string, evaluation_criteria: string }) => {
                acc[item.name] = item.evaluation_criteria;
                return acc;
            }, {});
        }
        return null;
    } catch (error) {
        console.error('Error fetching evaluation criteria:', error);
        return null;
    }
}

/**
 * 检查并激活容器（如果需要）
 * @param sessionId 会话ID
 * @returns Promise<boolean> 激活是否成功
 */
export async function ensureContainerActive(sessionId: string): Promise<boolean> {
  console.log('[ensureContainerActive] start', { sessionId });
  try {
    // 先获取 session 信息以获取 run_id 和容器状态
    const sessionRes = await apiClient.GetTraceSession({ session_id: sessionId });
    const runId = sessionRes.session?.run_id;
    let runDebugStatus = sessionRes.session?.run_debug_status;
    
    if (!runId) {
      console.log('[ensureContainerActive] no run_id found', { sessionId });
      return false;
    }

    // 检查容器状态，只有在休眠状态时才激活
    if (runDebugStatus === RunDebugStatus.Recoverable) {
      console.log('[ensureContainerActive] container is in recoverable state, activating...', { sessionId, runId });
      // 激活容器
      await apiClient.ResumeRuntime({ run_id: runId });
      console.log('[ensureContainerActive] activation request sent', { sessionId, runId });
      // 轮询状态，最多 10 次，每次间隔 1 秒
      let pollCount = 0;
      const maxPoll = 10;
      while (pollCount < maxPoll) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        const pollRes = await apiClient.GetTraceSession({ session_id: sessionId });
        runDebugStatus = pollRes.session?.run_debug_status;
        console.log(`[ensureContainerActive] poll #${pollCount + 1}, run_debug_status:`, runDebugStatus);
        if (runDebugStatus === RunDebugStatus.Running) {
          console.log('[ensureContainerActive] container is now running');
          return true;
        }
        pollCount++;
      }
      console.log('[ensureContainerActive] container did not become running after polling');
      return false;
    } else if (runDebugStatus === RunDebugStatus.Running) {
      console.log('[ensureContainerActive] container is already running', { sessionId, runId });
      return true;
    } else {
      console.log('[ensureContainerActive] container is in unrecoverable state', { sessionId, runId, runDebugStatus });
      return false;
    }
  } catch (e) {
    console.log('[ensureContainerActive] error', { sessionId, error: e });
    return false;
  }
}

/**
 * 智能拉取 trace 数据，失败时自动激活容器重试
 * @param sessionId 会话ID
 * @returns Promise<any> trace 数据
 */
export async function fetchTraceWithRetry(sessionId: string): Promise<any> {
  console.log('[fetchTraceWithRetry] start', { sessionId });
  
  try {
    // 先直接尝试拉取 trace
    console.log('[fetchTraceWithRetry] attempting direct trace fetch...');
    const traceData = await apiClient.GetSessionTrajectory({ session_id: sessionId });
    console.log('[fetchTraceWithRetry] direct fetch successful');
    return traceData;
  } catch (error) {
    console.log('[fetchTraceWithRetry] direct fetch failed, attempting container activation...', { error });
    
    // 拉取失败，尝试激活容器
    const activationResult = await ensureContainerActive(sessionId);
    if (!activationResult) {
      console.log('[fetchTraceWithRetry] container activation failed, throwing original error');
      throw error; // 抛出原始错误
    }
    
    // 激活成功，重试拉取 trace
    console.log('[fetchTraceWithRetry] container activated, retrying trace fetch...');
    try {
      const retryTraceData = await apiClient.GetSessionTrajectory({ session_id: sessionId });
      console.log('[fetchTraceWithRetry] retry fetch successful');
      return retryTraceData;
    } catch (retryError) {
      console.log('[fetchTraceWithRetry] retry fetch also failed', { retryError });
      throw retryError; // 抛出重试错误
    }
  }
}