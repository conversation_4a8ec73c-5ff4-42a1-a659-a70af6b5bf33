import { ChatStreamRequest } from "@/app/bam/aime/namespaces/trace";

export interface GroupInfo {
  groupName: string;
  groupId: string;
  data: ChatStreamRequest;
  response?: string;
}

export enum Role {
  User = "user",
  Assistant = "assistant",
  System = "system",
}

interface SupportedHyperParams {
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  thinking?: {
    type: string;
    budget_tokens?: number;
  }

}

/** 前端支持的模型参数 */
export type ModelOptions = Pick<ChatStreamRequest, 'model'| keyof SupportedHyperParams>