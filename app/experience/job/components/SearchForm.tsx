"use client";

import React from "react";
import { Form, Grid, Input, Select, Space, Button } from "@arco-design/web-react";
import { IconSearch, IconRefresh } from "@arco-design/web-react/icon";
import { statusOptions } from "../../api/task-execution";

const { Item: FormItem } = Form;

interface SearchFormProps {
  form: any;
  onSearch: () => void;
  onReset: () => void;
}

export function SearchForm({ form, onSearch, onReset }: SearchFormProps) {
  return (
    <Form layout="horizontal" className="mb-4 flex-shrink-0" form={form}>
      <Grid.Row gutter={16}>
        <Grid.Col span={5}>
          <FormItem label="会话ID" field="sessionId">
            <Input placeholder="请输入会话ID" allowClear />
          </FormItem>
        </Grid.Col>
        <Grid.Col span={4}>
          <FormItem label="用户" field="user">
            <Input placeholder="请输入用户名" allowClear />
          </FormItem>
        </Grid.Col>
        <Grid.Col span={5}>
          <FormItem label="状态" field="status">
            <Select placeholder="请选择状态" allowClear>
              {statusOptions.map(option => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </FormItem>
        </Grid.Col>
        <Grid.Col span={5}>
          <FormItem label="标签" field="tag">
            <Input placeholder="请输入标签" allowClear />
          </FormItem>
        </Grid.Col>
        <Grid.Col span={5}>
          <FormItem label=" ">
            <div className="flex justify-end">
              <Space>
                <Button 
                  type="primary" 
                  icon={<IconSearch />} 
                  onClick={onSearch}
                >
                  搜索
                </Button>
                <Button onClick={onReset}>重置</Button>
              </Space>
            </div>
          </FormItem>
        </Grid.Col>
      </Grid.Row>
    </Form>
  );
}