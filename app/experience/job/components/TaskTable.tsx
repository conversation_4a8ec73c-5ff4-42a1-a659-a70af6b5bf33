"use client";

import React from "react";
import { Table, Space, Button, Tag, Spin, Typography } from "@arco-design/web-react";
import { IconEye, IconDelete, IconPlayCircle, IconRefresh } from "@arco-design/web-react/icon";
import { TaskExecution, ExecutionStep, getStatusColor, getStatusText, getStepStatusText } from "../../api/task-execution";
import { getStepNameText } from "../utils";

const { Text } = Typography;

interface TaskTableProps {
  data: TaskExecution[];
  loading: boolean;
  onStartTask: (task: TaskExecution) => void;
  onViewDetail: (task: TaskExecution) => void;
  onDeleteTask: (task: TaskExecution) => void;
  onRestartTask: (task: TaskExecution) => void;
  selectedRowKeys?: (string | number)[];
  onSelectionChange?: (selectedRowKeys: (string | number)[]) => void;
}

// 处理步骤状态显示的函数
const renderExecutionSteps = (steps: ExecutionStep[]) => {
  if (!steps || steps.length === 0) return "-";
  
  return (
    <div className="space-y-1">
      {steps.map((step, index) => (
        <div key={index} className="flex items-center space-x-2">
          {(step.status === 'running') && (
            <Spin />
          )}
          <Tag color={getStatusColor(step.status)} size="small">
            {getStepStatusText(step.status)}
          </Tag>
          <Text className="text-sm">{getStepNameText(step.stepName)}</Text>
        </div>
      ))}
    </div>
  );
};

export function TaskTable({ data, loading, onStartTask, onViewDetail, onDeleteTask, onRestartTask, selectedRowKeys = [], onSelectionChange }: TaskTableProps) {
  const columns = [
    {
      title: "任务ID",
      dataIndex: "id",
      width: 120,
      render: (col: string) => (
        <div className="max-w-[120px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "会话ID",
      dataIndex: "sessionId",
      width: 150,
      render: (col: string) => (
        <div className="max-w-[150px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 100,
      render: (col: string) => {
        const shouldShowLoading = col === 'running';
        return (
          <div className="flex items-center space-x-1">
            {shouldShowLoading && (
              <Spin />
            )}
            <Tag color={getStatusColor(col)}>{getStatusText(col)}</Tag>
          </div>
        );
      },
    },
    {
      title: "用户",
      dataIndex: "user",
      width: 120,
      render: (col: string) => col || "-",
    },
    {
      title: "执行步骤",
      dataIndex: "executionSteps",
      width: 300,
      render: (col: ExecutionStep[]) => renderExecutionSteps(col),
    },
    {
      title: "开始时间",
      dataIndex: "startTime",
      width: 180,
      render: (col: string) => col ? new Date(col).toLocaleString() : "-",
    },
    {
      title: "结束时间",
      dataIndex: "endTime",
      width: 180,
      render: (col: string) => col ? new Date(col).toLocaleString() : "-",
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      width: 180,
      render: (col: string) => col ? new Date(col).toLocaleString() : "-",
    },
    {
      title: "更新时间",
      dataIndex: "updateTime",
      width: 180,
      render: (col: string) => col ? new Date(col).toLocaleString() : "-",
    },
    {
      title: "操作",
      width: 200,
      fixed: 'right' as const,
      render: (_: unknown, record: TaskExecution) => (
        <div className="flex flex-col space-y-2">
          {/* 第一排按钮 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<IconPlayCircle />}
              onClick={() => onStartTask(record)}
              type="text"
            >
              开始
            </Button>
            <Button 
              size="small" 
              icon={<IconRefresh />}
              onClick={() => onRestartTask(record)}
              type="text"
              status="warning"
            >
              重启任务
            </Button>
          </Space>
          {/* 第二排按钮 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<IconEye />}
              onClick={() => onViewDetail(record)}
              type="text"
            >
              详情
            </Button>
            <Button 
              size="small" 
              icon={<IconDelete />}
              onClick={() => onDeleteTask(record)}
              type="text"
              status="danger"
            >
              删除
            </Button>
          </Space>
        </div>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    type: 'checkbox' as const,
    selectedRowKeys,
    onChange: (newSelectedRowKeys: (string | number)[]) => {
      if (onSelectionChange) {
        onSelectionChange(newSelectedRowKeys);
      }
    },
  };

  return (
    <Table
      columns={columns}
      data={data}
      loading={loading}
      pagination={false}
      border={false}
      scroll={{ x: true, y: 'calc(100vh - 330px)'  }}
      rowSelection={rowSelection}
    />
  );
}