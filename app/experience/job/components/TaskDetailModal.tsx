"use client";

import React from "react";
import { Modal, Grid, Tag, Spin, Typography, Button } from "@arco-design/web-react";
import { IconPlayCircle, IconCheckCircle, IconCloseCircle } from "@arco-design/web-react/icon";
import { TaskExecution } from "../../api/task-execution";
import { getStepNameText } from "../utils";
import { getStatusColor, getStatusText, getStepStatusText } from "../../api/task-execution";
import { JSONEditor } from "@/components/ui/json-editor";
import { ResultPreview } from '@/app/experience/extract/components/Extraction/ResultPreview';

const { Text } = Typography;

interface TaskDetailModalProps {
  visible: boolean;
  task: TaskExecution | null;
  onClose: () => void;
  onStartTask: (task: TaskExecution) => void;
  onCompleteTask: (task: TaskExecution) => void;
  onFailTask: (task: TaskExecution) => void;
}

export function TaskDetailModal({ 
  visible, 
  task, 
  onClose, 
  onStartTask, 
  onCompleteTask, 
  onFailTask 
}: TaskDetailModalProps) {
  if (!task) return null;

  const renderStepResult = (step: any, index: number) => {
    const isExperienceStep = step.stepName && step.stepName.startsWith('exp_extra');
    
    if (isExperienceStep && step.result) {
      const isComparison = task.sessionId && task.sessionId.includes('_');
      let experienceType = 'Insights-BadCase';
      
      if (step.stepName.toLowerCase().includes('planner')) {
        experienceType = 'Insights-Planner';
      } else if (step.stepName.toLowerCase().includes('workflow')) {
        experienceType = 'Insights-Workflow';
      } else if (step.stepName.toLowerCase().includes('error')) {
        experienceType = 'Insights-BadCase';
      } else if (step.stepName.toLowerCase().includes('badcase')) {
        experienceType = 'Insights-BadCase';
      }

      if (isComparison) {
        const parts = task.sessionId.split('_');
        const goodCaseSessionId = parts[0].replace('goodId_', '');
        const badCaseSessionId = parts[1].replace('badId_', '');
        
        return (
          <ResultPreview
            result={step.result}
            goodCaseSessionId={goodCaseSessionId}
            badCaseSessionId={badCaseSessionId}
            experienceType={experienceType}
            reason={step.content || ''}
          />
        );
      } else {
        return (
          <ResultPreview
            result={step.result}
            sessionId={task.sessionId}
            experienceType={experienceType}
            reason={step.content || ''}
          />
        );
      }
    }

    return (
      <JSONEditor 
        value={step.result} 
        onChange={() => {}}
        showValidation={false}
      />
    );
  };

  return (
    <Modal
      title="任务执行详情"
      visible={visible}
      onCancel={onClose}
      footer={null}
      style={{ width: '80%' }}
    >
      <div className="space-y-4">
        <Grid.Row gutter={16}>
          <Grid.Col span={8}>
            <Text>任务ID：</Text>
            <Text>{task.id}</Text>
          </Grid.Col>
          <Grid.Col span={8}>
            <Text>会话ID：</Text>
            <Text>{task.sessionId}</Text>
          </Grid.Col>
          <Grid.Col span={8}>
            <Text>状态：</Text>
            {task.status === 'running' && (
              <Spin />
            )}
            <Tag color={getStatusColor(task.status)}>
              {getStatusText(task.status)}
            </Tag>
          </Grid.Col>
        </Grid.Row>
        
        <Grid.Row gutter={16}>
          <Grid.Col span={8}>
            <Text>用户：</Text>
            <Text>{task.user || '-'}</Text>
          </Grid.Col>
          <Grid.Col span={8}>
            <Text>开始时间：</Text>
            <Text>{task.startTime ? new Date(task.startTime).toLocaleString() : '-'}</Text>
          </Grid.Col>
          <Grid.Col span={8}>
            <Text>结束时间：</Text>
            <Text>{task.endTime ? new Date(task.endTime).toLocaleString() : '-'}</Text>
          </Grid.Col>
        </Grid.Row>
        
        <div>
          <Text>执行步骤：共 {task.executionSteps?.length} 个步骤</Text>
          <div className="mt-2">
            {task.executionSteps?.map((step, index) => (
              <div key={index} className="mb-3 p-3 border rounded">
                <div className="flex items-center justify-between mb-2">
                  <Text>{getStepNameText(step.stepName)}</Text>
                  <Tag color={getStatusColor(step.status)}>
                    {getStepStatusText(step.status)}
                  </Tag>
                  {step.status === 'running' && (
                    <Spin />
                  )}
                </div>
                
                <Grid.Row gutter={16}>
                  <Grid.Col span={12}>
                    <Text type="secondary">开始时间：</Text>
                    <Text>{step.startTime ? new Date(step.startTime).toLocaleString() : '-'}</Text>
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <Text type="secondary">结束时间：</Text>
                    <Text>{step.endTime ? new Date(step.endTime).toLocaleString() : '-'}</Text>
                  </Grid.Col>
                </Grid.Row>
                
                {step.errorMsg && (
                  <div className="mt-2">
                    <Text type="secondary">错误信息：</Text>
                    <Text className="text-red-500">{step.errorMsg}</Text>
                  </div>
                )}
                
                {step.content && (
                  <div className="mt-2">
                    <Text type="secondary">content:</Text>
                    <Text className="text-blue-500">{step.content}</Text>
                  </div>
                )}
                
                {step.result && (
                  <div className="mt-2">
                    <Text type="secondary">执行结果：</Text>
                    {renderStepResult(step, index)}
                  </div>
                )}
                
                {/* 显示步骤级别的配置信息 */}
                {(step.pythonScriptName || step.pythonScriptContent || step.scriptBeforeBash || step.environmentVariables) && (
                  <div className="mt-3 pt-2 border-t">
                    <Text className="font-semibold text-sm mb-2">步骤配置信息：</Text>
                    <div className="space-y-2 text-sm">
                      {step.pythonScriptName && (
                        <div>
                          <Text type="secondary" className="text-xs">脚本名称：</Text>
                          <Text className="ml-1 text-xs">{step.pythonScriptName}</Text>
                        </div>
                      )}
                      {step.pythonScriptContent && (
                        <div>
                          <Text type="secondary" className="text-xs">脚本内容：</Text>
                          <div className="mt-1 p-1 bg-gray-50 rounded text-xs font-mono max-h-20 overflow-y-auto">
                            {step.pythonScriptContent.length > 100 
                              ? `${step.pythonScriptContent.substring(0, 100)}...`
                              : step.pythonScriptContent
                            }
                          </div>
                        </div>
                      )}
                      {step.scriptBeforeBash && (
                        <div>
                          <Text type="secondary" className="text-xs">前置脚本：</Text>
                          <div className="mt-1 p-1 bg-gray-50 rounded text-xs font-mono max-h-20 overflow-y-auto">
                            {step.scriptBeforeBash.length > 100 
                              ? `${step.scriptBeforeBash.substring(0, 100)}...`
                              : step.scriptBeforeBash
                            }
                          </div>
                        </div>
                      )}
                      {step.environmentVariables && Object.keys(step.environmentVariables).length > 0 && (
                        <div>
                          <Text type="secondary" className="text-xs">环境变量：</Text>
                          <div className="mt-1 p-1 bg-gray-50 rounded text-xs">
                            <pre className="text-xs">
                              {JSON.stringify(step.environmentVariables, null, 1)}
                            </pre>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button 
            icon={<IconPlayCircle />}
            onClick={() => onStartTask(task)}
            disabled={task.status !== 'pending'}
          >
            开始执行
          </Button>
          <Button 
            icon={<IconCheckCircle />}
            onClick={() => onCompleteTask(task)}
            disabled={task.status !== 'running'}
            type="primary"
          >
            标记完成
          </Button>
          <Button 
            icon={<IconCloseCircle />}
            onClick={() => onFailTask(task)}
            disabled={task.status !== 'running'}
            status="danger"
          >
            标记失败
          </Button>
        </div>
      </div>
    </Modal>
  );
}