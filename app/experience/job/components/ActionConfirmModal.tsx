"use client";

import React from "react";
import { Modal } from "@arco-design/web-react";

interface ActionConfirmModalProps {
  visible: boolean;
  taskId?: string;
  actionType: 'start' | 'complete' | 'fail' | 'restart' | null;
  onConfirm: () => void;
  onCancel: () => void;
  loading: boolean;
}

export function ActionConfirmModal({ 
  visible, 
  taskId, 
  actionType, 
  onConfirm, 
  onCancel, 
  loading 
}: ActionConfirmModalProps) {
  const actionText = {
    'start': '开始执行',
    'complete': '标记完成',
    'fail': '标记失败',
    'restart': '重启任务'
  }[actionType || 'start'];

  return (
    <Modal
      title="确认操作"
      visible={visible}
      onConfirm={onConfirm}
      onCancel={onCancel}
      confirmLoading={loading}
    >
      <div>
        确定要{actionText}任务 &ldquo;{taskId}&rdquo; 吗？
      </div>
    </Modal>
  );
}