import { stepName} from "../api/task-execution";

// 获取步骤名称文本
export const getStepNameText = (stepNameValue: string) => {
  const option = stepName.find(opt => opt.value === stepNameValue);
  return option?.label || stepNameValue;
};

// 更新URL参数的函数
export const updateURLParams = (params: any, page: number, size: number) => {
  const urlParams = new URLSearchParams();
  
  // 添加搜索参数
  if (params.sessionId) urlParams.set('sessionId', params.sessionId);
  if (params.user) urlParams.set('user', params.user);
  if (params.status) urlParams.set('status', params.status);
  if (params.tag) urlParams.set('tag', params.tag);
  if (params.date_range && params.date_range[0] && params.date_range[1]) {
    urlParams.set('start_date', params.date_range[0]);
    urlParams.set('end_date', params.date_range[1]);
  }
  
  // 添加分页参数
  if (page !== 1) urlParams.set('page', page.toString());
  if (size !== 10) urlParams.set('size', size.toString());
  
  // 更新URL
  const newURL = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
  window.history.replaceState({}, '', newURL);
};