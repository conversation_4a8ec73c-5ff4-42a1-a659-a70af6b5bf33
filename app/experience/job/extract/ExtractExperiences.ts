import { getCurrentUser } from "@/app/api/auth";
import { getJwt } from "@/app/api/user";

export const variantsTransform = new Map<string, string>([
  ["LateralHire", "newbie"],
  ["IndustryVeteran", "invited_expert"],
  ["YoungTalent", "intern"]
])

interface BaseFormData {
  role: string;
  directPutIn: boolean;
  extractError: boolean;
}

export interface ExtractReason {
  session_id: string;
  check_info: string;
  check_reason: string;
  type: string;
}

export interface OnlineExtractFormData extends BaseFormData {
  startDate: string;
  endDate: string;
  onlyEvalNotPass: boolean;
  createEvalTask: boolean;
  createEvalSet: boolean;
  xDebugVersion: string;
}

export interface EvalTaskExtractFormData extends BaseFormData {
  evalTaskId: string;
  extractCheckpoint: boolean;
  extractSatisfaction: boolean;
  scoreLimit: number;
}

// 复刻 extract_experiences 方法的逻辑
export const extractExperiences = async (values: OnlineExtractFormData) => {
  const timeNow = new Date().toLocaleString('zh-CN');

  // 1. 获取线上的评测未通过的任务列表
  const failedTasks = await fetchFailedTaskList(values);

  // 2. 获取未通过的原因和任务sessionId
  const finalResults = await fetchReasons(failedTasks);

  // 3. 批量提取经验
  const executionResults = await submitExpExtractionPipeline(finalResults, timeNow, values);

  // 4. 运行评测任务，评测线上这些有问题的任务
  if (values.createEvalTask) {
    await createAimeTaskEval(
      `经验效果评测-online-${timeNow}`,
      failedTasks.map(task => task.session_id)
    );
  }

  // 5. 创建评测数据集，方便重测
  if (values.createEvalSet) {
    const csvContent = generateCsvFromTasks(failedTasks);
    await createEvaluationSetCsv(
      `经验效果评测-${timeNow}`,
      csvContent
    );
  }

  return executionResults;
};

export const extractExperiencesFromEvalTask = async (values: EvalTaskExtractFormData) => {
  const timeNow = new Date().toLocaleString('zh-CN');
  if (values.evalTaskId.includes("https://aime-auto-eval-fe.gf.bytedance.net/tasks/")) {
    values.evalTaskId = values.evalTaskId.split("/tasks/")[1].split("?")[0];
  }

  // 1. 获取未通过的原因和任务sessionId
  const finalResults = await fetchSingleTaskReasonById(values.evalTaskId, values.extractCheckpoint, values.extractSatisfaction, values.scoreLimit);

  // 2. 批量提取经验
  const executionResults = await submitExpExtractionPipeline(finalResults, timeNow, values);

  return executionResults;
}


// 获取线上的评测未通过的任务列表
const fetchFailedTaskList = async (values: OnlineExtractFormData) => {
  const startTime = `${values.startDate}T00:00:00.000+08:00`;
  const endTime = `${values.endDate}T23:59:59.999+08:00`;

  const url = "https://test-ai.bytedance.net/api/benchmark/aime_task/tasks_with_tag";
  const params = new URLSearchParams({
    page: "1",
    page_size: "100",
    only_eval_not_pass: values.onlyEvalNotPass.toString(),
    only_has_eval_result: "true",
    roles: values.role,
    start_time: startTime,
    end_time: endTime,
  });

  if (values.xDebugVersion) {
    params.append("agent_version_id", values.xDebugVersion);
  }

  const jwtToken = await getJwt();
  const response = await fetch(`${url}?${params}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "x-jwt-token": jwtToken,
    },
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  const failedTasks = [];

  if (data.data && data.data.tasks) {
    for (const task of data.data.tasks) {
      if (task.accomplishment_eval_ret && task.accomplishment_eval_ret.result_list) {
        for (const result of task.accomplishment_eval_ret.result_list) {
          if (result.msg && result.msg.startsWith("第1轮关键点检查结果") && result.is_exception) {
            const url = result.url || "";
            if (url.includes("/tasks/")) {
              const taskId = url.split("/tasks/")[1].split("?")[0];
              failedTasks.push({
                session_id: task.session_id || "",
                title: task.title || "",
                domain: task.domain || "",
                sub_domain: task.sub_domain || "",
                role: task.role || "",
                task_id: taskId,
              });
            }
          }
        }
      }
    }
  }

  return failedTasks;
};

export const fetchSessionIdsByTaskId = async (taskId: string): Promise<ExtractReason[]> => {
  const evalDetailsData = await fetchEvalDetails(taskId);
  const reasons = [];

  if (evalDetailsData.data && evalDetailsData.data.data_eval_group_result_list) {
    for (const groupResult of evalDetailsData.data.data_eval_group_result_list) {
      if (groupResult.datas) {
        for (const dataItem of groupResult.datas) {
          const sessionId = dataItem.aime_task_info?.aime_session_id;
          const reason = {
            session_id: sessionId,
            check_info: "",
            check_reason: "",
            type: "",
          }
          if (dataItem.satisfaction_status === "success") {
            const score = Number(dataItem.satisfaction_score);
            let scoreText = "";
            if (score <= 1) {
              scoreText = `不可用(${score}分)`;
            } else if (score <= 2) {
              scoreText = `基本可用(${score}分)`;
            } else {
              scoreText = `可用(${score}分)`;
            }
            reason.check_info = scoreText;
            reason.check_reason = dataItem.satisfaction_analysis;
            reason.type = "satisfaction";
          }
          reasons.push(reason);
        }
      }
    }
  }

  return reasons;
}

// 获取单个任务的评测未通过的原因
const fetchSingleTaskReasonById = async (taskId: any, extractCheckpoint: boolean, extractSatisfaction: boolean, scoreLimit: number): Promise<ExtractReason[]> => {
  try {
    const evalDetailsData = await fetchEvalDetails(taskId);
    const results = [];

    if (evalDetailsData.data && evalDetailsData.data.data_eval_group_result_list) {
      for (const groupResult of evalDetailsData.data.data_eval_group_result_list) {
        if (groupResult.datas) {
          for (const dataItem of groupResult.datas) {
            const sessionId = dataItem.aime_task_info?.aime_session_id;
            if (extractCheckpoint && dataItem.checkpoint_machine_eval_result && dataItem.checkpoint_machine_eval_result.check_rule_results) {
              for (const ruleResult of dataItem.checkpoint_machine_eval_result.check_rule_results) {
                if (ruleResult.rule_result === "false") {
                  results.push({
                    session_id: sessionId,
                    check_info: ruleResult.rule_info,
                    check_reason: ruleResult.reasoning,
                    type: "checkpoint",
                  });
                }
              }
            }
            if (extractSatisfaction && dataItem.satisfaction_status === "success") {
              const score = Number(dataItem.satisfaction_score);
              let scoreText = "";
              if (score <= 1) {
                scoreText = `不可用(${score}分)`;
              } else if (score <= 2) {
                scoreText = `基本可用(${score}分)`;
              } else {
                scoreText = `可用(${score}分)`;
              }
              if (score <= scoreLimit) {
                results.push({
                  session_id: sessionId,
                  check_info: scoreText,
                  check_reason: dataItem.satisfaction_analysis,
                  type: "satisfaction",
                });
              }
            }
          }
        }
      }
    }

    return results;
  } catch (error) {
    console.error(`Error fetching details for task ${taskId}:`, error);
    return [];
  }
};

// 线上任务逐个获取未通过原因
const fetchReasons = async (tasks: any[]): Promise<ExtractReason[]> => {
  const tasksToExecute = tasks.map(task => fetchSingleTaskReasonById(task.task_id, true, false, 0));
  const resultsLists = await Promise.allSettled(tasksToExecute);

  const finalResults = [];
  for (const result of resultsLists) {
    if (result.status === "fulfilled" && Array.isArray(result.value)) {
      finalResults.push(...result.value);
    } else if (result.status === "rejected") {
      console.error("Error in task execution:", result.reason);
    }
  }

  return finalResults;
};

// 获取单个评测任务的详情
const fetchEvalDetails = async (taskId: string) => {
  const url = "https://test-ai.bytedance.net/api/benchmark/eval_task/data_list";
  const params = new URLSearchParams({
    task_id: taskId,
    page: "1",
    page_size: "100",
  });

  const jwtToken = await getJwt();
  const response = await fetch(`${url}?${params}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "x-jwt-token": jwtToken,
    },
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};

// 创建评测任务并运行，评测传入的session_ids
const createAimeTaskEval = async (taskName: string, sessionIds: string[]) => {
  const url = "https://test-ai.bytedance.net/api/benchmark/eval_task/create_aime_task_eval";

  const aimeTaskMap: Record<string, { questions: any[] }> = {};
  for (const sessionId of sessionIds) {
    aimeTaskMap[`https://aime.bytedance.net/chat/${sessionId}`] = { questions: [] };
  }

  const payload = {
    task_name: taskName,
    eval_stage: "end_to_end",
    eval_method: "machine",
    machine_eval_types: ["executor_eval", "checkpoint_eval"],
    aime_task_map: aimeTaskMap,
  };

  const jwtToken = await getJwt();
  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "x-jwt-token": jwtToken,
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};

// 通过传入的csv内容创建评测集，返回评测集id
const createEvaluationSetCsv = async (name: string, csvContent: string) => {
  const url = "https://test-ai.bytedance.net/api/benchmark/eval_data/create_evaluation_set";

  const csvContentBase64 = btoa(unescape(encodeURIComponent(csvContent)));

  const payload = {
    name: name,
    file_name: "eval.csv",
    data_file: '77u/' + csvContentBase64,
    lark_base_url: "",
  };

  const jwtToken = await getJwt();
  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "x-jwt-token": jwtToken,
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const result = await response.json();
  return result.data?.set_id;
};

interface Step {
  stepName: string;
  status: string;
  content?: string;
  environmentVariables?: Record<string, any>;
}

// 从评测任务列表生成csv内容
const generateCsvFromTasks = (tasks: any[]) => {
  const headers = [
    "chatgroup",
    "任务描述",
    "任务类型",
    "GT",
    "检查点",
    "检查类型",
    "任务模版",
    "任务模版变量"
  ];

  const csvLines = [];

  // 添加头部行
  csvLines.push(headers.join(","));

  // 添加说明行
  csvLines.push('"说明（该行可不删）：\n- 对话组，编号为1、2、3；同数值的为一个group，表示一次任务里的多轮对话；\n- 必填","说明：\n- 任务query，输入给aime的query\n- 必填","说明：\n- 如调研分析、代码生成、信息检索等\n- 必填","说明：\n- Ground Truth，\n- 非必填","说明：\n- 核心场景检查点，格式是json\n- 非必填","说明：\n- 跟检查点相关，值为artifact的时候走数分评测其他值走普通的checkpoint评测\n- 非必填","说明：\n- aime的官方模版id，如5e95eac9-a903-49b1-b04b-7c7c12b26726\n- 非必填","说明：\n- 跟任务模版相关，不同的任务模版对应不同的变量，格式是json"');

  for (let i = 0; i < tasks.length; i++) {
    const task = tasks[i];
    const chatgroup = String(i + 1);
    const taskDescription = task.title;
    const taskType = task.sub_domain;

    const rowData = [
      chatgroup,
      taskDescription,
      taskType,
      "",  // GT
      "",  // 检查点
      "",  // 检查类型
      "",  // 任务模版
      ""   // 任务模版变量
    ];

    // 转义CSV字段中的特殊字符
    const escapedRow = rowData.map(field => {
      if (typeof field === 'string' && (field.includes('"') || field.includes(',') || field.includes('\n'))) {
        const escapedField = field.replace(/"/g, '""');
        return `"${escapedField}"`;
      }
      return field;
    });

    csvLines.push(escapedRow.join(","));
  }

  return csvLines.join("\n");
};

// 批量走经验提取Pipeline
const submitExpExtractionPipeline = async (checkReasons: ExtractReason[], timeNowStr: string, baseFormData: BaseFormData) => {
  const url = "https://meta-server.bytedance.net/api/task-executions";

  const checkReasonDict: Record<string, any[]> = {};
  for (const reason of checkReasons) {
    if (reason.session_id in checkReasonDict) {
      checkReasonDict[reason.session_id].push(reason);
    } else {
      checkReasonDict[reason.session_id] = [reason];
    }
  }

  const tasks = [];
  for (const [sessionId, reasons] of Object.entries(checkReasonDict)) {
    tasks.push(submitSingleTask(sessionId, reasons, timeNowStr, baseFormData));
  }

  return await Promise.allSettled(tasks);
};

// 提交单个任务
const submitSingleTask = async (sessionId: string, reasons: ExtractReason[], timeNowStr: string, baseFormData: BaseFormData) => {
  let checkpointReasonText = "";
  let satisfactionReasonText = "";
  for (const r of reasons) {
    if (r.type === "checkpoint") {
      checkpointReasonText += `检查规则：${r.check_info}\n评价理由：${r.check_reason}\n\n`;
    } else if (r.type === "satisfaction") {
      satisfactionReasonText += `可用性分数：${r.check_info}\n评价理由：${r.check_reason}\n\n`;
    }
  }
  const user = await getCurrentUser();
  const steps: Array<Step> = [];
  if (checkpointReasonText) {
    steps.push({
      stepName: "exp_extra_badcase",
      status: "pending",
      content: checkpointReasonText,
    });
  }
  if (satisfactionReasonText) {
    steps.push({
      stepName: "exp_extra_badcase",
      status: "pending",
      content: satisfactionReasonText,
    });
  }
  if (baseFormData.extractError) {
    steps.push({
      stepName: "exp_extra_error",
      status: "pending",
    });
  }
  steps.push(
    {
      stepName: "exp_submit",
      status: "pending",
      environmentVariables: {
        "isQuickEnterExp": baseFormData.directPutIn.toString(),
        "variant": variantsTransform.get(baseFormData.role),
      },
    },
    {
      stepName: "exp_send_lark",
      status: "pending",
      content: `${user?.username}@bytedance.com`,
    }
  );

  const payload = {
    sessionId: sessionId,
    status: "pending",
    tag: `经验提取-${timeNowStr}`,
    executionSteps: steps,
    user: user?.username,
  };

  try {
    const url = "https://meta-server.bytedance.net/api/task-executions"
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : "未知错误";
    return {
      session_id: sessionId,
      success: false,
      error: errorMessage
    };
  }
};