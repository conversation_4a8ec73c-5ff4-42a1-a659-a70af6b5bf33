"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Form,
  Input,
  Button,
  Card,
  Grid,
  Select,
  Space,
  Message,
  Typography,
  Checkbox
} from "@arco-design/web-react";
import { IconSave, IconArrowLeft } from "@arco-design/web-react/icon";
import { toast } from "sonner";
import { PageHeader } from "@/app/components/PageHeader";
import { EvalTaskExtractFormData, extractExperiences, extractExperiencesFromEvalTask, OnlineExtractFormData } from "./ExtractExperiences";

const roleOptions = [
  { label: "小美", value: "LateralHire" },
  { label: "大卫", value: "IndustryVeteran" },
  { label: "小帅", value: "YoungTalent" },
];

const scoreLimitOptions = [
  { label: "0分", value: 0 },
  { label: "1分", value: 1 },
  { label: "2分", value: 2 },
  { label: "3分", value: 3 },
  { label: "4分", value: 4 },
  { label: "5分", value: 5 },
];

export default function ExtractExperiencePage() {
  const router = useRouter();
  const [onlineExtractForm] = Form.useForm<OnlineExtractFormData>();
  const [evalTaskForm] = Form.useForm<EvalTaskExtractFormData>();
  const [onlineExtractLoading, setOnlineExtractLoading] = useState(false);
  const [evalTaskLoading, setEvalTaskLoading] = useState(false);

  // 获取当前日期作为默认值
  const getCurrentDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  // 初始化表单默认值
  React.useEffect(() => {
    const today = getCurrentDate();
    onlineExtractForm.setFieldsValue({
      startDate: today,
      endDate: today,
      role: "LateralHire",
      onlyEvalNotPass: true,
      createEvalTask: false,
      createEvalSet: false,
      extractError: true,
      directPutIn: false,
      xDebugVersion: "",
    });

    // 初始化评测任务表单默认值
    evalTaskForm.setFieldsValue({
      evalTaskId: "",
      role: "LateralHire",
      directPutIn: false,
      extractError: true,
      extractCheckpoint: false,
      extractSatisfaction: true,
      scoreLimit: 3,
    });
  }, [onlineExtractForm, evalTaskForm]);

  // 处理表单提交
  const handleSubmit = async (values: OnlineExtractFormData) => {
    setOnlineExtractLoading(true);
    try {
      await extractExperiences(values);
      toast.success("批量提取经验任务创建成功");
      router.push("/experience/job");
    } catch (error) {
      console.error("批量提取经验失败:", error);
      toast.error("批量提取经验失败");
    } finally {
      setOnlineExtractLoading(false);
    }
  };

  // 处理评测任务表单提交
  const handleEvalTaskSubmit = async (values: EvalTaskExtractFormData) => {
    setEvalTaskLoading(true);
    try {
      await extractExperiencesFromEvalTask(values);
      toast.success("通过评测任务提取经验任务创建成功");
      router.push("/experience/job");
    } catch (error) {
      console.error("通过评测任务提取经验失败:", error);
      toast.error("通过评测任务提取经验失败");
    } finally {
      setEvalTaskLoading(false);
    }
  };

  return (
    <div>
      <PageHeader
        title="批量提取经验"
        extra={
          <Button
            icon={<IconArrowLeft />}
            onClick={() => router.push("/experience/job")}
          >
            返回列表
          </Button>
        }
      />

      <div className="p-6">
        <Form
          form={onlineExtractForm}
          layout="vertical"
          onSubmit={handleSubmit}
        >
          <Card title="通过线上任务提取" className="mb-6">
            <Grid.Row gutter={24}>
              <Grid.Col span={12}>
                <Form.Item
                  label="开始日期"
                  field="startDate"
                  rules={[{ required: true, message: "请选择开始日期" }]}
                >
                  <Input type="date" placeholder="请选择开始日期" />
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={12}>
                <Form.Item
                  label="结束日期"
                  field="endDate"
                  rules={[{ required: true, message: "请选择结束日期" }]}
                >
                  <Input type="date" placeholder="请选择结束日期" />
                </Form.Item>
              </Grid.Col>
            </Grid.Row>

            <Grid.Row gutter={24}>
              <Grid.Col span={12}>
                <Form.Item
                  label="执行角色"
                  field="role"
                  rules={[{ required: true, message: "请选择执行角色" }]}
                >
                  <Select placeholder="请选择执行角色">
                    {roleOptions.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={12}>
                <Form.Item
                  label="x-debug-version"
                  field="xDebugVersion"
                >
                  <Input placeholder="用于筛选线上指定x-debug-version的任务" />
                </Form.Item>
              </Grid.Col>
            </Grid.Row>

            <Grid.Row gutter={24}>
              <Grid.Col span={5}>
                <Form.Item field="onlyEvalNotPass">
                  <Checkbox defaultChecked>是否只提取未通过</Checkbox>
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={5}>
                <Form.Item field="createEvalTask">
                  <Checkbox>是否创建评测任务</Checkbox>
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={5}>
                <Form.Item field="createEvalSet">
                  <Checkbox>是否创建评测集</Checkbox>
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={5}>
                <Form.Item field="directPutIn">
                  <Checkbox>是否直接入库</Checkbox>
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={4}>
                <Form.Item field="extractError">
                  <Checkbox defaultChecked>是否提取Error</Checkbox>
                </Form.Item>
              </Grid.Col>
            </Grid.Row>
          </Card>

          <div className="text-center">
            <Space>
              <Button onClick={() => router.push("/experience/job")}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={onlineExtractLoading}
                icon={<IconSave />}
              >
                提交
              </Button>
            </Space>
          </div>
        </Form>

        {/* 通过评测任务提取表单 */}
        <Form
          form={evalTaskForm}
          layout="vertical"
          onSubmit={handleEvalTaskSubmit}
          className="mt-8"
        >
          <Card title="通过评测任务提取" className="mb-6">
            <Grid.Row gutter={24}>
              <Grid.Col span={8}>
                <Form.Item
                  label="评测任务ID"
                  field="evalTaskId"
                  rules={[{ required: true, message: "请输入评测任务ID" }]}
                >
                  <Input placeholder="请输入评测任务ID，或Url，如https://aime-auto-eval-fe.gf.bytedance.net/tasks/68a7df36be677e5e4f85895f" />
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={8}>
                <Form.Item
                  label="执行角色"
                  field="role"
                  rules={[{ required: true, message: "请选择执行角色" }]}
                >
                  <Select placeholder="请选择执行角色">
                    {roleOptions.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={8}>
                <Form.Item
                  label="可用性分数限制(<=)"
                  field="scoreLimit"
                  rules={[{ required: true, message: "请选择最高抽取分数" }]}
                >
                  <Select placeholder="请选择最高抽取分数">
                    {scoreLimitOptions.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Grid.Col>
            </Grid.Row>
            <Grid.Row gutter={24}>
              <Grid.Col span={6}>
                <Form.Item field="directPutIn">
                  <Checkbox>是否直接入库</Checkbox>
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={6}>
                <Form.Item field="extractError">
                  <Checkbox defaultChecked>是否提取Error</Checkbox>
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={6}>
                <Form.Item field="extractCheckpoint">
                  <Checkbox>是否提取关键点</Checkbox>
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={6}>
                <Form.Item field="extractSatisfaction">
                  <Checkbox defaultChecked>是否提取可用性</Checkbox>
                </Form.Item>
              </Grid.Col>
            </Grid.Row>
          </Card>

          <div className="text-center">
            <Space>
              <Button onClick={() => router.push("/experience/job")}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={evalTaskLoading}
                icon={<IconSave />}
              >
                提交
              </Button>
            </Space>
          </div>
        </Form>
      </div>
    </div>
  );
}