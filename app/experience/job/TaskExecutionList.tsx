"use client";

import React, { useState, useMemo, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button, Space, Form, Modal } from "@arco-design/web-react";
import { 
  IconPlus, 
  IconRefresh,
  IconExport
} from "@arco-design/web-react/icon";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import ConfirmDeleteDialog from "../components/ConfirmDeleteDialog";
import { 
  deleteTaskExecution, 
  filterTaskExecutions,
  FilterTaskParams,
  TaskExecution,
  startTaskExecution,
  completeTaskExecution,
  failTaskExecution,
  restartTaskExecution
} from "../api/task-execution";
import { SearchForm, TaskTable, TaskDetailModal, ActionConfirmModal, PaginationControls } from "./components";
import { updateURLParams } from "./utils";

// 扩展搜索参数接口
interface SearchParams {
  sessionId?: string;
  user?: string;
  status?: string;
  tag?: string;
  date_range?: [string, string];
}

export function TaskExecutionList() {
  const router = useRouter();
  const urlSearchParams = useSearchParams();
  const [form] = Form.useForm();
  const [searchParams, setSearchParams] = useState<SearchParams>({});

  // 从URL参数初始化搜索条件
  useEffect(() => {
    const params: SearchParams = {};
    
    // 读取URL参数
    const sessionId = urlSearchParams.get('sessionId');
    const user = urlSearchParams.get('user');
    const status = urlSearchParams.get('status');
    const tag = urlSearchParams.get('tag');
    const start_date = urlSearchParams.get('start_date');
    const end_date = urlSearchParams.get('end_date');
    const page = urlSearchParams.get('page');
    const size = urlSearchParams.get('size');
    
    // 设置参数
    if (sessionId) params.sessionId = sessionId;
    if (user) params.user = user;
    if (status) params.status = status;
    if (tag) params.tag = tag;
    if (start_date && end_date) params.date_range = [start_date, end_date];
    if (page) setCurrentPage(parseInt(page));
    if (size) setPageSize(parseInt(size));
    
    // 更新状态和表单
    setSearchParams(params);
    form.setFieldsValue(params);
  }, [urlSearchParams, form]);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null);
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<TaskExecution | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [actionConfirmVisible, setActionConfirmVisible] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState<'start' | 'complete' | 'fail' | 'restart' | null>(null);
  const [taskToConfirm, setTaskToConfirm] = useState<TaskExecution | null>(null);
  const [batchActionToConfirm, setBatchActionToConfirm] = useState<'batch_start' | 'batch_delete' | null>(null);
  const [batchActionConfirmVisible, setBatchActionConfirmVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);

  // 查询任务执行列表
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["taskExecutionList", searchParams, currentPage, pageSize],
    queryFn: async () => {
      try {
        // 构建查询参数
        const filterParams: FilterTaskParams = {
          page: currentPage,
          size: pageSize,
        };

        // 添加各种筛选条件
        if (searchParams.sessionId) {
          filterParams.sessionId = searchParams.sessionId;
        }
        if (searchParams.user) {
          filterParams.user = searchParams.user;
        }
        if (searchParams.status) {
          filterParams.status = searchParams.status;
        }
        if (searchParams.tag) {
          filterParams.tag = searchParams.tag;
        }
        if (searchParams.date_range && searchParams.date_range[0] && searchParams.date_range[1]) {
          // 这里需要根据实际API需求调整日期筛选逻辑
          // filterParams.startDate = formatDateForServer(searchParams.date_range[0]);
          // filterParams.endDate = formatDateForServer(searchParams.date_range[1]);
        }

        const response = await filterTaskExecutions(filterParams);
        return response.data;
      } catch (error) {
        console.error("获取任务执行列表失败:", error);
        toast.error("获取任务执行列表失败");
        return { total: 0, page: 1, size: pageSize, data: [] };
      }
    },
  });

  // 表格数据
  const tableData = useMemo(() => {
    if (!data || !data.data) return [];
    return data.data.map((item: TaskExecution) => ({
      ...item,
      key: item.id || '',
    }));
  }, [data]);

  // 搜索表单变更
  const handleSearch = () => {
    const values = form.getFieldsValue();
    setSearchParams(values);
    setCurrentPage(1);
    updateURLParams(values, 1, pageSize);
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setSearchParams({});
    setCurrentPage(1);
    updateURLParams({}, 1, pageSize);
  };

  // 处理添加按钮点击
  const handleAdd = () => {
    router.push("/experience/job/edit");
  };

  // 处理批量提取经验
  const handleBatchExtract = () => {
    router.push("/experience/job/extract");
  };

  // 分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateURLParams(searchParams, page, pageSize);
  };

  // 每页条数变化
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
    updateURLParams(searchParams, 1, size);
  };

  // 处理开始任务
  const handleStartTask = (task: TaskExecution) => {
    if (task.status !== 'pending') {
      toast.error('只有待处理状态的任务可以开始执行');
      return;
    }
    setActionToConfirm('start');
    setTaskToConfirm(task);
    setActionConfirmVisible(true);
  };

  // 处理完成任务
  const handleCompleteTask = (task: TaskExecution) => {
    if (task.status !== 'running') {
      toast.error('只有运行中状态的任务可以标记为完成');
      return;
    }
    setActionToConfirm('complete');
    setTaskToConfirm(task);
    setActionConfirmVisible(true);
  };

  // 处理失败任务
  const handleFailTask = (task: TaskExecution) => {
    if (task.status !== 'running') {
      toast.error('只有运行中状态的任务可以标记为失败');
      return;
    }
    setActionToConfirm('fail');
    setTaskToConfirm(task);
    setActionConfirmVisible(true);
  };

  // 处理查看详情
  const handleViewDetail = (task: TaskExecution) => {
    setSelectedTask(task);
    setDetailModalVisible(true);
  };

  // 处理重启任务
  const handleRestartTask = (task: TaskExecution) => {
    if (!task.id) return;
    setActionToConfirm('restart');
    setTaskToConfirm(task);
    setActionConfirmVisible(true);
  };

  // 处理删除任务
  const handleDeleteTask = (task: TaskExecution) => {
    if (task.id) {
      setDeleteItemId(task.id);
      setDeleteConfirmVisible(true);
    }
  };

    // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) return;
    setBatchActionToConfirm('batch_delete');
    setBatchActionConfirmVisible(true);
  };

  // 处理批量开始
  const handleBatchStart = () => {
    if (selectedRowKeys.length === 0) return;
    setBatchActionToConfirm('batch_start');
    setBatchActionConfirmVisible(true);
  };

  // 确认删除
  const confirmDelete = async () => {
    if (!deleteItemId) return;
    
    setDeleteLoading(true);
    try {
      const response = await deleteTaskExecution(deleteItemId);
      if (response.code === 200) {
        toast.success("删除成功");
        refetch();
      } else {
        toast.error(response.msg || "删除失败");
      }
    } catch (error) {
      console.error("删除任务执行失败:", error);
      toast.error("删除任务执行失败");
    } finally {
      setDeleteLoading(false);
      setDeleteConfirmVisible(false);
      setDeleteItemId(null);
    }
  };

  // 确认操作
  const confirmAction = async () => {
    if (!taskToConfirm || !actionToConfirm || !taskToConfirm.id) return;

    setActionLoading(true);
    try {
      let response;
      switch (actionToConfirm) {
        case 'start':
          response = await startTaskExecution(taskToConfirm.id);
          break;
        case 'complete':
          response = await completeTaskExecution(taskToConfirm.id);
          break;
        case 'fail':
          response = await failTaskExecution(taskToConfirm.id, '任务执行失败');
          break;
        case 'restart':
          response = await restartTaskExecution(taskToConfirm.id);
          break;
      }

      if (response && response.code === 200) {
        const actionText = {
          'start': '开始执行',
          'complete': '标记完成',
          'fail': '标记失败',
          'restart': '重启任务'
        }[actionToConfirm];
        toast.success(`${actionText}成功`);
        refetch();
      } else {
        toast.error(response?.msg || '操作失败');
      }
    } catch (error) {
      console.error(`${actionToConfirm}失败:`, error);
      toast.error(`${actionToConfirm}失败`);
    } finally {
      setActionLoading(false);
      setActionConfirmVisible(false);
      setTaskToConfirm(null);
      setActionToConfirm(null);
    }
  };

  // 确认批量操作
  const confirmBatchAction = async () => {
    if (!batchActionToConfirm || selectedRowKeys.length === 0) return;

    // 将selectedRowKeys转换为string[]类型
    const stringKeys = selectedRowKeys.map(key => key.toString());
    
    setActionLoading(true);
    try {
      let successCount = 0;
      let failedCount = 0;
      
      if (batchActionToConfirm === 'batch_start') {
        // 批量调用单个开始接口
        const promises = stringKeys.map(id => startTaskExecution(id));
        const results = await Promise.allSettled(promises);
        
        successCount = results.filter(result => result.status === 'fulfilled' && result.value.code === 200).length;
        failedCount = results.length - successCount;
        
        if (successCount > 0) {
          toast.success(`成功开始${successCount}个任务`);
          setSelectedRowKeys([]);
          refetch();
        }
        
        if (failedCount > 0) {
          toast.error(`${failedCount}个任务开始失败`);
        }
      } else if (batchActionToConfirm === 'batch_delete') {
        // 批量调用单个删除接口
        const promises = stringKeys.map(id => deleteTaskExecution(id));
        const results = await Promise.allSettled(promises);
        
        successCount = results.filter(result => result.status === 'fulfilled' && result.value.code === 200).length;
        failedCount = results.length - successCount;
        
        if (successCount > 0) {
          toast.success(`成功删除${successCount}个任务`);
          setSelectedRowKeys([]);
          refetch();
        } 
        
        if (failedCount > 0) {
          toast.error(`${failedCount}个任务删除失败`);
        }
      }
    } catch (error) {
      console.error(`${batchActionToConfirm}失败:`, error);
      toast.error(`${batchActionToConfirm}失败`);
    } finally {
      setActionLoading(false);
      setBatchActionConfirmVisible(false);
      setBatchActionToConfirm(null);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <SearchForm 
        form={form}
        onSearch={handleSearch}
        onReset={handleReset}
      />

      <div className="flex-1 overflow-auto">
        <div className="mb-4 flex justify-between items-center">
          <Space>
            <Button 
              type="primary" 
              icon={<IconPlus />} 
              onClick={handleAdd}
            >
              创建任务
            </Button>
            <Button 
              type="primary" 
              icon={<IconExport />} 
              onClick={handleBatchExtract}
            >
              批量提取经验
            </Button>
            <Button 
              type="outline" 
              icon={<IconRefresh />} 
              onClick={() => refetch()}
              status="success"
            >
              刷新
            </Button>
          </Space>
          
          <Space>
            <Button 
              type="outline" 
              onClick={handleBatchStart}
              status="warning"
              disabled={selectedRowKeys.length === 0}
            >
              批量开始 {selectedRowKeys.length > 0 && `(${selectedRowKeys.length})`}
            </Button>
            <Button 
              type="outline" 
              onClick={handleBatchDelete}
              status="danger"
              disabled={selectedRowKeys.length === 0}
            >
              批量删除 {selectedRowKeys.length > 0 && `(${selectedRowKeys.length})`}
            </Button>
          </Space>
        </div>
        
        <TaskTable
          data={tableData}
          loading={isLoading}
          onStartTask={handleStartTask}
          onViewDetail={handleViewDetail}
          onDeleteTask={handleDeleteTask}
          onRestartTask={handleRestartTask}
          selectedRowKeys={selectedRowKeys}
          onSelectionChange={setSelectedRowKeys}
        />
        
        <PaginationControls
          total={data?.total || 0}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      </div>
  

      {/* 删除确认对话框 */}
      <ConfirmDeleteDialog
        open={deleteConfirmVisible}
        onOpenChange={(open) => {
          if (!open) {
            setDeleteConfirmVisible(false);
            setDeleteItemId(null);
          }
        }}
        title="确认删除"
        description="确定要删除这个任务执行记录吗？此操作不可撤销。"
        onConfirm={confirmDelete}
        loading={deleteLoading}
      />

      {/* 操作确认对话框 */}
      <ActionConfirmModal
        visible={actionConfirmVisible}
        taskId={taskToConfirm?.id}
        actionType={actionToConfirm}
        onConfirm={confirmAction}
        onCancel={() => {
          setActionConfirmVisible(false);
          setTaskToConfirm(null);
          setActionToConfirm(null);
        }}
        loading={actionLoading}
      />

      {/* 批量操作确认对话框 */}
      <Modal
        title="确认批量操作"
        visible={batchActionConfirmVisible}
        onConfirm={confirmBatchAction}
        onCancel={() => {
          setBatchActionConfirmVisible(false);
          setBatchActionToConfirm(null);
        }}
        confirmLoading={actionLoading}
      >
        <div>
          {batchActionToConfirm === 'batch_start' && (
            <span>确定要批量开始 {selectedRowKeys.length} 个任务吗？</span>
          )}
          {batchActionToConfirm === 'batch_delete' && (
            <span>确定要批量删除 {selectedRowKeys.length} 个任务吗？此操作不可撤销。</span>
          )}
        </div>
      </Modal>

      {/* 详情模态框 */}
      <TaskDetailModal
        visible={detailModalVisible}
        task={selectedTask}
        onClose={() => {
          setDetailModalVisible(false);
          setSelectedTask(null);
        }}
        onStartTask={handleStartTask}
        onCompleteTask={handleCompleteTask}
        onFailTask={handleFailTask}
      />
    </div>
  );
}