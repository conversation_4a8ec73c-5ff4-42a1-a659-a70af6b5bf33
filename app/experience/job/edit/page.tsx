"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Form,
  Input,
  Button,
  Card,
  Grid,
  Select,
  Space,
  Message,
  Typography,
  Switch
} from "@arco-design/web-react";
import {  IconSave, IconArrowLeft, IconPlus, IconDelete } from "@arco-design/web-react/icon";
import { JSONEditor } from "@/components/ui/json-editor";
import { toast } from "sonner";
import { PageHeader } from "@/app/components/PageHeader";
import { 
  createTaskExecution, 
  statusOptions,
  stepName
} from "../../api/task-execution";
import type { TaskExecution, ExecutionStep } from "../../api/task-execution";
import { useStore } from "@nanostores/react";
import { userAtom } from "@/app/store/auth";
import { variantOptions } from "../../api/experience";

const { Text } = Typography;

// 默认执行步骤
const defaultExecutionSteps: ExecutionStep[] = [
  { stepName: stepName[0].value, status: "pending", startTime: new Date().toISOString() },
];

// 更新执行步骤的接口，添加脚本相关字段
interface ExtendedExecutionStep extends ExecutionStep {
  pythonScriptName?: string;
  pythonScriptContent?: string;
  scriptBeforeBash?: string;
  environmentVariables?: Record<string, string>;
  variant?: string; // 用于 exp_submit 步骤的应用类型选择
  isQuickEnterExp?: boolean; // 用于 exp_submit 步骤的快速进入经验开关
}

export default function TaskExecutionEditPage() {
  const router = useRouter();
  // 移除编辑功能，只保留创建功能
  const isEdit = false;

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(false);
  const [executionSteps, setExecutionSteps] = useState<ExtendedExecutionStep[]>(defaultExecutionSteps);
  const user = useStore(userAtom);

  // 初始化新建任务的默认值
  useEffect(() => {
    form.setFieldsValue({
      status: "pending",
      startTime: new Date().toISOString(),
      user: user?.username || "system"
    });
  }, [form, user]);

  // 添加执行步骤
  const addExecutionStep = () => {
    const newStep: ExtendedExecutionStep = {
      stepName: stepName[0].value,
      status: "pending",
      startTime: new Date().toISOString()
    };
    setExecutionSteps([...executionSteps, newStep]);
  };

  // 删除执行步骤
  const removeExecutionStep = (index: number) => {
    if (executionSteps.length <= 1) {
      Message.warning('至少需要保留一个执行步骤');
      return;
    }
    const newSteps = executionSteps.filter((_, i) => i !== index);
    setExecutionSteps(newSteps);
  };

  // 更新执行步骤
  const updateExecutionStep = (index: number, field: keyof ExtendedExecutionStep, value: any) => {
    const newSteps = [...executionSteps];
    newSteps[index] = { ...newSteps[index], [field]: value };
    
    // 如果是 exp_submit 步骤且选择了 variant，更新环境变量
    if (field === 'variant' && isExpSubmitStep(newSteps[index].stepName)) {
      const currentEnvVars = newSteps[index].environmentVariables || {};
      newSteps[index].environmentVariables = {
        ...currentEnvVars,
        variant: value
      };
    }
    
    // 如果是 exp_submit 步骤且切换了快速进入开关，更新环境变量
    if (field === 'isQuickEnterExp' && isExpSubmitStep(newSteps[index].stepName)) {
      const currentEnvVars = newSteps[index].environmentVariables || {};
      newSteps[index].environmentVariables = {
        ...currentEnvVars,
        isQuickEnterExp: value ? 'true' : 'false'
      };
    }
    
    setExecutionSteps(newSteps);
  };

  // 判断是否为自定义脚本步骤
  const isCustomScriptStep = (stepName: string) => {
    return stepName === 'exp_custom_script';
  };

  // 判断是否为经验上传步骤
  const isExpSubmitStep = (stepName: string) => {
    return stepName === 'exp_submit';
  };

  // 提交表单
  const handleSubmit = async (values: Record<string, unknown>) => {
    // 验证自定义脚本步骤的必填字段
    for (let i = 0; i < executionSteps.length; i++) {
      const step = executionSteps[i];
      if (isCustomScriptStep(step.stepName)) {
        if (!step.pythonScriptName || !step.pythonScriptContent) {
          Message.error(`步骤 ${i + 1}：自定义脚本步骤必须填写脚本名称和内容`);
          return;
        }
      }
    }

    setLoading(true);
    try {
      // 处理执行步骤，将环境变量的JSON字符串解析为对象
      const processedSteps = executionSteps.map(step => {
        const processedStep = { ...step };
        
        // 如果是自定义脚本步骤，处理环境变量
        if (isCustomScriptStep(step.stepName) && step.environmentVariables) {
          try {
            // 如果environmentVariables是字符串，尝试解析为对象
            if (typeof step.environmentVariables === 'string') {
              processedStep.environmentVariables = JSON.parse(step.environmentVariables);
            }
          } catch (error) {
            console.error('解析环境变量JSON失败:', error);
            Message.error(`步骤 ${executionSteps.indexOf(step) + 1}：环境变量JSON格式无效`);
            throw new Error('环境变量JSON格式无效');
          }
        }
        
        return processedStep;
      });

      const taskData: Omit<TaskExecution, 'id' | 'createTime' | 'updateTime'> = {
        sessionId: values.sessionId as string,
        status: values.status as 'pending' | 'running' | 'completed' | 'failed',
        startTime: values.startTime as string,
        endTime: values.endTime as string | undefined,
        executionSteps: processedSteps,
        user: user?.username || "system"
      };

      const response = await createTaskExecution(taskData);

      if (response.code === 200) {
        toast.success(isEdit ? "更新任务成功" : "创建任务成功");
        router.push("/experience/job");
      } else {
        throw new Error(response.msg || "操作失败");
      }
    } catch (error) {
      console.error("保存任务失败:", error);
      toast.error("保存任务失败");
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return <div className="p-4">加载中...</div>;
  }

  return (
    <div>
      <PageHeader
        title="创建任务执行"
        description="创建新的任务执行记录"
        extra={
          <Button
            icon={<IconArrowLeft />}
            onClick={() => router.push("/experience/job")}
          >
            返回列表
          </Button>
        }
      />

      <div className="p-6">
        <Form
          form={form}
          layout="vertical"
          onSubmit={handleSubmit}
        >
          <Card title="基本信息" className="mb-6">
            <Grid.Row gutter={24}>
              <Grid.Col span={12}>
                <Form.Item
                  label="会话ID"
                  field="sessionId"
                  rules={[{ required: true, message: "请输入会话ID" }]}
                >
                  <Input placeholder="请输入会话 ID, 如果是对比提取用 _ 分割, 前面是goodcase id, 如：xxx_yyyy" />
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={12}>
                <Form.Item
                  label="状态"
                  field="status"
                  disabled
                  rules={[{ required: true, message: "请选择状态" }]}
                >
                  <Select placeholder="请选择状态">
                    {statusOptions.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Grid.Col>
            </Grid.Row>

          </Card>

          <Card title="执行步骤" className="mb-6">
            <div className="space-y-4">
              {executionSteps.map((step, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <Text>步骤 {index + 1}</Text>
                    {executionSteps.length > 1 && (
                      <Button
                        icon={<IconDelete />}
                        size="small"
                        status="danger"
                        onClick={() => removeExecutionStep(index)}
                      >
                        删除
                      </Button>
                    )}
                  </div>
                  
                  <Grid.Row gutter={16}>
                    <Grid.Col span={12}>
                      <Form.Item
                        label={`步骤名称`}
                        rules={[{ required: true, message: "请选择步骤名称" }]}
                      >
                        <Select
                          value={step.stepName}
                          onChange={(value) => updateExecutionStep(index, 'stepName', value)}
                          placeholder="请选择步骤名称"
                          showSearch
                          allowClear
                        >
                          {stepName.map(option => (
                            <Select.Option key={option.value} value={option.value}>
                              {option.label}
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <Form.Item label={`步骤状态`}>
                        <Input
                          value="待处理"
                          disabled
                          placeholder="状态默认为待处理"
                        />
                      </Form.Item>
                    </Grid.Col>
                  </Grid.Row>

                  <Form.Item label="Step 附加 Content（可选）">
                    <Input.TextArea
                      value={step.content || ''}
                      onChange={(value) => updateExecutionStep(index, 'content', value)}
                      placeholder="请输入 Step 附加 Content,  比如经验评价的理由，经验提取的 reason 等"
                      rows={2}
                    />
                  </Form.Item>

                  {/* 自定义脚本步骤的额外字段 */}
                  {isCustomScriptStep(step.stepName) && (
                    <>
                      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                        <h4 className="text-sm font-medium text-blue-900 mb-3">自定义脚本配置（目前仅支持Python2.xx脚本）</h4>
                        
                        <Grid.Row gutter={16} className="mb-3">
                          <Grid.Col span={12}>
                            <Form.Item
                              label="Python脚本名称"
                              required
                              rules={[{ required: true, message: "请输入Python脚本名称" }]}
                            >
                              <Input
                                value={step.pythonScriptName || ''}
                                onChange={(value) => updateExecutionStep(index, 'pythonScriptName', value)}
                                placeholder="请输入Python脚本文件名，如：data_processor.py"
                              />
                            </Form.Item>
                          </Grid.Col>
                          <Grid.Col span={12}>
                            <Form.Item label="前置Bash脚本（可选）">
                              <Input.TextArea
                                value={step.scriptBeforeBash || ''}
                                onChange={(value) => updateExecutionStep(index, 'scriptBeforeBash', value)}
                                placeholder="执行Python脚本前的bash命令，如：pip install xxx (确保依赖都在)"
                                rows={1}
                              />
                            </Form.Item>
                          </Grid.Col>
                        </Grid.Row>

                        <Form.Item
                          label="Python脚本内容"
                          required
                          rules={[{ required: true, message: "请输入Python脚本内容" }]}
                        >
                          <Input.TextArea
                            value={step.pythonScriptContent || ''}
                            onChange={(value) => updateExecutionStep(index, 'pythonScriptContent', value)}
                            placeholder="请输入Python脚本代码内容"
                            rows={5}
                            style={{ fontFamily: 'monospace', fontSize: '13px' }}
                          />
                        </Form.Item>

                        <Form.Item label="环境变量（可选，JSON格式）">
                          <div className="border rounded-lg bg-white">
                            <JSONEditor 
                              value={"{\"jwt\": \"xxxx\"}"}
                              onChange={(value) => updateExecutionStep(index, 'environmentVariables', value)}
                              showValidation={true}
                            />
                          </div>
                        </Form.Item>
                      </div>
                    </>
                  )}

                  {/* 经验上传步骤的额外字段 */}
                  {isExpSubmitStep(step.stepName) && (
                    <>
                      <div className="mt-4 p-3 bg-green-50 rounded-lg">
                        <h4 className="text-sm font-medium text-green-900 mb-3">经验上传配置</h4>
                        
                        <Grid.Row gutter={16}>
                          <Grid.Col span={12}>
                            <Form.Item
                              label="应用类型"
                              required
                              rules={[{ required: true, message: "请选择应用类型" }]}
                            >
                              <Select
                                value={step.environmentVariables?.variant || 'newbie'}
                                onChange={(value) => updateExecutionStep(index, 'variant' as keyof ExtendedExecutionStep, value)}
                                placeholder="请选择应用类型"
                                showSearch
                                allowClear
                              >
                                {variantOptions.map(option => (
                                  <Select.Option key={option.value} value={option.value}>
                                    {option.label}
                                  </Select.Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Grid.Col>
                          <Grid.Col span={12}>
                            <Form.Item label="快速进入经验库">
                              <Switch
                                checked={step.environmentVariables?.isQuickEnterExp === 'true'}
                                onChange={(value) => updateExecutionStep(index, 'isQuickEnterExp' as keyof ExtendedExecutionStep, value)}
                              />
                            </Form.Item>
                          </Grid.Col>
                        </Grid.Row>
                      </div>
                    </>
                  )}
                </div>
              ))}

              <div className="flex justify-center">
                <Button
                  icon={<IconPlus />}
                  type="outline"
                  onClick={addExecutionStep}
                >
                  添加执行步骤
                </Button>
              </div>
            </div>
          </Card>

          <div className="text-center">
            <Space>
              <Button onClick={() => router.push("/experience/job")}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<IconSave />}
              >
                创建
              </Button>
            </Space>
          </div>
        </Form>
      </div>
    </div>
  );
}