import { fetchRequest } from '../../api/request';

// 经验管理相关类型定义
export interface Experience {
  id?: string;                   // 经验ID，创建时自动生成
  vector_content: string;        // 向量内容，用于搜索匹配
  title: string;                 // 经验标题
  type: string;                  // 经验类型
  experience_content: string;    // 经验内容，支持Markdown格式
  source_id: string;             // 来源ID
  apply_type: string;            // 应用类型
  publish_status: string;        // 发布状态
  extract_type: string;          // 提取人员
  create_time?: string;          // 创建时间，自动生成
  update_time?: string;          // 更新时间，自动生成
  variant?: string;              // varint
  limit?: number;                // 限制string
  ext?: string;                  // 经验提取原因
}

// 经验类型选项
export const experienceTypeOptions = [
  { label: "Insights", value: "Insights" },
  { label: "Insights-Tools", value: "Insights-Tools" },
  { label: "Insights-Script", value: "Insights-Script" },
  { label: "Insights-BadCase", value: "Insights-BadCase" },
  { label: "Insights-Planner", value: "Insights-Planner" },
  { label: "Insights-Workflow", value: "Insights-Workflow" },
  { label: "Insights-Error", value: "Insights-Error" },
  { label: "ReusableTool", value: "ReusableTool" },
  { label: "ReusableWorkflow", value: "ReusableWorkflow" }
];

// 应用类型选项
export const applyTypeOptions = [
  { label: "Mewtwo", value: "mewtwo" },
  { label: "Planner", value: "planner" }
];

// 发布状态选项
export const publishStatusOptions = [
  { label: "待审核", value: "pending" },
  { label: "已发布", value: "publish" }
];

// 应用类型选项
export const variantOptions = [
  { label: "大卫", value: "invited_expert" },
  { label: "小美", value: "newbie" }
];

// 分页响应接口
export interface PaginationResponse<T> {
  total: number;
  page: number;
  size: number;
  data: T[];
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  msg?: string;
  data?: T;
}

// 高级搜索过滤条件类型
export interface FilterCondition {
  op: "must" | "should" | "must_not";
  field: string;
  conds: string[];
}

// 高级搜索查询参数
export interface AdvancedSearchParams {
  project: string;
  name: string;
  query: string;
  limit: number;
  dense_weight: number;
  query_param?: {
    doc_filter: {
      op: "and" | "or";
      conds: FilterCondition[];
    };
  };
}

// 高级搜索结果类型
export interface SearchResult {
  id: string;
  content: string;
  score: number;
  table_chunk_fields: {
    field_name: string;
    field_value: string;
  }[];
}

// 高级搜索响应类型
export interface SearchResponse {
  code: number;
  data: {
    code: number;
    message: string;
    request_id: string;
    data: {
      collection_name: string;
      count: number;
      result_list: SearchResult[];
      token_usage: {
        embedding_token_usage: {
          prompt_tokens: number;
          completion_tokens: number;
          total_tokens: number;
        };
        rerank_token_usage: number;
        rewrite_token_usage: number;
      };
    };
  };
  msg: string;
}

// 创建自定义request对象，用于发送请求
const request = {
  get: async (url: string, params?: Record<string, any>) => {
    const queryParams = params ? new URLSearchParams(params).toString() : '';
    const fullUrl = queryParams ? `${url}?${queryParams}` : url;
    return fetchRequest(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  },
  post: async (url: string, data: any) => {
    return fetchRequest(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
  put: async (url: string, data: any) => {
    return fetchRequest(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
  delete: async (url: string, options?: { data?: any }) => {
    return fetchRequest(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: options?.data ? JSON.stringify(options.data) : undefined,
    });
  },
};

// 筛选经验的参数接口
export interface FilterExperienceParams {
  type?: string;
  applyType?: string;
  publishStatus?: string;
  extractType?: string;
  keyword?: string;
  variant?: string;
  startDate?: string;
  endDate?: string;
  sourceId?: string;
  pointId?: string;
  page?: number;
  size?: number;
}

// 基础URL
export const BASE_URL = "https://meta-server.bytedance.net";
// export const BASE_URL = "http://localhost:8600";

// 新的统一筛选接口
export const filterExperiences = async (params: FilterExperienceParams): Promise<ApiResponse<PaginationResponse<Experience>>> => {
  // 构建查询参数
  const queryParams = new URLSearchParams();
  
  if (params.type) queryParams.append('type', params.type);
  if (params.applyType) queryParams.append('applyType', params.applyType);
  if (params.publishStatus) queryParams.append('publishStatus', params.publishStatus);
  if (params.extractType) queryParams.append('extractType', params.extractType);
  if (params.keyword) queryParams.append('keyword', params.keyword);
  if (params.variant) queryParams.append('variant', params.variant);
  if (params.startDate) queryParams.append('startDate', params.startDate);
  if (params.endDate) queryParams.append('endDate', params.endDate);
  if (params.sourceId) queryParams.append('source_id', params.sourceId);
  if (params.pointId) queryParams.append('point_id', params.pointId);
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.size) queryParams.append('size', params.size.toString());
  
  const url = `${BASE_URL}/api/experiences/filter${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
  return request.get(url);
};

// 创建经验
export const createExperience = async (data: Omit<Experience, 'id' | 'create_time' | 'update_time'>): Promise<ApiResponse<Experience>> => {
  return request.post(`${BASE_URL}/api/experiences`, data);
};

// 根据ID查询经验
export const getExperienceById = async (id: string): Promise<ApiResponse<Experience>> => {
  return request.get(`${BASE_URL}/api/experiences/${id}`);
};

// 高级搜索经验
export const advancedSearchExperiences = async (params: AdvancedSearchParams): Promise<SearchResponse> => {
  return request.post(`${BASE_URL}/api/experience/search/advanced`, params) as Promise<SearchResponse>;
};

// 更新经验
export const updateExperience = async (id: string, data: Partial<Experience>): Promise<ApiResponse<boolean>> => {
  return request.put(`${BASE_URL}/api/experiences/${id}`, data);
};

// 删除经验
export const deleteExperience = async (id: string): Promise<ApiResponse<boolean>> => {
  return request.delete(`${BASE_URL}/api/experiences/${id}`);
};

// 批量删除经验
export const batchDeleteExperiences = async (ids: string[]): Promise<ApiResponse<{ [key: string]: boolean }>> => {
  return request.delete(`${BASE_URL}/api/experiences/batch`, { data: ids });
};

// 统计相关类型定义
export interface BasicStats {
  total: number;          // 经验总数
  pending: number;        // 待审核经验数量
  published: number;      // 已发布经验数量
  manual: number;         // 人工经验总数（extract_type 不等于 llm）
}

export interface TypeStats {
  Insights: number;         // Insights类型经验数量
  ReusableTool: number;     // ReusableTool类型经验数量
  ReusableWorkflow: number; // ReusableWorkflow类型经验数量
}

export interface StatisticsData {
  basicStats: BasicStats;
  typeStats: TypeStats;
}

export interface DateStats {
  date: string;           // 日期（YYYY-MM-DD格式）
  count: number;          // 该日期新增的经验数量
}

// 获取经验数据统计
export const getExperienceStatistics = async (): Promise<ApiResponse<StatisticsData>> => {
  return request.get(`${BASE_URL}/api/experiences/statistics`);
};

// 获取按日期统计的经验数据
export const getExperienceStatisticsByDate = async (days: number = 30): Promise<ApiResponse<DateStats[]>> => {
  return request.get(`${BASE_URL}/api/experiences/statistics/by-date`, { days });
};