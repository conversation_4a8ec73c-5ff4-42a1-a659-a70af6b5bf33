import { fetchRequest } from '../../api/request';
import { BASE_URL } from './experience';

// 待入库经验相关类型定义
export interface ExperienceWait {
  id?: string;                        // 经验ID，创建时自动生成
  vector_content: string;             // 向量内容，用于搜索匹配
  title: string;                      // 经验标题
  type: string;                       // 经验类型: Insights/ReusableTool/ReusableWorkflow
  experience_content: string;         // 经验内容，支持Markdown格式
  source_id: string;                  // 来源ID
  apply_type: string;                 // 应用类型: mewtwo/planner
  status: string;                     // 状态: create/running/废弃/更新/新增
  extract_type: string;               // 提取的人员
  create_time?: string;               // 创建时间，自动生成
  update_time?: string;               // 更新时间，自动生成
  target_experience_id?: string;      // 如果是更新类型，记录目标经验ID
  check_result?: string;              // LLM检查结果
  check_reason?: string;              // 检查原因说明
  variant?: string;                   // variant
  limit?: number;                     // 限制
  ext?: string;                       // 经验提取原因
}

// 状态选项
export const statusOptions = [
  { label: "创建", value: "create" },
  { label: "运行中", value: "running" },
  { label: "废弃", value: "废弃" },
  { label: "更新", value: "更新" },
  { label: "新增", value: "新增" }
];

// 分页响应接口
export interface PaginationResponse<T> {
  total: number;
  page: number;
  size: number;
  data: T[];
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  msg?: string;
  data?: T;
}

// 筛选待入库经验的参数接口
export interface FilterExperienceWaitParams {
  type?: string;
  applyType?: string;
  status?: string;
  extractType?: string;
  keyword?: string;
  startDate?: string;
  endDate?: string;
  sourceId?: string;
  page?: number;
  size?: number;
}

// 创建自定义request对象，用于发送请求
const request = {
  get: async (url: string, params?: Record<string, any>) => {
    const queryParams = params ? new URLSearchParams(params).toString() : '';
    const fullUrl = queryParams ? `${url}?${queryParams}` : url;
    return fetchRequest(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  },
  post: async (url: string, data: any) => {
    return fetchRequest(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
  put: async (url: string, data: any) => {
    return fetchRequest(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
  delete: async (url: string, options?: { data?: any }) => {
    return fetchRequest(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: options?.data ? JSON.stringify(options.data) : undefined,
    });
  },
};


// 获取所有待入库经验（分页，支持筛选）
export const getExperiencesWait = async (params?: FilterExperienceWaitParams): Promise<ApiResponse<PaginationResponse<ExperienceWait>>> => {
  const queryParams = new URLSearchParams();
  
  // 分页参数
  if (params?.page) queryParams.append('page', params.page.toString());
  if (params?.size) queryParams.append('size', params.size.toString());
  
  // 筛选参数
  if (params?.type) queryParams.append('type', params.type);
  if (params?.applyType) queryParams.append('applyType', params.applyType);
  if (params?.status) queryParams.append('status', params.status);
  if (params?.extractType) queryParams.append('extractType', params.extractType);
  if (params?.keyword) queryParams.append('keyword', params.keyword);
  if (params?.sourceId) queryParams.append('source_id', params.sourceId);
  if (params?.startDate) queryParams.append('startDate', params.startDate);
  if (params?.endDate) queryParams.append('endDate', params.endDate);
  
  const url = `${BASE_URL}/api/experiences-wait${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
  return request.get(url);
};

// 删除待入库经验
export const deleteExperienceWait = async (id: string): Promise<ApiResponse<boolean>> => {
  return request.delete(`${BASE_URL}/api/experiences-wait/${id}`);
};

// 批量删除待入库经验
export const batchDeleteExperiencesWait = async (ids: string[]): Promise<ApiResponse<{ [key: string]: boolean }>> => {
  return request.delete(`${BASE_URL}/api/experiences-wait/batch`, { data: ids });
};

// 手动处理待入库经验
export const processExperienceWait = async (id: string): Promise<ApiResponse<any>> => {
  return request.post(`${BASE_URL}/api/experiences-wait/${id}/process`, {});
};

// 处理下一个待入库经验
export const processNextExperienceWait = async (): Promise<ApiResponse<any>> => {
  return request.post(`${BASE_URL}/api/experiences-wait/process-next`, {});
};

// 处理所有待入库经验
export const processAllExperiencesWait = async (): Promise<ApiResponse<any>> => {
  return request.post(`${BASE_URL}/api/experiences-wait/process-all`, {});
};

// 检查是否有未处理的经验
export const hasUnprocessedExperiences = async (): Promise<ApiResponse<{ hasUnprocessed: boolean; unprocessedCount: number }>> => {
  return request.get(`${BASE_URL}/api/experiences-wait/has-unprocessed`);
};

// 快速处理待入库经验（跳过模型判断）
export const quickProcessExperienceWait = async (id: string): Promise<ApiResponse<any>> => {
  return request.post(`${BASE_URL}/api/experiences-wait/${id}/quick-process`, {});
};

// 根据ID查询待入库经验
export const getExperienceWaitById = async (id: string): Promise<ApiResponse<ExperienceWait>> => {
  return request.get(`${BASE_URL}/api/experiences-wait/${id}`);
};

// 查询待入库经验的模型调用记录
export const getLLMCallRecords = async (id: string): Promise<ApiResponse<any>> => {
  return request.get(`${BASE_URL}/llm-call-records/experience-wait/${id}`);
};

// 经验更新相关类型定义
export interface ExperienceUpdate {
  id: string;
  experienceWaitId: string;
  targetExperienceId: string;
  originalExperience: ExperienceWait;
  updatedContent: string;
  updatedTitle: string;
  status: string;
  createTime: string;
  updateTime: string;
}

// 经验更新分页响应接口
export interface PendingUpdatesResponse {
  updates: ExperienceUpdate[];
  total: number;
  page: number;
  size: number;
}

// 获取待处理的经验更新
export const getPendingUpdates = async (params?: { page?: number; size?: number }): Promise<ApiResponse<PendingUpdatesResponse>> => {
  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.append('page', params.page.toString());
  if (params?.size) queryParams.append('size', params.size.toString());
  const url = `${BASE_URL}/api/experience-updates/pending${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
  return request.get(url);
};

// 同意经验更新
export const approveUpdate = async (id: string): Promise<ApiResponse<boolean>> => {
  return request.post(`${BASE_URL}/api/experience-updates/${id}/approve`, {});
};

// 拒绝经验更新
export const rejectUpdate = async (id: string): Promise<ApiResponse<boolean>> => {
  return request.post(`${BASE_URL}/api/experience-updates/${id}/reject`, {});
};