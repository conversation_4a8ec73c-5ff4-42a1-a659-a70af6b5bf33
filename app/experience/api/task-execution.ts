import { fetchRequest } from '../../api/request';
import { BASE_URL } from './experience';

// 任务执行相关类型定义
export interface ExecutionStep {
  stepName: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime?: string;
  endTime?: string;
  content?: string;
  errorMsg?: string;
  result?: string;
  pythonScriptName?: string;
  pythonScriptContent?: string;
  scriptBeforeBash?: string;
  environmentVariables?: Record<string, string>;
}

export interface TaskExecution {
  id?: string;
  sessionId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  executionSteps: ExecutionStep[];
  user?: string;
  createTime?: string;
  updateTime?: string;
}

// 分页响应接口
export interface PaginationResponse<T> {
  total: number;
  page: number;
  size: number;
  data: T[];
}

// API响应类型
export interface ApiResponse<T = unknown> {
  code: number;
  msg?: string;
  data?: T;
}

// 筛选任务执行的参数接口
export interface FilterTaskParams {
  status?: string;
  user?: string;
  sessionId?: string;
  tag?: string;
  page?: number;
  size?: number;
}

// 创建自定义request对象，用于发送请求
const request = {
  get: async (url: string, params?: Record<string, unknown>) => {
    const queryParams = params ? new URLSearchParams(
      Object.entries(params).filter(([_, value]) => value !== undefined && value !== null).map(([key, value]) => [key, String(value)])
    ).toString() : '';
    const fullUrl = queryParams ? `${url}?${queryParams}` : url;
    return fetchRequest(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  },
  post: async (url: string, data: unknown) => {
    return fetchRequest(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
  put: async (url: string, data: unknown) => {
    return fetchRequest(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
  delete: async (url: string, options?: { data?: unknown }) => {
    return fetchRequest(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: options?.data ? JSON.stringify(options.data) : undefined,
    });
  },
};


// 创建任务执行
export const createTaskExecution = async (data: Omit<TaskExecution, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<TaskExecution>> => {
  return request.post(`${BASE_URL}/api/task-executions`, data);
};

// 根据ID查询任务执行
export const getTaskExecutionById = async (id: string): Promise<ApiResponse<TaskExecution>> => {
  return request.get(`${BASE_URL}/api/task-executions/${id}`);
};

// 获取所有任务执行
export const getAllTaskExecutions = async (params?: { page?: number; size?: number }): Promise<ApiResponse<PaginationResponse<TaskExecution>>> => {
  return request.get(`${BASE_URL}/api/task-executions`, params);
};

// 根据筛选条件获取任务执行
export const filterTaskExecutions = async (params: FilterTaskParams): Promise<ApiResponse<PaginationResponse<TaskExecution>>> => {
  // 构建查询参数
  const queryParams = new URLSearchParams();
  
  if (params.status) queryParams.append('status', params.status);
  if (params.user) queryParams.append('user', params.user);
  if (params.sessionId) queryParams.append('sessionId', params.sessionId);
  if (params.tag) queryParams.append('tag', params.tag);
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.size) queryParams.append('size', params.size.toString());
  
  const url = `${BASE_URL}/api/task-executions/filter${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
  return request.get(url);
};

// 更新任务执行
export const updateTaskExecution = async (id: string, data: Partial<TaskExecution>): Promise<ApiResponse<boolean>> => {
  return request.put(`${BASE_URL}/api/task-executions/${id}`, data);
};

// 删除任务执行
export const deleteTaskExecution = async (id: string): Promise<ApiResponse<boolean>> => {
  return request.delete(`${BASE_URL}/api/task-executions/${id}`);
};

// 开始任务执行
export const startTaskExecution = async (id: string): Promise<ApiResponse<boolean>> => {
  return request.post(`${BASE_URL}/api/task-executions/${id}/start`, {});
};

// 重新开始任务执行
export const restartTaskExecution = async (id: string): Promise<ApiResponse<boolean>> => {
  return request.post(`${BASE_URL}/api/task-executions/${id}/restart`, {});
};

// 完成任务执行
export const completeTaskExecution = async (id: string): Promise<ApiResponse<boolean>> => {
  return request.post(`${BASE_URL}/api/task-executions/${id}/complete`,{});
};

// 标记任务执行失败
export const failTaskExecution = async (id: string, errorMsg?: string): Promise<ApiResponse<boolean>> => {
  const data = errorMsg ? { errorMsg } : {};
  return request.post(`${BASE_URL}/api/task-executions/${id}/fail`, data);
};

// 状态选项
export const statusOptions = [
  { label: '待处理', value: 'pending' },
  { label: '运行中', value: 'running' },
  { label: '已完成', value: 'completed' },
  { label: '失败', value: 'failed' }
];

export const stepName = [
  { label: '经验提取(BadCase)', value: 'exp_extra_badcase' },
  { label: '经验提取(Planner)', value: 'exp_extra_planner' },
  { label: '经验提取(Workflow)', value: 'exp_extra_workflow' },
  { label: '经验提取(Error)', value: 'exp_extra_error' },
  { label: '经验评价', value: 'exp_eval' },
  { label: '自定义ScriptStep', value: 'exp_custom_script' },
  { label: '经验上传进待入库', value: 'exp_submit' },
  { label: '发送飞书Lark完成通知', value: 'exp_send_lark' },
]

// 步骤状态选项
export const stepStatusOptions = [
  { label: '待处理', value: 'pending' },
  { label: '运行中', value: 'running' },
  { label: '已完成', value: 'completed' },
  { label: '失败', value: 'failed' }
];

// 获取状态标签颜色
export const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'orange';
    case 'running': return 'blue';
    case 'completed': return 'green';
    case 'failed': return 'red';
    default: return 'gray';
  }
};

// 获取状态文本
export const getStatusText = (status: string) => {
  const option = statusOptions.find(opt => opt.value === status);
  return option?.label || status;
};

// 获取步骤状态文本
export const getStepStatusText = (status: string) => {
  const option = stepStatusOptions.find(opt => opt.value === status);
  return option?.label || status;
};