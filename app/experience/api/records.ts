import { fetchRequest } from '../../api/request';
import { BASE_URL } from './experience';

// GenericRecord 接口
export interface GenericRecord {
  id?: string;
  session_id: string;
  type?: string;
  tag?: string;
  content: string;
  create_time?: string;
  update_time?: string;
}

// API 响应接口
export interface ApiResponse<T = any> {
  code: number;
  msg?: string;
  data?: T;
}

// 分页记录响应
export interface PaginatedRecordsResponse {
  records: GenericRecord[];
  total: number;
  page: number;
  size: number;
  total_pages: number;
}

// 请求辅助对象
const request = {
  get: async (url: string, params?: Record<string, any>) => {
    const queryParams = params ? new URLSearchParams(params).toString() : '';
    const fullUrl = queryParams ? `${url}?${queryParams}` : url;
    return fetchRequest(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  },
  post: async (url: string, data: any) => {
    return fetchRequest(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
};

/**
 * 创建新的通用记录
 * @param data 记录数据
 * @returns 
 */
export const createGenericRecord = async (data: Omit<GenericRecord, 'id' | 'create_time' | 'update_time'>): Promise<ApiResponse<GenericRecord>> => {
  return request.post(`${BASE_URL}/generic-records`, data);
};

/**
 * 根据ID查询记录
 * @param id 记录ID
 * @returns 
 */
export const getGenericRecordById = async (id: string): Promise<ApiResponse<GenericRecord>> => {
  return request.get(`${BASE_URL}/generic-records/${id}`);
};

/**
 * 根据会话ID查询所有记录
 * @param sessionId 会话ID
 * @returns 
 */
export const getRecordsBySessionId = async (sessionId: string): Promise<ApiResponse<GenericRecord[]>> => {
  return request.get(`${BASE_URL}/generic-records/session/${sessionId}`);
};

// 筛选记录参数
export interface FilterRecordsParams {
  session_id?: string;
  type?: string;
  tag?: string;
  content_keyword?: string;
  page?: number;
  size?: number;
}

/**
 * 根据筛选条件查询记录（分页）
 * @param params 筛选参数
 * @returns 
 */
export const filterGenericRecords = async (params: FilterRecordsParams): Promise<ApiResponse<PaginatedRecordsResponse>> => {
  const queryParams = new URLSearchParams();
  
  if (params.session_id) queryParams.append('session_id', params.session_id);
  if (params.type) queryParams.append('type', params.type);
  if (params.tag) queryParams.append('tag', params.tag);
  if (params.content_keyword) queryParams.append('content_keyword', params.content_keyword);
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.size) queryParams.append('size', params.size.toString());
  
  const queryString = queryParams.toString();
  const url = `${BASE_URL}/generic-records/filter${queryString ? '?' + queryString : ''}`;
  return request.get(url);
};