"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Form,
  Input,
  Button,
  Card,
  Grid,
  Select,
  Space
} from "@arco-design/web-react";
import {  IconSave, IconArrowLeft } from "@arco-design/web-react/icon";
import { toast } from "sonner";
import { PageHeader } from "@/app/components/PageHeader";
import { 
  createExperience, 
  updateExperience, 
  getExperienceById, 
  experienceTypeOptions, 
  applyTypeOptions,
  variantOptions
} from "../api/experience";
import type { Experience } from "../api/experience";
import { useStore } from "@nanostores/react";
import { userAtom } from "@/app/store/auth";

const { TextArea } = Input;

export default function ExperienceEditPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get("id");
  const isEdit = !!id;

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(false);
  const user = useStore(userAtom);

  // 加载现有经验数据
  useEffect(() => {
    if (isEdit) {
      setInitialLoading(true);
      getExperienceById(id!)
        .then((response) => {
          if (response.code === 200 && response.data) {
            const experience = response.data;
            form.setFieldsValue({
              vector_content: experience.vector_content,
              title: experience.title,
              type: experience.type,
              experience_content: experience.experience_content,
              source_id: experience.source_id,
              apply_type: experience.apply_type,
              variant: experience.variant,
              limit: experience.limit
            });
            console.log("set exp susccess");
          }
        })
        .catch((error) => {
          console.error("加载经验失败:", error);
          toast.error("加载经验失败");
        })
        .finally(() => {
          setInitialLoading(false);
        });
    }else{
       form.setFieldsValue({
            source_id: "人工专家经验",
            variant: variantOptions[1].value,
            limit: "default"
       });
    }
  }, [isEdit, id, form]);

  // 提交表单
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const experienceData: Omit<Experience, 'id' | 'create_time' | 'update_time'> = {
        vector_content: values.vector_content,
        title: values.title,
        type: values.type,
        experience_content: values.experience_content,
        source_id: isEdit ? values.source_id : "人工专家经验",
        apply_type: values.apply_type,
        variant: values.variant,
        limit: values.limit,
        publish_status: "pending",
        extract_type: user?.username || "manual"
      };

      let response;
      if (isEdit) {
        response = await updateExperience(id!, experienceData);
      } else {
        response = await createExperience(experienceData);
      }

      if (response.code === 200) {
        toast.success(isEdit ? "更新经验成功" : "创建经验成功");
        router.push(isEdit? "/experience/list": "/experience/wait");
      } else {
        throw new Error(response.msg || "操作失败");
      }
    } catch (error) {
      console.error("保存经验失败:", error);
      toast.error("保存经验失败");
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return <div className="p-4">加载中...</div>;
  }

  return (
    <div>
      <PageHeader
        title={isEdit ? "编辑经验" : "创建经验"}
        description={isEdit ? "编辑现有经验记录" : "创建新的经验记录"}
        extra={
          <Button
            icon={<IconArrowLeft />}
            onClick={() => router.push("/experience/list")}
          >
            返回列表
          </Button>
        }
      />

      <div className="p-6">
        <Form
          form={form}
          layout="vertical"
          onSubmit={handleSubmit}
          // initialValues={{
          //   publish_status: isEdit ? undefined : "pending",
          //   source_id: isEdit ? undefined : "人工专家经验",
          //   limit: isEdit ? undefined : "default"
          // }}
        >
          <Card title="经验信息" className="mb-6">
            <Grid.Row gutter={24}>
              <Grid.Col span={12}>
                <Form.Item
                  label="经验标题"
                  field="title"
                  rules={[{ required: true, message: "请输入经验标题" }]}
                >
                  <Input placeholder="请输入经验标题" />
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={12}>
                <Form.Item
                  label="经验类型"
                  field="type"
                  rules={[{ required: true, message: "请选择经验类型" }]}
                >
                  <Select placeholder="请选择经验类型" allowCreate>
                    {experienceTypeOptions.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Grid.Col>
            </Grid.Row>

            <Form.Item
              label="向量内容"
              field="vector_content"
              rules={[{ required: true, message: "请输入向量内容" }]}
            >
              <Input placeholder="请输入向量内容，用于搜索匹配，多个关键词可用 | 分隔" />
            </Form.Item>

            <Form.Item
              label="经验内容"
              field="experience_content"
              rules={[{ required: true, message: "请输入经验内容" }]}
            >
              <TextArea placeholder="请输入经验内容，支持Markdown格式" rows={15} />
            </Form.Item>
          </Card>

          <Card title="来源与应用信息" className="mb-6">
            <Grid.Row gutter={24}>
              <Grid.Col span={6}>
                <Form.Item
                  label="来源ID"
                  field="source_id"
                  required
                >
                  <Input value="人工专家经验" disabled />
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={6}>
                <Form.Item
                  label="应用类型"
                  field="apply_type"
                  rules={[{ required: true, message: "请选择应用类型" }]}
                >
                  <Select placeholder="请选择应用类型" allowCreate>
                    {applyTypeOptions.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={6}>
                <Form.Item
                  label="应用角色"
                  field="variant"
                  rules={[{ required: true, message: "请选择角色" }]}
                >
                  <Select placeholder="请选择角色">
                    {variantOptions.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={6}>
                <Form.Item
                  label="Limit 场景"
                  field="limit"
                  rules={[{ required: true, message: "请输入 Limit 场景" }, { type: 'string'}]}
                >
                  <Input placeholder="请输入 Limit 场景" type="string" />
                </Form.Item>
              </Grid.Col>
            </Grid.Row>
          </Card>

          <div className="text-center">
            <Space>
              <Button onClick={() => router.push("/experience/list")}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<IconSave />}
              >
                {isEdit ? "更新" : "创建"}
              </Button>
            </Space>
          </div>
        </Form>
      </div>
    </div>
  );
}