import { apiClient } from '@/app/api/request';
import { Message } from "../extract/components/Extraction/parse/types";
import { createGenericRecord, getRecordsBySessionId } from '../api/records';

interface IChatResponse {
  choices: {
    message: {
      content: string
    }
  }[]
}

export async function runEvaluationQuery(
  model: string,
  temperature: number,
  systemPrompt: string,
  trace: string,
  checkReason: string,
  sessionId: string,
  useCache: boolean,
  type: 'knowledge_evaluation' | 'experience_evaluation',
): Promise<{ ok: boolean, content: string }> {
  if (useCache) {
    const recordsRes = await getRecordsBySessionId(sessionId);
    if (recordsRes.data) {
      for (const record of recordsRes.data) {
        if (record.type === type) {
          return { ok: true, content: record.content };
        }
      }
    }
  }

  for (let i = 0; i < 3; i++) {
    try {
      const content = await ChatStreamRequest(model, temperature, systemPrompt, trace, checkReason);
      try {
        if (checkJsonValid(content)) {
          await createGenericRecord({
            session_id: sessionId,
            type: type,
            content,
          });
        } else {
          continue;
        }
      } catch (error) {
        console.error(`Error creating evaluation record:`, error);
      }
      return { ok: true, content };
    } catch (error) {
      console.error(`Error running evaluation query:`, error);
    }
  }
  return { ok: false, content: 'Error: Could not fetch result.' };
}

async function ChatStreamRequest(
  model: string,
  temperature: number,
  systemPrompt: string,
  trace: string,
  checkReason: string,
): Promise<string> {
  let finalQuery = trace;
  if (checkReason) {
    finalQuery = `<check>
${checkReason}
</check>
${trace}`;
  }

  const initialMessages: Message[] = [
    { role: 'system', content: systemPrompt, agent: 'default' },
    { role: 'user', content: finalQuery, agent: 'default' },
  ];
  const res = await apiClient.ChatStream({
    model,
    messages: initialMessages,
    temperature,
    max_tokens: 64000
  });
  return (res as IChatResponse)?.choices?.[0]?.message?.content ?? '';
}

function checkJsonValid(content: string): boolean {
  try {
    // 看看content是否包含合法的json
    let jsonToParse;
    const firstBraceIndex = content.indexOf('{');
    const lastBraceIndex = content.lastIndexOf('}');

    if (firstBraceIndex !== -1 && lastBraceIndex > firstBraceIndex) {
      jsonToParse = content.substring(firstBraceIndex, lastBraceIndex + 1);
    } else {
      jsonToParse = content
        .replace(/```json\s*/g, '')
        .replace(/```\s*/g, '')
        .trim();
    }
    JSON.parse(jsonToParse);
    return true;
  } catch (error) {
    return false;
  }
}