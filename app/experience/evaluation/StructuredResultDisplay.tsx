import React from 'react';
import { Card, Typography, Collapse, Tag, Space, Tabs } from '@arco-design/web-react';

const { Title, Text, Paragraph } = Typography;
const CollapseItem = Collapse.Item;

interface Experience {
    experience_title: string;
    id: string;
    content_summary: string;
    analysis_detail: string;
}

interface ExperienceEffectivenessAnalysis {
    negative_effect_experiences: Experience[];
    positive_effect_experiences: Experience[];
    ignored_experiences: Experience[];
    irrelevant_experiences: Experience[];
}

interface Recall {
    recall_scene: string;
    experience_effectiveness_analysis: ExperienceEffectivenessAnalysis;
}

interface Report {
    report_title: string;
    recalls: Recall[];
}

interface StructuredResultDisplayProps {
    result: string;
    sessionId: string;
    showResult: boolean;
    onExperienceIdClick?: (experienceId: string) => void;
}

const renderExperience = (exp: Experience, onExperienceIdClick?: (experienceId: string) => void) => {
    return (
        <Card
            key={exp.id}
            style={{
                marginBottom: '12px',
                borderColor: 'var(--border)',
                borderRadius: 'var(--radius-lg)',
                boxShadow: 'var(--shadow-sm)',
                transition: 'all 0.2s ease'
            }}
            bodyStyle={{
                padding: '16px 20px',
                backgroundColor: 'var(--surface)'
            }}
            hoverable
        >
            <Title
                heading={6}
                style={{
                    marginTop: 0,
                    marginBottom: '12px',
                    color: 'var(--text-primary)',
                    fontSize: 'var(--text-lg)',
                    fontWeight: 600
                }}
            >
                {exp.experience_title}
            </Title>

            <div style={{ display: 'grid', gap: '8px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{
                        fontSize: 'var(--text-sm)',
                        color: 'var(--text-secondary)',
                        fontWeight: 500,
                        minWidth: '80px'
                    }}>
                        经验ID:
                    </span>
                    <button
                        className="text-blue-500 hover:text-blue-700 underline bg-transparent border-none p-0 cursor-pointer"
                        onClick={() => onExperienceIdClick && onExperienceIdClick(exp.id)}
                    >
                        {exp.id}
                    </button>
                </div>

                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px' }}>
                    <span style={{
                        fontSize: 'var(--text-sm)',
                        color: 'var(--text-secondary)',
                        fontWeight: 500,
                        minWidth: '80px',
                        lineHeight: '1.4'
                    }}>
                        摘要:
                    </span>
                    <Text style={{
                        fontSize: 'var(--text-sm)',
                        color: 'var(--text-primary)',
                        lineHeight: '1.4',
                        margin: 0
                    }}>
                        {exp.content_summary}
                    </Text>
                </div>

                {exp.analysis_detail && (
                    <div style={{
                        marginTop: '12px',
                        padding: '12px',
                        backgroundColor: 'var(--primary-light)',
                        borderRadius: 'var(--radius-md)',
                        border: '1px solid var(--border)'
                    }}>
                        <span style={{
                            fontSize: 'var(--text-sm)',
                            color: 'var(--text-secondary)',
                            fontWeight: 600,
                            display: 'block',
                            marginBottom: '6px'
                        }}>
                            分析详情:
                        </span>
                        <Text style={{
                            fontSize: 'var(--text-sm)',
                            color: 'var(--text-primary)',
                            lineHeight: '1.5',
                            margin: 0
                        }}>
                            {exp.analysis_detail}
                        </Text>
                    </div>
                )}
            </div>
        </Card>
    );
};

export const StructuredResultDisplay = ({ result, sessionId, showResult, onExperienceIdClick }: StructuredResultDisplayProps) => {
    let data: Report;
    try {
        let jsonToParse;
        const firstBraceIndex = result.indexOf('{');
        const lastBraceIndex = result.lastIndexOf('}');

        if (firstBraceIndex !== -1 && lastBraceIndex > firstBraceIndex) {
            jsonToParse = result.substring(firstBraceIndex, lastBraceIndex + 1);
        } else {
            jsonToParse = result
                .replace(/```json\s*/g, '')
                .replace(/```\s*/g, '')
                .trim();
        }
        data = JSON.parse(jsonToParse);
    } catch (error) {
        return (
            <div className="bg-gray-100 p-6 rounded-lg mt-6 border border-gray-200">
                <p className="font-semibold text-gray-800 mb-3 text-lg">JSON 解析错误:</p>
                <pre className="whitespace-pre-wrap font-mono text-sm bg-white p-4 rounded border border-gray-300">
                    {result}
                </pre>
            </div>
        );
    }

    if (!data || !data.recalls || !Array.isArray(data.recalls)) {
        return (
            <div className="bg-gray-100 p-6 rounded-lg mt-6 border border-gray-200">
                <p className="font-semibold text-gray-800 mb-3 text-lg">结果:</p>
                <pre className="whitespace-pre-wrap font-mono text-sm bg-white p-4 rounded border border-gray-300">
                    {result}
                </pre>
            </div>
        );
    }

    const { report_title, recalls } = data;

    if (recalls.length === 0) {
        return (
            <div className="mt-6 p-6 bg-white rounded-lg border border-gray-200 shadow-sm">
                <Text style={{ color: 'var(--text-secondary)', margin: 0 }}>
                    未召回任何经验。查看会话:
                    <a
                        href={`https://aime.bytedance.net/chat/${sessionId}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                            color: 'var(--primary)',
                            textDecoration: 'none',
                            fontWeight: 500,
                            marginLeft: '8px'
                        }}
                    >
                        {report_title}
                    </a>
                </Text>
            </div>
        );
    } else {
        return (
            <div className="mt-6">
                <a
                    href={`https://aime.bytedance.net/chat/${sessionId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                        color: 'var(--primary)',
                        textDecoration: 'none',
                        fontSize: '18px',
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: '6px',
                        fontWeight: 500,
                        marginBottom: '15px'
                    }}
                >
                    <span>Case: {report_title}</span>
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6M15 3h6v6M10 14L21 3"
                            strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                </a>

                <Collapse
                    defaultActiveKey={showResult ? recalls.map((r, i) => i.toString()) : []}
                    style={{
                        borderRadius: 'var(--radius-lg)',
                        border: '1px solid var(--border)',
                        overflow: 'hidden'
                    }}
                    accordion={false}
                >
                    {recalls.map((recall, index) => (
                        <CollapseItem
                            name={index.toString()}
                            header={
                                <div style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '12px',
                                }}>
                                    <div style={{
                                        width: '32px',
                                        height: '32px',
                                        borderRadius: '8px',
                                        backgroundColor: 'var(--primary-light)',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: 'var(--primary)',
                                        fontSize: '14px',
                                        fontWeight: 'bold',
                                        flexShrink: 0
                                    }}>
                                        {index + 1}
                                    </div>
                                    <Title heading={6} style={{
                                        margin: 0,
                                        color: 'var(--text-primary)',
                                        fontWeight: 600
                                    }}>
                                        {recall.recall_scene}
                                    </Title>
                                    <Space size="small" style={{ marginLeft: 'auto' }}>
                                        {recall.experience_effectiveness_analysis?.positive_effect_experiences?.length > 0 && (
                                            <Tag
                                                color="green"
                                                size="small"
                                                style={{
                                                    fontWeight: 500,
                                                    fontSize: '12px'
                                                }}
                                            >
                                                正向: {recall.experience_effectiveness_analysis.positive_effect_experiences.length}
                                            </Tag>
                                        )}
                                        {recall.experience_effectiveness_analysis?.negative_effect_experiences?.length > 0 && (
                                            <Tag
                                                color="red"
                                                size="small"
                                                style={{
                                                    fontWeight: 500,
                                                    fontSize: '12px'
                                                }}
                                            >
                                                负向: {recall.experience_effectiveness_analysis.negative_effect_experiences.length}
                                            </Tag>
                                        )}
                                        {recall.experience_effectiveness_analysis?.ignored_experiences?.length > 0 && (
                                            <Tag
                                                color="blue"
                                                size="small"
                                                style={{
                                                    fontWeight: 500,
                                                    fontSize: '12px'
                                                }}
                                            >
                                                忽略: {recall.experience_effectiveness_analysis.ignored_experiences.length}
                                            </Tag>
                                        )}
                                        {recall.experience_effectiveness_analysis?.irrelevant_experiences?.length > 0 && (
                                            <Tag
                                                color="orange"
                                                size="small"
                                                style={{
                                                    fontWeight: 500,
                                                    fontSize: '12px'
                                                }}
                                            >
                                                无关: {recall.experience_effectiveness_analysis.irrelevant_experiences.length}
                                            </Tag>
                                        )}
                                    </Space>
                                </div>
                            }
                            key={index.toString()}
                            style={{
                                backgroundColor: 'var(--surface)',
                                borderBottom: '1px solid var(--border)'
                            }}
                        >
                            <div style={{ padding: '0px', backgroundColor: 'transparent' }}>
                                <Tabs
                                    defaultActiveTab='positive'
                                    type="rounded"
                                    size="mini"
                                    style={{ padding: '0px', backgroundColor: 'transparent' }}
                                >
                                    <Tabs.TabPane
                                        key="positive"
                                        title={
                                            <Space>
                                                <Tag
                                                    color="green"
                                                    size="small"
                                                    style={{
                                                        fontWeight: 600,
                                                        fontSize: 'var(--text-sm)'
                                                    }}
                                                >
                                                    正向影响
                                                </Tag>
                                                <span style={{
                                                    color: 'var(--text-secondary)',
                                                    fontSize: 'var(--text-sm)',
                                                    fontWeight: 500
                                                }}>
                                                    ({recall.experience_effectiveness_analysis?.positive_effect_experiences?.length ?? 0})
                                                </span>
                                            </Space>
                                        }>
                                        {(recall.experience_effectiveness_analysis?.positive_effect_experiences?.length ?? 0) > 0 ?
                                            recall.experience_effectiveness_analysis.positive_effect_experiences.map((exp) => renderExperience(exp, onExperienceIdClick)) :
                                            <div style={{
                                                padding: '32px',
                                                textAlign: 'center',
                                                color: 'var(--text-tertiary)',
                                                backgroundColor: 'var(--surface)',
                                                borderRadius: 'var(--radius-md)',
                                                border: '1px dashed var(--border)'
                                            }}>
                                                <Text>暂无正向影响经验</Text>
                                            </div>
                                        }
                                    </Tabs.TabPane>
                                    <Tabs.TabPane
                                        key="negative"
                                        title={
                                            <Space>
                                                <Tag
                                                    color="red"
                                                    size="small"
                                                    style={{
                                                        fontWeight: 600,
                                                        fontSize: 'var(--text-sm)'
                                                    }}
                                                >
                                                    负向影响
                                                </Tag>
                                                <span style={{
                                                    color: 'var(--text-secondary)',
                                                    fontSize: 'var(--text-sm)',
                                                    fontWeight: 500
                                                }}>
                                                    ({recall.experience_effectiveness_analysis?.negative_effect_experiences?.length ?? 0})
                                                </span>
                                            </Space>
                                        }>
                                        {(recall.experience_effectiveness_analysis?.negative_effect_experiences?.length ?? 0) > 0 ?
                                            recall.experience_effectiveness_analysis.negative_effect_experiences.map((exp) => renderExperience(exp, onExperienceIdClick)) :
                                            <div style={{
                                                padding: '32px',
                                                textAlign: 'center',
                                                color: 'var(--text-tertiary)',
                                                backgroundColor: 'var(--surface)',
                                                borderRadius: 'var(--radius-md)',
                                                border: '1px dashed var(--border)'
                                            }}>
                                                <Text>暂无负向影响经验</Text>
                                            </div>
                                        }
                                    </Tabs.TabPane>
                                    <Tabs.TabPane
                                        key="ignored"
                                        title={
                                            <Space>
                                                <Tag
                                                    color="blue"
                                                    size="small"
                                                    style={{
                                                        fontWeight: 600,
                                                        fontSize: 'var(--text-sm)'
                                                    }}
                                                >
                                                    被忽略
                                                </Tag>
                                                <span style={{
                                                    color: 'var(--text-secondary)',
                                                    fontSize: 'var(--text-sm)',
                                                    fontWeight: 500
                                                }}>
                                                    ({recall.experience_effectiveness_analysis?.ignored_experiences?.length ?? 0})
                                                </span>
                                            </Space>
                                        }>
                                        {(recall.experience_effectiveness_analysis?.ignored_experiences?.length ?? 0) > 0 ?
                                            recall.experience_effectiveness_analysis.ignored_experiences.map((exp) => renderExperience(exp, onExperienceIdClick)) :
                                            <div style={{
                                                padding: '32px',
                                                textAlign: 'center',
                                                color: 'var(--text-tertiary)',
                                                backgroundColor: 'var(--surface)',
                                                borderRadius: 'var(--radius-md)',
                                                border: '1px dashed var(--border)'
                                            }}>
                                                <Text>暂无被忽略经验</Text>
                                            </div>
                                        }
                                    </Tabs.TabPane>
                                    <Tabs.TabPane
                                        key="irrelevant"
                                        title={
                                            <Space>
                                                <Tag
                                                    color="orange"
                                                    size="small"
                                                    style={{
                                                        fontWeight: 600,
                                                        fontSize: 'var(--text-sm)'
                                                    }}
                                                >
                                                    无关召回
                                                </Tag>
                                                <span style={{
                                                    color: 'var(--text-secondary)',
                                                    fontSize: 'var(--text-sm)',
                                                    fontWeight: 500
                                                }}>
                                                    ({recall.experience_effectiveness_analysis?.irrelevant_experiences?.length ?? 0})
                                                </span>
                                            </Space>
                                        }>
                                        {(recall.experience_effectiveness_analysis?.irrelevant_experiences?.length ?? 0) > 0 ?
                                            recall.experience_effectiveness_analysis.irrelevant_experiences.map((exp) => renderExperience(exp, onExperienceIdClick)) :
                                            <div style={{
                                                padding: '32px',
                                                textAlign: 'center',
                                                color: 'var(--text-tertiary)',
                                                backgroundColor: 'var(--surface)',
                                                borderRadius: 'var(--radius-md)',
                                                border: '1px dashed var(--border)'
                                            }}>
                                                <Text>暂无冗余召回经验</Text>
                                            </div>
                                        }
                                    </Tabs.TabPane>
                                </Tabs>
                            </div>
                        </CollapseItem>
                    ))}
                </Collapse>
            </div>
        );
    }
};