"use client";
import { useState, useMemo, useEffect } from "react";
import { useSearchPara<PERSON>, useRouter, usePathname } from "next/navigation";
import { PageHeader } from "@/app/components/PageHeader";
import { Select, InputNumber } from "@arco-design/web-react";
import { apiClient } from "@/app/api/request";
import { useQuery } from "@tanstack/react-query";
import { Model } from "@/app/bam/aime/namespaces/trace";
import { uniq } from "lodash-es";
import { ExtractionTabs } from "@/app/experience/evaluation/index";

export default function EnhancePage() {
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const modelParam = searchParams.get('model');
  const temperatureParam = searchParams.get('temperature');

  const [model, setModel] = useState<string>(modelParam || "gemini-2.5-pro");
  const [temperature, setTemperature] = useState<number>(temperatureParam ? parseFloat(temperatureParam) : 0.5);
  const [activeTab, setActiveTab] = useState<string>(tabParam || "1");

  const router = useRouter();
  const pathname = usePathname();

  // 更新URL参数的函数
  const updateUrlParams = (params: { tab?: string; model?: string; temperature?: number }) => {
    const searchParamsObj = new URLSearchParams(searchParams.toString());

    if (params.tab !== undefined) searchParamsObj.set('tab', params.tab);
    if (params.model !== undefined) searchParamsObj.set('model', params.model);
    if (params.temperature !== undefined) searchParamsObj.set('temperature', params.temperature.toString());

    router.push(`${pathname}?${searchParamsObj.toString()}`, { scroll: false });
  };

  // 当URL参数变化时更新状态
  useEffect(() => {
    if (tabParam) setActiveTab(tabParam);
    if (modelParam) setModel(modelParam);
    if (temperatureParam) setTemperature(parseFloat(temperatureParam));
  }, [tabParam, modelParam, temperatureParam]);

  const { data } = useQuery({
    queryKey: ["list-models"],
    queryFn: () => apiClient.ListModels({}),
    staleTime: Infinity,
  });

  const models = useMemo(() => {
    const models: Model[] = [];
    for (const model of data?.models ?? []) {
      const exists = models?.find((item) => item.type === model?.type);
      if (!exists) {
        models.push(model);
      } else {
        exists?.models?.push(...(model?.models ?? []));
        exists.models = uniq(exists?.models);
      }
    }
    return models;
  }, [data]);

  return (
    <>
      <PageHeader
        title="经验效果评价"
      // description="通过模型 + Prompt 进行经验智能提取"
      />
      <div className="p-6">
        <div className="flex items-center gap-4 mb-4">
          <div className="w-3/4">
            <Select
              placeholder="Select a model"
              onChange={(value) => {
                setModel(value);
                updateUrlParams({ model: value });
              }}
              value={model}
              showSearch
            >
              {models.map((modelGroup, modelIndex) => (
                <Select.OptGroup
                  label={modelGroup.type}
                  key={`${modelGroup?.type}_${modelIndex}`}
                >
                  {modelGroup?.models?.map((item, index) => (
                    <Select.Option
                      key={`${modelGroup?.type}_${modelIndex}_${index}`}
                      value={item}
                    >
                      {item}
                    </Select.Option>
                  ))}
                </Select.OptGroup>
              ))}
            </Select>
          </div>
          <div className="w-1/4">
            <InputNumber
              prefix="Temperature:"
              step={0.1}
              value={temperature}
              onChange={(value) => {
                setTemperature(value);
                updateUrlParams({ temperature: value });
              }}
            />
          </div>
        </div>
        <ExtractionTabs
          model={model}
          temperature={temperature}
          activeTab={activeTab}
          onTabChange={(tab) => {
            setActiveTab(tab);
            updateUrlParams({ tab });
          }}
        />
      </div>
    </>
  );
}
