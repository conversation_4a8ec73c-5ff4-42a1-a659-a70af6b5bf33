import React, { useState, useEffect } from 'react';
import { Modal, Tag, Typography } from '@arco-design/web-react';

const { Text } = Typography;

interface ExperienceDetail {
  id: string;
  vector_content: string;
  title: string;
  type: string;
  experience_content: string;
  source_id: string;
  apply_type: string;
  publish_status: string;
  extract_type: string;
  create_time: string;
  update_time: string;
  point_id: string;
  variant: string;
  limit: string;
  ext: string;
}

interface ExperienceDetailModalProps {
  visible: boolean;
  onClose: () => void;
  experienceId: string;
}

export function ExperienceDetailModal({ visible, onClose, experienceId }: ExperienceDetailModalProps) {
  const [experienceDetail, setExperienceDetail] = useState<ExperienceDetail | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && experienceId) {
      fetchExperienceDetail();
    } else {
      setExperienceDetail(null);
    }
  }, [visible, experienceId]);

  const fetchExperienceDetail = async () => {
    setLoading(true);
    try {
      const response = await fetch(`https://meta-server.bytedance.net/api/experiences/filter?point_id=${experienceId}&page=1&size=100`);
      const data = await response.json();

      if (data.code === 200 && data.data && data.data.data && data.data.data.length > 0) {
        setExperienceDetail(data.data.data[0]);
      } else {
        console.error('Failed to fetch experience detail:', data.msg || 'Unknown error');
      }
    } catch (error) {
      console.error('Error fetching experience detail:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderSourceId = (sourceId: string, showLink = false) => {
    if (!sourceId) return '-';

    return showLink ? (
      <a
        href={`https://aime.bytedance.net/chat/${sourceId}`}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-500 hover:text-blue-700 underline"
      >
        {sourceId}
      </a>
    ) : (
      <span>{sourceId}</span>
    );
  };

  const publishStatusOptions = [
    { value: 'publish', label: '已发布' },
    { value: 'pending', label: '待审核' },
  ];

  const variantOptions = [
    { value: 'newbie', label: '小美' },
    { value: 'invited_expert', label: '大卫' },
  ];

  return (
    <Modal
      title="经验详情"
      visible={visible}
      onCancel={onClose}
      footer={null}
      style={{ width: '80%', maxWidth: 1200 }}
      confirmLoading={loading}
    >
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <span>加载中...</span>
        </div>
      ) : experienceDetail ? (
        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Text className="font-bold">标题：</Text>
              <Text>{experienceDetail.title}</Text>
            </div>
            <div className="flex items-center gap-2 mb-2">
              <Text className="font-bold">类型：</Text>
              <Tag color="blue">
                {experienceDetail.type}
              </Tag>
            </div>
            <div className="flex items-center gap-2 mb-2">
              <Text className="font-bold">应用类型：</Text>
              <Text>
                {experienceDetail.apply_type}
              </Text>
            </div>
            <div className="flex items-center gap-2 mb-2">
              <Text className="font-bold">发布状态：</Text>
              <Tag color={experienceDetail.publish_status === 'publish' ? 'green' : 'orange'}>
                {publishStatusOptions.find(opt => opt.value === experienceDetail.publish_status)?.label || experienceDetail.publish_status}
              </Tag>
            </div>
            <div className="flex items-center gap-2 mb-2">
              <Text className="font-bold">提取类型：</Text>
              <Text>
                {experienceDetail.extract_type}
              </Text>
            </div>
            <div className="flex items-center gap-2 mb-2">
              <Text className="font-bold">向量内容：</Text>
              <Text>{experienceDetail.vector_content}</Text>
            </div>
            <div className="flex items-center gap-2 mb-2">
              <Text className="font-bold">来源ID：</Text>
              {renderSourceId(experienceDetail.source_id, true)}
            </div>
            {experienceDetail.variant && (
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">Variant：</Text>
                <Text>{variantOptions.find(opt => opt.value === experienceDetail.variant)?.label || experienceDetail.variant}</Text>
              </div>
            )}
            {experienceDetail.limit && (
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">Limit：</Text>
                <Text>{experienceDetail.limit}</Text>
              </div>
            )}
            <div className="flex items-center gap-2 mb-2">
              <Text className="font-bold">创建时间：</Text>
              <Text>{experienceDetail.create_time ? new Date(experienceDetail.create_time).toLocaleString() : "-"}</Text>
            </div>
            <div className="flex items-center gap-2 mb-2">
              <Text className="font-bold">更新时间：</Text>
              <Text>{experienceDetail.update_time ? new Date(experienceDetail.update_time).toLocaleString() : "-"}</Text>
            </div>
          </div>

          <div>
            <Text className="font-bold text-lg">经验内容：</Text>
            <div className="bg-gray-50 p-4 rounded mt-2 whitespace-pre-wrap">
              {experienceDetail.experience_content}
            </div>
          </div>
          {experienceDetail.ext && (
            <div className="mb-4">
              <Text className="font-bold text-lg">提取原因：</Text>
              <div className="bg-gray-50 p-4 rounded mt-2 whitespace-pre-wrap">
                {experienceDetail.ext}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="flex justify-center items-center h-64">
          <span>未找到经验详情</span>
        </div>
      )}
    </Modal>
  );
}