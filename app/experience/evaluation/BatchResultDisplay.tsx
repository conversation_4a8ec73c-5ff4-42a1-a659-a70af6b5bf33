import React, { useState, useEffect } from 'react';
import { Table } from '@arco-design/web-react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Toolt<PERSON>, Legend, ResponsiveContainer } from 'recharts';

export interface AggregatedResult {
  id: string;
  title: string;
  positiveCount: number;
  positiveSessionIds: string[];
  negativeCount: number;
  negativeSessionIds: string[];
  ignoredCount: number;
  ignoredSessionIds: string[];
  irrelevantCount: number;
  irrelevantSessionIds: string[];
}

interface BatchResultDisplayProps {
  aggregatedResults: AggregatedResult[];
  onExperienceIdClick?: (experienceId: string) => void;
  isKnowledge?: boolean;
}

// 可复用的SessionLinks组件，用于显示可点击的sessionId链接
const SessionLinks = ({ sessionIds }: { sessionIds: string[] }) => {
  if (sessionIds.length === 0) {
    return <span>-</span>;
  }

  return (
    <div className="overflow-y-auto">
      {sessionIds.map((sessionId, index) => (
        <React.Fragment key={sessionId}>
          <a
            href={`https://aime.bytedance.net/chat/${sessionId}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:text-blue-700 underline mr-1"
          >
            {sessionId}
          </a>
          {index < sessionIds.length - 1 && <br />}
        </React.Fragment>
      ))}
    </div>
  );
};

export function BatchResultDisplay({ aggregatedResults, onExperienceIdClick, isKnowledge }: BatchResultDisplayProps) {
  // 计算总次数数据
  const chartData = React.useMemo(() => {
    const totals = {
      positiveCount: 0,
      negativeCount: 0,
      ignoredCount: 0,
      irrelevantCount: 0
    };

    aggregatedResults.forEach(result => {
      totals.positiveCount += result.positiveCount;
      totals.negativeCount += result.negativeCount;
      totals.ignoredCount += result.ignoredCount;
      totals.irrelevantCount += result.irrelevantCount;
    });

    return [
      { name: '正向', value: totals.positiveCount, color: '#52c41a' },
      { name: '负向', value: totals.negativeCount, color: '#ff4d4f' },
      { name: '忽略', value: totals.ignoredCount, color: '#1890ff' }, // 改为蓝色
      { name: '无关', value: totals.irrelevantCount, color: '#faad14' } // 改为黄色
    ];
  }, [aggregatedResults]);

  // 计算总召回次数
  const totalRecallCount = React.useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.value, 0);
  }, [chartData]);

  // 统计表格数据
  const statsTableData = React.useMemo(() => {
    return [
      { type: '召回总次数', count: totalRecallCount },
      { type: '正向次数', count: chartData[0]?.value || 0 },
      { type: '负向次数', count: chartData[1]?.value || 0 },
      { type: '忽略次数', count: chartData[2]?.value || 0 },
      { type: '无关次数', count: chartData[3]?.value || 0 }
    ];
  }, [chartData, totalRecallCount]);

  // 统计表格列定义
  const statsColumns = [
    {
      title: "类型",
      dataIndex: "type",
      width: 100,
    },
    {
      title: "次数",
      dataIndex: "count",
      width: 80,
    }
  ];

  const columns = [
    {
      title: "No.",
      dataIndex: "index",
      width: 60,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: isKnowledge ? "知识标题" : "经验标题",
      dataIndex: "title",
      width: 220,
    },
    {
      title: isKnowledge ? "知识ID" : "经验ID",
      dataIndex: "id",
      width: 220,
      render: (experienceId: string) => {
        if (isKnowledge) {
          return experienceId;
        } else {
          return (
            <button
              onClick={() => onExperienceIdClick && onExperienceIdClick(experienceId)}
              className="text-blue-500 hover:text-blue-700 underline bg-transparent border-none p-0 cursor-pointer"
            >
              {experienceId}
            </button>
          )
        }
      },
    },
    {
      title: "正向次数",
      dataIndex: "positiveCount",
      width: 90,
    },
    {
      title: "负向次数",
      dataIndex: "negativeCount",
      width: 90,
    },
    {
      title: "忽略次数",
      dataIndex: "ignoredCount",
      width: 90,
    },
    {
      title: "无关次数",
      dataIndex: "irrelevantCount",
      width: 90,
    },
    {
      title: "正向SessionId",
      dataIndex: "positiveSessionIds",
      width: 350,
      render: (sessionIds: string[]) => <SessionLinks sessionIds={sessionIds} />,
    },
    {
      title: "负向SessionId",
      dataIndex: "negativeSessionIds",
      width: 350,
      render: (sessionIds: string[]) => <SessionLinks sessionIds={sessionIds} />,
    },
    {
      title: "被忽略SessionId",
      dataIndex: "ignoredSessionIds",
      width: 350,
      render: (sessionIds: string[]) => <SessionLinks sessionIds={sessionIds} />,
    },
    {
      title: "无关召回SessionId",
      dataIndex: "irrelevantSessionIds",
      width: 350,
      render: (sessionIds: string[]) => <SessionLinks sessionIds={sessionIds} />,
    },
  ];

  return (
    <>
      <div className="mb-6 border p-4 rounded-lg shadow">
        <div className="flex justify-center items-center">
          {/* 左侧统计表格 */}
          <div className="w-1/4 pr-4">
            <Table
              columns={statsColumns}
              data={statsTableData}
              pagination={false}
              border
              size="small"
            />
          </div>

          {/* 右侧饼图 */}
          <div className="w-3/4 h-65">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percent }) => {
                    // 只显示大于5%的百分比，避免文字重叠
                    const percentage = percent ? percent * 100 : 0;
                    return percentage > 3 ? `${name}:${percentage.toFixed(1)}%` : '';
                  }}
                  isAnimationActive={true}
                  animationDuration={500}
                  animationEasing="ease-out"
                >
                  {chartData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={entry.color}
                      stroke="#fff"
                      strokeWidth={2}
                    />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value) => [`${value}`, '召回次数']}
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
                  }}
                />
                <Legend 
                  verticalAlign="bottom"
                  height={36}
                  iconType="circle"
                  iconSize={10}
                  formatter={(value) => (
                    <span style={{ color: '#333', fontSize: '12px' }}>{value}</span>
                  )}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      <div className="mb-6 border rounded-lg overflow-hidden">
        <Table
          columns={columns}
          data={aggregatedResults}
          rowKey="id"
          scroll={{ x: 1600, y: 600 }}
          pagination={false}
          border
          stripe
        />
      </div>
    </>
  );
}