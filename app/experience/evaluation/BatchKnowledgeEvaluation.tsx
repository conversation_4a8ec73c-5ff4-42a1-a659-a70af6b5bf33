import React, { useState, useEffect } from 'react';
import { Input, Button, Modal, Switch } from '@arco-design/web-react';
import { fetchTraceWithRetry } from '@/app/experience/extract/utils';
import { fetchSessionIdsByTaskId } from '@/app/experience/job/extract/ExtractExperiences';
import { toast } from 'sonner';
import { runEvaluationQuery } from './Evaluation';
import { AggregatedResult, BatchResultDisplay } from './BatchResultDisplay';
import { KnowledgeResultDisplay } from './KnowledgeResultDisplay';

let nextId = 0;
const generateId = () => `item-${nextId++}`;

interface BatchKnowledgeEvaluationProps {
  model: string;
  temperature: number;
}

interface EvaluationItem {
  id: string;
  sessionId: string;
  checkReason: string;
  result: string;
  isLoading: boolean;
  query: string;
}

const initialEvaluationItems: Omit<EvaluationItem, 'id' | 'result' | 'isLoading' | 'query'>[] = [
  { sessionId: '', checkReason: '' }
];

export function BatchKnowledgeEvaluation({ model, temperature }: BatchKnowledgeEvaluationProps) {
  const [items, setItems] = useState<EvaluationItem[]>(() =>
    initialEvaluationItems.map(item => ({
      ...item,
      id: generateId(),
      result: '',
      isLoading: false,
      query: '',
    }))
  );
  const [systemPrompt, setSystemPrompt] = useState('');
  const [isPromptExpanded, setIsPromptExpanded] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [jsonInput, setJsonInput] = useState('');
  const [showResultTable, setShowResultTable] = useState(false);
  const [aggregatedResults, setAggregatedResults] = useState<any[]>([]);
  const [isEvalTaskModalVisible, setIsEvalTaskModalVisible] = useState(false);
  const [evalTaskInput, setEvalTaskInput] = useState('');
  const [useSatisfactionAnalysis, setUseSatisfactionAnalysis] = useState(true);

  const [useCache, setUseCache] = useState(true);

  useEffect(() => {
    const fetchPrompt = async () => {
      try {
        const response = await fetch(`https://tosv-cn.byted.org/obj/ttclient-android/aime/knowledge_evaluation.md?_t=${Date.now()}`, {
          method: 'GET',
          cache: 'no-store'
        });
        const text = await response.text();
        setSystemPrompt(text);
      } catch (error) {
        console.error('Error fetching system prompt:', error);
      }
    };
    fetchPrompt();
  }, []);

  useEffect(() => {
    // 检查是否所有项目都已完成且不再加载
    const allCompleted = items.every(item => !item.isLoading);
    const hasResults = items.some(item => item.result);

    if (allCompleted && hasResults) {
      const aggregated = processResults(items);
      setAggregatedResults(aggregated);
      setShowResultTable(true);
    }
  }, [items]);

  const handleItemChange = (id: string, update: Partial<Omit<EvaluationItem, 'id'>>) => {
    setItems(prevItems =>
      prevItems.map(item => (item.id === id ? { ...item, ...update } : item))
    );
  };

  const handleAddItem = () => {
    setItems([...items, { id: generateId(), sessionId: '', checkReason: '', result: '', isLoading: false, query: '' }]);
  };

  const handleRemoveItem = (id: string) => {
    setItems(items.filter(item => item.id !== id));
  };

  const handleModalOk = () => {
    if (!jsonInput.trim()) {
      toast.error('请输入内容');
      return;
    }
    try {
      const parsedData = JSON.parse(jsonInput);
      if (Array.isArray(parsedData)) {
        const newItems: EvaluationItem[] = parsedData.map((item: any) => ({
          sessionId: item.sessionId || '',
          checkReason: item.checkReason || '',
          id: generateId(),
          result: '',
          isLoading: false,
          query: '',
        }));
        setItems(newItems);
        setIsModalVisible(false);
        setJsonInput('');
        toast.success(`成功导入 ${newItems.length} 个sessionId`);
      } else {
        toast.error('JSON数据格式不正确，根节点应该是一个数组');
      }
    } catch (error) {
      toast.error('JSON解析失败，请检查格式');
      console.error('Error parsing JSON:', error);
    }
  };

  const handleEvalTaskModalOk = async () => {
    if (!evalTaskInput.trim()) {
      toast.error('请输入评测任务ID或URL');
      return;
    }

    try {
      let taskId = evalTaskInput;
      if (taskId.includes("https://aime-auto-eval-fe.gf.bytedance.net/tasks/")) {
        taskId = taskId.split("/tasks/")[1].split("?")[0];
      }

      const reasons = await fetchSessionIdsByTaskId(taskId);

      if (reasons.length === 0) {
        toast.error('未找到相关的sessionIds');
        return;
      }

      const jsonData = reasons.map(reason => {
        if (reason.type === "satisfaction" && useSatisfactionAnalysis) {
          return {
            sessionId: reason.session_id,
            checkReason: `可用性分数：${reason.check_info}\n评价理由：${reason.check_reason}`,
          }
        } else {
          return {
            sessionId: reason.session_id,
            checkReason: '',
          }
        }
      });

      const newItems: EvaluationItem[] = jsonData.map((item: any) => ({
        sessionId: item.sessionId || '',
        checkReason: item.checkReason || '',
        id: generateId(),
        result: '',
        isLoading: false,
        query: '',
      }));

      setItems(newItems);
      setIsEvalTaskModalVisible(false);
      setEvalTaskInput('');

      toast.success(`成功导入 ${reasons.length} 个sessionId`);
    } catch (error) {
      toast.error('获取sessionIds失败，请检查评测任务ID');
      console.error('Error fetching sessionIds:', error);
    }
  };

  const processResults = (items: EvaluationItem[]) => {
    const resultMap: { [key: string]: AggregatedResult } = {};

    items.forEach(item => {
      if (!item.result || item.result === 'Error: Could not fetch result.') return;

      try {
        let jsonToParse;
        const firstBraceIndex = item.result.indexOf('{');
        const lastBraceIndex = item.result.lastIndexOf('}');

        if (firstBraceIndex !== -1 && lastBraceIndex > firstBraceIndex) {
          jsonToParse = item.result.substring(firstBraceIndex, lastBraceIndex + 1);
        } else {
          jsonToParse = item.result
            .replace(/```json\s*/g, '')
            .replace(/```\s*/g, '')
            .trim();
        }
        const resultData = JSON.parse(jsonToParse);
        if (!resultData.recalls) return;

        resultData.recalls.forEach((recall: any) => {
          if (recall.knowledge_effectiveness_analysis) {
            const analysis = recall.knowledge_effectiveness_analysis;

            // 处理正向作用知识
            analysis.positive_effect_knowledges?.forEach((exp: any) => {
              if (!resultMap[exp.id]) {
                resultMap[exp.id] = {
                  id: exp.id,
                  title: exp.knowledge_title,
                  positiveCount: 0,
                  positiveSessionIds: [],
                  negativeCount: 0,
                  negativeSessionIds: [],
                  ignoredCount: 0,
                  ignoredSessionIds: [],
                  irrelevantCount: 0,
                  irrelevantSessionIds: []
                };
              }
              resultMap[exp.id].positiveCount++;
              if (!resultMap[exp.id].positiveSessionIds.includes(item.sessionId)) {
                resultMap[exp.id].positiveSessionIds.push(item.sessionId);
              }
            });

            // 处理负向作用知识
            analysis.negative_effect_knowledges?.forEach((exp: any) => {
              if (!resultMap[exp.id]) {
                resultMap[exp.id] = {
                  id: exp.id,
                  title: exp.knowledge_title,
                  positiveCount: 0,
                  positiveSessionIds: [],
                  negativeCount: 0,
                  negativeSessionIds: [],
                  ignoredCount: 0,
                  ignoredSessionIds: [],
                  irrelevantCount: 0,
                  irrelevantSessionIds: []
                };
              }
              resultMap[exp.id].negativeCount++;
              if (!resultMap[exp.id].negativeSessionIds.includes(item.sessionId)) {
                resultMap[exp.id].negativeSessionIds.push(item.sessionId);
              }
            });

            // 处理被忽略知识
            analysis.ignored_knowledges?.forEach((exp: any) => {
              if (!resultMap[exp.id]) {
                resultMap[exp.id] = {
                  id: exp.id,
                  title: exp.knowledge_title,
                  positiveCount: 0,
                  positiveSessionIds: [],
                  negativeCount: 0,
                  negativeSessionIds: [],
                  ignoredCount: 0,
                  ignoredSessionIds: [],
                  irrelevantCount: 0,
                  irrelevantSessionIds: []
                };
              }
              resultMap[exp.id].ignoredCount++;
              if (!resultMap[exp.id].ignoredSessionIds.includes(item.sessionId)) {
                resultMap[exp.id].ignoredSessionIds.push(item.sessionId);
              }
            });

            // 处理无关召回知识
            analysis.irrelevant_knowledges?.forEach((exp: any) => {
              if (!resultMap[exp.id]) {
                resultMap[exp.id] = {
                  id: exp.id,
                  title: exp.knowledge_title,
                  positiveCount: 0,
                  positiveSessionIds: [],
                  negativeCount: 0,
                  negativeSessionIds: [],
                  ignoredCount: 0,
                  ignoredSessionIds: [],
                  irrelevantCount: 0,
                  irrelevantSessionIds: []
                };
              }
              resultMap[exp.id].irrelevantCount++;
              if (!resultMap[exp.id].irrelevantSessionIds.includes(item.sessionId)) {
                resultMap[exp.id].irrelevantSessionIds.push(item.sessionId);
              }
            });
          }
        });
      } catch (error) {
        console.error('Error parsing result JSON:', error);
      }
    });

    return Object.values(resultMap);
  };

  const handleRunAll = async () => {
    if (!model) return;
    setShowResultTable(false);

    const runSingle = async (item: EvaluationItem) => {
      if (!item.sessionId) return;

      handleItemChange(item.id, { isLoading: true, result: '' });

      let queryToUse = item.query;
      if (!queryToUse) {
        try {
          const res = await fetchTraceWithRetry(item.sessionId);
          //only check trace
          const traceData = JSON.stringify(res, null);
          queryToUse = `<trace>
${traceData}
</trace>`;
          handleItemChange(item.id, { query: queryToUse });
        } catch (e) {
          console.error(e);
          handleItemChange(item.id, { result: `获取 trace 失败: ${(e as Error).message}`, isLoading: false });
          return;
        }
      }

      const res = await runEvaluationQuery(
        model,
        temperature,
        systemPrompt,
        queryToUse,
        item.checkReason,
        item.sessionId,
        useCache,
        'knowledge_evaluation',
      );
      handleItemChange(item.id, { result: res.content, isLoading: false });
    }

    const promises = items.map(item => runSingle(item));
    await Promise.all(promises);

    // 聚合结果并显示表格
    const aggregated = processResults(items);
    setAggregatedResults(aggregated);
    setShowResultTable(true);
  };

  return (
    <div>
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold">System Prompt</h2>
          <div className="flex gap-2">
            <Button onClick={() => setIsEvalTaskModalVisible(true)}>从评测任务导入</Button>
            <Button onClick={() => setIsModalVisible(true)}>从 JSON 导入</Button>
          </div>
        </div>
        <textarea
          className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
          value={systemPrompt}
          onChange={(e) => setSystemPrompt(e.target.value)}
          rows={isPromptExpanded ? Math.max(10, systemPrompt.split('\n').length) : 3}
        />
        <button
          className="text-sm text-blue-500 mt-1"
          onClick={() => setIsPromptExpanded(!isPromptExpanded)}
        >
          {isPromptExpanded ? '收起' : '展开'}
        </button>
      </div>

      <div className="flex items-center gap-4 mb-4">
        <Button type="primary" onClick={handleAddItem}>
          添加一项
        </Button>
        <Button type="primary" status="success" onClick={handleRunAll} loading={items.some(i => i.isLoading)}>
          全部运行
        </Button>
        <Switch checked={useCache} onChange={setUseCache} />
        <label onClick={() => setUseCache(v => !v)} className="cursor-pointer select-none">
          是否使用缓存
        </label>
      </div>

      {showResultTable && aggregatedResults.length > 0 && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-semibold">评测结果</h2>
          </div>
          <BatchResultDisplay aggregatedResults={aggregatedResults} isKnowledge={true} />
        </div>
      )}

      {items.map((item, index) => (
        <div key={item.id} className="mb-4 border p-4 rounded-md">
          <div className="flex items-center gap-4 mb-4">
            <div className="font-semibold">No.{index + 1}</div>
            <Input
              placeholder="Enter Session ID"
              value={item.sessionId}
              onChange={value => handleItemChange(item.id, { sessionId: value })}
              className="w-1/2"
            />
            {items.length > 1 && (
              <Button status="danger" onClick={() => handleRemoveItem(item.id)}>
                删除
              </Button>
            )}
          </div>
          <div className="mb-4">
            <p className="font-semibold">{'<check> 原因:'}</p>
            <Input.TextArea
              value={item.checkReason}
              onChange={value => handleItemChange(item.id, { checkReason: value })}
              rows={3}
            />
          </div>
          {item.result && (
            <KnowledgeResultDisplay result={item.result} sessionId={item.sessionId} showResult={false} />
          )}
          {item.isLoading && (
            <div className="p-4 rounded-md mt-2">
              <p className="font-semibold">评价中...</p>
            </div>
          )}
        </div>
      ))}

      <Modal
        title="从评测任务导入"
        visible={isEvalTaskModalVisible}
        onOk={handleEvalTaskModalOk}
        onCancel={() => {
          setIsEvalTaskModalVisible(false);
          setEvalTaskInput('');
        }}
        autoFocus={false}
        focusLock={true}
      >
        <Input.TextArea
          placeholder={`请输入评测任务ID或完整URL，例如：
https://aime-auto-eval-fe.gf.bytedance.net/tasks/68a7df36be677e5e4f85895f
或
68a7df36be677e5e4f85895f`}
          value={evalTaskInput}
          onChange={setEvalTaskInput}
          rows={6}
          style={{ height: 120 }}
        />
        <div className="flex items-center gap-2 mt-4">
          <Switch checked={useSatisfactionAnalysis} onChange={setUseSatisfactionAnalysis} />
          <label onClick={() => setUseSatisfactionAnalysis(v => !v)} className="cursor-pointer select-none">
            是否使用可用性分析
          </label>
        </div>
      </Modal>

      <Modal
        title="从JSON导入"
        visible={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setIsModalVisible(false);
          setJsonInput('');
        }}
        autoFocus={false}
        focusLock={true}
      >
        <Input.TextArea
          placeholder={`[
  {
    "sessionId": "xxxx",
    "checkReason": "xxxx"
  }
]`}
          value={jsonInput}
          onChange={setJsonInput}
          rows={15}
          style={{ height: 400 }}
        />
      </Modal>

    </div>
  );
}