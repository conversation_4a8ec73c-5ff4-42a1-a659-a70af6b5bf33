import React from 'react';
import { Tabs } from '@arco-design/web-react';
import { ExperienceEvaluation } from "@/app/experience/evaluation/ExperienceEvaluation";
import { BatchExperienceEvaluation } from "@/app/experience/evaluation/BatchExperienceEvaluation";
import { BatchKnowledgeEvaluation } from './BatchKnowledgeEvaluation';

const TabPane = Tabs.TabPane;

interface ExtractionTabsProps {
  model: string;
  temperature: number;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export function ExtractionTabs({ model, temperature, activeTab, onTabChange }: ExtractionTabsProps) {
  return (
    <Tabs activeTab={activeTab} onChange={onTabChange}>
      <TabPane key="1" title="批量经验评价">
        <BatchExperienceEvaluation model={model} temperature={temperature} />
      </TabPane>
      <TabPane key="2" title="批量知识评价">
        <BatchKnowledgeEvaluation model={model} temperature={temperature} />
      </TabPane>
      <TabPane key="3" title="单个效果评价">
        <ExperienceEvaluation model={model} temperature={temperature} />
      </TabPane>
    </Tabs>
  );
}