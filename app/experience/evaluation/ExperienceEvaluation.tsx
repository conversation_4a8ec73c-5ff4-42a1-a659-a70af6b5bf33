import React, { useState, useEffect, useRef } from 'react';
import { Input, Button, Switch, Space, Radio } from '@arco-design/web-react';
import { apiClient } from '@/app/api/request';
import { processAgentTrace } from "@/app/experience/extract/compress";
import Editor from "@monaco-editor/react";
import { Message } from "@/app/experience/extract/components/Extraction/parse/types";
import { fetchTraceWithRetry } from "@/app/experience/extract/utils";
import { StructuredResultDisplay } from './StructuredResultDisplay';
import { runEvaluationQuery } from './Evaluation';
import { ExperienceDetailModal } from './ExperienceDetailModal';
import { KnowledgeResultDisplay } from './KnowledgeResultDisplay';

interface SingleExtractionProps {
  model: string;
  temperature: number;
}

interface IChatResponse {
  choices: {
    message: {
      content: string
    }
  }[]
}

export function ExperienceEvaluation({ model, temperature }: SingleExtractionProps) {
  const [sessionId, setSessionId] = useState('');
  const [query, setQuery] = useState('');
  const [result, setResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [isFetchingTrace, setIsFetchingTrace] = useState(false);
  const [systemPrompt, setSystemPrompt] = useState('');
  const [checkContent, setCheckContent] = useState('');
  const [isPromptExpanded, setIsPromptExpanded] = useState(false);
  const [isContinueQuestion, setIsContinueQuestion] = useState(false);
  const isProgrammaticChange = useRef(false);
  const [isSingleRoundOnly, setIsSingleRoundOnly] = useState(false);
  const [isCompressionEnabled, setIsCompressionEnabled] = useState(false);
  const [useCache, setUseCache] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [followUpQuery, setFollowUpQuery] = useState('');
  const [isExperienceDetailModalVisible, setIsExperienceDetailModalVisible] = useState(false);
  const [selectedExperienceId, setSelectedExperienceId] = useState('');
  const [evaluationType, setEvaluationType] = useState<'experience' | 'knowledge'>('experience');

  useEffect(() => {
    if (isProgrammaticChange.current) {
      isProgrammaticChange.current = false;
      return;
    }
  }, [query]);

  useEffect(() => {
    fetchPrompt(evaluationType);
  }, [evaluationType]);

  const fetchPrompt = async (type: 'knowledge' | 'experience') => {
    try {
      const response = await fetch(`https://tosv-cn.byted.org/obj/ttclient-android/aime/${type === 'knowledge' ? 'knowledge' : 'exp'}_evaluation.md?_t=${Date.now()}`, {
        method: 'GET',
        cache: 'no-store'
      });
      const text = await response.text();
      setSystemPrompt(text);
    } catch (error) {
      console.error('Error fetching system prompt:', error);
    }
  };

  const handleFetchTrace = async () => {
    if (!sessionId) {
      return null;
    }
    setIsFetchingTrace(true);
    try {
      let res = await fetchTraceWithRetry(sessionId);
      if (isCompressionEnabled) {
        res = processAgentTrace(res);
      }
      if (isSingleRoundOnly && res.data) {
        let userMessageCount = 0;
        const secondUserMessageIndex = res.data.findIndex((item: any) => {
          if (item.UserMessage) {
            userMessageCount++;
          }
          return userMessageCount === 2;
        });

        if (secondUserMessageIndex !== -1) {
          res.data = res.data.slice(0, secondUserMessageIndex);
        }
      }
      const traceData = JSON.stringify(res, null);
      const newQuery = `<trace>
${traceData}
</trace>`;
      setQuery(newQuery);
      setIsFetchingTrace(false);
      return newQuery;
    } catch (e) {
      console.error(e);
      setQuery(`获取 trace 失败: ${(e as Error).message}`);
      setIsFetchingTrace(false);
      return null;
    }
  };

  const handleRun = async () => {
    if (isLoading || !model) return;
    const queryFromUI = query || await handleFetchTrace();
    if (!queryFromUI) {
      return;
    }
    setIsLoading(true);
    setResult('');

    let rawQuery = queryFromUI;
    if (queryFromUI.startsWith('<check>')) {
      const checkEndIndex = queryFromUI.indexOf('</check>');
      if (checkEndIndex !== -1) {
        rawQuery = queryFromUI.substring(checkEndIndex + '</check>'.length).trim();
      }
    }

    let finalQuery = rawQuery;
    const combinedCheckContent = checkContent;

    if (combinedCheckContent) {
      finalQuery = `<check>
${combinedCheckContent}
</check>
${rawQuery}`;
    }

    if (query !== finalQuery) {
      isProgrammaticChange.current = true;
      setQuery(finalQuery);
    }

    setLoadingMessage('经验分析中...');

    const initialMessages: Message[] = [
      { role: 'system', content: systemPrompt, agent: 'default' },
      { role: 'user', content: finalQuery, agent: 'default' },
    ];

    const res = await runEvaluationQuery(
      model,
      temperature,
      systemPrompt,
      rawQuery,
      combinedCheckContent,
      sessionId,
      useCache,
      `${evaluationType}_evaluation`
    );
    setResult(res.content);
    if (res.ok) {
      setMessages([...initialMessages, { role: 'assistant', content: res.content, agent: 'default' }]);
    }
    setIsLoading(false);
    setLoadingMessage('');
  }

  const handleFollowUp = async () => {
    if (isLoading || !model || !followUpQuery) return;
    setIsLoading(true);
    setLoadingMessage('多轮对话中...');

    const newMessages: Message[] = [...messages, { role: 'user', content: followUpQuery, agent: 'default' }];
    setMessages(newMessages);
    setFollowUpQuery('');

    try {
      const res = await apiClient.ChatStream({
        model,
        messages: newMessages,
        temperature,
        max_tokens: 64000
      });
      const content = (res as IChatResponse)?.choices?.[0]?.message?.content ?? '';
      setResult(content);
      setMessages([...newMessages, { role: 'assistant', content, agent: 'default' }]);
    } catch (error) {
      console.error(`Error running follow-up query:`, error);
      setResult('Error: Could not fetch result.');
    }
    setIsLoading(false);
    setLoadingMessage('');
  };

  const handleExperienceIdClick = (experienceId: string) => {
    setSelectedExperienceId(experienceId);
    setIsExperienceDetailModalVisible(true);
  };

  return (
    <div>
      <div className="mb-4">
        <div className="flex items-center gap-4 mb-2">
          <span className="text-sm font-medium">System Prompt 评估类型:</span>
          <Radio.Group
            value={evaluationType}
            onChange={setEvaluationType}
            type="button"
          >
            <Radio value="experience">经验</Radio>
            <Radio value="knowledge">知识</Radio>
          </Radio.Group>
        </div>
        <textarea
          className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
          value={systemPrompt}
          onChange={(e) => setSystemPrompt(e.target.value)}
          rows={isPromptExpanded ? Math.max(10, systemPrompt.split('\n').length) : 3}
        />
        <button
          className="text-sm text-blue-500 mt-1"
          onClick={() => setIsPromptExpanded(!isPromptExpanded)}
        >
          {isPromptExpanded ? '收起' : '展开'}
        </button>
      </div>
      <div className="flex items-center gap-4 mb-4">
        <Input
          placeholder="Enter Session ID"
          value={sessionId}
          onChange={setSessionId}
          className="w-1/2"
        />
        <Button type="primary" onClick={handleFetchTrace} loading={isFetchingTrace}>
          {isFetchingTrace ? '拉取中...' : '拉取 trace'}
        </Button>
      </div>
      <div className="mb-4">
        <Input.TextArea
          value={checkContent}
          placeholder='可以输入任务不符合预期的地方，以供模型检查是否是经验导致的'
          onChange={setCheckContent}
          rows={1}
        />
      </div>
      <div className="mb-4">
        <p className="font-semibold">Query:</p>
        <div style={{ border: '1px solid var(--color-border-2)', borderRadius: 'var(--border-radius-medium)' }}>
          <Editor
            height="240px" // 对应原来 rows={10} 的大致高度
            language="plaintext"
            value={query}
            onChange={(value) => setQuery(value || '')}
            options={{
              wordWrap: 'on',
              minimap: { enabled: true },
              scrollBeyondLastLine: false,
              automaticLayout: true,
              unusualLineTerminators: 'off',
              unicodeHighlight: {
                ambiguousCharacters: false,
              },
            }}
          />
        </div>
      </div>
      <div className="flex items-center gap-2 mb-4">
        <Switch checked={isSingleRoundOnly} onChange={setIsSingleRoundOnly} />
        <label onClick={() => setIsSingleRoundOnly(v => !v)} className="cursor-pointer select-none">
          是否只提取第一轮对话
        </label>
        <Switch checked={isCompressionEnabled} onChange={setIsCompressionEnabled} />
        <label onClick={() => setIsCompressionEnabled(v => !v)} className="cursor-pointer select-none">
          是否开启上下文压缩
        </label>
        <Switch checked={useCache} onChange={setUseCache} />
        <label onClick={() => setUseCache(v => !v)} className="cursor-pointer select-none">
          是否使用缓存
        </label>
      </div>
      <Space size='large'>
        <Button type="primary" onClick={handleRun} loading={isLoading} className="mb-4">
          {isLoading ? loadingMessage : 'Run Extraction'}
        </Button>
        {
          messages.length > 0 && (<Button type="primary" onClick={() => setIsContinueQuestion(!isContinueQuestion)}>
            二次追问 {isContinueQuestion ? '收起' : '展开'}
          </Button>)
        }
      </Space>
      {messages.length > 0 && (
        <div className="mt-4">
          {
            isContinueQuestion ? <div>
              <div className="bg-gray-100 p-4 rounded-md w-full" style={{ maxHeight: '400px', overflowY: 'auto' }}>
                {messages.map((msg, index) => {
                  if (msg.role === 'system') return null;
                  if (msg.role == 'user' && index == 1) {
                    return (
                      <div key={index} className={`mb-2 p-2 rounded-md ${msg.role === 'user' ? 'bg-blue-100' : 'bg-gray-200'}`}>
                        <p className="font-semibold capitalize">{msg.role}</p>
                        <pre className="whitespace-pre-wrap font-mono text-sm">{"check/trace 内容省略...."}</pre>
                      </div>
                    )
                  }
                  return (
                    <div key={index} className={`mb-2 p-2 rounded-md ${msg.role === 'user' ? 'bg-blue-100' : 'bg-gray-200'}`}>
                      <p className="font-semibold capitalize">{msg.role}</p>
                      <pre className="whitespace-pre-wrap font-mono text-sm">{msg.content}</pre>
                    </div>
                  )
                })}
              </div>
              <div className="mt-4 flex items-center gap-2">
                <Input.TextArea
                  value={followUpQuery}
                  onChange={setFollowUpQuery}
                  placeholder="二次提问，请输入你的问题..."
                  rows={2}
                  style={{ flex: 1 }}
                />
                <Button type="primary" onClick={handleFollowUp} loading={isLoading}>
                  {isLoading ? '发送中...' : '发送'}
                </Button>
              </div>
            </div> : <></>
          }
        </div>
      )}

      {result && evaluationType === 'experience' && (
        <StructuredResultDisplay
          result={result}
          sessionId={sessionId}
          showResult={true}
          onExperienceIdClick={handleExperienceIdClick}
        />
      )}

      {result && evaluationType === 'knowledge' && (
        <KnowledgeResultDisplay
          result={result}
          sessionId={sessionId}
          showResult={true}
        />
      )}

      <ExperienceDetailModal
        visible={isExperienceDetailModalVisible}
        onClose={() => setIsExperienceDetailModalVisible(false)}
        experienceId={selectedExperienceId}
      />
    </div>
  );
}