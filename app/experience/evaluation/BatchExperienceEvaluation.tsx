import React, { useState, useEffect } from 'react';
import { Input, Button, Modal, Message, Table, Tag, Radio, Switch } from '@arco-design/web-react';
import { fetchTraceWithRetry } from '@/app/experience/extract/utils';
import { fetchSessionIdsByTaskId } from '@/app/experience/job/extract/ExtractExperiences';
import { toast } from 'sonner';
import { StructuredResultDisplay } from './StructuredResultDisplay';
import { runEvaluationQuery } from './Evaluation';
import { AggregatedResult, BatchResultDisplay } from './BatchResultDisplay';
import { getCurrentUser } from '@/app/api/auth';
import { ExperienceDetailModal } from './ExperienceDetailModal';

let nextId = 0;
const generateId = () => `item-${nextId++}`;

interface BatchExperienceEvaluationProps {
  model: string;
  temperature: number;
}

interface EvaluationItem {
  id: string;
  sessionId: string;
  checkReason: string;
  result: string;
  isLoading: boolean;
  query: string;
}

interface IChatResponse {
  choices: {
    message: {
      content: string
    }
  }[]
}

interface UnrecalledExperience {
  title: string;
  point_id: string;
}

const initialEvaluationItems: Omit<EvaluationItem, 'id' | 'result' | 'isLoading' | 'query'>[] = [
  { sessionId: '', checkReason: '' }
];

export function BatchExperienceEvaluation({ model, temperature }: BatchExperienceEvaluationProps) {
  const [items, setItems] = useState<EvaluationItem[]>(() =>
    initialEvaluationItems.map(item => ({
      ...item,
      id: generateId(),
      result: '',
      isLoading: false,
      query: '',
    }))
  );
  const [systemPrompt, setSystemPrompt] = useState('');
  const [isPromptExpanded, setIsPromptExpanded] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [jsonInput, setJsonInput] = useState('');
  const [showResultTable, setShowResultTable] = useState(false);
  const [aggregatedResults, setAggregatedResults] = useState<any[]>([]);
  const [isEvalTaskModalVisible, setIsEvalTaskModalVisible] = useState(false);
  const [evalTaskInput, setEvalTaskInput] = useState('');
  const [useSatisfactionAnalysis, setUseSatisfactionAnalysis] = useState(true);

  const [isVariantModalVisible, setIsVariantModalVisible] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState('newbie');
  const [usernameInput, setUsernameInput] = useState('');
  const [unrecalledExperiences, setUnrecalledExperiences] = useState<UnrecalledExperience[]>([]);
  const [isLoadingUnrecalled, setIsLoadingUnrecalled] = useState(false);
  const [isExperienceDetailModalVisible, setIsExperienceDetailModalVisible] = useState(false);
  const [selectedExperienceId, setSelectedExperienceId] = useState('');

  const [useCache, setUseCache] = useState(true);

  useEffect(() => {
    const fetchPrompt = async () => {
      try {
        const response = await fetch(`https://tosv-cn.byted.org/obj/ttclient-android/aime/exp_evaluation.md?_t=${Date.now()}`, {
          method: 'GET',
          cache: 'no-store'
        });
        const text = await response.text();
        setSystemPrompt(text);
      } catch (error) {
        console.error('Error fetching system prompt:', error);
      }
    };
    fetchPrompt();
  }, []);

  useEffect(() => {
    // 检查是否所有项目都已完成且不再加载
    const allCompleted = items.every(item => !item.isLoading);
    const hasResults = items.some(item => item.result);

    if (allCompleted && hasResults) {
      const aggregated = processResults(items);
      setAggregatedResults(aggregated);
      setShowResultTable(true);
    }
  }, [items]);

  const handleItemChange = (id: string, update: Partial<Omit<EvaluationItem, 'id'>>) => {
    setItems(prevItems =>
      prevItems.map(item => (item.id === id ? { ...item, ...update } : item))
    );
  };

  const handleAddItem = () => {
    setItems([...items, { id: generateId(), sessionId: '', checkReason: '', result: '', isLoading: false, query: '' }]);
  };

  const handleRemoveItem = (id: string) => {
    setItems(items.filter(item => item.id !== id));
  };

  const handleModalOk = () => {
    if (!jsonInput.trim()) {
      toast.error('请输入内容');
      return;
    }
    try {
      const parsedData = JSON.parse(jsonInput);
      if (Array.isArray(parsedData)) {
        const newItems: EvaluationItem[] = parsedData.map((item: any) => ({
          sessionId: item.sessionId || '',
          checkReason: item.checkReason || '',
          id: generateId(),
          result: '',
          isLoading: false,
          query: '',
        }));
        setItems(newItems);
        setIsModalVisible(false);
        setJsonInput('');
        toast.success(`成功导入 ${newItems.length} 个sessionId`);
      } else {
        toast.error('JSON数据格式不正确，根节点应该是一个数组');
      }
    } catch (error) {
      toast.error('JSON解析失败，请检查格式');
      console.error('Error parsing JSON:', error);
    }
  };

  const handleEvalTaskModalOk = async () => {
    if (!evalTaskInput.trim()) {
      toast.error('请输入评测任务ID或URL');
      return;
    }

    try {
      let taskId = evalTaskInput;
      if (taskId.includes("https://aime-auto-eval-fe.gf.bytedance.net/tasks/")) {
        taskId = taskId.split("/tasks/")[1].split("?")[0];
      }

      const reasons = await fetchSessionIdsByTaskId(taskId);

      if (reasons.length === 0) {
        toast.error('未找到相关的sessionIds');
        return;
      }

      const jsonData = reasons.map(reason => {
        if (reason.type === "satisfaction" && useSatisfactionAnalysis) {
          return {
            sessionId: reason.session_id,
            checkReason: `可用性分数：${reason.check_info}\n评价理由：${reason.check_reason}`,
          }
        } else {
          return {
            sessionId: reason.session_id,
            checkReason: '',
          }
        }
      });

      const newItems: EvaluationItem[] = jsonData.map((item: any) => ({
        sessionId: item.sessionId || '',
        checkReason: item.checkReason || '',
        id: generateId(),
        result: '',
        isLoading: false,
        query: '',
      }));

      setItems(newItems);
      setIsEvalTaskModalVisible(false);
      setEvalTaskInput('');

      toast.success(`成功导入 ${reasons.length} 个sessionId`);
    } catch (error) {
      toast.error('获取sessionIds失败，请检查评测任务ID');
      console.error('Error fetching sessionIds:', error);
    }
  };

  const fetchUnrecalledExperiences = async () => {
    if (aggregatedResults.length === 0) {
      toast.error('请先运行评测获取结果');
      return;
    }

    setIsLoadingUnrecalled(true);
    try {
      // 获取已召回的经验ID集合
      const recalledExperienceIds = new Set(aggregatedResults.map(result => result.experienceId));

      // 使用输入的用户名，如果没有输入则使用当前用户名
      const usernameToUse = usernameInput.trim() || (await getCurrentUser())?.username || '';
      const apiUrl = `https://meta-server.bytedance.net/api/experiences/filter?publishStatus=pending&extractType=${usernameToUse}&variant=${selectedVariant}&page=1&size=1000`;

      const response = await fetch(apiUrl);
      const jsonRes = await response.json();

      if (jsonRes.data && Array.isArray(jsonRes.data.data)) {
        // 过滤出未召回的经验
        const unrecalled = jsonRes.data.data
          .filter((exp: any) => !recalledExperienceIds.has(exp.point_id))
          .map((exp: any) => ({
            title: exp.title,
            point_id: exp.point_id
          }));

        setUnrecalledExperiences(unrecalled);

        if (unrecalled.length === 0) {
          toast.success('所有经验都已被召回');
        } else {
          toast.success(`找到 ${unrecalled.length} 个未召回的经验`);
        }
      } else {
        toast.error('获取经验列表失败');
      }
    } catch (error) {
      console.error('Error fetching unrecalled experiences:', error);
      toast.error('获取未召回经验失败');
    } finally {
      setIsLoadingUnrecalled(false);
      setIsVariantModalVisible(false);
    }
  };

  const processResults = (items: EvaluationItem[]) => {
    const resultMap: { [key: string]: AggregatedResult } = {};

    items.forEach(item => {
      if (!item.result || item.result === 'Error: Could not fetch result.') return;

      try {
        let jsonToParse;
        const firstBraceIndex = item.result.indexOf('{');
        const lastBraceIndex = item.result.lastIndexOf('}');

        if (firstBraceIndex !== -1 && lastBraceIndex > firstBraceIndex) {
          jsonToParse = item.result.substring(firstBraceIndex, lastBraceIndex + 1);
        } else {
          jsonToParse = item.result
            .replace(/```json\s*/g, '')
            .replace(/```\s*/g, '')
            .trim();
        }
        const resultData = JSON.parse(jsonToParse);
        if (!resultData.recalls) return;

        resultData.recalls.forEach((recall: any) => {
          if (recall.experience_effectiveness_analysis) {
            const analysis = recall.experience_effectiveness_analysis;

            // 处理正向作用经验
            analysis.positive_effect_experiences?.forEach((exp: any) => {
              if (!resultMap[exp.id]) {
                resultMap[exp.id] = {
                  id: exp.id,
                  title: exp.experience_title,
                  positiveCount: 0,
                  positiveSessionIds: [],
                  negativeCount: 0,
                  negativeSessionIds: [],
                  ignoredCount: 0,
                  ignoredSessionIds: [],
                  irrelevantCount: 0,
                  irrelevantSessionIds: []
                };
              }
              resultMap[exp.id].positiveCount++;
              if (!resultMap[exp.id].positiveSessionIds.includes(item.sessionId)) {
                resultMap[exp.id].positiveSessionIds.push(item.sessionId);
              }
            });

            // 处理负向作用经验
            analysis.negative_effect_experiences?.forEach((exp: any) => {
              if (!resultMap[exp.id]) {
                resultMap[exp.id] = {
                  id: exp.id,
                  title: exp.experience_title,
                  positiveCount: 0,
                  positiveSessionIds: [],
                  negativeCount: 0,
                  negativeSessionIds: [],
                  ignoredCount: 0,
                  ignoredSessionIds: [],
                  irrelevantCount: 0,
                  irrelevantSessionIds: []
                };
              }
              resultMap[exp.id].negativeCount++;
              if (!resultMap[exp.id].negativeSessionIds.includes(item.sessionId)) {
                resultMap[exp.id].negativeSessionIds.push(item.sessionId);
              }
            });

            // 处理被忽略经验
            analysis.ignored_experiences?.forEach((exp: any) => {
              if (!resultMap[exp.id]) {
                resultMap[exp.id] = {
                  id: exp.id,
                  title: exp.experience_title,
                  positiveCount: 0,
                  positiveSessionIds: [],
                  negativeCount: 0,
                  negativeSessionIds: [],
                  ignoredCount: 0,
                  ignoredSessionIds: [],
                  irrelevantCount: 0,
                  irrelevantSessionIds: []
                };
              }
              resultMap[exp.id].ignoredCount++;
              if (!resultMap[exp.id].ignoredSessionIds.includes(item.sessionId)) {
                resultMap[exp.id].ignoredSessionIds.push(item.sessionId);
              }
            });

            // 处理无关召回经验
            analysis.irrelevant_experiences?.forEach((exp: any) => {
              if (!resultMap[exp.id]) {
                resultMap[exp.id] = {
                  id: exp.id,
                  title: exp.experience_title,
                  positiveCount: 0,
                  positiveSessionIds: [],
                  negativeCount: 0,
                  negativeSessionIds: [],
                  ignoredCount: 0,
                  ignoredSessionIds: [],
                  irrelevantCount: 0,
                  irrelevantSessionIds: []
                };
              }
              resultMap[exp.id].irrelevantCount++;
              if (!resultMap[exp.id].irrelevantSessionIds.includes(item.sessionId)) {
                resultMap[exp.id].irrelevantSessionIds.push(item.sessionId);
              }
            });
          }
        });
      } catch (error) {
        console.error('Error parsing result JSON:', error);
      }
    });

    return Object.values(resultMap);
  };

  const handleRunAll = async () => {
    if (!model) return;
    setShowResultTable(false);

    const runSingle = async (item: EvaluationItem) => {
      if (!item.sessionId) return;

      handleItemChange(item.id, { isLoading: true, result: '' });

      let queryToUse = item.query;
      if (!queryToUse) {
        try {
          const res = await fetchTraceWithRetry(item.sessionId);
          //only check trace
          const traceData = JSON.stringify(res, null);
          queryToUse = `<trace>
${traceData}
</trace>`;
          handleItemChange(item.id, { query: queryToUse });
        } catch (e) {
          console.error(e);
          handleItemChange(item.id, { result: `获取 trace 失败: ${(e as Error).message}`, isLoading: false });
          return;
        }
      }

      const res = await runEvaluationQuery(
        model,
        temperature,
        systemPrompt,
        queryToUse,
        item.checkReason,
        item.sessionId,
        useCache,
        'experience_evaluation',
      );
      handleItemChange(item.id, { result: res.content, isLoading: false });
    }

    const promises = items.map(item => runSingle(item));
    await Promise.all(promises);

    // 聚合结果并显示表格
    const aggregated = processResults(items);
    setAggregatedResults(aggregated);
    setShowResultTable(true);
  };

  const handleExperienceIdClick = (experienceId: string) => {
    setSelectedExperienceId(experienceId);
    setIsExperienceDetailModalVisible(true);
  };

  return (
    <div>
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold">System Prompt</h2>
          <div className="flex gap-2">
            <Button onClick={() => setIsEvalTaskModalVisible(true)}>从评测任务导入</Button>
            <Button onClick={() => setIsModalVisible(true)}>从 JSON 导入</Button>
          </div>
        </div>
        <textarea
          className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
          value={systemPrompt}
          onChange={(e) => setSystemPrompt(e.target.value)}
          rows={isPromptExpanded ? Math.max(10, systemPrompt.split('\n').length) : 3}
        />
        <button
          className="text-sm text-blue-500 mt-1"
          onClick={() => setIsPromptExpanded(!isPromptExpanded)}
        >
          {isPromptExpanded ? '收起' : '展开'}
        </button>
      </div>

      <div className="flex items-center gap-4 mb-4">
        <Button type="primary" onClick={handleAddItem}>
          添加一项
        </Button>
        <Button type="primary" status="success" onClick={handleRunAll} loading={items.some(i => i.isLoading)}>
          全部运行
        </Button>
        <Switch checked={useCache} onChange={setUseCache} />
        <label onClick={() => setUseCache(v => !v)} className="cursor-pointer select-none">
          是否使用缓存
        </label>
      </div>

      {showResultTable && aggregatedResults.length > 0 && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-semibold">评测结果</h2>
            <Button
              type="primary"
              onClick={() => setIsVariantModalVisible(true)}
              loading={isLoadingUnrecalled}
            >
              查看未召回经验
            </Button>
          </div>
          <BatchResultDisplay aggregatedResults={aggregatedResults} onExperienceIdClick={handleExperienceIdClick} />
        </div>
      )}

      {unrecalledExperiences.length > 0 && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-semibold">没有被召回的经验</h2>
          </div>
          <Table
            columns={[
              {
                title: "No.",
                dataIndex: "index",
                width: 40,
                render: (_: any, __: any, index: number) => index + 1,
              },
              {
                title: "经验ID",
                dataIndex: "point_id",
                width: 150,
                render: (point_id: string) => (
                  <button
                    onClick={() => handleExperienceIdClick(point_id)}
                    className="text-blue-500 hover:text-blue-700 underline bg-transparent border-none p-0 cursor-pointer"
                  >
                    {point_id}
                  </button>
                ),
              },
              {
                title: "经验标题",
                dataIndex: "title",
                width: 400,
              },
            ]}
            data={unrecalledExperiences}
            rowKey="point_id"
            scroll={{ x: 700, y: 400 }}
            pagination={false}
            border
            stripe
          />
        </div>
      )}

      {items.map((item, index) => (
        <div key={item.id} className="mb-4 border p-4 rounded-md">
          <div className="flex items-center gap-4 mb-4">
            <div className="font-semibold">No.{index + 1}</div>
            <Input
              placeholder="Enter Session ID"
              value={item.sessionId}
              onChange={value => handleItemChange(item.id, { sessionId: value })}
              className="w-1/2"
            />
            {items.length > 1 && (
              <Button status="danger" onClick={() => handleRemoveItem(item.id)}>
                删除
              </Button>
            )}
          </div>
          <div className="mb-4">
            <p className="font-semibold">{'<check> 原因:'}</p>
            <Input.TextArea
              value={item.checkReason}
              onChange={value => handleItemChange(item.id, { checkReason: value })}
              rows={3}
            />
          </div>
          {item.result && (
            <StructuredResultDisplay result={item.result} sessionId={item.sessionId} showResult={false} onExperienceIdClick={handleExperienceIdClick} />
          )}
          {item.isLoading && (
            <div className="p-4 rounded-md mt-2">
              <p className="font-semibold">评价中...</p>
            </div>
          )}
        </div>
      ))}

      <Modal
        title="从评测任务导入"
        visible={isEvalTaskModalVisible}
        onOk={handleEvalTaskModalOk}
        onCancel={() => {
          setIsEvalTaskModalVisible(false);
          setEvalTaskInput('');
        }}
        autoFocus={false}
        focusLock={true}
      >
        <Input.TextArea
          placeholder={`请输入评测任务ID或完整URL，例如：
https://aime-auto-eval-fe.gf.bytedance.net/tasks/68a7df36be677e5e4f85895f
或
68a7df36be677e5e4f85895f`}
          value={evalTaskInput}
          onChange={setEvalTaskInput}
          rows={6}
          style={{ height: 120 }}
        />
        <div className="flex items-center gap-2 mt-4">
          <Switch checked={useSatisfactionAnalysis} onChange={setUseSatisfactionAnalysis} />
          <label onClick={() => setUseSatisfactionAnalysis(v => !v)} className="cursor-pointer select-none">
            是否使用可用性分析
          </label>
        </div>
      </Modal>

      <Modal
        title="从JSON导入"
        visible={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setIsModalVisible(false);
          setJsonInput('');
        }}
        autoFocus={false}
        focusLock={true}
      >
        <Input.TextArea
          placeholder={`[
  {
    "sessionId": "xxxx",
    "checkReason": "xxxx"
  }
]`}
          value={jsonInput}
          onChange={setJsonInput}
          rows={15}
          style={{ height: 400 }}
        />
      </Modal>

      <Modal
        title="选择Variant"
        visible={isVariantModalVisible}
        onOk={fetchUnrecalledExperiences}
        onCancel={() => {
          setIsVariantModalVisible(false);
        }}
        autoFocus={false}
        focusLock={true}
        confirmLoading={isLoadingUnrecalled}
      >
        <div className="mb-4">
          <label className="block mb-2">用户名</label>
          <Input
            placeholder="输入提取经验的用户名"
            value={usernameInput}
            onChange={setUsernameInput}
          />
        </div>
        <Radio.Group
          value={selectedVariant}
          onChange={(value) => setSelectedVariant(value)}
          className="flex flex-col gap-4 mt-4"
        >
          <Radio defaultChecked value="newbie">小美 (newbie)</Radio>
          <Radio value="invited_expert">大卫 (invited_expert)</Radio>
        </Radio.Group>
      </Modal>

      <ExperienceDetailModal
        visible={isExperienceDetailModalVisible}
        onClose={() => setIsExperienceDetailModalVisible(false)}
        experienceId={selectedExperienceId}
      />
    </div>
  );
}