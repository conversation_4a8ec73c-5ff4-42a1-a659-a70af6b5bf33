"use client";

import React, { useState, useMemo, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { 
  Table, 
  Button, 
  Input, 
  Form, 
  Grid, 
  Select, 
  Space,
  Tag,
  Modal,
  Typography,
  Message,
  DatePicker
} from "@arco-design/web-react";
import { 
  IconPlus, 
  IconEdit, 
  IconDelete, 
  IconEye, 
  IconSearch,
  IconCheck,
  IconUndo,
  IconExport
} from "@arco-design/web-react/icon";
import * as XLSX from 'xlsx';
import { useQuery } from "@tanstack/react-query";
import { useStore } from "@nanostores/react";
import { toast } from "sonner";
import ConfirmDeleteDialog from "../components/ConfirmDeleteDialog";
import { userAtom } from "../../store/auth";
import { 
  deleteExperience, 
  batchDeleteExperiences,
  updateExperience,
  filterExperiences,
  FilterExperienceParams,
  experienceTypeOptions,
  applyTypeOptions,
  publishStatusOptions,
  Experience,
  variantOptions
} from "../api/experience";

const { Text } = Typography;

// 扩展搜索参数接口
interface SearchParams {
  keyword?: string;
  type?: string;
  apply_type?: string;
  publish_status?: string;
  extract_type?: string;
  source_id?: string;
  variant?: string;
  point_id?: string;
  date_range?: [string, string];
}

// 添加日期范围选择器组件
const RangePicker = DatePicker.RangePicker;

// 日期格式转换函数
const formatDateForServer = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toISOString().split('T')[0]; // 格式化为 yyyy-MM-dd
};

// 处理来源ID显示的函数
const renderSourceId = (sourceId: string, isInline: boolean = false) => {
  if (!sourceId) return "-";
  
  // 如果是人工专家经验，不可点击
  if (sourceId === "人工专家经验") {
    return isInline ? sourceId : (
      <div className="max-w-[150px] truncate" title={sourceId}>
        {sourceId}
      </div>
    );
  }
  
  // 如果是goodId开头的格式
  if (sourceId.startsWith("goodId_")) {
    const parts = sourceId.split("_");
    if (parts.length == 4 && parts[0] === "goodId" && parts[2] === "badId") {
      const goodId = parts[1];
      const badId = parts[3];
      const goodUrl = `https://aime.bytedance.net/chat/${goodId}`;
      const badUrl = `https://aime.bytedance.net/chat/${badId}`;
      
      return (
        <div className={isInline ? "inline-flex items-center" : "max-w-[150px] truncate"}>
          <a 
            href={goodUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 mr-1"
            title={`跳转到goodId: ${goodUrl}`}
          >
            goodId: {goodId}
          </a>
          <span className="text-gray-400 mx-1">|</span>
          <a 
            href={badUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800"
            title={`跳转到badId: ${badUrl}`}
          >
            badId: {badId}
          </a>
        </div>
      );
    }
  }
  
  // 其他情况，直接展示source_id并生成跳转链接
  const url = `https://aime.bytedance.net/chat/${sourceId}`;
  return (
    <div className={isInline ? "inline-flex items-center" : "max-w-[150px] truncate"}>
      <a 
        href={url} 
        target="_blank" 
        rel="noopener noreferrer"
        className="text-blue-600 hover:text-blue-800"
        title={`跳转到: ${url}`}
      >
        {sourceId}
      </a>
    </div>
  );
};

export function ExperienceList() {
  const router = useRouter();
  const urlSearchParams = useSearchParams();
  const [form] = Form.useForm();
  const [searchParams, setSearchParams] = useState<SearchParams>({});

  // 从URL参数初始化搜索条件
  useEffect(() => {
    const params: SearchParams = {};
    
    // 读取URL参数
    const keyword = urlSearchParams.get('keyword');
    const type = urlSearchParams.get('type');
    const apply_type = urlSearchParams.get('apply_type');
    const publish_status = urlSearchParams.get('publish_status');
    const extract_type = urlSearchParams.get('extract_type');
    const source_id = urlSearchParams.get('source_id');
    const variant = urlSearchParams.get('variant');
    const point_id = urlSearchParams.get('point_id');
    const start_date = urlSearchParams.get('start_date');
    const end_date = urlSearchParams.get('end_date');
    const page = urlSearchParams.get('page');
    const size = urlSearchParams.get('size');
    
    // 设置参数
    if (keyword) params.keyword = keyword;
    if (type) params.type = type;
    if (apply_type) params.apply_type = apply_type;
    if (publish_status) params.publish_status = publish_status;
    if (extract_type) params.extract_type = extract_type;
    if (source_id) params.source_id = source_id;
    if (variant) params.variant = variant;
    if (point_id) params.point_id = point_id;
    if (start_date && end_date) params.date_range = [start_date, end_date];
    if (page) setCurrentPage(parseInt(page));
    if (size) setPageSize(parseInt(size));
    
    // 更新状态和表单
    setSearchParams(params);
    form.setFieldsValue(params);
    
    // 检查是否是只看我的状态
    if (extract_type && !Array.isArray(extract_type)) {
      setIsMyOnly(true);
    }
  }, [urlSearchParams, form]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(100);
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null);
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedExperience, setSelectedExperience] = useState<Experience | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);
  const [batchDeleteLoading, setBatchDeleteLoading] = useState(false);
  const [approveLoading, setApproveLoading] = useState(false);
  const [approvingId, setApprovingId] = useState<string | null>(null);
  const [actionConfirmVisible, setActionConfirmVisible] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState<'approve' | 'rollback' | null>(null);
  const [recordToConfirm, setRecordToConfirm] = useState<Experience | null>(null);
  const [isMyOnly, setIsMyOnly] = useState(false);
  const [permissionModalVisible, setPermissionModalVisible] = useState(false);
  const user = useStore(userAtom);

  // 权限判断函数
  const hasPermission = () => {
    return user?.username === 'dingdegao' || 
           user?.username === 'lanjunjian' ||
           user?.username === 'sunjuntao.rex' ||
           user?.username === 'xieran.sai'
  };

  // 查询经验列表
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["experienceList", searchParams, currentPage, pageSize],
    queryFn: async () => {
      try {
        // 构建查询参数
        const filterParams: FilterExperienceParams = {
          page: currentPage,
          size: pageSize,
        };

        // 添加各种筛选条件
        if (searchParams.keyword) {
          filterParams.keyword = searchParams.keyword;
        }
        if (searchParams.type) {
          filterParams.type = searchParams.type;
        }
        if (searchParams.apply_type) {
          filterParams.applyType = searchParams.apply_type;
        }
        if (searchParams.publish_status) {
          filterParams.publishStatus = searchParams.publish_status;
        }
        if (searchParams.extract_type) {
          filterParams.extractType = searchParams.extract_type;
        }
        if (searchParams.source_id) {
          filterParams.sourceId = searchParams.source_id;
        }
        if (searchParams.variant) {
          filterParams.variant = searchParams.variant;
        }
        if (searchParams.point_id) {
          filterParams.pointId = searchParams.point_id;
        }
        if (searchParams.date_range && searchParams.date_range[0] && searchParams.date_range[1]) {
          filterParams.startDate = formatDateForServer(searchParams.date_range[0]);
          filterParams.endDate = formatDateForServer(searchParams.date_range[1]);
        }

        const response = await filterExperiences(filterParams);
        return response.data;
      } catch (error) {
        console.error("获取经验列表失败:", error);
        toast.error("获取经验列表失败");
        return { total: 0, page: 1, size: pageSize, data: [] };
      }
    },
  });

  // 表格数据
  const tableData = useMemo(() => {
    if (!data || !data.data) return [];
    return data.data.map((item: Experience) => ({
      ...item,
      key: item.id || '',
    }));
  }, [data]);

  // 表格列定义
  const columns = [
    {
      title: "标题",
      dataIndex: "title",
      width: 300,
      render: (col: string) => (
        <div className="max-w-[300px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "来源ID",
      dataIndex: "source_id",
      width: 150,
      render: (col: string) => renderSourceId(col),
    },
    {
      title: "类型",
      dataIndex: "type",
      width: 150,
      render: (col: string) => {
        const option = experienceTypeOptions.find(opt => opt.value === col);
        return <Tag color="blue">{option?.label || col}</Tag>;
      },
    },
    {
      title: "应用类型",
      dataIndex: "apply_type",
      width: 120,
      render: (col: string) => {
        const option = applyTypeOptions.find(opt => opt.value === col);
        return option?.label || col;
      },
    },
    {
      title: "发布状态",
      dataIndex: "publish_status",
      width: 100,
      render: (col: string) => {
        const option = publishStatusOptions.find(opt => opt.value === col);
        return <Tag color={col === 'publish' ? 'green' : 'orange'}>{option?.label || col}</Tag>;
      },
    },
    {
      title: "提取类型",
      dataIndex: "extract_type",
      width: 100,
      render: (col: string) => {
        if(col.includes("llm")){
          return "模型提取";
        }else{
          return "人工";
        }
      },
    },
    {
      title: "创建时间",
      dataIndex: "create_time",
      width: 180,
      render: (col: string) => col ? new Date(col).toLocaleString() : "-",
    },
    {
      title: "更新时间",
      dataIndex: "update_time",
      width: 180,
      render: (col: string) => col ? new Date(col).toLocaleString() : "-",
    },
    {
      title: "Variant",
      dataIndex: "variant",
      width: 100,
      render: (col: string) => {
        if (!col) return "-";
        const option = variantOptions.find(opt => opt.value === col);
        return option?.label || col;
      },
    },
    // {
    //   title: "Limit",
    //   dataIndex: "limit",
    //   width: 80,
    //   render: (col: number) => col ? col : "-",
    // },
  ];

  // 更新URL参数的函数
  const updateURLParams = (params: SearchParams, page: number, size: number) => {
    const urlParams = new URLSearchParams();
    
    // 添加搜索参数
    if (params.keyword) urlParams.set('keyword', params.keyword);
    if (params.type) urlParams.set('type', params.type);
    if (params.apply_type) urlParams.set('apply_type', params.apply_type);
    if (params.publish_status) urlParams.set('publish_status', params.publish_status);
    if (params.extract_type) urlParams.set('extract_type', params.extract_type);
    if (params.source_id) urlParams.set('source_id', params.source_id);
    if (params.variant) urlParams.set('variant', params.variant);
    if (params.point_id) urlParams.set('point_id', params.point_id);
    if (params.date_range && params.date_range[0] && params.date_range[1]) {
      urlParams.set('start_date', params.date_range[0]);
      urlParams.set('end_date', params.date_range[1]);
    }
    
    // 添加分页参数
    if (page !== 1) urlParams.set('page', page.toString());
    if (size !== 10) urlParams.set('size', size.toString());
    
    // 更新URL
    const newURL = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
    window.history.replaceState({}, '', newURL);
  };

  // 搜索表单变更
  const handleSearch = () => {
    const values = form.getFieldsValue();
    setSearchParams(values);
    setCurrentPage(1);
    updateURLParams(values, 1, pageSize);
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setSearchParams({});
    setCurrentPage(1);
    setIsMyOnly(false);
    updateURLParams({}, 1, pageSize);
  };

  // 切换只看我的/全部
  const handleToggleMyOnly = () => {
    if (!isMyOnly) {
      // 切换到"只看我的"
      if (user?.username) {
        const newSearchParams = {
          ...searchParams,
          extract_type: user.username
        };
        form.setFieldValue('extract_type', [user.username]);
        setSearchParams(newSearchParams);
        setCurrentPage(1);
        setIsMyOnly(true);
        updateURLParams(newSearchParams, 1, pageSize);
      }
    } else {
      // 切换到"全部"
      const newSearchParams = {
        ...searchParams,
        extract_type: undefined
      };
      form.setFieldValue('extract_type', []);
      setSearchParams(newSearchParams);
      setCurrentPage(1);
      setIsMyOnly(false);
      updateURLParams(newSearchParams, 1, pageSize);
    }
  };

  // 处理查看详情
  const handleViewDetail = (item: Experience) => {
    setSelectedExperience(item);
    setDetailModalVisible(true);
  };

  // 处理编辑
  const handleEdit = (item: Experience) => {
    router.push(`/experience/edit?id=${item.id}`);
  };

  // 处理添加按钮点击
  const handleAdd = () => {
    router.push("/experience/edit");
  };

  // 处理删除按钮点击
  const handleDelete = (id: string) => {
    setDeleteItemId(id);
    setDeleteConfirmVisible(true);
  };

  // 添加批量删除处理函数
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      Message.warning('请至少选择一条记录');
      return;
    }
    setDeleteConfirmVisible(true);
  };

  // 修改确认删除函数，支持批量删除
  const confirmDelete = async () => {
    if (deleteItemId) {
      // 单条删除
      setDeleteLoading(true);
      try {
        const response = await deleteExperience(deleteItemId);
        if (response.code === 200) {
          toast.success("删除成功");
          refetch();
        } else {
          toast.error(response.msg || "删除失败");
        }
      } catch (error) {
        console.error("删除经验失败:", error);
        toast.error("删除经验失败");
      } finally {
        setDeleteLoading(false);
        setDeleteConfirmVisible(false);
        setDeleteItemId(null);
      }
    } else if (selectedRowKeys.length > 0) {
      // 批量删除
      setBatchDeleteLoading(true);
      try {
        const response = await batchDeleteExperiences(selectedRowKeys.map(key => String(key)));
        if (response.code === 200) {
          const successCount = Object.values(response.data || {}).filter(Boolean).length;
          toast.success(`批量删除成功，成功删除${successCount}条记录`);
          setSelectedRowKeys([]);
          refetch();
        } else {
          toast.error(response.msg || "批量删除失败");
        }
      } catch (error) {
        console.error("批量删除经验失败:", error);
        toast.error("批量删除经验失败");
      } finally {
        setBatchDeleteLoading(false);
        setDeleteConfirmVisible(false);
      }
    }
  };

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: (string | number)[], selectedRows: Experience[]) => {
      setSelectedRowKeys(selectedRowKeys);
    }
  };

  // 分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateURLParams(searchParams, page, pageSize);
  };

  // 每页条数变化
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
    updateURLParams(searchParams, 1, size);
  };

  // 导出Excel功能
  const handleExportExcel = () => {
    if (!tableData || tableData.length === 0) {
      Message.warning('没有数据可以导出');
      return;
    }

    // 准备导出数据
    const exportData = tableData.map(item => ({
      '标题': item.title,
      '来源ID': item.source_id,
      '类型': item.type,
      '应用类型': item.apply_type,
      '发布状态': item.publish_status === 'publish' ? '已发布' : '待审核',
      '提取类型': item.extract_type?.includes('llm') ? '模型提取' : '人工',
      '创建时间': item.create_time ? new Date(item.create_time).toLocaleString() : '-',
      '更新时间': item.update_time ? new Date(item.update_time).toLocaleString() : '-',
      'Variant': item.variant || '-',
      '经验内容': item.experience_content,
      '向量内容': item.vector_content || '-',
      '提取原因': item.ext || '-'
    }));

    // 创建工作簿
    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '经验数据');

    // 生成文件名
    const fileName = `经验列表_${new Date().toISOString().split('T')[0]}.xlsx`;

    // 导出文件
    XLSX.writeFile(wb, fileName);
    toast.success('导出成功');
  };

  // 修改审核函数
  const handleApprove = (record: Experience) => {
    if (!hasPermission()) {
      setPermissionModalVisible(true);
      return;
    }
    setActionToConfirm('approve');
    setRecordToConfirm(record);
    setActionConfirmVisible(true);
  };

  const handleRollback = (record: Experience) => {
    if (!hasPermission()) {
      setPermissionModalVisible(true);
      return;
    }
    setActionToConfirm('rollback');
    setRecordToConfirm(record);
    setActionConfirmVisible(true);
  };

  const confirmAction = async () => {
    if (!recordToConfirm || !actionToConfirm) return;

    setApproveLoading(true);
    setApprovingId(recordToConfirm.id!);
    try {
      const newStatus = actionToConfirm === 'approve' ? 'publish' : 'pending';
      const updatedExperience = {
        ...recordToConfirm,
        publish_status: newStatus,
      };

      const response = await updateExperience(recordToConfirm.id!, updatedExperience);

      if (response.code === 200) {
        toast.success(actionToConfirm === 'approve' ? '审核通过，已发布' : '回滚成功');
        refetch();
      } else {
        toast.error(response.msg || '操作失败');
      }
    } catch (error) {
      console.error(`${actionToConfirm}失败:`, error);
      toast.error(`${actionToConfirm === 'approve' ? '审核' : '回滚'}失败`);
    } finally {
      setApproveLoading(false);
      setApprovingId(null);
      setActionConfirmVisible(false);
      setRecordToConfirm(null);
      setActionToConfirm(null);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <Form layout="horizontal" className="mb-4 flex-shrink-0" form={form}>
        <Grid.Row gutter={16}>
          <Grid.Col span={6}>
            <Form.Item label="关键词" field="keyword">
              <Input placeholder="请输入关键词" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={5}>
            <Form.Item label="日期" field="date_range">
              <RangePicker format="YYYY-MM-DD" />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={5}>
            <Form.Item label="类型" field="type">
              <Select placeholder="请选择经验类型" allowClear allowCreate>
                {experienceTypeOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4}>
            <Form.Item label="应用" field="apply_type">
              <Select placeholder="请选择应用类型" allowClear allowCreate>
                {applyTypeOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4}>
            <Form.Item label="状态" field="publish_status">
              <Select placeholder="请选择发布状态" allowClear>
                {publishStatusOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={3}>
          </Grid.Col>
        </Grid.Row>
        <Grid.Row gutter={16} className="mt-4">
          <Grid.Col span={5}>
            <Form.Item label="提取" field="extract_type">
              <Select placeholder="请输入提取人员" allowClear mode="tags">
              
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={5}>
            <Form.Item label="来源" field="source_id">
              <Input placeholder="请输入来源ID" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4}>
            <Form.Item label="ID" field="point_id">
              <Input placeholder="请输入经验ID" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="Variant" field="variant">
              <Select placeholder="请选择Variant" allowClear>
                {variantOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4} className="flex justify-end items-end">
            <Space>
              <Button type="primary" icon={<IconSearch />} onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Grid.Col>
        </Grid.Row>
      </Form>

      <div className="flex justify-between mb-4 flex-shrink-0">
        <div>
          <Space>
            <Button type="primary" icon={<IconPlus />} onClick={handleAdd}>
              创建经验
            </Button>
            <Button type="outline" onClick={()=>{
              router.push("/experience/search");
            }}>
              RAG 模拟召回
            </Button>
            <Button type="outline" onClick={()=>{
              router.push("/experience/analysis??type=Insights-BadCase");
            }}>
             已入库经验分析
            </Button>
          </Space>
        </div>
        <div>
          <Space>
            <Button onClick={handleToggleMyOnly}>
              {isMyOnly ? '全部' : '只看我的'}
            </Button>
            <Button 
              type="primary" 
              status="danger" 
              icon={<IconDelete />} 
              onClick={handleBatchDelete}
              disabled={selectedRowKeys.length === 0}
            >
              批量删除
            </Button>
            <Button 
              type="primary" 
              icon={<IconExport />} 
              onClick={handleExportExcel}
            >
              导出Excel
            </Button>
          </Space>
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        <Table
          rowSelection={rowSelection}
          columns={[...columns, {
            title: "操作",
            dataIndex: "operation",
            fixed: "right",
            width: 210,
            render: (_: unknown, record: Experience) => (
              <Space wrap>
                <Button
                  type="text"
                  size="small"
                  icon={<IconEye />}
                  onClick={() => handleViewDetail(record)}
                >
                  查看
                </Button>
                <Button
                  type="text"
                  size="small"
                  icon={<IconEdit />}
                  onClick={() => handleEdit(record)}
                >
                  编辑
                </Button>
                {record.publish_status === 'pending' && (
                  <Button
                    type="text"
                    status="success"
                    size="small"
                    icon={<IconCheck />}
                    onClick={() => handleApprove(record)}
                    loading={approveLoading && approvingId === record.id}
                  >
                    审核
                  </Button>
                )}
                {record.publish_status === 'publish' && (
                  <Button
                    type="text"
                    status="warning"
                    size="small"
                    icon={<IconUndo />}
                    onClick={() => handleRollback(record)}
                    loading={approveLoading && approvingId === record.id}
                  >
                    回滚
                  </Button>
                )}
                <Button
                  type="text"
                  status="danger"
                  size="small"
                  icon={<IconDelete />}
                  onClick={() => handleDelete(record.id!)}
                >
                  删除
                </Button>
              </Space>
            ),
          }]}
          data={tableData}
          loading={isLoading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: data?.total || 0,
            showTotal: (total) => `共 ${total} 条`,
            onChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            sizeOptions: [100, 500, 1000],
          }}
          scroll={{ x: 1650, y: 'calc(100vh - 380px)' }}
        />
      </div>

      {/* 删除确认对话框 */}
      <ConfirmDeleteDialog
        open={deleteConfirmVisible}
        onOpenChange={setDeleteConfirmVisible}
        title="确认删除经验"
        description="确定要删除这个经验吗？此操作无法撤销。"
        onConfirm={confirmDelete}
        loading={deleteLoading}
      />

      {/* 审核/回滚确认对话框 */}
      <Modal
        title={actionToConfirm === 'approve' ? '确认审核' : '确认回滚'}
        visible={actionConfirmVisible}
        onOk={confirmAction}
        onCancel={() => {
          setActionConfirmVisible(false);
          setRecordToConfirm(null);
          setActionToConfirm(null);
        }}
        confirmLoading={approveLoading}
      >
        <p>
          {actionToConfirm === 'approve'
            ? `确定要审核通过经验 "${recordToConfirm?.title}" 吗？`
            : `确定要回滚经验 "${recordToConfirm?.title}" 吗？状态将变为待审核。`}
        </p>
      </Modal>

      {/* 经验详情模态框 */}
      <Modal
        title="经验详情"
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        style={{ width: '80%', maxWidth: 1200 }}
      >
        {selectedExperience && (
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded mb-4">
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">标题：</Text>
                <Text>{selectedExperience.title}</Text>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">类型：</Text>
                <Tag color="blue">
                  {experienceTypeOptions.find(opt => opt.value === selectedExperience.type)?.label || selectedExperience.type}
                </Tag>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">应用类型：</Text>
                <Text>
                  {applyTypeOptions.find(opt => opt.value === selectedExperience.apply_type)?.label || selectedExperience.apply_type}
                </Text>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">发布状态：</Text>
                <Tag color={selectedExperience.publish_status === 'publish' ? 'green' : 'orange'}>
                  {publishStatusOptions.find(opt => opt.value === selectedExperience.publish_status)?.label || selectedExperience.publish_status}
                </Tag>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">提取类型：</Text>
                <Text>
                 { selectedExperience.extract_type}
                </Text>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">向量内容：</Text>
                <Text>{selectedExperience.vector_content}</Text>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">来源ID：</Text>
                {renderSourceId(selectedExperience.source_id, true)}
              </div>
              {selectedExperience.variant && (
                <div className="flex items-center gap-2 mb-2">
                  <Text className="font-bold">Variant：</Text>
                  <Text>{variantOptions.find(opt => opt.value === selectedExperience.variant)?.label || selectedExperience.variant}</Text>
                </div>
              )}
              {selectedExperience.limit && (
                <div className="flex items-center gap-2 mb-2">
                  <Text className="font-bold">Limit：</Text>
                  <Text>{selectedExperience.limit}</Text>
                </div>
              )}
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">创建时间：</Text>
                <Text>{selectedExperience.create_time ? new Date(selectedExperience.create_time).toLocaleString() : "-"}</Text>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">更新时间：</Text>
                <Text>{selectedExperience.update_time ? new Date(selectedExperience.update_time).toLocaleString() : "-"}</Text>
              </div>
            </div>

            <div>
              <Text className="font-bold text-lg">经验内容：</Text>
              <div className="bg-gray-50 p-4 rounded mt-2 whitespace-pre-wrap">
                {selectedExperience.experience_content}
              </div>
            </div>
            {selectedExperience.ext && (
                <div className="mb-4">
                  <Text className="font-bold text-lg">提取原因：</Text>
                  <div className="bg-gray-50 p-4 rounded mt-2 whitespace-pre-wrap">
                    {selectedExperience.ext}
                  </div>
                </div>
            )}
          </div>
        )}
      </Modal>

      {/* 权限不足弹窗 */}
      <Modal
        title="权限提示"
        visible={permissionModalVisible}
        onCancel={() => setPermissionModalVisible(false)}
        footer={[
          <Button key="ok" type="primary" onClick={() => setPermissionModalVisible(false)}>
            确定
          </Button>
        ]}
      >
        <p>你暂无权限管理经验。</p>
      </Modal>
    </div>
  );
}