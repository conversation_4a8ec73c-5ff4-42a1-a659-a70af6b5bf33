"use client";

import React, { useState, useEffect } from "react";
import { PageHeader } from "@/app/components/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { BarChart3, TrendingUp, Users, Calendar, Sparkles, Bot, Workflow } from "lucide-react";
import { getExperienceStatistics, getExperienceStatisticsByDate, StatisticsData, DateStats } from "../api/experience";
import { LoadingSpinner } from "@/app/components/LoadingSpinner";
import { Separator } from "@/components/ui/separator";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

export default function ExperienceStatisticsPage() {
  const [statisticsData, setStatisticsData] = useState<StatisticsData | null>(null);
  const [dateStats, setDateStats] = useState<DateStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [dateLoading, setDateLoading] = useState(false);
  const [selectedDays, setSelectedDays] = useState<number>(7);

  // 加载基础统计数据
  const loadStatistics = async () => {
    try {
      setLoading(true);
      const response = await getExperienceStatistics();
      if (response.code === 200 && response.data) {
        setStatisticsData(response.data);
      }
    } catch (error) {
      console.error("获取统计数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  // 加载日期统计数据
  const loadDateStats = async (days: number) => {
    try {
      setDateLoading(true);
      const response = await getExperienceStatisticsByDate(days);
      if (response.code === 200 && response.data) {
        setDateStats(response.data);
      }
    } catch (error) {
      console.error("获取日期统计数据失败:", error);
    } finally {
      setDateLoading(false);
    }
  };

  useEffect(() => {
    loadStatistics();
    loadDateStats(selectedDays);
  }, []);

  useEffect(() => {
    loadDateStats(selectedDays);
  }, [selectedDays]);

  const handleRefresh = () => {
    loadStatistics();
    loadDateStats(selectedDays);
  };

  const handleDaysChange = (value: string) => {
    const days = parseInt(value);
    setSelectedDays(days);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <PageHeader 
          title="经验数据统计" 
          description="查看经验管理的各项统计数据和趋势分析"
        />
        <Button onClick={handleRefresh} variant="outline">
          刷新数据
        </Button>
      </div>

      {/* 基础统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">经验总数</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statisticsData?.basicStats.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              所有类型经验总计
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已发布</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{statisticsData?.basicStats.published || 0}</div>
            <p className="text-xs text-muted-foreground">
              发布状态的经验数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待审核</CardTitle>
            <Calendar className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{statisticsData?.basicStats.pending || 0}</div>
            <p className="text-xs text-muted-foreground">
              等待审核的经验数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">人工经验</CardTitle>
            <Users className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{statisticsData?.basicStats.manual || 0}</div>
            <p className="text-xs text-muted-foreground">
              人工创建的经验数量
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 类型统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            按类型统计
          </CardTitle>
          <CardDescription>
            不同类型经验的数量分布
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 border rounded-lg">
              <div className="flex justify-center mb-2">
                <Sparkles className="h-8 w-8 text-purple-500" />
              </div>
              <div className="text-2xl font-bold text-purple-600">
                {statisticsData?.typeStats.Insights || 0}
              </div>
              <div className="text-sm text-muted-foreground">Insights经验</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="flex justify-center mb-2">
                <Bot className="h-8 w-8 text-blue-500" />
              </div>
              <div className="text-2xl font-bold text-blue-600">
                {statisticsData?.typeStats.ReusableTool || 0}
              </div>
              <div className="text-sm text-muted-foreground">ReusableTool经验</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="flex justify-center mb-2">
                <Workflow className="h-8 w-8 text-green-500" />
              </div>
              <div className="text-2xl font-bold text-green-600">
                {statisticsData?.typeStats.ReusableWorkflow || 0}
              </div>
              <div className="text-sm text-muted-foreground">ReusableWorkflow经验</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 日期统计 */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                按日期统计
              </CardTitle>
              <CardDescription>
                最近{selectedDays}天新增经验数量趋势
              </CardDescription>
            </div>
            <Select value={selectedDays.toString()} onValueChange={handleDaysChange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">最近7天</SelectItem>
                <SelectItem value="30">最近30天</SelectItem>
                <SelectItem value="90">最近90天</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          {dateLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : dateStats.length > 0 ? (
            <div className="space-y-6">
              {/* 折线图 */}
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={dateStats.map(stat => ({
                      ...stat,
                      formattedDate: new Date(stat.date).toLocaleDateString('zh-CN', { 
                        month: 'short', 
                        day: 'numeric' 
                      })
                    }))}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="formattedDate" 
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: '#888' }}
                    />
                    <YAxis 
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: '#888' }}
                    />
                    <Tooltip 
                      formatter={(value: any) => [`${value} 个经验`, '新增数量']}
                      labelFormatter={(label: any) => `日期: ${label}`}
                      contentStyle={{
                        backgroundColor: 'hsl(var(--card))',
                        border: '1px solid hsl(var(--border))',
                        borderRadius: '6px'
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="count" 
                      stroke="hsl(var(--primary))" 
                      strokeWidth={2}
                      dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, stroke: 'hsl(var(--primary))', strokeWidth: 2 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
              
              {/* 汇总信息 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-muted rounded-lg">
                  <div className="text-sm text-muted-foreground mb-1">总计</div>
                  <div className="text-2xl font-bold text-primary">
                    {dateStats.reduce((sum, stat) => sum + stat.count, 0)}
                  </div>
                  <div className="text-xs text-muted-foreground">个经验</div>
                </div>
                <div className="text-center p-4 bg-muted rounded-lg">
                  <div className="text-sm text-muted-foreground mb-1">日均</div>
                  <div className="text-2xl font-bold text-blue-600">
                    {(dateStats.reduce((sum, stat) => sum + stat.count, 0) / dateStats.length).toFixed(1)}
                  </div>
                  <div className="text-xs text-muted-foreground">个经验</div>
                </div>
                <div className="text-center p-4 bg-muted rounded-lg">
                  <div className="text-sm text-muted-foreground mb-1">峰值</div>
                  <div className="text-2xl font-bold text-green-600">
                    {Math.max(...dateStats.map(stat => stat.count))}
                  </div>
                  <div className="text-xs text-muted-foreground">个经验</div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              暂无数据
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 