"use client";

import React, { useState } from "react";
import { PageHeader } from "@/app/components/PageHeader";
import { 
  Form, 
  Input, 
  Button, 
  Select, 
  Card, 
  Space, 
  Typography, 
  Slider,
  Table,
  Tag,
  Collapse,
  Spin,
  Message
} from "@arco-design/web-react";
import { 
  experienceTypeOptions, 
  applyTypeOptions, 
  publishStatusOptions, 
  advancedSearchExperiences,
  AdvancedSearchParams,
  SearchResult,
  SearchResponse,
  FilterCondition
} from "../api/experience";
import { IconSearch } from "@arco-design/web-react/icon";

const { Title, Text } = Typography;
const { TextArea } = Input;
const CollapseItem = Collapse.Item;

export default function ExperienceSearchPage() {
  const [form] = Form.useForm();
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [tokenUsage, setTokenUsage] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [requestId, setRequestId] = useState<string>("");
  const [resultCount, setResultCount] = useState<number>(0);

  // 添加查询过滤条件
  const [typeFilters, setTypeFilters] = useState<string[]>([]);
  const [applyTypeFilters, setApplyTypeFilters] = useState<string[]>([]);
  const [publishStatusFilters, setPublishStatusFilters] = useState<string[]>(['publish']);
  const [extractTypeFilters, setExtractTypeFilters] = useState<string[]>([]);

  // 高级搜索函数
  const handleSearch = async () => {
    try {
      setLoading(true);
      const values = form.getFieldsValue();
      
      // 构建查询过滤条件
      const conds: FilterCondition[] = [];
      
      if (typeFilters.length > 0) {
        conds.push({
          op: "must",
          field: "type",
          conds: typeFilters
        });
      }
      
      if (applyTypeFilters.length > 0) {
        conds.push({
          op: "must",
          field: "apply_type",
          conds: applyTypeFilters
        });
      }
      
      if (publishStatusFilters.length > 0) {
        conds.push({
          op: "must",
          field: "publish_status",
          conds: publishStatusFilters
        });
      }
      
      if (extractTypeFilters.length > 0) {
        conds.push({
          op: "must",
          field: "extract_type",
          conds: extractTypeFilters
        });
      }
      
      // 构建请求体
      const requestBody: AdvancedSearchParams = {
        project: values.project || "ark_project_20250122_qd2mx",
        name: values.collection || "AgentExpV3",
        query: values.query,
        limit: Number(values.limit) || 5,
        dense_weight: values.dense_weight || 0.5,
      };
      
      // 只有在有筛选条件时才添加query_param
      if (conds.length > 0) {
        requestBody.query_param = {
          doc_filter: {
            op: "and",
            conds: conds
          }
        };
      }
      
      // 发送请求
      const response = await advancedSearchExperiences(requestBody);
      
      if (response.code === 200 && response.data.code === 0) {
        setSearchResults(response.data.data.result_list);
        setTokenUsage(response.data.data.token_usage);
        setRequestId(response.data.request_id);
        setResultCount(response.data.data.count);
        Message.success("搜索成功");
      } else {
        Message.error(`搜索失败: ${response.msg || response.data.message}`);
      }
    } catch (error) {
      console.error("搜索请求失败:", error);
      Message.error("搜索请求失败");
    } finally {
      setLoading(false);
    }
  };

  // 结果表格列定义
  const columns = [
    {
      title: "分数",
      dataIndex: "score",
      width: 100,
      render: (score: number) => score.toFixed(4),
      sorter: (a: SearchResult, b: SearchResult) => a.score - b.score,
    },
    {
      title: "标题",
      width: 200,
      render: (_: any, record: SearchResult) => {
        const titleField = record.table_chunk_fields.find(field => field.field_name === "title");
        return titleField ? titleField.field_value : "-";
      },
    },
    {
      title: "类型",
      width: 150,
      render: (_: any, record: SearchResult) => {
        const typeField = record.table_chunk_fields.find(field => field.field_name === "type");
        if (!typeField) return "-";
        
        const option = experienceTypeOptions.find(opt => opt.value === typeField.field_value);
        return <Tag color="blue">{option?.label || typeField.field_value}</Tag>;
      },
    },
    {
      title: "应用类型",
      width: 120,
      render: (_: any, record: SearchResult) => {
        const applyTypeField = record.table_chunk_fields.find(field => field.field_name === "apply_type");
        if (!applyTypeField) return "-";
        
        const option = applyTypeOptions.find(opt => opt.value === applyTypeField.field_value);
        return option?.label || applyTypeField.field_value;
      },
    },
    {
      title: "发布状态",
      width: 100,
      render: (_: any, record: SearchResult) => {
        const statusField = record.table_chunk_fields.find(field => field.field_name === "publish_status");
        if (!statusField) return "-";
        
        const option = publishStatusOptions.find(opt => opt.value === statusField.field_value);
        return <Tag color={statusField.field_value === 'publish' ? 'green' : 'orange'}>
          {option?.label || statusField.field_value}
        </Tag>;
      },
    },
    {
      title: "创建时间",
      width: 180,
      render: (_: any, record: SearchResult) => {
        const timeField = record.table_chunk_fields.find(field => field.field_name === "create_time");
        return timeField ? timeField.field_value : "-";
      },
    },
  ];

  return (
    <div>
      <PageHeader 
        title="经验检索测试" 
        description="测试经验检索API，支持高级搜索和过滤条件"
      />
      
      <Card className="mb-4">
        <Form form={form} layout="vertical">
          <Form.Item label="查询内容" field="query">
            
            <TextArea 
              placeholder="请输入查询内容" 
              style={{ minHeight: 120 }} 
              autoSize={{ minRows: 4, maxRows: 8 }}
            />
          </Form.Item>
          
          <Collapse className="mb-4" defaultActiveKey={[]}>
            <CollapseItem header="基础配置" name="basic">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Form.Item label="项目ID" field="project" initialValue="ark_project_20250122_qd2mx">
                  <Input placeholder="请输入项目ID" />
                </Form.Item>
                
                <Form.Item label="集合名称" field="collection" initialValue="AgentExpV3">
                  <Input placeholder="请输入集合名称" />
                </Form.Item>
                
                <Form.Item label="检索条数" field="limit" initialValue={5}>
                  <Select placeholder="请选择检索条数">
                    <Select.Option value={3}>3条</Select.Option>
                    <Select.Option value={5}>5条</Select.Option>
                    <Select.Option value={10}>10条</Select.Option>
                    <Select.Option value={20}>20条</Select.Option>
                  </Select>
                </Form.Item>
                
                <Form.Item 
                  label="密集向量权重" 
                  field="dense_weight" 
                  initialValue={0.5}
                  extra="用于控制从知识库中检索知识的策略。取值越低越偏向关键词检索，取值越高越偏向语义检索。取值范围：0.2～1，默认 0.5。"
                >
                  <Slider min={0} max={1} step={0.1} />
                </Form.Item>
              </div>
            </CollapseItem>
            
            <CollapseItem header="过滤条件" name="filter">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Form.Item label="经验类型">
                  <Select 
                    placeholder="请选择经验类型" 
                    mode="multiple"
                    allowClear
                    onChange={(values: string[]) => setTypeFilters(values)}
                  >
                    {experienceTypeOptions.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                
                <Form.Item label="应用类型">
                  <Select 
                    placeholder="请选择应用类型" 
                    mode="multiple"
                    allowClear
                    onChange={(values: string[]) => setApplyTypeFilters(values)}
                  >
                    {applyTypeOptions.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                
                <Form.Item label="发布状态" initialValue={['publish']}>
                  <Select 
                    placeholder="请选择发布状态" 
                    mode="multiple"
                    allowClear
                    onChange={(values: string[]) => setPublishStatusFilters(values)}
                  >
                    {publishStatusOptions.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                
                <Form.Item label="提取人员">
                  <Select 
                    placeholder="请输入提取人员" 
                    mode="tags"
                    allowClear
                    onChange={(values: string[]) => setExtractTypeFilters(values)}
                  >
                  </Select>
                </Form.Item>
              </div>
            </CollapseItem>
          </Collapse>
          
          <div className="flex justify-center">
            <Button type="primary" icon={<IconSearch />} onClick={handleSearch} loading={loading}>
              开始检索
            </Button>
          </div>
        </Form>
      </Card>
      
      {loading ? (
        <div className="flex justify-center my-8">
          <Spin tip="检索中..." />
        </div>
      ) : searchResults.length > 0 && (
        <>
          <Card className="mb-4">
            <div className="flex justify-between items-center mb-4">
              <Title heading={5}>检索结果 ({resultCount}条)</Title>
              <Space>
                <Text>请求ID: {requestId}</Text>
              </Space>
            </div>
            
            {tokenUsage && (
              <div className="mb-4 p-3 bg-gray-50 rounded">
                <Title heading={6}>Token 使用情况</Title>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Text type="secondary">嵌入Token:</Text>
                    <div>Prompt: {tokenUsage.embedding_token_usage.prompt_tokens}</div>
                    <div>Total: {tokenUsage.embedding_token_usage.total_tokens}</div>
                  </div>
                  <div>
                    <Text type="secondary">重排序Token:</Text>
                    <div>{tokenUsage.rerank_token_usage}</div>
                  </div>
                  <div>
                    <Text type="secondary">重写Token:</Text>
                    <div>{tokenUsage.rewrite_token_usage}</div>
                  </div>
                </div>
              </div>
            )}
            
            <Table
              columns={columns}
              data={searchResults}
              expandedRowRender={(record) => {
                const contentField = record.table_chunk_fields.find(field => field.field_name === "experience_content");
                const vectorContentField = record.table_chunk_fields.find(field => field.field_name === "vector_content");
                
                return (
                  <div className="space-y-4">
                    <div>
                      <Text bold>向量内容:</Text>
                      <div className="bg-gray-50 p-3 rounded mt-1">
                        {vectorContentField?.field_value || "-"}
                      </div>
                    </div>
                    <div>
                      <Text bold>经验内容:</Text>
                      <div className="bg-gray-50 p-3 rounded mt-1 whitespace-pre-wrap">
                        {contentField?.field_value || "-"}
                      </div>
                    </div>
                    <div>
                      <Text bold>所有字段:</Text>
                      <div className="bg-gray-50 p-3 rounded mt-1">
                        <Table
                          columns={[
                            { title: "字段名", dataIndex: "field_name" },
                            { title: "字段值", dataIndex: "field_value" }
                          ]}
                          data={record.table_chunk_fields}
                          pagination={false}
                          border={false}
                          size="small"
                        />
                      </div>
                    </div>
                  </div>
                );
              }}
              rowKey="id"
              border={true}
              pagination={false}
              className="mt-4"
            />
          </Card>
        </>
      )}
    </div>
  );
}