"use client";

import React from "react";
import { useSearchParams } from "next/navigation";
import { TraceVisualizer } from "./TraceVisualizer";
import { PageHeader } from "@/app/components/PageHeader";

export default function TaskExecutionPage() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get("sessionId");

  return (
    <div className="h-full flex flex-col">
      <PageHeader 
        title="Trace 经验 / Knowledge 可视化分析" 
        description="可视化展示任务执行过程中的经验和知识"
      />
      <div className="flex-1 overflow-hidden">
        <TraceVisualizer initialSessionId={sessionId} />
      </div>
    </div>
  );
}