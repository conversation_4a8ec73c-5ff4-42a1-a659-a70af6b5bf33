/**
 * @file 定义用于解析AI智能体交互追踪（Trace）数据的TypeScript类型。
 * @description 这套类型适用于分析包含用户消息、AI规划、执行、工具调用和递归思考过程的复杂JSON结构。
 */

/**
 * 经验（Experience）对象，通常用于AI规划阶段作为参考。
 */
export interface Experience {
  idx: string;
  id: string;
  content: string;
  score: number;
  type: string;
}

/**
 * AI智能体的规划（Plan）阶段。
 */
export interface Plan {
  Rationale: string;
  ProgressPlan: string;
  Actor: string;
  Persona: string;
  TaskDescription: string;
  Tools: string[];
  Experiences: Experience[] | null;
  Knowledges: Knowledge[] | null;
}

/**
 * 引用（Reference）对象，指向执行过程中产出的文件或链接。
 */
export interface Reference {
  ID: number;
  Title: string;
  URI: string;
  MetaData: Record<string, any>;
}

/**
 * 工具（Tool）的参数定义。
 */
export interface ToolParameter {
  Name: string;
  Description: string;
  Required?: boolean;
  type?: string;
  // 支持更复杂的参数结构，例如数组或对象
  Parameters?: ToolParameter[];
}

/**
 * AI智能体可用的工具（Tool）的完整定义。
 */
export interface Tool {
  Name: string;
  Description: string;
  Parameters: ToolParameter[];
  Hints: string;
}

/**
 * 知识库（Knowledge）对象，为AI提供背景知识。
 */
export interface Knowledge {
  id: string;
  category: string;
  category_key: string;
  tags: string[] | null;
  title: string;
  pinned: boolean;
  enabled_if: string;
  used_when: string;
  content: string;
}

/**
 * 执行上下文（Context），包含AI执行任务时所需的知识和历史信息。
 */
export interface Context {
  Knowledges: Knowledge[] | null;
  Experiences: Experience[] | null;
  FormerOutput: string;
}

/**
 * AI智能体执行的具体行动（Action）。
 */
export interface Action {
  Tool?: string;
  Parameters?: Record<string, any> | null;
  Results?: any; // `Results` 结构多变，使用 any 类型
  Error?: string | null;
}

/**
 * AI智能体的思考（Think）过程。
 */
export interface Think {
  Rationale: string;
}

/**
 * 递归的执行细节。
 * 这是整个结构的核心，用于描述一个任务或子任务的完整执行过程。
 * @interface ExecutionDetail
 */
export interface ExecutionDetail {
  Task: string;
  Tools: Tool[] | null;
  ExecutionTrace: string;
  Context: Context | null;
  Rounds: ExecutionRound[] | null; // 包含一系列的执行回合
  Summarized: string;
}

/**
 * 单个执行回合，包含一次思考和可选的、更深层次的执行细节。
 * 这是实现递归结构的关键。
 * @interface ExecutionRound
 */
export interface ExecutionRound {
  Think: Think;
  Detail: ExecutionDetail | null; // 递归地引用 ExecutionDetail
  Action: Action;
}

/**
 * AI智能体的执行（Execution）阶段的完整记录。
 */
export interface Execution {
  Evaluation: string; // e.g., "success", "failed"
  Output: string;
  Reference: Reference[] | null;
  Detail: ExecutionDetail;
}

/**
 * 用户的输入消息。
 */
export interface UserMessage {
  id: string;
  content: string;
  from: string;
  mentions: any | null;
  struct_content: Record<string, any>;
  created_at: string; // ISO 8601 timestamp
  attachments: any[];
}

/**
 * 代表AI智能体的一个完整响应回合，包括规划和执行。
 */
export interface Round {
  Plan: Plan;
  Execution: Execution;
}

/**
 * 追踪数据中的一个条目，可以是一个用户消息，也可以是AI的一个响应回合。
 */
export interface TraceEntry {
  UserMessage: UserMessage | null;
  Round: Round | null;
}

/**
 * 整个追踪（Trace）JSON数据的根类型。
 */
export interface Trace {
  data: TraceEntry[];
}