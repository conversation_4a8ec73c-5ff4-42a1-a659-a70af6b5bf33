import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Input, Button, Card, Collapse, Space, Tag, Typography, Spin } from '@arco-design/web-react';
import { fetchTraceWithRetry } from '@/app/experience/extract/utils';
import { Trace, TraceEntry, UserMessage, Round, Plan, Execution, ExecutionDetail, ExecutionRound, Think, Action, Experience, Tool, Knowledge } from './types';

const { Text } = Typography;

interface TraceVisualizerProps {
  model?: string;
  temperature?: number;
  initialSessionId?: string | null;
}

export function TraceVisualizer({initialSessionId }: TraceVisualizerProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [sessionId, setSessionId] = useState('');
  const [traceData, setTraceData] = useState<Trace | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedEntries, setExpandedEntries] = useState<Set<number>>(new Set());
  const [expandedExperiences, setExpandedExperiences] = useState<Set<string>>(new Set());
  const [expandedActions, setExpandedActions] = useState<Set<string>>(new Set());
  const [expandedExecutionRounds, setExpandedExecutionRounds] = useState<Set<string>>(new Set());
  const [sourceIds, setSourceIds] = useState<string[]>([]);

  // 处理初始 sessionId
  useEffect(() => {
    if (initialSessionId && initialSessionId.trim()) {
      setSessionId(initialSessionId.trim());
      // 自动获取 trace 数据
      const fetchInitialTrace = async () => {
        setIsLoading(true);
        setError(null);
        setTraceData(null);

        try {
          const data = await fetchTraceWithRetry(initialSessionId.trim());
          setTraceData(data);
          // 默认展开第一个条目
          const flattenedEntries = flattenTraceEntries(data);
          if (flattenedEntries.length > 0) {
            setExpandedEntries(new Set([0]));
          }
          
          // 提取所有经验的id并获取source_ids
          const experienceIds = extractAllExperienceIds(data);
          await fetchSourceIds(experienceIds);
        } catch (err) {
          setError(`获取 trace 失败: ${(err as Error).message}`);
          console.error('Error fetching trace:', err);
        } finally {
          setIsLoading(false);
        }
      };
      
      fetchInitialTrace();
    }
  }, [initialSessionId]);



  const handleFetchTrace = async () => {
    if (!sessionId.trim()) {
      setError('请输入 Session ID');
      return;
    }

    setIsLoading(true);
    setError(null);
    setTraceData(null);

    try {
      const data = await fetchTraceWithRetry(sessionId.trim());
      setTraceData(data);
      // 默认展开第一个条目
      const flattenedEntries = flattenTraceEntries(data);
      if (flattenedEntries.length > 0) {
        setExpandedEntries(new Set([0]));
      }
      
      // 提取所有经验的id并获取source_ids
      const experienceIds = extractAllExperienceIds(data);
      await fetchSourceIds(experienceIds);
      
      // 更新 URL 参数
      const params = new URLSearchParams(searchParams.toString());
      params.set('sessionId', sessionId.trim());
      router.push(`/experience/visual?${params.toString()}`, { scroll: false });
    } catch (err) {
      setError(`获取 trace 失败: ${(err as Error).message}`);
      console.error('Error fetching trace:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleEntry = (index: number) => {
    const newExpanded = new Set(expandedEntries);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedEntries(newExpanded);
  };

  // 处理来源ID显示的函数
  const renderSourceId = (sourceId: string, isInline: boolean = false) => {
    if (!sourceId) return "-";
    
    // 如果是人工专家经验，不可点击
    if (sourceId === "人工专家经验") {
      return isInline ? sourceId : (
        <div className="max-w-[150px] truncate" title={sourceId}>
          {sourceId}
        </div>
      );
    }
     
    // 其他情况，直接展示source_id并生成跳转链接
    // const url = `https://aime.bytedance.net/chat/${sourceId}`;
    const url = `https://aime.bytedance.net/lab/experience/list?source_id=${sourceId}&size=100`;
    return (
      <div className={isInline ? "inline-flex items-center" : "max-w-[150px] truncate"}>
        <a 
          href={url} 
          target="_blank" 
          rel="noopener noreferrer"
          className="text-blue-600 hover:text-blue-800"
          title={`跳转到: ${url}`}
        >
          {sourceId}
        </a>
      </div>
    );
  };

  // 提取所有经验的id
  const extractAllExperienceIds = (traceData: Trace | null): string[] => {
    if (!traceData || !traceData.data) return [];
    
    const allIds: string[] = [];
    
    traceData.data.forEach(entry => {
      // 从Plan中提取经验id
      if (entry.Round?.Plan?.Experiences) {
        entry.Round.Plan.Experiences.forEach(exp => {
          if (exp.id) {
            allIds.push(exp.id);
          }
        });
      }
      
      // 从Execution的Context中提取经验id
      if (entry.Round?.Execution?.Detail?.Context?.Experiences) {
        entry.Round.Execution.Detail.Context.Experiences.forEach(exp => {
          if (exp.id) {
            allIds.push(exp.id);
          }
        });
      }
      
      // 从progress_think中提取经验id
      if (entry.Round?.Execution) {
        const progressThinkData = extractProgressThinkDataFromExecution(entry.Round.Execution);
        progressThinkData.experiences.forEach(exp => {
          if (exp.id) {
            allIds.push(exp.id);
          }
        });
      }
    });
    
    // 去重
    return [...new Set(allIds)];
  };

  // 获取source_ids的函数
  const fetchSourceIds = async (experienceIds: string[]) => {
    if (experienceIds.length === 0) {
      setSourceIds([]);
      return;
    }
    
    try {
      const allSourceIds: string[] = [];
      
      // 为每个experience id调用接口
      for (const expId of experienceIds) {
        try {
          const response = await fetch(`https://meta-server.bytedance.net/api/experiences/filter?point_id=${expId}&page=1&size=100`);
          if (response.ok) {
            const data = await response.json();
            if (data.data.data && Array.isArray(data.data.data)) {
              data.data.data.forEach((item: any) => {
                if (item.source_id) {
                  allSourceIds.push(item.source_id);
                }
              });
            }
          }
        } catch (error) {
          console.warn(`获取experience ${expId} 的source_id失败:`, error);
        }
      }
      
      // 去重
      const uniqueSourceIds = [...new Set(allSourceIds)];
      setSourceIds(uniqueSourceIds);
    } catch (error) {
      console.error('Error fetching source_ids:', error);
    } 
  };

  const renderUserMessage = (userMessage: UserMessage) => {
    if (!userMessage?.content) return null;
    
    return (
      <Card style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 8 }}>
          <Tag color="blue">用户消息</Tag>
          <Text type="secondary" style={{ marginLeft: 8, fontSize: 12 }}>
            {userMessage.created_at ? new Date(userMessage.created_at).toLocaleString() : ''}
          </Text>
        </div>
        <div style={{ backgroundColor: '#f5f5f5', padding: 12, borderRadius: 4 }}>
          <pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontFamily: 'monospace', maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>
            {userMessage.content}
          </pre>
        </div>
      </Card>
    );
  };

  const renderExperiences = (experiences: Experience[] | null, knowledges: Knowledge[] | null, parentId: string = '', title: string = '经验与知识引用') => {
    if ((!experiences || experiences.length === 0) && (!knowledges || knowledges.length === 0)) return null;
    
    // 合并经验和知识为一个数组
    const combinedItems = [
      ...(experiences || []).map((exp, idx) => ({
        type: 'experience',
        id: `exp-${idx}`,
        data: exp,
        isFromProgressThink: (exp as any).isFromProgressThink || false
      })),
      ...(knowledges || []).map((knowledge, idx) => ({
        type: 'knowledge',
        id: `know-${idx}`,
        data: knowledge,
        isFromProgressThink: (knowledge as any).isFromProgressThink || false
      }))
    ];
    
    const toggleExperience = (index: number) => {
      const key = `${parentId}-${combinedItems[index].id}`;
      const newExpanded = new Set(expandedExperiences);
      if (newExpanded.has(key)) {
        newExpanded.delete(key);
      } else {
        newExpanded.add(key);
      }
      setExpandedExperiences(newExpanded);
    };
    
    return (
      <div style={{ marginBottom: 16 }}>
        <Text>{title}:</Text>
        <div style={{ marginTop: 8 }}>
          {combinedItems.map((item, idx) => {
            const key = `${parentId}-${item.id}`;
            const isExpanded = expandedExperiences.has(key);
            
            if (item.type === 'experience') {
              const exp = item.data as Experience;
              return (
                <Card key={idx} style={{ marginBottom: 8 }} size="small">
                  <div 
                    style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', cursor: 'pointer' }}
                    onClick={() => toggleExperience(idx)}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <Tag color="green">经验</Tag>
                      {item.isFromProgressThink && <Tag color="orange">progress_think</Tag>}
                      <Text>{exp.type || '未知类型'}</Text>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      {exp.id && (
                        <a 
                          href={`/lab/experience/list?point_id=${exp.id}`} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          onClick={(e) => e.stopPropagation()}
                          style={{ 
                            display: 'inline-flex', 
                            alignItems: 'center', 
                            gap: 4,
                            padding: '2px 8px',
                            backgroundColor: '#165DFF',
                            color: 'white',
                            borderRadius: '4px',
                            textDecoration: 'none',
                            fontSize: '12px',
                            fontWeight: 500
                          }}
                        >
                          查看详情
                        </a>
                      )}
                      <Tag color="green">评分: {exp.score || 0}</Tag>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {isExpanded ? '收起' : '展开'}
                      </Text>
                    </div>
                  </div>
                  {isExpanded && exp.content && (
                    <div style={{ marginTop: 8, fontSize: 12 }}>
                      <pre style={{ whiteSpace: 'pre-wrap', margin: 0, maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>{exp.content}</pre>
                    </div>
                  )}
                </Card>
              );
            } else {
              const knowledge = item.data as Knowledge;
              return (
                <Card key={idx} style={{ marginBottom: 8 }} size="small">
                  <div 
                    style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', cursor: 'pointer' }}
                    onClick={() => toggleExperience(idx)}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <Tag color="blue">知识</Tag>
                      {item.isFromProgressThink && <Tag color="orange">progress_think</Tag>}
                      <Text>{knowledge.category || '未知分类'}</Text>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <Tag color="blue">{knowledge.title || '无标题'}</Tag>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {isExpanded ? '收起' : '展开'}
                      </Text>
                    </div>
                  </div>
                  {isExpanded && (
                    <>
                      {knowledge.content && (
                        <div style={{ marginTop: 8, fontSize: 12 }}>
                          <pre style={{ whiteSpace: 'pre-wrap', margin: 0, maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>{knowledge.content}</pre>
                        </div>
                      )}
                      {knowledge.tags && knowledge.tags.length > 0 && (
                        <div style={{ marginTop: 8, fontSize: 12 }}>
                          <Text type="secondary">标签: </Text>
                          {knowledge.tags.map((tag, tagIdx) => (
                            <Tag key={tagIdx} color="cyan" size="small" style={{ marginRight: 4 }}>{tag}</Tag>
                          ))}
                        </div>
                      )}
                    </>
                  )}
                </Card>
              );
            }
          })}
        </div>
      </div>
    );
  };



  const renderPlan = (plan: Plan, knowledges: Knowledge[] | null, parentId: string = '') => {
    if (!plan) return null;
    
    return (
      <Card style={{ marginBottom: 16 }}>
        {plan.Rationale && (
          <div style={{ marginBottom: 12 }}>
            <Text>Planer Think:</Text>
            <div style={{ marginTop: 4, backgroundColor: '#f9f9f9', padding: 8, borderRadius: 4 }}>
              <pre style={{ whiteSpace: 'pre-wrap', margin: 0, maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>{plan.Rationale}</pre>
            </div>
          </div>
        )}
        
        {plan.ProgressPlan && (
          <div style={{ marginBottom: 12 }}>
            <Text>进度计划:</Text>
            <div style={{ marginTop: 4, backgroundColor: '#f9f9f9', padding: 8, borderRadius: 4 }}>
              <pre style={{ whiteSpace: 'pre-wrap', margin: 0, maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>{plan.ProgressPlan}</pre>
            </div>
          </div>
        )}
        
        {/* Planner 的经验与知识引用 */}
        {renderExperiences(plan.Experiences || null, plan.Knowledges || null, `${parentId}-plan`, 'Planner 的经验与知识')}
      </Card>
    );
  };

  const toggleAction = (actionKey: string) => {
    const newExpanded = new Set(expandedActions);
    if (newExpanded.has(actionKey)) {
      newExpanded.delete(actionKey);
    } else {
      newExpanded.add(actionKey);
    }
    setExpandedActions(newExpanded);
  };

  const toggleExecutionRound = (roundKey: string) => {
    const newExpanded = new Set(expandedExecutionRounds);
    if (newExpanded.has(roundKey)) {
      newExpanded.delete(roundKey);
    } else {
      newExpanded.add(roundKey);
    }
    setExpandedExecutionRounds(newExpanded);
  };

  const renderAction = (action: Action, actionKey: string) => {
    if (!action) return "is nulll";
    
    const isExpanded = expandedActions.has(actionKey);
    
    // 生成参数预览，最多100个字符
    let parameterPreview = '';
    if (action.Parameters && Object.keys(action.Parameters).length > 0) {
      try {
        const paramString = JSON.stringify(action.Parameters);
        parameterPreview = paramString.length > 100 ? paramString.substring(0, 100) + '...' : paramString;
      } catch (error) {
        parameterPreview = '参数解析错误';
      }
    }
    
    return (
      <Card style={{ marginBottom: 8 }} size="small">
        <div 
          style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', cursor: 'pointer' }}
          onClick={() => toggleAction(actionKey)}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: 8, flex: 1 }}>
            {action.Tool && (
              <Tag color="orange">{action.Tool}</Tag>
            )}
            {parameterPreview && (
              <Text type="secondary" style={{ fontSize: 12, flex: 1, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                {parameterPreview}
              </Text>
            )}
          </div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {isExpanded ? '收起' : '展开'}
          </Text>
        </div>
        
        {isExpanded && (
          <div style={{ marginTop: 8 }}>
            {action.Parameters && Object.keys(action.Parameters).length > 0 && (
              <div style={{ marginBottom: 8 }}>
                <Text>参数:</Text>
                <div style={{ marginTop: 4, backgroundColor: '#f5f5f5', padding: 8, borderRadius: 4 }}>
                  <pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontSize: 12, maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>
                    {JSON.stringify(action.Parameters, null, 2)}
                  </pre>
                </div>
              </div>
            )}
            
            {action.Error && (
              <div>
                <Text type="error">错误:</Text>
                <div style={{ marginTop: 4, backgroundColor: '#fee', padding: 8, borderRadius: 4 }}>
                  <pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontSize: 12, color: 'red', maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>
                    {action.Error}
                  </pre>
                </div>
              </div>
            )}
            
            {action.Results && (
              <div>
                <Text>结果:</Text>
                <div style={{ marginTop: 4, backgroundColor: '#f0f9ff', padding: 8, borderRadius: 4 }}>
                  <pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontSize: 12, maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>
                    {typeof action.Results === 'string' ? action.Results : JSON.stringify(action.Results, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}
      </Card>
    );
  };

  const renderThink = (think: Think, action: Action, parentId: string = '') => {
    if (!think) return null;
    
    return (
      <Card style={{ marginBottom: 16 }}> 
        {think.Rationale && (
          <div style={{ marginBottom: 12 }}>
            <div style={{ marginTop: 4, backgroundColor: '#f0f9ff', padding: 8, borderRadius: 4 }}>
              <pre style={{ whiteSpace: 'pre-wrap', margin: 0, maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>{think.Rationale}</pre>
            </div>
          </div>
        )}
        
        {action && (
          <div>
            <div style={{ marginTop: 8 }}>
              {renderAction(action, `${parentId}-action`)}
            </div>
          </div>
        )}
      </Card>
    );
  };

  const renderExecutionRound = (round: ExecutionRound, level: number = 0, parentId: string = '') => {
    if (!round) return null;
    
    const indent = level * 32;
    const backgroundColor = level > 0 ? `rgba(250, 250, 250, ${0.1 + level * 0.1})` : 'transparent';
    const borderColor = level > 0 ? '#d0d0d0' : 'transparent';
    const borderWidth = level > 0 ? Math.max(1, 4 - level) : 0;

    
    return (
      <div key={level} style={{ 
        marginLeft: indent, 
        borderLeft: level > 0 ? `${borderWidth}px solid ${borderColor}` : 'none', 
        paddingLeft: level > 0 ? 20 : 0,
        backgroundColor: backgroundColor,
        borderRadius: level > 0 ? 4 : 0,
        marginBottom: level > 0 ? 12 : 0
      }}>
        {renderThink(round.Think, round.Action, parentId)}
        
        {round.Detail && (
          <div style={{ marginTop: 16 }}>
            <Card style={{ 
              marginBottom: 16, 
              backgroundColor: level > 0 ? '#fafafa' : 'white',
              border: level > 0 ? '1px solid #e8e8e8' : 'none'
            }}>
              {round.Detail.Task && (
                <div style={{ marginBottom: 8 }}>
                  <Tag color="purple">子任务详情</Tag>
                  <div style={{ marginTop: 4, backgroundColor: '#f0f9ff', padding: 8, borderRadius: 4 }}><pre style={{ maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>{round.Detail.Task}</pre></div>
                </div>
              )}
              
              {round.Detail.Rounds && round.Detail.Rounds.length > 0 && (
                <div>
                  <div 
                    style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center', 
                      cursor: 'pointer',
                      padding: '8px 0',
                      borderBottom: '1px solid #e8e8e8'
                    }}
                    onClick={() => toggleExecutionRound(parentId)}
                  >
                    <Text style={{ fontWeight: 500 }}>执行过程:</Text>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {expandedExecutionRounds.has(parentId) ? '收起' : '展开'}
                    </Text>
                  </div>
                  {expandedExecutionRounds.has(parentId) && (
                    <div style={{ marginTop: 8 }}>
                      {round.Detail.Rounds.map((subRound, idx) => (
                        <div key={idx} style={{ marginBottom: 8 }}>
                          {renderExecutionRound(subRound, level + 1, `${parentId}-sub-${idx}`)}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </Card>
          </div>
        )}
      </div>
    );
  };

  const renderExecution = (execution: Execution, parentId: string = '') => {
    if (!execution) return null;
    
    const roundCount = execution.Detail?.Rounds?.length || 0;
    
    return (
      <Card style={{ marginBottom: 16 }}>
      
        {execution.Detail?.Task && (
          <div style={{ marginBottom: 12 }}>
            <Text>任务:</Text>
            <div style={{ marginTop: 4, backgroundColor: '#f0f9ff', padding: 8, borderRadius: 4 }}>
              <pre style={{ maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>{execution.Detail.Task}</pre>
            </div>
          </div>
        )}
        
        {/* 执行者的经验与知识引用 */}
        {renderExperiences(execution.Detail?.Context?.Experiences || null, execution.Detail?.Context?.Knowledges || null, `${parentId}-execution`, '执行者的经验与知识')}
        
        {/* {execution.Detail?.ExecutionTrace && (
          <div style={{ marginBottom: 12 }}>
            <Text>执行追踪:</Text>
            <div style={{ marginTop: 4, backgroundColor: '#f9f9f9', padding: 8, borderRadius: 4 }}>
              <pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontSize: 12, maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>
                {execution.Detail.ExecutionTrace}
              </pre>
            </div>
          </div>
        )} */}
        
        {execution.Detail?.Rounds && execution.Detail.Rounds.length > 0 && (
          <div>
            <div 
              style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center', 
                cursor: 'pointer',
                padding: '8px 0',
                borderBottom: '1px solid #e8e8e8'
              }}
              onClick={() => toggleExecutionRound(`${parentId}-execution`)}
            >
              <Text>执行过程:</Text>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {expandedExecutionRounds.has(`${parentId}-execution`) ? '收起' : '展开'}
              </Text>
            </div>
            {expandedExecutionRounds.has(`${parentId}-execution`) && (
              <div style={{ marginTop: 8 }}>
                {execution.Detail.Rounds.map((round, idx) => (
                  <div key={idx} style={{ marginBottom: 16 }}>
                    {renderExecutionRound(round, 0, `${parentId}-round-${idx}`)}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
        
        {execution.Output && (
          <div style={{ marginTop: 12 }}>
            <Text>输出:</Text>
            <div style={{ marginTop: 4, backgroundColor: '#f0f9ff', padding: 8, borderRadius: 4 }}>
              <pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontSize: 12, maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>
                {execution.Output}
              </pre>
            </div>
          </div>
        )}
      </Card>
    );
  };

  const renderRound = (round: Round, parentId: string = '') => {
    if (!round) return null;
    
    // 提取当前round中所有progress_think的数据
    let progressThinkExperiences: Experience[] = [];
    let progressThinkKnowledges: Knowledge[] = [];
    
    if (round.Execution?.Detail?.Rounds) {
      round.Execution.Detail.Rounds.forEach(execRound => {
        const roundData = traverseExecutionRound(execRound);
        progressThinkExperiences = progressThinkExperiences.concat(roundData.experiences);
        progressThinkKnowledges = progressThinkKnowledges.concat(roundData.knowledges);
      });
    }
    
    // 创建增强的Plan对象（保持原有的Plan经验，不添加progress_think的经验）
    const enhancedPlan = {
      ...round.Plan
    };
    
    // 执行者的经验数据（包括原有的Context中的经验和progress_think中的经验）
    // 注意：这里不修改原始数据，而是创建一个新的副本用于渲染
    const executorExperiences = [...(round.Execution?.Detail?.Context?.Experiences || []), ...progressThinkExperiences];
    
    // 执行者的知识数据（包括原有的Context中的知识和progress_think中的知识）
    // 注意：这里不修改原始数据，而是创建一个新的副本用于渲染
    const executorKnowledges = [...(round.Execution?.Detail?.Context?.Knowledges || []), ...progressThinkKnowledges];
    
    // 创建执行Context的副本，避免修改原始数据
    const executionContext = round.Execution?.Detail?.Context ? {
      ...round.Execution.Detail.Context,
      Experiences: executorExperiences,
      Knowledges: executorKnowledges
    } : null;
    
    // 创建执行对象的副本
    const executionCopy = round.Execution ? {
      ...round.Execution,
      Detail: {
        ...round.Execution.Detail,
        Context: executionContext
      }
    } : null;
    
    return (
      <div>
        {renderPlan(enhancedPlan, null, parentId)}
        {executionCopy && renderExecution(executionCopy, parentId)}
      </div>
    );
  };

  // 递归遍历所有Action，提取progress_think中的经验和知识
  const extractProgressThinkData = (action: Action): { experiences: Experience[], knowledges: Knowledge[] } => {
    let experiences: Experience[] = [];
    let knowledges: Knowledge[] = [];
    
    if (!action) return { experiences, knowledges };
    
    // 如果是progress_think工具，尝试解析Results
    if (action.Tool === 'progress_think' && action.Results) {
      try {
        let resultsData;
        if (typeof action.Results === 'string') {
          resultsData = JSON.parse(action.Results);
        } else {
          resultsData = action.Results;
        }
        
        // 解析experience数据
        if (resultsData.experience && Array.isArray(resultsData.experience)) {
          experiences = resultsData.experience.map((exp: any) => ({
            idx: exp.idx || '',
            id: exp.id || '',
            content: exp.content || '',
            score: exp.score || 0,
            type: exp.type || '',
            isFromProgressThink: true
          }));
        }
        
        // 解析knowledge_renders数据
        if (resultsData.knowledge_renders && Array.isArray(resultsData.knowledge_renders)) {
          knowledges = resultsData.knowledge_renders
            .filter((kr: any) => kr.knowledge)
            .map((kr: any) => ({
              id: kr.knowledge.id || '',
              category: kr.knowledge.category || '',
              category_key: kr.knowledge.category_key || '',
              tags: kr.knowledge.tags || [],
              title: kr.knowledge.title || '',
              pinned: kr.knowledge.pinned || false,
              enabled_if: kr.knowledge.enabled_if || '',
              used_when: kr.knowledge.used_when || '',
              content: kr.knowledge.content || '',
              isFromProgressThink: true
            }));
        }
      } catch (error) {
        console.warn('解析progress_think结果失败:', error);
      }
    }
    
    return { experiences, knowledges };
  };
  
  // 递归遍历ExecutionRound中的所有Action
  const traverseExecutionRound = (round: ExecutionRound): { experiences: Experience[], knowledges: Knowledge[] } => {
    let experiences: Experience[] = [];
    let knowledges: Knowledge[] = [];
    
    if (!round) return { experiences, knowledges };
    
    // 处理当前Action
    const actionData = extractProgressThinkData(round.Action);
    experiences = experiences.concat(actionData.experiences);
    knowledges = knowledges.concat(actionData.knowledges);
    
    // 递归处理子任务
    if (round.Detail?.Rounds) {
      round.Detail.Rounds.forEach(subRound => {
        const subData = traverseExecutionRound(subRound);
        experiences = experiences.concat(subData.experiences);
        knowledges = knowledges.concat(subData.knowledges);
      });
    }
    
    return { experiences, knowledges };
  };
  
  // 从Execution中提取所有progress_think的数据
  const extractProgressThinkDataFromExecution = (execution: Execution): { experiences: Experience[], knowledges: Knowledge[] } => {
    let experiences: Experience[] = [];
    let knowledges: Knowledge[] = [];
    
    if (!execution.Detail?.Rounds) return { experiences, knowledges };
    
    // 遍历所有执行回合
    execution.Detail.Rounds.forEach(round => {
      const roundData = traverseExecutionRound(round);
      experiences = experiences.concat(roundData.experiences);
      knowledges = knowledges.concat(roundData.knowledges);
    });
    
    return { experiences, knowledges };
  };
  
  // 从整个entry中提取所有progress_think的数据（保持向后兼容）
  const extractAllProgressThinkData = (entry: TraceEntry): { experiences: Experience[], knowledges: Knowledge[] } => {
    if (!entry.Round?.Execution) return { experiences: [], knowledges: [] };
    return extractProgressThinkDataFromExecution(entry.Round.Execution);
  };

  // 将原始TraceEntry转换为扁平化的条目数组
  const flattenTraceEntries = (traceData: Trace | null): Array<{
    type: 'user' | 'plan' | 'execution';
    data: any;
    originalIndex: number;
    roundIndex?: number;
  }> => {
    if (!traceData || !traceData.data) return [];
    
    const flattenedEntries: Array<{
      type: 'user' | 'plan' | 'execution';
      data: any;
      originalIndex: number;
      roundIndex?: number;
    }> = [];
    
    traceData.data.forEach((entry, originalIndex) => {
      // 添加用户消息
      if (entry.UserMessage) {
        flattenedEntries.push({
          type: 'user',
          data: entry.UserMessage,
          originalIndex
        });
      }
      
      // 添加Plan
      if (entry.Round?.Plan) {
        flattenedEntries.push({
          type: 'plan',
          data: entry.Round.Plan,
          originalIndex,
          roundIndex: originalIndex
        });
      }
      
      // 添加Execution
      if (entry.Round?.Execution) {
        flattenedEntries.push({
          type: 'execution',
          data: entry.Round.Execution,
          originalIndex,
          roundIndex: originalIndex
        });
      }
    });
    
    return flattenedEntries;
  };
  
  // 检查Plan是否有实际内容
  const hasPlanContent = (plan: Plan): boolean => {
    return !!(plan.Rationale || plan.ProgressPlan || 
           (plan.Experiences && plan.Experiences.length > 0) ||
           (plan.Knowledges && plan.Knowledges.length > 0));
  };
  
  // 检查Execution是否有实际内容
  const hasExecutionContent = (execution: Execution): boolean => {
    return !!(execution.Detail?.Task || execution.Output ||
           (execution.Detail?.Context?.Experiences && execution.Detail.Context.Experiences.length > 0) ||
           (execution.Detail?.Context?.Knowledges && execution.Detail.Context.Knowledges.length > 0) ||
           (execution.Detail?.Rounds && execution.Detail.Rounds.length > 0));
  };
  
  const renderTraceEntry = (entry: {
    type: 'user' | 'plan' | 'execution';
    data: any;
    originalIndex: number;
    roundIndex?: number;
  }, index: number) => {
    if (!entry) return null;
    
    const isExpanded = expandedEntries.has(index);
    let title = '';
    let actor = '';
    let tools: string[] = [];
    let roundCount = 0;
    let totalExperiences: Experience[] = [];
    let totalKnowledges: Knowledge[] = [];
    
    // 根据类型设置不同的标题和计算信息
    switch (entry.type) {
      case 'user':
        title = '用户消息';
        break;
      case 'plan':
        title = `Planner 第 ${entry.originalIndex} 轮`;
        actor = entry.data.Actor;
        tools = entry.data.Tools?.slice(0, 15) || [];
        totalExperiences = entry.data.Experiences || [];
        totalKnowledges = entry.data.Knowledges || [];
        break;
      case 'execution':
        title = `执行 第 ${entry.originalIndex} 轮`;
        if (entry.data.Detail?.Rounds) {
          roundCount = entry.data.Detail.Rounds.length;
        }
        // 提取progress_think中的经验和知识
        const progressThinkData = extractProgressThinkDataFromExecution(entry.data);
        const progressThinkExperiences = progressThinkData.experiences;
        const progressThinkKnowledges = progressThinkData.knowledges;
        
        const contextExperiences = entry.data.Detail?.Context?.Experiences || [];
        const contextKnowledges = entry.data.Detail?.Context?.Knowledges || [];
        
        totalExperiences = [...contextExperiences, ...progressThinkExperiences];
        totalKnowledges = [...contextKnowledges, ...progressThinkKnowledges];
        break;
    }
    
    return (
      <div key={index} style={{ marginBottom: 16 }}>
        <Card
          hoverable
          onClick={() => toggleEntry(index)}
          style={{ 
            cursor: 'pointer',
            position: isExpanded ? 'sticky' : 'relative',
            top: isExpanded ? 0 : 'auto',
            zIndex: isExpanded ? 10 : 1
          }}
        >
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 8 }}>
              <Tag color="blue">{title}</Tag>
              
              {actor && (
                <Tag color="purple">{actor}</Tag>
              )}
              
              {roundCount > 0 && (
                <Tag color="blue">总 {roundCount} 轮</Tag>
              )}
              
              {tools.length > 0 && (
                <div style={{ display: 'flex', gap: 4, flexWrap: 'wrap' }}>
                  {tools.map((tool, idx) => (
                    <Tag key={idx} color="orange">{tool}</Tag>
                  ))}
                </div>
              )}

              {totalExperiences.length > 0 && (
                <Tag color="green" style={{ fontSize: 12 }}>使用了{totalExperiences.length}条经验</Tag>
              )}
              {totalKnowledges.length > 0 && (
                <Tag color="blue" style={{ fontSize: 12 }}>使用了{totalKnowledges.length}条知识</Tag>
              )}
              {totalExperiences.length === 0 && totalKnowledges.length === 0 && (
                <Tag color="green" style={{ fontSize: 12 }}>没有使用经验与知识</Tag>
              )}
              
            </div>
            <div>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {isExpanded ? '收起' : '展开'}
              </Text>
            </div>
          </div>
        </Card>
        
        {isExpanded && (
          <div style={{ 
            marginTop: 8,
            marginLeft: 8,
            padding: 16,
            backgroundColor: '#fafafa',
            borderRadius: 8,
            border: '1px solid #e8e8e8',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
          }}>
            {entry.type === 'user' && renderUserMessage(entry.data)}
            {entry.type === 'plan' && renderPlan(entry.data, null, `plan-${entry.originalIndex}`)}
            {entry.type === 'execution' && (() => {
              // 为execution类型创建包含progress_think数据的副本
              const progressThinkData = extractProgressThinkDataFromExecution(entry.data);
              const progressThinkExperiences = progressThinkData.experiences;
              const progressThinkKnowledges = progressThinkData.knowledges;
              
              const contextExperiences = entry.data.Detail?.Context?.Experiences || [];
              const contextKnowledges = entry.data.Detail?.Context?.Knowledges || [];
              
              const totalExperiences = [...contextExperiences, ...progressThinkExperiences];
              const totalKnowledges = [...contextKnowledges, ...progressThinkKnowledges];
              
              // 创建执行Context的副本
              const executionContext = entry.data.Detail?.Context ? {
                ...entry.data.Detail.Context,
                Experiences: totalExperiences,
                Knowledges: totalKnowledges
              } : null;
              
              // 创建执行对象的副本
              const executionCopy = {
                ...entry.data,
                Detail: {
                  ...entry.data.Detail,
                  Context: executionContext
                }
              };
              
              return renderExecution(executionCopy, `execution-${entry.originalIndex}`);
            })()}
          </div>
        )}
      </div>
    );
  };

  return (
    <div style={{ paddingLeft:10, paddingRight:10 }}>
      <div style={{ marginBottom: 24 }}>
        {/* <h1 style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 16 }}>Trace 可视化分析器</h1> */}
        
        <div style={{ display: 'flex', gap: 16, alignItems: 'center', marginBottom: 16 }}>
          <Input
            placeholder="输入 Session ID"
            value={sessionId}
            onChange={setSessionId}
            style={{ flex: 1 }}
            onPressEnter={handleFetchTrace}
          />
          <Button
            type="primary"
            onClick={handleFetchTrace}
            loading={isLoading}
            disabled={!sessionId.trim()}
          >
            {isLoading ? '加载中...' : '获取 Trace'}
          </Button>
        </div>

        {/* 显示所有source_ids */}
        {sourceIds.length > 0 && (
          <Card style={{ marginTop: 16 }}>
            <div style={{ marginBottom: 12 }}>
              <Text >经验来源链接 ({sourceIds.length}个):</Text>
            </div>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
              {sourceIds.map((sourceId, index) => (
                <div key={index} style={{ marginBottom: 8 }}>
                  {renderSourceId(sourceId, true)}
                </div>
              ))}
            </div>
          </Card>
        )}
        
        
        {error && (
          <div style={{ 
            backgroundColor: '#fee', 
            color: 'red', 
            padding: 12, 
            borderRadius: 4,
            marginBottom: 16 
          }}>
            {error}
          </div>
        )}
      </div>
      
      {isLoading && (
        <div style={{ textAlign: 'center', padding: 48 }}>
          <Spin  />
          <div style={{ marginTop: 16 }}>正在获取 Trace 数据...</div>
        </div>
      )}
      
      {traceData && (
        <div>
          {/* <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h2 style={{ fontSize: 18, fontWeight: 'bold' }}>Trace 数据</h2>
            <Text type="secondary">共 {traceData.data.length} 个条目</Text>
          </div>
           */}
          {traceData.data.length === 0 ? (
            <Card>
              <div style={{ textAlign: 'center', padding: 24, color: '#999' }}>
                暂无数据
              </div>
            </Card>
          ) : (
            <div style={{ maxHeight: 'calc(100vh - 50px)', overflowY: 'auto' }}>
              {flattenTraceEntries(traceData)
                .filter(entry => {
                  // 过滤掉空内容的条目
                  if (entry.type === 'user') return true; // 用户消息总是显示
                  if (entry.type === 'plan') return hasPlanContent(entry.data);
                  if (entry.type === 'execution') return hasExecutionContent(entry.data);
                  return false;
                })
                .map((entry, index) => renderTraceEntry(entry, index))}
            </div>
          )}
        </div>
      )}
      
      {!traceData && !isLoading && !error && (
        <Card>
          <div style={{ textAlign: 'center', padding: 48, color: '#999' }}>
            请输入 Session ID 并点击"获取 Trace"按钮开始分析
          </div>
        </Card>
      )}
    </div>
  );
}