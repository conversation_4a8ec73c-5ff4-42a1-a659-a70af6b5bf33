"use client";

import React, { useState, useMemo, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { 
  Table, 
  Button, 
  Input, 
  Form, 
  Grid, 
  Select, 
  Space,
  Tag,
  Typography,
  Message,
  DatePicker,
  Card
} from "@arco-design/web-react";
import { 
  IconEye, 
  IconSearch,
} from "@arco-design/web-react/icon";
import { useQuery } from "@tanstack/react-query";
import { useStore } from "@nanostores/react";
import { toast } from "sonner";
import { PageHeader } from "@/app/components/PageHeader";
import { 
  filterExperiences,
  FilterExperienceParams,
  experienceTypeOptions,
  applyTypeOptions,
  publishStatusOptions,
  Experience,
  variantOptions
} from "../api/experience";
import { apiClient } from "@/app/api/request";

const { Text } = Typography;
const RangePicker = DatePicker.RangePicker;

// 扩展搜索参数接口
interface SearchParams {
  keyword?: string;
  type?: string;
  apply_type?: string;
  publish_status?: string;
  extract_type?: string;
  source_id?: string;
  variant?: string;
  point_id?: string;
  date_range?: [string, string];
}

// 日期格式转换函数
const formatDateForServer = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toISOString().split('T')[0]; // 格式化为 yyyy-MM-dd
};

// 处理来源ID显示的函数
const renderSourceId = (sourceId: string, isInline: boolean = false) => {
  if (!sourceId) return "-";
  
  // 如果是人工专家经验，不可点击
  if (sourceId === "人工专家经验") {
    return isInline ? sourceId : (
      <div className="max-w-[150px] truncate" title={sourceId}>
        {sourceId}
      </div>
    );
  }
  
  // 如果是goodId开头的格式
  if (sourceId.startsWith("goodId_")) {
    const parts = sourceId.split("_");
    if (parts.length == 4 && parts[0] === "goodId" && parts[2] === "badId") {
      const goodId = parts[1];
      const badId = parts[3];
      const goodUrl = `https://aime.bytedance.net/chat/${goodId}`;
      const badUrl = `https://aime.bytedance.net/chat/${badId}`;
      
      return (
        <div className={isInline ? "inline-flex items-center" : "max-w-[150px] truncate"}>
          <a 
            href={goodUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 mr-1"
            title={`跳转到goodId: ${goodUrl}`}
          >
            goodId: {goodId}
          </a>
          <span className="text-gray-400 mx-1">|</span>
          <a 
            href={badUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800"
            title={`跳转到badId: ${badUrl}`}
          >
            badId: {badId}
          </a>
        </div>
      );
    }
  }
  
  // 其他情况，直接展示source_id并生成跳转链接
  const url = `https://aime.bytedance.net/chat/${sourceId}`;
  return (
    <div className={isInline ? "inline-flex items-center" : "max-w-[150px] truncate"}>
      <a 
        href={url} 
        target="_blank" 
        rel="noopener noreferrer"
        className="text-blue-600 hover:text-blue-800"
        title={`跳转到: ${url}`}
      >
        {sourceId}
      </a>
    </div>
  );
};

export default function ExperienceAnaysisPage() {
  const urlSearchParams = useSearchParams();
  const [form] = Form.useForm();
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(200);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState('');
  const [showAnalysisConfig, setShowAnalysisConfig] = useState(false);
  const [selectedExperiences, setSelectedExperiences] = useState<Experience[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);
  const [systemPrompt, setSystemPrompt] = useState('');
  const [userMessage, setUserMessage] = useState('');
  const [isPromptExpanded, setIsPromptExpanded] = useState(false);
  const [isMessageExpanded, setIsMessageExpanded] = useState(false);

  // 从URL参数初始化搜索条件
  useEffect(() => {
    const params: SearchParams = {};
    
    // 读取URL参数
    const keyword = urlSearchParams.get('keyword');
    const type = urlSearchParams.get('type');
    const apply_type = urlSearchParams.get('apply_type');
    const publish_status = urlSearchParams.get('publish_status');
    const extract_type = urlSearchParams.get('extract_type');
    const source_id = urlSearchParams.get('source_id');
    const variant = urlSearchParams.get('variant');
    const point_id = urlSearchParams.get('point_id');
    const start_date = urlSearchParams.get('start_date');
    const end_date = urlSearchParams.get('end_date');
    const page = urlSearchParams.get('page');
    const size = urlSearchParams.get('size');
    
    // 设置参数
    if (keyword) params.keyword = keyword;
    if (type) params.type = type;
    if (apply_type) params.apply_type = apply_type;
    if (publish_status) params.publish_status = publish_status;
    if (extract_type) params.extract_type = extract_type;
    if (source_id) params.source_id = source_id;
    if (variant) params.variant = variant;
    if (point_id) params.point_id = point_id;
    if (start_date && end_date) params.date_range = [start_date, end_date];
    if (page) setCurrentPage(parseInt(page));
    if (size) setPageSize(parseInt(size));
    
    // 更新状态和表单
    setSearchParams(params);
    form.setFieldsValue(params);
  }, [urlSearchParams, form]);

  // 加载 system prompt
  useEffect(() => {
    const fetchPrompt = async () => {
      try {
       
        const promptUrl = `https://tosv-cn.byted.org/obj/ttclient-android/aime/exp_anayisis.md?_t=${Date.now()}`;
        const response = await fetch(promptUrl, {
          method: 'GET',
          cache: 'no-store'
        });
        const text = await response.text();
        setSystemPrompt(text);
      } catch (error) {
        console.error('Error fetching system prompt:', error);
      }
    };

    fetchPrompt();
  }, []);

  // 查询经验列表
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["experienceAnalysisList", searchParams, currentPage, pageSize],
    queryFn: async () => {
      try {
        // 构建查询参数
        const filterParams: FilterExperienceParams = {
          page: currentPage,
          size: pageSize,
        };

        // 添加各种筛选条件
        if (searchParams.keyword) {
          filterParams.keyword = searchParams.keyword;
        }
        if (searchParams.type) {
          filterParams.type = searchParams.type;
        }
        if (searchParams.apply_type) {
          filterParams.applyType = searchParams.apply_type;
        }
        if (searchParams.publish_status) {
          filterParams.publishStatus = searchParams.publish_status;
        }
        if (searchParams.extract_type) {
          filterParams.extractType = searchParams.extract_type;
        }
        if (searchParams.source_id) {
          filterParams.sourceId = searchParams.source_id;
        }
        if (searchParams.variant) {
          filterParams.variant = searchParams.variant;
        }
        if (searchParams.point_id) {
          filterParams.pointId = searchParams.point_id;
        }
        if (searchParams.date_range && searchParams.date_range[0] && searchParams.date_range[1]) {
          filterParams.startDate = formatDateForServer(searchParams.date_range[0]);
          filterParams.endDate = formatDateForServer(searchParams.date_range[1]);
        }

        const response = await filterExperiences(filterParams);
        return response.data;
      } catch (error) {
        console.error("获取经验列表失败:", error);
        toast.error("获取经验列表失败");
        return { total: 0, page: 1, size: pageSize, data: [] };
      }
    },
  });

  // 表格数据
  const tableData = useMemo(() => {
    if (!data || !data.data) return [];
    return data.data.map((item: Experience) => ({
      ...item,
      key: item.id || '',
    }));
  }, [data]);

  // 表格列定义
  const columns = [
    {
      title: "标题",
      dataIndex: "title",
      width: 300,
      render: (col: string) => (
        <div className="max-w-[300px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "来源ID",
      dataIndex: "source_id",
      width: 150,
      render: (col: string) => renderSourceId(col),
    },
    {
      title: "类型",
      dataIndex: "type",
      width: 150,
      render: (col: string) => {
        const option = experienceTypeOptions.find(opt => opt.value === col);
        return <Tag color="blue">{option?.label || col}</Tag>;
      },
    },
    {
      title: "应用类型",
      dataIndex: "apply_type",
      width: 120,
      render: (col: string) => {
        const option = applyTypeOptions.find(opt => opt.value === col);
        return option?.label || col;
      },
    },
    {
      title: "发布状态",
      dataIndex: "publish_status",
      width: 100,
      render: (col: string) => {
        const option = publishStatusOptions.find(opt => opt.value === col);
        return <Tag color={col === 'publish' ? 'green' : 'orange'}>{option?.label || col}</Tag>;
      },
    },
    {
      title: "提取类型",
      dataIndex: "extract_type",
      width: 100,
      render: (col: string) => {
        if(col.includes("llm")){
          return "模型提取";
        }else{
          return "人工";
        }
      },
    },
    {
      title: "创建时间",
      dataIndex: "create_time",
      width: 180,
      render: (col: string) => col ? new Date(col).toLocaleString() : "-",
    },
    {
      title: "更新时间",
      dataIndex: "update_time",
      width: 180,
      render: (col: string) => col ? new Date(col).toLocaleString() : "-",
    },
    {
      title: "Variant",
      dataIndex: "variant",
      width: 100,
      render: (col: string) => {
        if (!col) return "-";
        const option = variantOptions.find(opt => opt.value === col);
        return option?.label || col;
      },
    },
  ];

  // 更新URL参数的函数
  const updateURLParams = (params: SearchParams, page: number, size: number) => {
    const urlParams = new URLSearchParams();
    
    // 添加搜索参数
    if (params.keyword) urlParams.set('keyword', params.keyword);
    if (params.type) urlParams.set('type', params.type);
    if (params.apply_type) urlParams.set('apply_type', params.apply_type);
    if (params.publish_status) urlParams.set('publish_status', params.publish_status);
    if (params.extract_type) urlParams.set('extract_type', params.extract_type);
    if (params.source_id) urlParams.set('source_id', params.source_id);
    if (params.variant) urlParams.set('variant', params.variant);
    if (params.point_id) urlParams.set('point_id', params.point_id);
    if (params.date_range && params.date_range[0] && params.date_range[1]) {
      urlParams.set('start_date', params.date_range[0]);
      urlParams.set('end_date', params.date_range[1]);
    }
    
    // 添加分页参数
    if (page !== 1) urlParams.set('page', page.toString());
    if (size !== 10) urlParams.set('size', size.toString());
    
    // 更新URL
    const newURL = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
    window.history.replaceState({}, '', newURL);
  };

  // 搜索表单变更
  const handleSearch = () => {
    const values = form.getFieldsValue();
    setSearchParams(values);
    setCurrentPage(1);
    updateURLParams(values, 1, pageSize);
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setSearchParams({});
    setCurrentPage(1);
    updateURLParams({}, 1, pageSize);
  };

  // 分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateURLParams(searchParams, page, pageSize);
  };

  // 每页条数变化
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
    updateURLParams(searchParams, 1, size);
  };

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: (string | number)[], selectedRows: Experience[]) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedExperiences(selectedRows);
    }
  };

  // 拼接经验内容
  const concatenateExperiences = (experiences: Experience[]): string => {
    return experiences.map((exp, index) => {
      return `经验 ${index + 1}:
标题: ${exp.title}
类型: ${exp.type}
应用类型: ${exp.apply_type}
经验内容: ${exp.experience_content}
向量内容: ${exp.vector_content}
提取原因: ${exp.ext || '无'}
来源ID: ${exp.source_id}
创建时间: ${exp.create_time}
---`;
    }).join('\n\n');
  };

  // 获取分析用的system prompt
  const getAnalysisSystemPrompt = (): string => {
    return systemPrompt;
  };

  // 显示分析配置区域
  const handleAnalyze = () => {
    if (selectedExperiences.length === 0) {
      Message.warning('请至少选择一条经验进行分析');
      return;
    }

    // 初始化system prompt和user message
    const systemPromptContent = getAnalysisSystemPrompt();
    const concatenatedContent = concatenateExperiences(selectedExperiences);
    const userMessageContent = `请分析以下经验：\n\n${concatenatedContent}`;
    
    setSystemPrompt(systemPromptContent);
    setUserMessage(userMessageContent);
    setAnalysisResult('');
    setShowAnalysisConfig(true);
  };

  // 执行实际的分析
  const executeAnalysis = async () => {
    handleAnalyze();
    if (!systemPrompt.trim() || !userMessage.trim()) {
      Message.warning('System Prompt 和 User Message 都不能为空');
      return;
    }

    setIsAnalyzing(true);
    setAnalysisResult('');

    try {
      // 调用模型进行分析
      const res = await apiClient.ChatStream({
        model: 'gemini-2.5-pro',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userMessage },
        ],
        temperature: 0.7,
        max_tokens: 64000
      });
      
      const content = (res as any)?.choices?.[0]?.message?.content ?? '';
      setAnalysisResult(content);
      
    } catch (error) {
      console.error('经验分析失败:', error);
      setAnalysisResult('分析过程中出现错误，请稍后重试');
      toast.error('经验分析失败');
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <PageHeader 
        title="经验分析" 
        description="分析已入库经验"
      />
      
      <Form layout="horizontal" className="mb-4 flex-shrink-0" form={form}>
        <Grid.Row gutter={16}>
          <Grid.Col span={6}>
            <Form.Item label="关键词" field="keyword">
              <Input placeholder="请输入关键词" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={5}>
            <Form.Item label="日期" field="date_range">
              <RangePicker format="YYYY-MM-DD" />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={5}>
            <Form.Item label="类型" field="type">
              <Select placeholder="请选择经验类型" allowClear allowCreate>
                {experienceTypeOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4}>
            <Form.Item label="应用" field="apply_type">
              <Select placeholder="请选择应用类型" allowClear allowCreate>
                {applyTypeOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4}>
            <Form.Item label="状态" field="publish_status">
              <Select placeholder="请选择发布状态" allowClear>
                {publishStatusOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
        </Grid.Row>
        <Grid.Row gutter={16} className="mt-4">
          <Grid.Col span={5}>
            <Form.Item label="提取" field="extract_type">
              <Select placeholder="请输入提取人员" allowClear mode="tags">
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={5}>
            <Form.Item label="来源" field="source_id">
              <Input placeholder="请输入来源ID" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4}>
            <Form.Item label="ID" field="point_id">
              <Input placeholder="请输入经验ID" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="Variant" field="variant">
              <Select placeholder="请选择Variant" allowClear>
                {variantOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4} className="flex justify-end items-end">
            <Space>
              <Button type="primary" icon={<IconSearch />} onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Grid.Col>
        </Grid.Row>
      </Form>

      <div className="flex justify-between mb-4 flex-shrink-0">
        <div>
          <Space>
            <Button 
              type="primary" 
              icon={<IconEye />}
              onClick={handleAnalyze}
              disabled={selectedRowKeys.length === 0}
              loading={isAnalyzing}
            >
              分析选中经验
            </Button>
          </Space>
        </div>
        <div>
          <Text>已选择 {selectedRowKeys.length} 条经验</Text>
        </div>
      </div>

      <div className="flex-shrink-0" style={{ height: '400px', overflow: 'hidden' }}>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          data={tableData}
          loading={isLoading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: data?.total || 0,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            sizeOptions: [10, 20, 50, 100, 200]
          }}
          scroll={{ x: 1500, y: 300 }}
          border
        />
      </div>

      {/* 分析配置和结果区域 */}
      {showAnalysisConfig && (
        <Card className="mt-4 flex-shrink-0">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">
              {analysisResult ? "经验分析结果" : "经验分析配置"}
            </h2>
            <Space>
              <Button onClick={() => setShowAnalysisConfig(false)}>
                关闭
              </Button>
              {!analysisResult && (
                <Button 
                  type="primary" 
                  onClick={executeAnalysis}
                  loading={isAnalyzing}
                >
                  开始分析
                </Button>
              )}
            </Space>
          </div>
          
          {!analysisResult ? (
            <div className="analysis-config">
              <div className="mb-4">
                <h3 className="text-md font-medium mb-2">System Prompt</h3>
                <textarea
                  className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
                  value={systemPrompt}
                  onChange={(e) => setSystemPrompt(e.target.value)}
                  rows={isPromptExpanded ? 20 : 6}
                  style={{ maxHeight: '300px', overflowY: 'auto' }}
                />
                <Button 
                  type="text" 
                  size="small" 
                  onClick={() => setIsPromptExpanded(!isPromptExpanded)}
                  className="mt-2"
                >
                  {isPromptExpanded ? '收起' : '展开'}
                </Button>
              </div>
              
              <div className="mb-4">
                <h3 className="text-md font-medium mb-2">User Message</h3>
                <textarea
                  className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
                  value={userMessage}
                  onChange={(e) => setUserMessage(e.target.value)}
                  rows={isMessageExpanded ? 20 : 6}
                  style={{ maxHeight: '300px', overflowY: 'auto' }}
                />
                <Button 
                  type="text" 
                  size="small" 
                  onClick={() => setIsMessageExpanded(!isMessageExpanded)}
                  className="mt-2"
                >
                  {isMessageExpanded ? '收起' : '展开'}
                </Button>
              </div>
            </div>
          ) : (
            <div className="analysis-result">
              <div className="mb-4">
                <h3 className="text-md font-medium mb-2">分析结果</h3>
                {/* <div className="bg-gray-50 p-4 rounded-md whitespace-pre-wrap text-sm overflow-y-auto"> */}
                  <pre className="whitespace-pre-wrap bg-gray-100 p-2 rounded mt-2">
                   {analysisResult}
                  </pre>
                {/* </div> */}
              </div>
            </div>
          )}
        </Card>
      )}
    </div>
  );
}