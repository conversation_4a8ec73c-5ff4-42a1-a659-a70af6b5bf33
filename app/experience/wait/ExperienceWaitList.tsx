"use client";

import React, { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { 
  Table, 
  Button, 
  Input, 
  Form, 
  Grid, 
  Select, 
  Space,
  Tag,
  Modal,
  Typography,
  Message,
  DatePicker,
  Badge
} from "@arco-design/web-react";
import { 
  IconDelete, 
  IconEye, 
  IconSearch,
  IconRefresh,
  IconPlayArrow,
  IconPlayCircle,
  IconSettings
} from "@arco-design/web-react/icon";
import { useQuery } from "@tanstack/react-query";
import { useStore } from "@nanostores/react";
import { toast } from "sonner";
import ConfirmDeleteDialog from "../components/ConfirmDeleteDialog";
import { userAtom } from "../../store/auth";
import { 
  getExperiencesWait,
  deleteExperienceWait, 
  batchDeleteExperiencesWait,
  processExperienceWait,
  processNextExperienceWait,
  processAllExperiencesWait,
  quickProcessExperienceWait,
  hasUnprocessedExperiences,
  getLLMCallRecords,
  statusOptions,
  ExperienceWait
} from "../api/experienceWait";
import { applyTypeOptions, experienceTypeOptions } from "../api/experience";
import { variantOptions } from "../api/experience";

const { Text } = Typography;

// 搜索参数接口
interface SearchParams {
  keyword?: string;
  type?: string;
  apply_type?: string;
  status?: string[];  // 修改为数组类型以支持多状态筛选
  extract_type?: string | string[];
  source_id?: string;
  date_range?: [string, string];
}

// Tab类型定义
type TabType = "pending" | "archived";

// 添加日期范围选择器组件
const RangePicker = DatePicker.RangePicker;

// 日期格式转换函数
const formatDateForServer = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toISOString().split('T')[0]; // 格式化为 yyyy-MM-dd
};

// 处理来源ID显示的函数
const renderSourceId = (sourceId: string, isInline: boolean = false) => {
  if (!sourceId) return "-";
  
  // 如果是人工专家经验，不可点击
  if (sourceId === "人工专家经验") {
    return isInline ? sourceId : (
      <div className="max-w-[150px] truncate" title={sourceId}>
        {sourceId}
      </div>
    );
  }
  
  // 如果是goodId开头的格式
  if (sourceId.startsWith("goodId_")) {
    const parts = sourceId.split("_");
    if (parts.length == 4 && parts[0] === "goodId" && parts[2] === "badId") {
      const goodId = parts[1];
      const badId = parts[3];
      const goodUrl = `https://aime.bytedance.net/chat/${goodId}`;
      const badUrl = `https://aime.bytedance.net/chat/${badId}`;
      
      return (
        <div className={isInline ? "inline-flex items-center" : "max-w-[150px] truncate"}>
          <a 
            href={goodUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 mr-1"
            title={`跳转到goodId: ${goodUrl}`}
          >
            goodId: {goodId}
          </a>
          <span className="text-gray-400 mx-1">|</span>
          <a 
            href={badUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800"
            title={`跳转到badId: ${badUrl}`}
          >
            badId: {badId}
          </a>
        </div>
      );
    }
  }
  
  // 其他情况，直接展示source_id并生成跳转链接
  const url = `https://aime.bytedance.net/chat/${sourceId}`;
  return (
    <div className={isInline ? "inline-flex items-center" : "max-w-[150px] truncate"}>
      <a 
        href={url} 
        target="_blank" 
        rel="noopener noreferrer"
        className="text-blue-600 hover:text-blue-800"
        title={`跳转到: ${url}`}
      >
        {sourceId}
      </a>
    </div>
  );
};

export function ExperienceWaitList() {
  const router = useRouter();
  const [form] = Form.useForm();
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null);
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedExperience, setSelectedExperience] = useState<ExperienceWait | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);
  const [batchDeleteLoading, setBatchDeleteLoading] = useState(false);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [processingAll, setProcessingAll] = useState(false);
  const [processingNext, setProcessingNext] = useState(false);
  const [quickProcessingId, setQuickProcessingId] = useState<string | null>(null);
  const [quickProcessConfirmVisible, setQuickProcessConfirmVisible] = useState(false);
  const [quickProcessItemId, setQuickProcessItemId] = useState<string | null>(null);
  const [isReprocess, setIsReprocess] = useState(false); // 新增状态，用于区分是快速入库还是重新入库
  const [isMyOnly, setIsMyOnly] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>("pending");
  const [modelCallModalVisible, setModelCallModalVisible] = useState(false);
  const [modelCallData, setModelCallData] = useState<any>(null);
  const [modelCallLoading, setModelCallLoading] = useState(false);
  const user = useStore(userAtom);

  // 根据Tab页设置不同的状态筛选
  const getStatusByTab = (tab: TabType): string[] => {
    switch (tab) {
      case "pending":
        return ["create", "running"];
      case "archived":
        return ["废弃", "更新", "新增"];
      default:
        return [];
    }
  };

  // 获取当前Tab的搜索参数
  const getCurrentSearchParams = useMemo(() => {
    return {
      ...searchParams,
      status: getStatusByTab(activeTab)
    };
  }, [searchParams, activeTab]);

  // 查询待入库经验列表
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["experienceWaitList", currentPage, pageSize, getCurrentSearchParams],
    queryFn: async () => {
      try {
        // 构建查询参数
        const queryParams: any = {
          page: currentPage,
          size: pageSize,
        };

        // 添加筛选参数
        if (getCurrentSearchParams.keyword) queryParams.keyword = getCurrentSearchParams.keyword;
        if (getCurrentSearchParams.type) queryParams.type = getCurrentSearchParams.type;
        if (getCurrentSearchParams.apply_type) queryParams.applyType = getCurrentSearchParams.apply_type;
        // 修改状态参数处理，支持多个状态
        if (getCurrentSearchParams.status && getCurrentSearchParams.status.length > 0) {
          queryParams.status = getCurrentSearchParams.status.join(',');
        }
        if (getCurrentSearchParams.extract_type) {
          // 处理 extract_type 可能是数组的情况
          if (Array.isArray(getCurrentSearchParams.extract_type)) {
            // 如果是数组，取第一个值（服务端目前只支持单个值）
            if (getCurrentSearchParams.extract_type.length > 0) {
              queryParams.extractType = getCurrentSearchParams.extract_type[0];
            }
          } else {
            queryParams.extractType = getCurrentSearchParams.extract_type;
          }
        }
        if (getCurrentSearchParams.source_id) queryParams.sourceId = getCurrentSearchParams.source_id;
        
        // 处理日期范围
        if (getCurrentSearchParams.date_range && getCurrentSearchParams.date_range.length === 2) {
          queryParams.startDate = formatDateForServer(getCurrentSearchParams.date_range[0]);
          queryParams.endDate = formatDateForServer(getCurrentSearchParams.date_range[1]);
        }

        const response = await getExperiencesWait(queryParams);
        return response.data;
      } catch (error) {
        console.error("获取待入库经验列表失败:", error);
        toast.error("获取待入库经验列表失败");
        return { total: 0, page: 1, size: pageSize, data: [] };
      }
    },
  });

  // 查询未处理经验统计
  const { data: unprocessedData, refetch: refetchUnprocessed } = useQuery({
    queryKey: ["unprocessedExperiences"],
    queryFn: async () => {
      try {
        const response = await hasUnprocessedExperiences();
        return response.data;
      } catch (error) {
        console.error("获取未处理经验统计失败:", error);
        return { hasUnprocessed: false, unprocessedCount: 0 };
      }
    },
    refetchInterval: 30000, // 每30秒刷新一次
  });

  // 表格数据
  const tableData = useMemo(() => {
    if (!data || !data.data) return [];
    
    return data.data.map((item: ExperienceWait) => ({
      ...item,
      key: item.id || '',
    }));
  }, [data]);

  // 表格列定义
  const columns = [
    {
      title: "标题",
      dataIndex: "title",
      width: 250,
      render: (col: string) => (
        <div className="max-w-[250px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "来源ID",
      dataIndex: "source_id",
      width: 150,
      render: (col: string) => renderSourceId(col),
    },
    {
      title: "类型",
      dataIndex: "type",
      width: 150,
      render: (col: string) => {
        const option = experienceTypeOptions.find(opt => opt.value === col);
        return <Tag color="blue">{option?.label || col}</Tag>;
      },
    },
    {
      title: "应用类型",
      dataIndex: "apply_type",
      width: 120,
      render: (col: string) => {
        const option = applyTypeOptions.find(opt => opt.value === col);
        return option?.label || col;
      },
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 100,
      render: (col: string) => {
        const option = statusOptions.find(opt => opt.value === col);
        const getColor = (status: string) => {
          switch (status) {
            case 'create': return 'blue';
            case 'running': return 'orange';
            case '废弃': return 'red';
            case '更新': return 'purple';
            case '新增': return 'green';
            default: return 'gray';
          }
        };
        return <Tag color={getColor(col)}>{option?.label || col}</Tag>;
      },
    },
    {
      title: "提取类型",
      dataIndex: "source_id",
      width: 100,
      render: (col: string) => {
        if (col?.includes("人工")) {
          return "人工专家";
        } else {
          return "大模型";
        }
      },
    },
    {
      title: "目标经验ID",
      dataIndex: "target_experience_id",
      width: 150,
      render: (col: string) => (
        <div className="max-w-[150px] truncate" title={col}>
          {col || "-"}
        </div>
      ),
    },
    {
      title: "检查结果",
      dataIndex: "check_result",
      width: 120,
      render: (col: string) => {
        if (!col) return "-";
        return (
          <div className="max-w-[120px] truncate" title={col}>
            {col}
          </div>
        );
      },
    },
    {
      title: "创建时间",
      dataIndex: "create_time",
      width: 180,
      render: (col: string) => col ? new Date(col).toLocaleString() : "-",
    },
    {
      title: "更新时间",
      dataIndex: "update_time",
      width: 180,
      render: (col: string) => col ? new Date(col).toLocaleString() : "-",
    },
    {
      title: "Variant",
      dataIndex: "variant",
      width: 100,
      render: (col: string) => {
        if (!col) return "-";
        const option = variantOptions.find(opt => opt.value === col);
        return option?.label || col;
      },
    },
    // {
    //   title: "Limit",
    //   dataIndex: "limit",
    //   width: 80,
    //   render: (col: number) => col ? col : "-",
    // },
  ];

  // 搜索表单变更
  const handleSearch = () => {
    const values = form.getFieldsValue();
    setSearchParams(values);
    setCurrentPage(1);
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setSearchParams({});
    setCurrentPage(1);
  };

  // Tab切换处理
  const handleTabChange = (tab: string) => {
    setActiveTab(tab as TabType);
    setCurrentPage(1);
    setSelectedRowKeys([]);
  };

  // 切换只看我的/全部
  const handleToggleMyOnly = () => {
    if (!isMyOnly) {
      // 切换到"只看我的"
      if (user?.username) {
        const newSearchParams = {
          ...searchParams,
          extract_type: [user.username]
        };
        form.setFieldValue('extract_type', [user.username]);
        setSearchParams(newSearchParams);
        setCurrentPage(1);
        setIsMyOnly(true);
      }
    } else {
      // 切换到"全部"
      const newSearchParams = {
        ...searchParams,
        extract_type: undefined
      };
      form.setFieldValue('extract_type', []);
      setSearchParams(newSearchParams);
      setCurrentPage(1);
      setIsMyOnly(false);
    }
  };

  // 处理查看详情
  const handleViewDetail = (item: ExperienceWait) => {
    setSelectedExperience(item);
    setDetailModalVisible(true);
  };

  // 处理删除按钮点击
  const handleDelete = (id: string) => {
    setDeleteItemId(id);
    setDeleteConfirmVisible(true);
  };

  // 添加批量删除处理函数
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      Message.warning('请至少选择一条记录');
      return;
    }
    setDeleteConfirmVisible(true);
  };

  // 修改确认删除函数，支持批量删除
  const confirmDelete = async () => {
    if (deleteItemId) {
      // 单条删除
      setDeleteLoading(true);
      try {
        const response = await deleteExperienceWait(deleteItemId);
        if (response.code === 200) {
          toast.success("删除成功");
          refetch();
        } else {
          toast.error(response.msg || "删除失败");
        }
      } catch (error) {
        console.error("删除待入库经验失败:", error);
        toast.error("删除待入库经验失败");
      } finally {
        setDeleteLoading(false);
        setDeleteConfirmVisible(false);
        setDeleteItemId(null);
      }
    } else if (selectedRowKeys.length > 0) {
      // 批量删除
      setBatchDeleteLoading(true);
      try {
        const response = await batchDeleteExperiencesWait(selectedRowKeys.map(key => String(key)));
        if (response.code === 200) {
          const successCount = Object.values(response.data || {}).filter(Boolean).length;
          toast.success(`批量删除成功，成功删除${successCount}条记录`);
          setSelectedRowKeys([]);
          refetch();
        } else {
          toast.error(response.msg || "批量删除失败");
        }
      } catch (error) {
        console.error("批量删除待入库经验失败:", error);
        toast.error("批量删除待入库经验失败");
      } finally {
        setBatchDeleteLoading(false);
        setDeleteConfirmVisible(false);
      }
    }
  };

  // 处理单个经验
  const handleProcess = async (id: string) => {
    setProcessingId(id);
    try {
      const response = await processExperienceWait(id);
      if (response.code === 200) {
        toast.success("处理成功");
        refetchUnprocessed();
      } else {
        toast.error(response.msg || "处理失败");
      }
    } catch (error) {
      console.error("处理经验失败:", error);
      toast.error("处理经验失败");
    } finally {
      refetch();
      setProcessingId(null);
    }
  };

  // 处理下一个经验
  const handleProcessNext = async () => {
    setProcessingNext(true);
    try {
      const response = await processNextExperienceWait();
      if (response.code === 200) {
        toast.success("处理下一个经验成功");
        refetch();
        refetchUnprocessed();
      } else {
        toast.error(response.msg || "处理失败");
      }
    } catch (error) {
      console.error("处理下一个经验失败:", error);
      toast.error("处理下一个经验失败");
    } finally {
      setProcessingNext(false);
    }
  };

  // 处理快速入库按钮点击
  const handleQuickProcess = (id: string, isReprocessOperation = false) => {
    setQuickProcessItemId(id);
    setIsReprocess(isReprocessOperation);
    setQuickProcessConfirmVisible(true);
  };

  // 确认快速处理
  const confirmQuickProcess = async () => {
    if (!quickProcessItemId) return;
    
    setQuickProcessingId(quickProcessItemId);
    try {
      const response = await quickProcessExperienceWait(quickProcessItemId);
      if (response.code === 200) {
        toast.success(isReprocess ? "重新入库成功" : "快速入库成功");
        refetch();
        refetchUnprocessed();
      } else {
        toast.error(response.msg || (isReprocess ? "重新入库失败" : "快速入库失败"));
      }
    } catch (error) {
      console.error(isReprocess ? "重新入库失败:" : "快速入库失败:", error);
      toast.error(isReprocess ? "重新入库失败" : "快速入库失败");
    } finally {
      setQuickProcessingId(null);
      setQuickProcessConfirmVisible(false);
      setQuickProcessItemId(null);
      setIsReprocess(false); // 重置isReprocess状态
    }
  };

  // 查看模型调用记录
  const handleViewModelCall = async (id: string) => {
    setModelCallLoading(true);
    try {
      const response = await getLLMCallRecords(id);
      if (response.code === 200) {
        setModelCallData(response.data);
        setModelCallModalVisible(true);
      } else {
        toast.error(response.msg || "获取模型调用记录失败");
      }
    } catch (error) {
      console.error("获取模型调用记录失败:", error);
      toast.error("获取模型调用记录失败");
    } finally {
      setModelCallLoading(false);
    }
  };

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: (string | number)[], selectedRows: ExperienceWait[]) => {
      setSelectedRowKeys(selectedRowKeys);
    }
  };

  // 分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 每页条数变化
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  // 渲染操作按钮区域
  const renderActionButtons = () => {
    if (activeTab === "pending") {
      return (
        <div>
          <Space>
            {unprocessedData && unprocessedData.hasUnprocessed && (
              <Badge count={unprocessedData.unprocessedCount}>
                <Button 
                  type="primary" 
                  icon={<IconPlayArrow />} 
                  onClick={handleProcessNext}
                  loading={processingNext}
                >
                  处理下一个
                </Button>
              </Badge>
            )}
            <Button icon={<IconRefresh />} onClick={() => { refetch(); refetchUnprocessed(); }}>
              刷新
            </Button>
          </Space>
        </div>
      );
    } else {
      return (
        <div>
          <Space>
            <Button icon={<IconRefresh />} onClick={() => refetch()}>
              刷新
            </Button>
          </Space>
        </div>
      );
    }
  };

  // 渲染操作列
  const renderOperationColumn = () => {
    return {
      title: "操作",
      dataIndex: "operation",
      fixed: "right" as const,
      width: 120,
      render: (_: unknown, record: ExperienceWait) => (
        <Space wrap size={[4, 2]}>
          <Button
            type="text"
            size="small"
            icon={<IconEye />}
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          {activeTab === "archived" && (
            <>
              <Button
                type="text"
                size="small"
                icon={<IconSettings />}
                onClick={() => handleViewModelCall(record.id!)}
                loading={modelCallLoading}
              >
                模型调用
              </Button>
              <Button
                type="text"
                size="small"
                status="success"
                icon={<IconPlayArrow />}
                onClick={() => handleQuickProcess(record.id!, true)}
                loading={quickProcessingId === record.id}
              >
                重新入库
              </Button>
            </>
          )}
          {activeTab === "pending" && record.status === 'create' && (
            <>
              <Button
                type="text"
                status="success"
                size="small"
                icon={<IconSettings />}
                onClick={() => handleProcess(record.id!)}
                loading={processingId === record.id}
              >
                处理
              </Button>
              <Button
                type="text"
                size="small"
                status="danger"
                icon={<IconPlayArrow />}
                onClick={() => handleQuickProcess(record.id!)}
                loading={quickProcessingId === record.id}
              >
                快速入库
              </Button>
            </>
          )}
          <Button
            type="text"
            status="danger"
            size="small"
            icon={<IconDelete />}
            onClick={() => handleDelete(record.id!)}
          >
            删除
          </Button>
        </Space>
      ),
    };
  };

  return (
    <div className="h-full flex flex-col">
      <Form layout="horizontal" className="mb-4 flex-shrink-0" form={form}>
        <Grid.Row gutter={16}>
          <Grid.Col span={6}>
            <Form.Item label="关键词" field="keyword">
              <Input placeholder="请输入关键词" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="日期" field="date_range">
              <RangePicker format="YYYY-MM-DD" />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="类型" field="type" >
              <Select placeholder="请选择经验类型" allowClear allowCreate>
                {experienceTypeOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="应用" field="apply_type">
              <Select placeholder="请选择应用类型" allowClear allowCreate>
                {applyTypeOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
        </Grid.Row>
        <Grid.Row gutter={16} className="mt-4">
          <Grid.Col span={6}>
            <Form.Item label="提取" field="extract_type">
              <Select placeholder="请输入提取人员" allowClear mode="tags">
                
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="来源ID" field="source_id">
              <Input placeholder="请输入来源ID" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={12} className="flex justify-end items-end">
            <Space>
              <Button type="primary" icon={<IconSearch />} onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Grid.Col>
        </Grid.Row>
      </Form>

      {/* Tab 导航 */}
      <div className="mb-4 flex-shrink-0">
        <div className="grid w-full grid-cols-2 bg-gray-100 rounded-lg p-1">
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center justify-center cursor-pointer ${
              activeTab === "pending"
                ? "bg-white text-blue-600 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
            onClick={() => handleTabChange("pending")}
          >
            待入库
            {unprocessedData && unprocessedData.hasUnprocessed && activeTab === "pending" && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                {unprocessedData.unprocessedCount}
              </span>
            )}
          </button>
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors cursor-pointer ${
              activeTab === "archived"
                ? "bg-white text-blue-600 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
            onClick={() => handleTabChange("archived")}
          >
            历史归档
          </button>
        </div>
      </div>

      <div className="flex justify-between mb-4 flex-shrink-0">
        {renderActionButtons()}
        <div>
          <Space>
            <Button onClick={handleToggleMyOnly}>
              {isMyOnly ? '全部' : '只看我的'}
            </Button>
            <Button 
              type="primary" 
              status="danger" 
              icon={<IconDelete />} 
              onClick={handleBatchDelete}
              disabled={selectedRowKeys.length === 0}
            >
              批量删除
            </Button>
          </Space>
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        <Table
          rowSelection={rowSelection}
          columns={[...columns, renderOperationColumn()]}
          data={tableData}
          loading={isLoading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: data?.total || 0,
            showTotal: (total) => `共 ${total} 条`,
            onChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
          }}
          scroll={{ x: 1860, y: 'calc(100vh - 380px)' }}
        />
      </div>

      {/* 删除确认对话框 */}
      <ConfirmDeleteDialog
        open={deleteConfirmVisible}
        onOpenChange={setDeleteConfirmVisible}
        title="确认删除待入库经验"
        description="确定要删除这个待入库经验吗？此操作无法撤销。"
        onConfirm={confirmDelete}
        loading={deleteLoading || batchDeleteLoading}
      />

      {/* 快速入库/重新入库确认对话框 */}
      <Modal
        title={isReprocess ? "确认重新入库" : "确认快速入库"}
        visible={quickProcessConfirmVisible}
        onCancel={() => setQuickProcessConfirmVisible(false)}
        onOk={confirmQuickProcess}
        confirmLoading={quickProcessingId !== null}
        okText={isReprocess ? "确认重新入库" : "确认入库"}
        cancelText="取消"
        okButtonProps={{ status: "danger" }}
      >
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-yellow-600">
            <IconPlayArrow />
            <Text className="font-medium">{isReprocess ? "重新入库警告" : "快速入库警告"}</Text>
          </div>
          <div className="bg-yellow-50 p-4 rounded border border-yellow-200">
            <Text className="text-gray-700">
              {isReprocess 
                ? "重新入库将跳过模型判断，直接将历史归档经验重新标记为新增状态并入库。"
                : "快速入库将跳过模型判断，直接将经验标记为新增状态并入库。"}
            </Text>
            <br />
            <Text className="text-red-600 font-medium">
              ⚠️ 注意：此操作可能会导致重复经验入库，请慎重考虑！
            </Text>
          </div>
          <Text className="text-gray-600">
            {isReprocess 
              ? "确定要重新入库此经验吗？此操作无法撤销。"
              : "确定要快速入库此经验吗？此操作无法撤销。"}
          </Text>
        </div>
      </Modal>

      {/* 模型调用记录模态框 */}
      <Modal
        title="模型调用记录"
        visible={modelCallModalVisible}
        onCancel={() => setModelCallModalVisible(false)}
        footer={null}
        style={{ width: 1000 }}
      >
        {modelCallData ? (
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded border border-blue-200">
              <div className="flex items-center gap-4 mb-2">
                <Text className="font-bold">经验ID：</Text>
                <Text className="font-mono text-sm">{modelCallData.experience_wait_id}</Text>
              </div>
              <div className="flex items-center gap-4">
                <Text className="font-bold">总调用次数：</Text>
                <Badge count={modelCallData.total_calls} />
              </div>
            </div>
            
            {modelCallData.records && modelCallData.records.length > 0 ? (
              <div className="space-y-4">
                <Text className="font-bold text-lg">调用记录详情：</Text>
                {modelCallData.records.map((record: any, index: number) => (
                  <div key={record.id} className="border border-gray-200 rounded p-4">
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <Text className="font-bold text-sm text-gray-600">记录ID：</Text>
                        <Text className="font-mono text-xs">{record.id}</Text>
                      </div>
                      <div>
                        <Text className="font-bold text-sm text-gray-600">模型名称：</Text>
                        <Tag color="blue">{record.model_name}</Tag>
                      </div>
                      <div>
                        <Text className="font-bold text-sm text-gray-600">调用类型：</Text>
                        <Tag color="green">{record.call_type}</Tag>
                      </div>
                      <div>
                        <Text className="font-bold text-sm text-gray-600">状态：</Text>
                        <Tag color={record.status === 'success' ? 'green' : 'red'}>
                          {record.status === 'success' ? '成功' : '失败'}
                        </Tag>
                      </div>
                      <div>
                        <Text className="font-bold text-sm text-gray-600">调用耗时：</Text>
                        <Text>{record.call_duration_ms ? `${record.call_duration_ms}ms` : '-'}</Text>
                      </div>
                      <div>
                        <Text className="font-bold text-sm text-gray-600">创建时间：</Text>
                        <Text className="text-xs">{record.create_time ? new Date(record.create_time).toLocaleString() : '-'}</Text>
                      </div>
                    </div>
                    
                    {record.system_prompt && (
                      <div className="mb-4">
                        <Text className="font-bold text-sm text-gray-600 mb-2 block">系统提示词：</Text>
                        <div className="bg-gray-50 p-3 rounded border max-h-40 overflow-y-auto break-words">
                          <Text className="text-xs whitespace-pre-wrap break-words">{record.system_prompt}</Text>
                        </div>
                      </div>
                    )}
                    
                    {record.user_query && (
                      <div className="mb-4">
                        <Text className="font-bold text-sm text-gray-600 mb-2 block">用户查询：</Text>
                        <div className="bg-gray-50 p-3 rounded border max-h-40 overflow-y-auto break-words">
                          <Text className="text-xs whitespace-pre-wrap break-words">{record.user_query}</Text>
                        </div>
                      </div>
                    )}
                    
                    {record.llm_response && (
                      <div className="mb-4">
                        <Text className="font-bold text-sm text-gray-600 mb-2 block">模型响应：</Text>
                        <div className="bg-green-50 p-3 rounded border max-h-40 overflow-y-auto break-words">
                          <Text className="text-xs whitespace-pre-wrap break-words">{record.llm_response}</Text>
                        </div>
                      </div>
                    )}
                    
                    {record.error_message && (
                      <div className="mb-4">
                        <Text className="font-bold text-sm text-gray-600 mb-2 block">错误信息：</Text>
                        <div className="bg-red-50 p-3 rounded border break-words">
                          <Text className="text-xs text-red-600 whitespace-pre-wrap break-words">{record.error_message}</Text>
                        </div>
                      </div>
                    )}
                    
                    {(record.request_tokens || record.response_tokens || record.total_tokens) && (
                      <div className="grid grid-cols-3 gap-4 text-center bg-gray-50 p-3 rounded">
                        <div>
                          <Text className="font-bold text-sm text-gray-600">请求Token：</Text>
                          <Text>{record.request_tokens || '-'}</Text>
                        </div>
                        <div>
                          <Text className="font-bold text-sm text-gray-600">响应Token：</Text>
                          <Text>{record.response_tokens || '-'}</Text>
                        </div>
                        <div>
                          <Text className="font-bold text-sm text-gray-600">总Token：</Text>
                          <Text>{record.total_tokens || '-'}</Text>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Text className="text-gray-500">暂无调用记录</Text>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <Text className="text-gray-500">暂无数据</Text>
          </div>
        )}
      </Modal>

      {/* 经验详情模态框 */}
      <Modal
        title="待入库经验详情"
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        style={{ width: '80%', maxWidth: 1200 }}
      >
        {selectedExperience && (
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded mb-4">
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">标题：</Text>
                <Text>{selectedExperience.title}</Text>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">类型：</Text>
                <Tag color="blue">
                  {experienceTypeOptions.find(opt => opt.value === selectedExperience.type)?.label || selectedExperience.type}
                </Tag>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">应用类型：</Text>
                <Text>
                  {applyTypeOptions.find(opt => opt.value === selectedExperience.apply_type)?.label || selectedExperience.apply_type}
                </Text>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">状态：</Text>
                <Tag color={
                  selectedExperience.status === 'create' ? 'blue' :
                  selectedExperience.status === 'running' ? 'orange' :
                  selectedExperience.status === '废弃' ? 'red' :
                  selectedExperience.status === '更新' ? 'purple' :
                  selectedExperience.status === '新增' ? 'green' : 'gray'
                }>
                  {statusOptions.find(opt => opt.value === selectedExperience.status)?.label || selectedExperience.status}
                </Tag>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">提取类型：</Text>
                <Text>
                { selectedExperience.extract_type}
                </Text>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">向量内容：</Text>
                <Text>{selectedExperience.vector_content}</Text>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">来源ID：</Text>
                {renderSourceId(selectedExperience.source_id, true)}
              </div>
              {selectedExperience.target_experience_id && (
                <div className="flex items-center gap-2 mb-2">
                  <Text className="font-bold">目标经验ID：</Text>
                  <Text>{selectedExperience.target_experience_id}</Text>
                </div>
              )}
              {selectedExperience.check_result && (
                <div className="flex items-center gap-2 mb-2">
                  <Text className="font-bold">检查结果：</Text>
                  <Text>{selectedExperience.check_result}</Text>
                </div>
              )}
              {selectedExperience.check_reason && (
                <div className="flex items-center gap-2 mb-2">
                  <Text className="font-bold">检查原因：</Text>
                  <Text>{selectedExperience.check_reason}</Text>
                </div>
              )}
              {selectedExperience.variant && (
                <div className="flex items-center gap-2 mb-2">
                  <Text className="font-bold">Variant：</Text>
                  <Text>{variantOptions.find(opt => opt.value === selectedExperience.variant)?.label || selectedExperience.variant}</Text>
                </div>
              )}
              {selectedExperience.limit && (
                <div className="flex items-center gap-2 mb-2">
                  <Text className="font-bold">Limit：</Text>
                  <Text>{selectedExperience.limit}</Text>
                </div>
              )}
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">创建时间：</Text>
                <Text>{selectedExperience.create_time ? new Date(selectedExperience.create_time).toLocaleString() : "-"}</Text>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Text className="font-bold">更新时间：</Text>
                <Text>{selectedExperience.update_time ? new Date(selectedExperience.update_time).toLocaleString() : "-"}</Text>
              </div>
            </div>

            <div>
              <Text className="font-bold text-lg">经验内容：</Text>
              <div className="bg-gray-50 p-4 rounded mt-2 whitespace-pre-wrap">
                {selectedExperience.experience_content}
              </div>
            </div>

             {selectedExperience.ext && (
                <div className="mb-4">
                  <Text className="font-bold text-lg">提取原因：</Text>
                  <div className="bg-gray-50 p-4 rounded mt-2 whitespace-pre-wrap">
                    {selectedExperience.ext}
                  </div>
                </div>
              )}
          </div>
        )}
      </Modal>
    </div>
  );
}