"use client";

import React from "react";
import { PageHeader } from "@/app/components/PageHeader";
import { ExperienceWaitList } from "./ExperienceWaitList";
import { ExperienceUpdateList } from "./ExperienceUpdateList";
import { Tabs } from "@arco-design/web-react";

const TabPane = Tabs.TabPane;

export default function ExperienceWaitPage() {
  return (
    <div className="h-full flex flex-col">
      <PageHeader 
        title="待入库经验管理" 
        description="管理和处理等待入库的经验数据，点击处理，会有 AI 自动判断当前经验是否支持入经验库 更新/新增/废弃"
      />
      <div className="flex-1 overflow-hidden">
        <Tabs defaultActiveTab="1">
          <TabPane key="1" title="待处理经验">
            <ExperienceWaitList />
          </TabPane>
          <TabPane key="2" title="待更新经验【需审核】">
            <ExperienceUpdateList />
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
}