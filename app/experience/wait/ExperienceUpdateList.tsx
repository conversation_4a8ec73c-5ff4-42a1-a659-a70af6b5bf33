"use client";

import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  Table, 
  Button, 
  Space,
  Modal,
  Typography,
  Message,
  Card,
  Grid,
  Spin,
  Tag
} from "@arco-design/web-react";
import { IconRefresh } from "@arco-design/web-react/icon";
import { toast } from "sonner";
import { 
  getPendingUpdates,
  approveUpdate,
  rejectUpdate,
  ExperienceUpdate,
  ExperienceWait,
  getExperienceWaitById
} from "../api/experienceWait";
// @ts-ignore
import ReactDiffViewer from 'react-diff-viewer';
import { ColumnProps } from "@arco-design/web-react/es/Table";

const { Title, Text } = Typography;

export function ExperienceUpdateList() {
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedUpdate, setSelectedUpdate] = useState<ExperienceUpdate | null>(null);

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["pendingUpdates", currentPage, pageSize],
    queryFn: async () => {
      try {
        const response = await getPendingUpdates({ page: currentPage, size: pageSize });
        if (response.code === 200) {
          return response.data;
        }
        toast.error(response.msg || "获取待更新列表失败");
        return { updates: [], total: 0, page: 1, size: 10 };
      } catch (error) {
        console.error("获取待更新列表失败:", error);
        toast.error("获取待更新列表失败");
        return { updates: [], total: 0, page: 1, size: 10 };
      }
    },
  });

  const { 
    data: newExperience, 
    isLoading: isLoadingNewExperience,
    error: newExperienceError,
  } = useQuery({
    queryKey: ["experienceWait", selectedUpdate?.experienceWaitId],
    queryFn: async () => {
      if (!selectedUpdate?.experienceWaitId) return null;
      const response = await getExperienceWaitById(selectedUpdate.experienceWaitId);
      if (response.code === 200) {
        return response.data;
      }
      throw new Error(response.msg || "获取新增经验详情失败");
    },
    enabled: !!selectedUpdate,
    retry: false,
  });

  React.useEffect(() => {
    if (newExperienceError) {
      toast.error((newExperienceError as Error).message);
    }
  }, [newExperienceError]);

  const handleModalClose = () => {
    setDetailModalVisible(false);
    setSelectedUpdate(null);
  };

  const approveMutation = useMutation({
    mutationFn: approveUpdate,
    onSuccess: () => {
      toast.success("批准成功");
      queryClient.invalidateQueries({ queryKey: ["pendingUpdates"] });
      handleModalClose();
    },
    onError: (error) => {
      toast.error("批准失败");
      console.error("批准更新失败:", error);
    },
  });

  const rejectMutation = useMutation({
    mutationFn: rejectUpdate,
    onSuccess: () => {
      toast.success("拒绝成功");
      queryClient.invalidateQueries({ queryKey: ["pendingUpdates"] });
      handleModalClose();
    },
    onError: (error) => {
      toast.error("拒绝失败");
      console.error("拒绝更新失败:", error);
    },
  });

  const handleApprove = (id: string) => {
    approveMutation.mutate(id);
  };

  const handleReject = (id: string) => {
    rejectMutation.mutate(id);
  };

  const handleViewDetail = (record: ExperienceUpdate) => {
    setSelectedUpdate(record);
    setDetailModalVisible(true);
  };

  const columns: ColumnProps<ExperienceUpdate>[] = [
    {
      title: "更新标题",
      dataIndex: "updatedTitle",
      width: 300,
      render: (col: string) => (
        <div className="max-w-[300px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "原标题",
      dataIndex: "originalExperience",
      width: 300,
      render: (col: ExperienceWait) => (
        <div className="max-w-[300px] truncate" title={col.title}>
          {col.title}
        </div>
      ),
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 100,
      render: (col: string) => <Tag color="orange">{col}</Tag>,
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      width: 180,
      render: (col: string) => new Date(col).toLocaleString(),
    },
    {
      title: "操作",
      dataIndex: "operation",
      fixed: "right" as const,
      width: 120,
      render: (_: unknown, record: ExperienceUpdate) => (
        <Button
          type="text"
          size="small"
          onClick={() => handleViewDetail(record)}
        >
          查看对比
        </Button>
      ),
    },
  ];

  const tableData = useMemo(() => {
    if (!data || !data.updates) return [];
    return data.updates.map((item: ExperienceUpdate) => ({
      ...item,
      key: item.id,
    }));
  }, [data]);

  return (
    <div className="h-full flex flex-col p-4">
      <div className="flex justify-between items-center mb-4">
        <Title heading={5} style={{ margin: 0 }}>待更新经验列表</Title>
        <Button icon={<IconRefresh />} onClick={() => refetch()} type="primary">
          刷新
        </Button>
      </div>
      <Table
        loading={isLoading}
        columns={columns}
        data={tableData}
        pagination={{
          total: data?.total,
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, pageSize) => {
            setCurrentPage(page);
            setPageSize(pageSize);
          },
        }}
      />
      <Modal
        title="经验更新对比"
        visible={detailModalVisible}
        onCancel={handleModalClose}
        footer={
          <Space>
            <Button onClick={handleModalClose}>取消</Button>
            <Button
              type="primary"
              status="danger"
              onClick={() => selectedUpdate && handleReject(selectedUpdate.id)}
              loading={rejectMutation.isPending}
            >
              拒绝
            </Button>
            <Button
              type="primary"
              onClick={() => selectedUpdate && handleApprove(selectedUpdate.id)}
              loading={approveMutation.isPending}
            >
              批准
            </Button>
          </Space>
        }
        style={{ width: '80%', maxWidth: '80%' }}
      >
        {selectedUpdate ? (
          <Spin loading={approveMutation.isPending || rejectMutation.isPending} style={{ width: '100%' }}>
            <Grid.Row gutter={16}>
              <Grid.Col span={8}>
                <Card title="经验库中被选定的原始经验">
                  <Title heading={6}>{selectedUpdate.originalExperience.title}</Title>
                  <pre style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}>
                    {selectedUpdate.originalExperience.experience_content}
                  </pre>
                </Card>
              </Grid.Col>
              <Grid.Col span={8}>
                <Card title="新增经验">
                  {isLoadingNewExperience ? (
                    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <Spin />
                    </div>
                  ) : (
                    <>
                      <Title heading={6}>{newExperience?.title}</Title>
                      <pre style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}>
                        {newExperience?.experience_content}
                      </pre>
                    </>
                  )}
                </Card>
              </Grid.Col>
              <Grid.Col span={8}>
                <Card title="更新后经验">
                  <Title heading={6}>{selectedUpdate.updatedTitle}</Title>
                   <pre style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}>
                    {selectedUpdate.updatedContent}
                  </pre>
                </Card>
              </Grid.Col>
            </Grid.Row>
            <Card title="内容对比" style={{ marginTop: 16 }}>
              <ReactDiffViewer
                oldValue={selectedUpdate.originalExperience.experience_content}
                newValue={selectedUpdate.updatedContent}
                splitView={true}
                leftTitle="原始版本"
                rightTitle="更新版本"
              />
            </Card>
          </Spin>
        ) : (
          <Spin />
        )}
      </Modal>
    </div>
  );
}