import { parseJwt } from "@bytecloud/common-lib";
import { getJwt, getJwtWithoutRedirect } from "./user";
import { apiClient } from "./request";
import { UserRole } from "../bam/aime/namespaces/session";

/**
 * 验证用户认证和权限状态
 * @returns 认证结果对象，包含认证状态、权限状态和消息
 */
export async function verifyAuth() {
  try {
    // 1. 获取token（不触发重定向）
    const token = await getJwtWithoutRedirect();
    
    // 2. 检查token是否存在
    if (!token) {
      return createAuthResult(false, false, "未认证");
    }

    // 3. 验证用户权限
    return await checkUserPermission();
  } catch (error) {
    console.error("认证验证失败:", error);
    return createAuthResult(false, false, "认证验证失败");
  }
}

/**
 * 检查已登录用户的权限
 * @returns 认证结果
 */
async function checkUserPermission() {
  try {
    // 调用API获取用户角色
    const roles = await apiClient.GetUserRoles();
    
    const hasAimoPermission = roles.roles.some(role => role.includes("Aimo"));
    // 根据角色判断权限 - 有任一权限都可以访问
    if (!hasAimoPermission) {
      return createAuthResult(true, false, "您已登录但没有访问权限");
    }
    
    return createAuthResult(true, true, "认证成功");
  } catch (error) {
    console.error("权限验证失败:", error);
    // API调用失败时，用户仍视为已登录但无权限
    return createAuthResult(true, false, "权限验证失败");
  }
}

/**
 * 检查用户是否有特定权限
 * @param permission 权限名称
 * @returns 是否有权限
 */
export async function hasSpecificPermission(permission: UserRole): Promise<boolean> {
  try {
    const roles = await apiClient.GetUserRoles();
    return roles.roles.includes(permission as UserRole);
  } catch (error) {
    console.error("权限检查失败:", error);
    return false;
  }
}

/**
 * 创建认证结果对象
 */
function createAuthResult(
  authenticated: boolean, 
  hasPermission: boolean, 
  message: string
) {
  return { authenticated, hasPermission, message };
}

// 获取当前用户信息
export async function getCurrentUser() {
  try {
    const token = await getJwt();
    
    if (!token) {
      return null;
    }
    const user = parseJwt(token)
    if (!user) return null
    
    return {
      username: user.username,
      avatarUrl: user.avatar_url,
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
    return null;
  }
} 