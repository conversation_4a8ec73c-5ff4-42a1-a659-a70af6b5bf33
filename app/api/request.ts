import { fetchEventSource, FetchEventSourceInit } from "@microsoft/fetch-event-source";
import AimeService from "../bam/aime";
import { getJwt } from "./user";
import { urlMap, knowledgeUrlMap } from "./const";
import { getEnv } from "./env";

type fetchParams = Parameters<typeof fetch>
export async function fetchRequest(...args: fetchParams) {
  const response = await fetch(...args)
  if (response.ok) {
    const contentType = response.headers.get('Content-Type')
    if (contentType?.includes('application/json')) {
      return await response.json()
    }
    return response
  }
  let errorMessage = `Request failed with status: ${response.status} ${response.statusText}`
  try {
    const errorData = await response.json()
    errorMessage = `code: ${errorData.code || 'unknown'} message: ${errorData.message || errorData.msg || '未知错误'}`
  } catch (e) {
    console.debug(`parse error: ${e}`)
  }

  throw new Error(errorMessage)
}


function getBaseURL() {
  const env = getEnv()
  return urlMap[env]
}

export const apiClient = new AimeService<FetchEventSourceInit>({
  baseURL: getBaseURL(),
  request:  async ({ url, method, data, params, headers }, options) => {
    const targetUrl = new URL(url)
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined) targetUrl.searchParams.append(key, params[key])
      })
    }
    const jwt = getJwt()

    if (headers?.['Content-Type'] === 'multipart/form-data') {
      delete headers['Content-Type'] // if set Content-Type to multipart/form-data, it always missing boundary
      return fetchRequest(targetUrl, {
        method,
        headers: {
          ...headers,
          Authorization: `Byte-Cloud-JWT ${await jwt}`,
        },
        body: data,
        credentials: 'include',
        mode: 'cors',
      })
    }

    const req = new Request(targetUrl, {
      method,
      headers: {
        ...headers,
        Authorization: `Byte-Cloud-JWT ${await jwt}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      credentials: 'include',
      mode: 'cors',
    })

    if (options?.onmessage) {
      return fetchEventSource(req, {
        headers: {
          ...headers,
          Authorization: `Byte-Cloud-JWT ${await jwt}`,
        },
        signal: options.signal,
        openWhenHidden: true,
        onmessage: options.onmessage,
      })
    }

    return fetchRequest(req);
  },
});
