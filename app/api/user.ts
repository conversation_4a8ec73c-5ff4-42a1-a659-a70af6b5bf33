import { bytecloudAsyncJwtService } from '@bytecloud/common-lib'
import { redirectInProgressAtom } from '../store/auth'

function getDomainID() {
  if (process.env.NODE_ENV === 'development') return 'sandbox'
  if (typeof window === 'undefined') return 'online'
  if (window.location.host.includes('-boe')) return 'boe'
  if (window.location.host.includes('tiktok-row')) return 'i18n'

  return 'online'
}
const domainID = getDomainID()

export async function getJwtWithoutRedirect() {
  const service = await bytecloudAsyncJwtService.getService(domainID)
  const cached = await service.getCachedJwt()
  if (!cached) {
    const res = await service.pureFetchJwt()
    if (res.status === 401) {
      return undefined
    }
    const token = res.headers.get('X-Jwt-Token')

    return token
  }
  return cached
}

export async function redirectToLogin() {
  if (typeof window === 'undefined') return
  
  // 设置重定向状态为true
  try {
    // 如果redirectInProgressAtom不可用，不影响重定向功能
    redirectInProgressAtom.set(true)
  } catch (e) {
    console.error('无法设置重定向状态', e)
  }
  
  const service = await bytecloudAsyncJwtService.getService(domainID)
  service.redirectToLogin()
}

export async function getJwt() {
  if (typeof window === 'undefined') return ''
  return bytecloudAsyncJwtService.getJwt(domainID)
}

