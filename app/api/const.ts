export enum Envs {
  BOE = "boe",
  ONLINE = "online",
  DEV = "dev",
  I18N = "i18n",
}

export const urlMap: Record<Envs, string> = {
  [Envs.BOE]: "https://aime-boe.bytedance.net",
  [Envs.ONLINE]: "https://aime.bytedance.net",
  [Envs.I18N]: "https://aime.tiktok-row.net",
  [Envs.DEV]: "http://127.0.0.1:6789",
};

export const knowledgeUrlMap: Record<Envs, string> = {
  [Envs.BOE]: "https://meta-server.bytedance.net",
  [Envs.ONLINE]: "https://meta-server.bytedance.net",
  [Envs.I18N]: "https:// xxx",
  [Envs.DEV]: "http://localhost:8600",
};

export const ARGOS_BOE_URL =
  "https://cloud-boe.bytedance.net/argos";
export const ARGOS_ONLINE_URL =
  "https://cloud.bytedance.net/argos";
export const ARGOS_I18N_URL =
  "https://cloud-i18n.bytedance.net/argos";

