import { Envs } from "./const";

export const getEnv = () => {
  if (typeof window === "undefined") {
    return Envs.ONLINE;
  }

//  if (
//    location.hostname.includes("-boe") ||
//    location.hostname.includes("localhost") ||
//    location.hostname.includes("127.0.0.1")
//  ) {
//    const localDebug = localStorage.getItem("aimo_debug");
//    if (localDebug) {
//      return Envs.DEV;
//    }
//    return Envs.BOE;
//  }

  if (location.hostname.includes("tiktok-row")) {
    return Envs.I18N;
  }
  return Envs.ONLINE;
};

export const isBoe = () => {
  return getEnv() === Envs.BOE;
};

export const isI18N = () => {
  return getEnv() === Envs.I18N;
};

export const BpmDomainMap = {
  [Envs.ONLINE]: "bpm.bytedance.net",
  [Envs.I18N]: "bpm-i18n.tiktok-row.net",
  [Envs.BOE]: "bpm-boe.bytedance.net",
  [Envs.DEV]: "bpm-boe.bytedance.net",
};
