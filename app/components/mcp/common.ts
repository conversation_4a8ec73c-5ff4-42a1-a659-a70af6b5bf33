import { SessionRole } from "@/app/bam/aime/namespaces/types";
import { MCPType, MCPSource } from "@/app/bam/aime/namespaces/mcp";

export const SessionRoleMap = {
  [SessionRole.IndustryVeteran]: "行业专家",
  [SessionRole.LateralHire]: "社招新人",
  [SessionRole.YoungTalent]: "萌锐青年",
  [SessionRole.Unknown]: "错误情况",
};

export const TypesMap = {
  [MCPType.STDIO]: "STDIO类型",
  [MCPType.SSE]: "SSE类型",
  [MCPType.StreamableHTTP]: "StreamableHTTP类型",
  [MCPType.CloudSDK]: "CloudSDK类型",
};

export const SourcesMap = {
  [MCPSource.AIME]: "AIME平台",
  [MCPSource.Cloud]: "字节云平台",
  [MCPSource.UserDefine]: "用户自定义",
};
