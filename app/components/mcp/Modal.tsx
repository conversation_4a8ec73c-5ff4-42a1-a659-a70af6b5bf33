import { Modal, ModalProps, Form, Input, Select } from "@arco-design/web-react";
import { MCP, MCPSource, MCPType } from "@/app/bam/aime/namespaces/mcp";
import { SessionRoleMap, TypesMap, SourcesMap } from "./common";
import { SessionRole } from "@/app/bam/aime/namespaces/types";
import { useEffect } from "react";
import { apiClient } from "@/app/api/request";
import { toast } from "sonner";

export interface McpModalProps extends ModalProps {
  isEdit?: boolean;
  data?: MCP;
}

export default function McpModal(props: McpModalProps) {
  const { data, isEdit = false } = props || {};
  const [form] = Form.useForm();

  useEffect(() => {
    if (data && props.visible && form && isEdit) {
      form.setFieldsValue({
        name: data.name,
        description: data.description,
        icon_url: data.icon_url,
        config: JSON.stringify(data.config, null, 2),
        source: data.source,
        type: data.type,
        id: data.id,
        en_name: data.en_name,
        en_description: data.en_description,
        session_roles: data.session_roles,
        force_active: data.force_active ? 1 : 0,
      });
    } else if (!props.visible) {
      form.clearFields();
    }
  }, [data, props.visible, form]);

  const handleOk = async () => {
    const value = await form.validate();

    const reqData = {
      ...value,
      config: JSON.parse(value.config || "{}"),
      force_active: !!value.force_active,
      session_roles: value?.session_roles?.length ? value.session_roles : null,
    };
    try {
      const { mcp } = await (isEdit
        ? apiClient.UpdateBuildInMCP(reqData)
        : apiClient.CreateBuildInMCP(reqData));
      if (mcp) {
        toast.success("操作成功");
      }
    } catch (error) {
      console.log(error);
      toast.error("操作失败");
    }
    props?.onOk?.();
  };
  return (
    <Modal {...props} style={{ width: 800 }} onOk={handleOk}>
      <Form
        layout="vertical"
        className="mt-6"
        style={{ maxHeight: 600, overflow: "auto" }}
        form={form}
      >
        <Form.Item label="Name" field="name" rules={[{ required: true }]}>
          <Input placeholder="请输入MCP工具名称" allowClear />
        </Form.Item>
        <Form.Item
          label="description"
          field="description"
          rules={[{ required: true }]}
        >
          <Input placeholder="请输入MCP工具描述" allowClear />
        </Form.Item>
        <Form.Item label="icon_url" field="icon_url">
          <Input placeholder="请输入MCP工具图标URL" allowClear />
        </Form.Item>
        <Form.Item label="config" field="config">
          <Input.TextArea
            autoSize={{ minRows: 8, maxRows: 50 }}
            placeholder="请输入MCP工具参数结构"
            allowClear
          />
        </Form.Item>
        <Form.Item label="来源" field="source" rules={[{ required: true }]}>
          <Select placeholder="请选择来源" allowClear>
            {Object.keys(SourcesMap).map((key) => {
              const targetKey = Number(key) as MCPSource;
              if (targetKey === MCPSource.UserDefine) {
                return null;
              }
              return (
                <Select.Option key={targetKey} value={targetKey}>
                  {SourcesMap[targetKey]}
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item label="类型" field="type" rules={[{ required: true }]}>
          <Select placeholder="请选择类型" allowClear>
            {Object.keys(TypesMap).map((key) => {
              const targetKey = Number(key) as MCPType;
              return (
                <Select.Option key={targetKey} value={targetKey}>
                  {TypesMap[targetKey]}
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>
        
        <Form.Item label="uid" field={isEdit ? 'id' : 'uid'}>
          <Input
            placeholder="请输入uid，mcp的唯一id，runtime使用需要"
            allowClear
            disabled={isEdit}
          />
        </Form.Item>
        <Form.Item label="en_name" field="en_name">
          <Input placeholder="请输入MCP工具英文名称" allowClear />
        </Form.Item>
        <Form.Item label="en_description" field="en_description">
          <Input placeholder="请输入mcp 工具英文描述" allowClear />
        </Form.Item>
        <Form.Item label="SessionRole" field="session_roles">
          <Select placeholder="请选择SessionRole" mode="multiple" allowClear>
            {Object.keys(SessionRoleMap).map((key) => {
              const targetKey = Number(key) as SessionRole;
              if (targetKey === SessionRole.Unknown) {
                return null;
              }
              return (
                <Select.Option key={targetKey} value={targetKey}>
                  {SessionRoleMap[targetKey]}
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item label="force_active" field="force_active">
          <Select placeholder="请选择是否强制激活" allowClear>
            <Select.Option value={1}>是</Select.Option>
            <Select.Option value={0}>否</Select.Option>
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
}
