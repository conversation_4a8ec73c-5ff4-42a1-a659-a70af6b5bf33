"use client";

import React, { useMemo, useState } from "react";
import {
  Table,
  TableColumnProps,
  Button,
  Form,
  Grid,
  Input,
  Select,
} from "@arco-design/web-react";
import { PageHeader } from "@/app/components/PageHeader";
import {
  MCPSource,
  MCPType,
  ListMCPRequest,
  MCP,
} from "@/app/bam/aime/namespaces/mcp";
import { SessionRole } from "@/app/bam/aime/namespaces/types";
import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@/app/api/request";
import { IconPlus, IconEdit } from "@arco-design/web-react/icon";
import { SessionRoleMap, TypesMap, SourcesMap } from "./common";
import McpModal from "./Modal";

const columns: TableColumnProps[] = [
  {
    title: "id",
    dataIndex: "id",
    width: 140,
  },
  {
    title: "name",
    dataIndex: "name",
    width: 140,
  },
  {
    title: "description",
    dataIndex: "description",
  },
  {
    title: "icon_url",
    dataIndex: "icon_url",
  },
  {
    title: "config",
    dataIndex: "config",
  },
  {
    title: "source",
    dataIndex: "source",
    width: 140,
  },
  {
    title: "type",
    dataIndex: "type",
    width: 140,
  },
  {
    title: "force_active",
    dataIndex: "force_active",
  },
  {
    title: "en_name",
    dataIndex: "en_name",
  },
  {
    title: "en_description",
    dataIndex: "en_description",
  },
  {
    title: "session_roles",
    dataIndex: "session_roles",
  },
];

export const McpList = () => {
  const [requestData, setRequestData] = useState<ListMCPRequest>({});
  const [visible, setVisible] = useState(false);
  const [targetItem, setTargetItem] = useState<MCP | undefined>();
  const [isEdit, setIsEdit] = useState(false);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onFormChange = (_changeValue: any, values: ListMCPRequest) => {
    setRequestData({
      ...values,
      ...(values.is_active === undefined
        ? {}
        : { is_active: !!values.is_active }),
    });
  };

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["mcpList", requestData],
    queryFn: async () => {
      const response = await apiClient.ListMCP(requestData);
      return response.mcps || [];
    },
  });

  const tableData = useMemo(() => {
    return data?.map((item) => {
      return {
        ...item,
        key: item.id,
        force_active: item.force_active ? "是" : "否",
        source: SourcesMap[item.source],
        type: TypesMap[item.type],
        session_roles:
          item.session_roles?.map((role) => SessionRoleMap[role]).join(",") ||
          "-",
        config: JSON.stringify(item.config, null, 2),
      };
    });
  }, [data]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleTableItemClick = (item: any) => {
    const target = data?.find((i) => i.id === item.id);
    if (target) {
      setTargetItem(target);
    }
    setIsEdit(true);
    setVisible(true);
  };

  const handleAdd = () => {
    setIsEdit(false);
    setVisible(true);
  };

  const handleOk = () => {
    setVisible(false);
    refetch();
  };

  return (
    <div>
      <PageHeader title="MCP 列表" />
      <Form layout="vertical" className="mt-6" onValuesChange={onFormChange}>
        <Grid.Row gutter={24}>
          <Grid.Col span={4}>
            <Form.Item label="Name" field="name">
              <Input placeholder="请输入名称" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4}>
            <Form.Item label="来源" field="sources">
              <Select mode="multiple" placeholder="请选择来源" allowClear>
                {Object.keys(SourcesMap).map((key) => {
                  const targetKey = Number(key) as MCPSource;
                  return (
                    <Select.Option key={targetKey} value={targetKey}>
                      {SourcesMap[targetKey]}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4}>
            <Form.Item label="类型" field="types">
              <Select mode="multiple" placeholder="请选择类型" allowClear>
                {Object.keys(TypesMap).map((key) => {
                  const targetKey = Number(key) as MCPType;
                  return (
                    <Select.Option key={targetKey} value={targetKey}>
                      {TypesMap[targetKey]}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4}>
            <Form.Item label="SessionRole" field="session_role">
              <Select
                placeholder="请选择SessionRole"
                mode="multiple"
                allowClear
              >
                {Object.keys(SessionRoleMap).map((key) => {
                  const targetKey = Number(key) as SessionRole;
                  return (
                    <Select.Option key={targetKey} value={targetKey}>
                      {SessionRoleMap[targetKey]}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Grid.Col>
          {/* <Grid.Col span={4}>
            <Form.Item label="is_active" field="is_active">
              <Select placeholder="请选择添加状态" allowClear>
                <Select.Option value={1}>已添加</Select.Option>
                <Select.Option value={0}>未添加</Select.Option>
              </Select>
            </Form.Item>
          </Grid.Col> */}
        </Grid.Row>
      </Form>
      <div className="mb-6 overflow-hidden">
        <Button
          type="primary"
          className="float-right"
          icon={<IconPlus />}
          onClick={handleAdd}
        >
          添加MCP
        </Button>
      </div>
      <Table
        columns={columns.concat({
          title: "Operation",
          dataIndex: "operation",
          render: (col, item) => (
            <Button
              type="secondary"
              size="mini"
              icon={<IconEdit />}
              onClick={() => handleTableItemClick(item)}
            />
          ),
          fixed: "right",
          width: 20,
        })}
        data={tableData}
        loading={isLoading}
      />
      <McpModal
        visible={visible}
        data={targetItem}
        isEdit={isEdit}
        onCancel={() => setVisible(false)}
        onOk={handleOk}
        title={isEdit ? "修改 MCP" : "添加 MCP"}
      />
    </div>
  );
};
