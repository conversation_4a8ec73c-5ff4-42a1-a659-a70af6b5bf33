"use client";

import { Flex, Box } from "@radix-ui/themes";
import { usePathname } from "next/navigation";
import { Sidebar } from "./Sidebar";
import { PermissionGuard } from "./PermissionGuard";
import { UserRole } from "../bam/aime/namespaces/session";

const PATHS_WITHOUT_SIDEBAR = ["/login", "/no-permission"];
const PATHS_WITHOUT_AUTH = ["/login", "/no-permission", "/"];

// 路径权限映射
const PATH_PERMISSIONS: Record<string, string | null> = {
  "/playground/mcp": UserRole.UserRoleAimoMCPPlayground,
  // 主页和 playground 主页不需要权限检查
  "/": null,
  "/playground": null,
  // 其他路径默认需要 TraceSession 权限
};

function getRequiredPermission(pathname: string): string | null {
  // 不需要认证的路径
  if (PATHS_WITHOUT_AUTH.includes(pathname)) {
    return null;
  }

  // 检查特定路径权限
  for (const [path, permission] of Object.entries(PATH_PERMISSIONS)) {
    if (pathname.startsWith(path)) {
      return permission;
    }
  }

  // 默认需要 TraceSession 权限
  return "TraceSession";
}

export function LayoutWithoutSidebar({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const hideSidebar = PATHS_WITHOUT_SIDEBAR.some(path => pathname === path);
  const requiredPermission = getRequiredPermission(pathname);

  if (hideSidebar) {
    return <Box className="w-full min-h-screen p-4 md:p-6">{children}</Box>;
  }

  // 需要权限的页面包装在 PermissionGuard 中
  const content = requiredPermission ? (
    <PermissionGuard requiredPermission={requiredPermission as UserRole}>
      {children}
    </PermissionGuard>
  ) : (
    children
  );

  return (
    <Flex>
      <Sidebar />
      <Box className="ml-[250px] w-[calc(100%-250px)] min-h-screen p-4 md:p-6">
        {content}
      </Box>
    </Flex>
  );
}
