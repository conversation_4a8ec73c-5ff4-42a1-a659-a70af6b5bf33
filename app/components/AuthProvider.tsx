"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { useStore } from "@nanostores/react";
import {
  isAuthenticatedAtom,
  isLoading<PERSON>tom,
  userAtom,
  checkAuth,
  User,
  redirectInProgressAtom,
  lastAuthCheckAtom,
  hasPermissionAtom,
  authErrorMessageAtom,
  AuthResult,
} from "../store/auth";

// ==================== 常量定义 ====================

// 路径配置
const LOGIN_PATH = "/login"; // 登录页面路径
const NO_PERMISSION_PATH = "/no-permission"; // 无权限页面路径

// 认证配置
const AUTH_CHECK_INTERVAL = 60000; // 避免频繁认证检查的时间间隔（毫秒）

// ==================== 类型定义 ====================

// 认证上下文类型
type AuthContextType = {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User;
  hasPermission: boolean;
  authErrorMessage: string;
  checkAuth: () => Promise<AuthResult>;
};

// 空认证上下文（默认值）
const defaultAuthContext: AuthContextType = {
  isAuthenticated: false,
  isLoading: true,
  user: null,
  hasPermission: false,
  authErrorMessage: "",
  checkAuth: async () => ({
    authenticated: false,
    hasPermission: false,
    message: "",
  }),
};

// 创建认证上下文
const AuthContext = createContext<AuthContextType>(defaultAuthContext);

// ==================== 主组件 ====================

/**
 * 认证提供者组件
 * 处理全局认证状态并提供上下文
 */
export function AuthProvider({ children }: { children: React.ReactNode }) {
  // 从存储中读取认证状态
  const isAuthenticated = useStore(isAuthenticatedAtom);
  const isLoading = useStore(isLoadingAtom);
  const user = useStore(userAtom);
  const hasPermission = useStore(hasPermissionAtom);
  const authErrorMessage = useStore(authErrorMessageAtom);
  const redirectInProgress = useStore(redirectInProgressAtom);
  const lastAuthCheck = useStore(lastAuthCheckAtom);

  // 本地状态
  const pathname = usePathname();
  const router = useRouter();
  const [initialCheckDone, setInitialCheckDone] = useState(false);

  // 处理认证逻辑
  useEffect(() => {
    const handleAuth = async () => {
      // 1. 特殊页面处理
      if (shouldSkipAuthCheck(pathname)) {
        isLoadingAtom.set(false);
        return;
      }

      // 2. 重定向状态检查
      if (redirectInProgress) {
        return; // 重定向进行中，避免重复检查
      }
      // 3. 缓存状态检查
      if (canUseAuthCache(lastAuthCheck, isAuthenticated, hasPermission)) {
        finishInitialCheck();
        return;
      }

      // 4. 执行认证检查
      try {
        const result = await checkAuth();
        finishInitialCheck();
        // 5. 处理认证结果
        handleAuthResult(result, pathname, router);
      } catch (error) {
        console.error("认证检查失败", error);
        finishInitialCheck();
        handleAuthError(pathname, router);
      }
    };

    handleAuth();
  }, [
    pathname,
    isAuthenticated,
    hasPermission,
    lastAuthCheck,
    redirectInProgress,
    router,
  ]);

  // 提供加载UI
  if (isLoading && !initialCheckDone) {
    return <AuthLoadingScreen />;
  }

  // 提供认证上下文
  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        user,
        hasPermission,
        authErrorMessage,
        checkAuth,
      }}
    >
      {children}
    </AuthContext.Provider>
  );

  // ==================== 内部辅助函数 ====================

  /**
   * 完成初始认证检查
   */
  function finishInitialCheck() {
    isLoadingAtom.set(false);
    setInitialCheckDone(true);
  }
}

// ==================== 辅助函数 ====================

/**
 * 判断是否应该跳过认证检查
 */
function shouldSkipAuthCheck(pathname: string): boolean {
  return pathname === NO_PERMISSION_PATH;
}

/**
 * 判断是否可以使用认证缓存
 */
function canUseAuthCache(
  lastAuthCheck: number,
  isAuthenticated: boolean,
  hasPermission: boolean,
): boolean {
  const currentTime = Date.now();
  return (
    lastAuthCheck > 0 &&
    currentTime - lastAuthCheck < AUTH_CHECK_INTERVAL &&
    isAuthenticated &&
    hasPermission
  );
}

/**
 * 处理认证结果
 */
function handleAuthResult(
  result: AuthResult,
  pathname: string,
  router: ReturnType<typeof useRouter>,
): void {
  // 已登录但无权限，跳转到无权限页面
  if (
    result.authenticated &&
    !result.hasPermission &&
    pathname !== NO_PERMISSION_PATH
  ) {
    redirectInProgressAtom.set(true);
    router.push(NO_PERMISSION_PATH);
    return;
  }

  // 未登录，跳转到登录页面
  if (!result.authenticated && pathname !== LOGIN_PATH) {
    redirectInProgressAtom.set(true);
    router.push(LOGIN_PATH);
    return;
  }
}

/**
 * 处理认证错误
 */
function handleAuthError(
  pathname: string,
  router: ReturnType<typeof useRouter>,
): void {
  // 认证失败时重定向到登录页面
  if (pathname !== LOGIN_PATH) {
    redirectInProgressAtom.set(true);
    router.push(LOGIN_PATH);
  }
}

// ==================== 组件 ====================

/**
 * 认证加载骨架屏
 * 显示应用布局的骨架，提供更好的视觉体验
 */
function AuthLoadingScreen() {
  return (
    <div className="fixed inset-0 bg-white z-50 overflow-hidden">
      {/* 顶部导航骨架 */}
      <div className="flex items-center px-6 py-4 border-b border-gray-100">
        <div className="w-10 h-10 rounded-full bg-gray-200 pulse-animation"></div>
        <div className="ml-4 flex-1">
          <div className="h-4 w-32 bg-gray-200 rounded pulse-animation"></div>
          <div className="h-3 w-24 bg-gray-100 rounded mt-2 pulse-animation"></div>
        </div>
        <div className="flex space-x-2">
          <div className="w-8 h-8 rounded-full bg-gray-200 pulse-animation"></div>
          <div className="w-8 h-8 rounded-full bg-gray-200 pulse-animation"></div>
        </div>
      </div>

      {/* 内容骨架 */}
      <div className="flex h-[calc(100vh-66px)]">
        {/* 侧边栏骨架 */}
        <div className="w-64 border-r border-gray-100 p-4 hidden md:block">
          <div className="h-8 w-3/4 bg-gray-200 rounded pulse-animation"></div>
          <div className="mt-6 space-y-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="flex items-center">
                <div className="w-6 h-6 rounded bg-gray-200 pulse-animation"></div>
                <div className="ml-3 h-4 w-20 bg-gray-200 rounded pulse-animation"></div>
              </div>
            ))}
          </div>
        </div>

        {/* 主内容骨架 */}
        <div className="flex-1 p-6 overflow-auto">
          <div className="h-8 w-64 bg-gray-200 rounded pulse-animation mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="bg-gray-50 rounded-lg p-5 shadow-sm border border-gray-100"
              >
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-gray-200 pulse-animation"></div>
                  <div className="ml-3">
                    <div className="h-4 w-24 bg-gray-200 rounded pulse-animation"></div>
                    <div className="h-3 w-16 bg-gray-100 rounded mt-2 pulse-animation"></div>
                  </div>
                </div>
                <div className="h-3 w-full bg-gray-100 rounded mb-2 pulse-animation"></div>
                <div className="h-3 w-full bg-gray-100 rounded mb-2 pulse-animation"></div>
                <div className="h-3 w-4/5 bg-gray-100 rounded pulse-animation"></div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 添加脉冲动画的CSS */}
      <style jsx>{`
        .pulse-animation {
          animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        @keyframes pulse {
          0%,
          100% {
            opacity: 1;
          }
          50% {
            opacity: 0.5;
          }
        }
      `}</style>
    </div>
  );
}

// ==================== 自定义Hooks ====================

/**
 * 使用认证上下文的Hook
 */
export function useAuth() {
  return useContext(AuthContext);
}
