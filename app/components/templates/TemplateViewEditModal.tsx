"use client";

import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Modal, Form, Input, Button, Select, Switch, Link, Tabs } from '@arco-design/web-react';
import { apiClient } from '@/app/api/request';
import { TemplateVersion, OpsEditTemplateRequest } from '@/app/bam/aime/namespaces/ops';
import { toast } from "sonner";
import { ZodError } from 'zod';
import {
  SOPSchema,
  convertTemplateVersionToSOPSchema,
  sopSchemaSchema
} from '@/app/lib/sop-schema';
import SchemaEditor from './sop-editor/SchemaEditor';
import JsonDiffViewer from './sop-editor/JsonDiffViewer';
import ErrorBoundary from './sop-editor/ErrorBoundary';

const { TabPane } = Tabs;

interface TemplateViewEditModalProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  data?: TemplateVersion;
  isEdit: boolean;
}

// 字段名映射配置
const editFieldNameMap = new Map<string, string>([
  ['plan', 'progress_plan'],
  ['prompt_content', 'query_template'],
  ['prompt_variables', 'query_template_placeholders'],
]);

/**
 * 模板查看/编辑模态框组件 - 优化版本
 * 支持基础信息编辑和SOP编辑两种模式
 */
const TemplateViewEditModal: React.FC<TemplateViewEditModalProps> = React.memo((props) => {
  const { visible, onOk, onCancel, data, isEdit } = props;
  const [form] = Form.useForm<Partial<TemplateVersion>>();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [sopData, setSopData] = useState<SOPSchema | undefined>();
  const [originalExpSopData, setOriginalExpSopData] = useState<any>(null); // 保存原始ExpSOP数据
  const [originalSOPData, setOriginalSOPData] = useState<SOPSchema | undefined>(); // 保存原始SOP数据用于diff对比
  const [showDiffTab, setShowDiffTab] = useState(false);
  const [pendingSOPData, setPendingSOPData] = useState<SOPSchema | undefined>(); // 待确认的SOP数据
  const [hasSOPModifications, setHasSOPModifications] = useState(false); // 跟踪SOP是否有修改

  // exp_sop字段可以在基础信息中以JSON形式编辑，也可以在SOP编辑器中结构化编辑
  const jsonFieldNames: Array<keyof TemplateVersion> = ['plan_steps', 'exp_sop', 'support_mcps', 'prompt_variables'];

  // 工具函数：安全解析ExpSOP数据
  const safeParseExpSopData = useCallback((expSopData: any) => {
    if (!expSopData) return {};
    
    if (typeof expSopData === 'string') {
      try {
        return JSON.parse(expSopData);
      } catch (error) {
        console.warn('ExpSOP字符串解析失败:', error);
        return {};
      }
    } else if (typeof expSopData === 'object' && expSopData !== null) {
      return expSopData;
    }
    
    return {};
  }, []);

  // 工具函数：合并SOP编辑数据和原始ExpSOP数据中的未知字段
  const mergeSOPDataWithOriginal = useCallback((sopData: SOPSchema, originalData: any) => {
    return {
      ...originalData, // 保留原始数据中的所有字段
      // 覆盖编辑过的字段
      name: sopData.name,
      used_when: sopData.used_when,
      user_query: sopData.user_query,
      progress_plan: sopData.progress_plan,
      user_query_placeholders: sopData.user_query_placeholders,
      plan_steps: sopData.plan_steps,
    };
  }, []);

  useEffect(() => {
    if (data && visible) {
      // 重置所有状态到初始状态
      setLoading(false);
      setActiveTab('basic');
      setShowDiffTab(false);
      setPendingSOPData(undefined);
      setHasSOPModifications(false);

      const initialFormValues: Partial<TemplateVersion> = { ...data };
      jsonFieldNames.forEach(fieldName => {
        if (data[fieldName] !== undefined && data[fieldName] !== null) {
          initialFormValues[fieldName] = JSON.stringify(data[fieldName], null, 2) as any;
        } else {
          initialFormValues[fieldName] = '' as any; // 空的 JSON 字段在表单中显示为空字符串
        }
      });
      form.setFieldsValue(initialFormValues);

      // 初始化 SOP 数据 - 只从 exp_sop 字段读取，同时保存原始数据
      try {
        // 保存原始的ExpSOP数据（包含所有字段）
        const originalExpSop = safeParseExpSopData(data.exp_sop);
        setOriginalExpSopData(originalExpSop);
        console.log('原始ExpSOP数据:', originalExpSop);

        const initialSopData = convertTemplateVersionToSOPSchema(data);
        setSopData(initialSopData);
        setOriginalSOPData(initialSopData); // 保存原始SOP数据用于diff对比
        console.log('SOP数据初始化成功（仅从exp_sop字段）:', initialSopData);
      } catch (error) {
        console.error('SOP数据初始化失败:', error);
        // 如果exp_sop字段为空或无效，设置默认的空SOP数据
        setOriginalExpSopData({});
        const defaultSOPData = {
          name: '',
          used_when: '',
          user_query: '',
          progress_plan: '',
          user_query_placeholders: [],
          plan_steps: [],
        };
        setSopData(defaultSOPData);
        setOriginalSOPData(defaultSOPData); // 保存原始SOP数据用于diff对比
      }
    } else if (!visible) {
      // 模态框关闭时清理所有状态
      form.resetFields();
      setLoading(false);
      setActiveTab('basic');
      setSopData(undefined);
      setOriginalExpSopData(null);
      setOriginalSOPData(undefined);
      setShowDiffTab(false);
      setPendingSOPData(undefined);
      setHasSOPModifications(false);
    }
  }, [data, visible, form, safeParseExpSopData]);

  // 使用useCallback优化JSON验证函数
  const validateJsonField = useCallback((fieldName: string, value: string): any => {
    if (!value || typeof value !== 'string' || value.trim() === '') {
      return null;
    }
    
    try {
      return JSON.parse(value);
    } catch (error) {
      throw new Error(`字段 '${fieldName}' 的JSON格式无效: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }, []);

  // 使用useCallback优化基础信息保存逻辑
  const handleBasicInfoSave = useCallback(async (): Promise<OpsEditTemplateRequest> => {
    if (!data || !data.template_id) {
      throw new Error("原始数据或模板ID缺失");
    }

    const formValues = await form.validate();
    const requestChanges: Partial<OpsEditTemplateRequest> = {};
    let hasChanges = false;

    for (const key of Object.keys(formValues) as Array<keyof TemplateVersion>) {
      // 跳过不可编辑的字段
      if (key === 'template_id' || key === 'creator' || key === 'session_id') {
        continue;
      }

      const formFieldValue = formValues[key];
      const originalValue = data[key];
      const editKey = editFieldNameMap.get(key) || key;

      if (jsonFieldNames.includes(key)) {
        // 处理JSON字段
        const parsedFormJson = validateJsonField(key, formFieldValue as string);
        
        if (JSON.stringify(originalValue) !== JSON.stringify(parsedFormJson)) {
          // 特殊处理 exp_sop 字段：只有在基础信息 tab 中编辑时才包含
          // 如果是在 SOP 编辑器中修改，应该通过 SOP 保存逻辑处理
          if (key === 'exp_sop' && activeTab === 'sop') {
            // 在 SOP 编辑模式下，不通过基础信息保存 exp_sop
            console.log('在SOP编辑模式下，跳过基础信息中的exp_sop字段变更');
          } else {
            (requestChanges as any)[editKey] = formFieldValue;
            hasChanges = true;
          }
        }
      } else {
        // 处理普通字段
        if (String(formFieldValue ?? '') !== String(originalValue ?? '')) {
          (requestChanges as any)[editKey] = formFieldValue;
          hasChanges = true;
        }
      }
    }

    if (!hasChanges) {
      toast.info("没有检测到更改");
      return { template_id: data.template_id };
    }

    return {
      ...requestChanges,
      template_id: data.template_id,
    };
  }, [data, form, jsonFieldNames, validateJsonField]);

  // 检查SOP数据是否有变更
  const hasSOPChanges = useCallback((current: SOPSchema, original: SOPSchema): boolean => {
    try {
      // 使用JSON.stringify进行深度比较，但先对对象进行标准化
      const normalizeForComparison = (obj: any): any => {
        if (obj === null || obj === undefined) return obj;
        if (Array.isArray(obj)) {
          return obj.map(normalizeForComparison);
        }
        if (typeof obj === 'object' && obj.constructor === Object) {
          const normalized: any = {};
          const keys = Object.keys(obj).sort(); // 排序键以忽略顺序
          for (const key of keys) {
            normalized[key] = normalizeForComparison(obj[key]);
          }
          return normalized;
        }
        return obj;
      };
      
      const normalizedCurrent = normalizeForComparison(current);
      const normalizedOriginal = normalizeForComparison(original);
      
      return JSON.stringify(normalizedCurrent) !== JSON.stringify(normalizedOriginal);
    } catch (error) {
      console.error('变更检测失败:', error);
      return true; // 出错时假设有变更，确保安全
    }
  }, []);

  // 处理变更确认
  const handleConfirmChanges = useCallback(async (confirmedSOPData: SOPSchema): Promise<OpsEditTemplateRequest> => {
    if (!data?.template_id) {
      throw new Error("模板ID缺失");
    }

    // 验证SOP数据
    try {
      sopSchemaSchema.parse(confirmedSOPData);
    } catch (error) {
      if (error instanceof ZodError) {
        const errorMessages = error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join('; ');
        throw new Error(`SOP数据验证失败: ${errorMessages}`);
      }
      throw error;
    }

    // SOP编辑只更新exp_sop字段，保留原始数据中的未知字段
    const expSopData = mergeSOPDataWithOriginal(confirmedSOPData, originalExpSopData);
    
    // console.log('保存ExpSOP数据（保留未知字段）:', expSopData);
    
    return {
      template_id: data.template_id,
      exp_sop: JSON.stringify(expSopData),
    };
  }, [data, originalExpSopData, mergeSOPDataWithOriginal]);

  // 使用useCallback优化SOP保存逻辑
  const handleSOPSave = useCallback(async (): Promise<OpsEditTemplateRequest> => {
    if (!sopData || !data?.template_id) {
      throw new Error("SOP数据或模板ID缺失");
    }

    if (!originalSOPData) {
      throw new Error("原始SOP数据缺失，无法进行变更比较");
    }

    // 检查是否有变更
    if (!hasSOPChanges(sopData, originalSOPData)) {
      console.log('SOP数据无变更，跳过保存');
      toast.info("SOP 数据无变更");
      return { template_id: data.template_id }; // 返回只包含template_id的对象，表示无变更
    }

    console.log('检测到SOP数据变更，显示差异确认界面');
    
    // 显示差异确认tab
    return new Promise<OpsEditTemplateRequest>((resolve, reject) => {
      setPendingSOPData(sopData);
      setShowDiffTab(true);
      setActiveTab('diff'); // 切换到diff tab
      
      // 将resolve和reject函数保存到组件状态中，以便在确认或取消时调用
      (window as any).sopSaveResolve = resolve;
      (window as any).sopSaveReject = reject;
    });
  }, [sopData, data, originalSOPData, hasSOPChanges]);

  // 处理差异确认的回调
  const handleDiffConfirm = useCallback(async () => {
    if (!pendingSOPData) {
      console.error('没有待确认的SOP数据');
      return;
    }

    try {
      setLoading(true);
      const requestData = await handleConfirmChanges(pendingSOPData);
      
      // 调用resolve函数
      if ((window as any).sopSaveResolve) {
        (window as any).sopSaveResolve(requestData);
        (window as any).sopSaveResolve = null;
        (window as any).sopSaveReject = null;
      }
      
      setShowDiffTab(false);
      setPendingSOPData(undefined);
      setActiveTab('sop'); // 返回到SOP编辑tab
    } catch (error) {
      console.error('确认变更失败:', error);
      if ((window as any).sopSaveReject) {
        (window as any).sopSaveReject(error);
        (window as any).sopSaveResolve = null;
        (window as any).sopSaveReject = null;
      }
      setShowDiffTab(false);
      setPendingSOPData(undefined);
      setActiveTab('sop'); // 返回到SOP编辑tab
    } finally {
      setLoading(false);
    }
  }, [pendingSOPData, handleConfirmChanges]);

  // 处理差异取消的回调
  const handleDiffCancel = useCallback(() => {
    // 调用reject函数
    if ((window as any).sopSaveReject) {
      (window as any).sopSaveReject(new Error('用户取消了变更确认'));
      (window as any).sopSaveResolve = null;
      (window as any).sopSaveReject = null;
    }
    
    setShowDiffTab(false);
    setPendingSOPData(undefined);
    setActiveTab('sop'); // 返回到SOP编辑tab
  }, []);

  // 手动同步SOP数据到表单的exp_sop字段
  const syncSOPDataToForm = useCallback(() => {
    if (!sopData || !originalSOPData) return false;
    
    try {
      // 只有在SOP数据真正发生变更时才同步到表单
      if (!hasSOPModifications || !hasSOPChanges(sopData, originalSOPData)) {
        console.log('SOP数据无变更，跳过同步到exp_sop字段');
        return false;
      }
      
      // 合并SOP编辑的数据和原始未知字段
      const mergedExpSopData = mergeSOPDataWithOriginal(sopData, originalExpSopData);
      
      // 将合并后的数据转换为JSON字符串
      const sopJsonString = JSON.stringify(mergedExpSopData, null, 2);
      
      // 获取当前表单值并更新exp_sop字段
      const currentFormValues = form.getFieldsValue();
      const syncedFormData = {
        ...currentFormValues,
        exp_sop: sopJsonString
      };
      
      // 更新表单字段值
      form.setFieldsValue(syncedFormData);
      
      console.log('SOP编辑器数据已同步到exp_sop字段（检测到变更）:', sopData, mergedExpSopData);
      return true;
    } catch (error) {
      console.error('同步SOP数据到exp_sop字段失败:', error);
      return false;
    }
  }, [sopData, originalSOPData, hasSOPChanges, mergeSOPDataWithOriginal, originalExpSopData, form, hasSOPModifications]);

  // 使用useCallback优化确认处理函数
  const handleConfirm = useCallback(async () => {
    if (!isEdit) {
      onOk();
      return;
    }

    try {
      setLoading(true);
      
      let finalRequestData: OpsEditTemplateRequest;
      
      if (activeTab === 'sop') {
        // 在SOP编辑模式下确认时，先同步数据到表单，然后保存
        syncSOPDataToForm();
        finalRequestData = await handleSOPSave();
      } else {
        finalRequestData = await handleBasicInfoSave();
      }

      // 如果没有变更，直接返回
      if (Object.keys(finalRequestData).length === 1 && finalRequestData.template_id) {
        onOk();
        return;
      }

      console.log('提交的数据:', finalRequestData);

      await apiClient.OpsEditTemplate(finalRequestData);
      
      // 清除自动保存的数据
      if (data?.template_id) {
        const autoSaveKey = `sop_autosave_${data.template_id}`;
        localStorage.removeItem(autoSaveKey);
      }
      
      toast.success("模板更新成功");
      onOk();
      
    } catch (error) {
      console.error("更新失败:", error);
      
      // 提供更具体的错误信息
      if (error instanceof Error) {
        toast.error(`模板更新失败: ${error.message}`);
      } else if (error && typeof error === 'object' && 'errorFields' in error) {
        // 表单验证错误，不显示toast
        return;
      } else {
        toast.error("模板更新失败：未知错误");
      }
    } finally {
      setLoading(false);
    }
  }, [isEdit, activeTab, handleSOPSave, handleBasicInfoSave, onOk, syncSOPDataToForm]);

  // 使用useCallback优化标签切换函数 - exp_sop字段双向同步
  const handleTabChange = useCallback(async (key: string) => {
    // 如果试图切换到diff tab但没有待确认数据，则阻止切换
    if (key === 'diff' && !showDiffTab) {
      return;
    }
    
    try {
      // 从基础信息切换到SOP编辑时，同步基础信息中的exp_sop JSON到SOP编辑器
      if (activeTab === 'basic' && key === 'sop') {
        const formValues = form.getFieldsValue();
        const expSopJson = formValues.exp_sop;
        
        if (expSopJson && expSopJson.trim && expSopJson.trim()) {
          try {
            const parsedExpSop = safeParseExpSopData(expSopJson);
            // 更新原始ExpSOP数据
            setOriginalExpSopData(parsedExpSop);
            // 验证并转换为SOPSchema
            const validatedSopData = sopSchemaSchema.parse(parsedExpSop);
            setSopData(validatedSopData);
            console.log('从基础信息的exp_sop JSON同步到SOP编辑器:', validatedSopData);
          } catch (parseError) {
            console.warn('基础信息中的exp_sop JSON格式无效，使用当前SOP数据:', parseError, expSopJson);
            toast.warning('基础信息中的exp_sop JSON格式无效，将使用当前SOP数据');
          }
        }
      }
      
      // 从SOP编辑切换到基础信息时，同步SOP编辑器数据到exp_sop字段
      if (activeTab === 'sop' && key === 'basic' && sopData) {
        const synced = syncSOPDataToForm();
        if (synced) {
          console.log('从SOP编辑器切换到基础信息，已同步数据到exp_sop字段');
        } else {
          console.log('从SOP编辑器切换到基础信息，无需同步（无变更）');
        }
      }
      
      setActiveTab(key);
    } catch (error) {
      console.error('选项卡切换时数据同步失败:', error);
      toast.error('数据同步失败，请检查数据格式');
      setActiveTab(key); // 仍然允许切换
    }
  }, [activeTab, form, sopData, originalExpSopData, mergeSOPDataWithOriginal, safeParseExpSopData, showDiffTab, syncSOPDataToForm]);

  // 使用useCallback优化SOP数据更新回调（只更新状态，不进行实时同步）
  const handleSopDataUpdate = useCallback((updatedSopData: SOPSchema) => {
    setSopData(updatedSopData);
    
    // 检查是否有变更并更新修改状态
    if (originalSOPData) {
      const hasChanges = hasSOPChanges(updatedSopData, originalSOPData);
      setHasSOPModifications(hasChanges);
      console.log('SOP数据已更新:', updatedSopData, '有变更:', hasChanges);
    } else {
      console.log('SOP数据已更新:', updatedSopData);
    }
  }, [originalSOPData, hasSOPChanges]);

  // 使用useMemo缓存表单验证规则
  const jsonFieldValidator = useMemo(() => ({
    validator: (value: string | undefined, callback: (error?: React.ReactNode) => void) => {
      if (!value || typeof value !== 'string') {
        callback();
        return;
      }
      try {
        JSON.parse(value);
        callback();
      } catch (e) {
        callback("JSON格式无效");
      }
    }
  }), []);

  return (
    <Modal
      title={
        <div>
          {isEdit ? "编辑模板" : "查看模板"}
        </div>
      }
      visible={visible}
      onOk={handleConfirm}
      onCancel={onCancel}
      confirmLoading={loading}
      style={{ width: '95vw', maxWidth: '1600px', height: '90vh' }}
      maskClosable={false}
      footer={
        isEdit
          ? [
              <Button key="cancel" onClick={onCancel}>
                取消
              </Button>,
              <Button key="submit" type="primary" loading={loading} onClick={handleConfirm}>
                确认
              </Button>,
            ]
          : [
              <Button key="close" type="primary" onClick={onCancel}>
                关闭
              </Button>,
            ]
      }
    >
      <ErrorBoundary
        showDetails={true}
        onError={(error, errorInfo) => {
          console.error('模板编辑器错误:', error, errorInfo);
        }}
      >
        <Tabs activeTab={activeTab} onChange={handleTabChange}>
          <TabPane key="basic" title="基础信息">
            <div style={{ 
              height: 'calc(90vh - 200px)', 
              overflowY: 'auto', 
              overflowX: 'hidden', 
              padding: '16px 0',
              scrollbarWidth: 'thin',
              scrollbarColor: '#c1c1c1 transparent'
            }}>
              <Form form={form} layout="vertical">
                <Form.Item label="template_id" field="template_id">
                  <Input disabled /> 
                </Form.Item>
                
                <Form.Item 
                  label="name" 
                  field="name" 
                  rules={isEdit ? [{ required: true, message: "名称不能为空" }] : []}
                >
                  <Input placeholder="请输入名称" />
                </Form.Item>
                
                <Form.Item label="status" field="status">
                  <Input placeholder="请输入状态" />
                </Form.Item>
                
                <Form.Item label="creator" field="creator">
                  <Input disabled />
                </Form.Item>
                
                <Form.Item label="session_id" field="session_id">
                  <Input disabled />
                </Form.Item>
                
                <Form.Item label="scope" field="scope">
                  <Select 
                    options={["Public", "Private", "Shared", "Official"].map(item => ({ 
                      label: item, 
                      value: item 
                    }))}
                    placeholder="请选择范围"
                  />
                </Form.Item>
                
                <Form.Item label="query_template" field="prompt_content">
                  <Input.TextArea 
                    autoSize={{ minRows: 3, maxRows: 10 }} 
                    placeholder="请输入查询模板内容" 
                  />
                </Form.Item>
                
                <Form.Item label="progress_plan" field="plan">
                  <Input.TextArea 
                    autoSize={{ minRows: 3, maxRows: 10 }} 
                    placeholder="请输入 exp progress plan" 
                  />
                </Form.Item>
                
                {jsonFieldNames.map(fieldName => (
                  <Form.Item
                    key={String(fieldName)}
                    label={`${String(fieldName)} (JSON)`}
                    field={String(fieldName)}
                    rules={isEdit ? [jsonFieldValidator] : []}
                  >
                    <Input.TextArea
                      autoSize={{ minRows: 5, maxRows: 15 }}
                      placeholder={`请输入 ${String(fieldName)} (JSON格式)`}
                    />
                  </Form.Item>
                ))}
                
                <Form.Item label="expired" field="expired" triggerPropName="checked">
                  <Switch checked={data?.expired} disabled={!isEdit} />
                </Form.Item>
                
                <Form.Item label="edited" field="edited" triggerPropName="checked">
                  <Switch checked={data?.edited} disabled={!isEdit} />
                </Form.Item>
              </Form>
            </div>
          </TabPane>
          
          <TabPane key="sop" title="SOP 编辑">
            <div style={{ 
              height: 'calc(90vh - 200px)', 
              overflowY: 'auto', 
              overflowX: 'hidden', 
              padding: '16px 0',
              scrollbarWidth: 'thin',
              scrollbarColor: '#c1c1c1 transparent'
            }}>
              {sopData ? (
                <ErrorBoundary
                  fallback={
                    <div className="text-center py-8">
                      <p className="text-red-500 mb-4">SOP编辑器加载失败</p>
                      <Button onClick={() => window.location.reload()}>
                        重新加载页面
                      </Button>
                    </div>
                  }
                >
                  <SchemaEditor
                    initialData={sopData}
                    evaluateResult={originalExpSopData?.evaluate_result && 
                      typeof originalExpSopData.evaluate_result === 'object' ? 
                      originalExpSopData.evaluate_result : undefined}
                    className="p-4"
                    onChange={handleSopDataUpdate}
                  />
                </ErrorBoundary>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <p>SOP 数据加载中...</p>
                </div>
              )}
            </div>
          </TabPane>
          
          {/* ExpSOP 变更确认 Tab */}
          {showDiffTab && (
            <TabPane key="diff" title="Diff 确认">
              <div style={{ 
                height: 'calc(90vh - 200px)', 
                overflow: 'auto', 
                padding: '16px 0',
                scrollbarWidth: 'thin',
                scrollbarColor: '#c1c1c1 transparent',
                minWidth: 0,
                width: '100%'
              }}>
                {originalSOPData && pendingSOPData ? (
                  <div className="space-y-4">
                    {/* 操作按钮 */}
                    <div className="mb-4">
                      <div className="bg-blue-50 border border-blue-200 rounded p-3 mb-3">
                        <div className="text-blue-800">
                          <strong>请确认以下变更：</strong>检测到 ExpSOP 数据有变更，请仔细查看差异内容
                        </div>
                      </div>
                      <div className="flex justify-end gap-3">
                        <button 
                          onClick={handleDiffCancel}
                          className="px-4 py-2 border border-gray-300 rounded bg-white hover:bg-gray-50"
                        >
                          取消变更
                        </button>
                        <button 
                          onClick={() => {
                            console.log('原生按钮被点击了！');
                            console.log('loading:', loading);
                            console.log('pendingSOPData:', pendingSOPData);
                            handleDiffConfirm();
                          }}
                          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 font-bold"
                          style={{ pointerEvents: 'auto', cursor: 'pointer' }}
                        >
                          ✓ 确认变更
                        </button>
                      </div>
                    </div>
                    
                    {/* Diff 展示 */}
                    <div style={{ minWidth: 0, width: '100%', overflow: 'hidden' }}>
                      <JsonDiffViewer
                        originalData={originalSOPData}
                        modifiedData={pendingSOPData}
                        title="SOP 变更对比"
                        showToolbar={true}
                        className="h-full"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <p>差异数据加载中...</p>
                  </div>
                )}
              </div>
            </TabPane>
          )}
        </Tabs>
      </ErrorBoundary>
    </Modal>
  );
});

// 设置displayName以便于调试
TemplateViewEditModal.displayName = 'TemplateViewEditModal';

export default TemplateViewEditModal;