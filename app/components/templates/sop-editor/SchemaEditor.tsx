"use client";

import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { Card, Form, Input, Button, Grid, Space, Upload, Spin, Collapse, Checkbox } from '@arco-design/web-react';
import { FileText, Plus, Trash2, Upload as UploadIcon, ChevronDown, ChevronRight } from 'lucide-react';
import { SOPSchema, sopSchemaSchema, Placeholder, PlanStep } from '@/app/lib/sop-schema';
import JsonPreview from './JsonPreview';
import PlaceholderHighlight from './PlaceholderHighlight';
import ErrorBoundary from './ErrorBoundary';
import { toast } from 'sonner';

const { Row, Col } = Grid;

// 懒加载StepEditor组件以减少初始加载时间
const StepEditor = React.lazy(() => import('./StepEditor'));

interface SchemaEditorProps {
  initialData: SOPSchema;
  className?: string;
  onChange?: (data: SOPSchema) => void;
  evaluateResult?: {
    score: number;
    aspects: Array<{
      aspect: string;
      score: number;
      justification: string;
    }>;
  };
}

const SchemaEditor: React.FC<SchemaEditorProps> = React.memo(({ initialData, onChange, className = '', evaluateResult }) => {

  const [form] = Form.useForm<SOPSchema>();
  const [formData, setFormData] = useState<SOPSchema>(initialData);
  const [isInitializing, setIsInitializing] = useState(true);
  const [expandedAspects, setExpandedAspects] = useState<Set<number>>(new Set());
  const [showAddPlaceholder, setShowAddPlaceholder] = useState(false);
  
  const [newPlaceholder, setNewPlaceholder] = useState<Placeholder>({ 
    name: '', 
    default: '', 
    description: '', 
    ref_attachments: false,
    required: false
  });

  // 当initialData变化时，更新formData
  useEffect(() => {
    setIsInitializing(true);
    setFormData(initialData);
    form.setFieldsValue(initialData);
    // 延迟设置初始化完成状态，避免立即触发onSave
    setTimeout(() => setIsInitializing(false), 0);
  }, []);

  const formDataRef = useRef<SOPSchema>(formData);
  // useEffect(() => {
  //   formDataRef.current = formData;
  // }, [formData]);

  const triggerOnChange = useCallback(() => {
    if (onChange) {
      console.log("onChange formData", formDataRef.current);
      onChange(formDataRef.current);
    }
  }, [onChange, initialData]);

  const updateFormData = useCallback((field: keyof SOPSchema, value: any) => {
    // 只有在值真正变化时才更新
    setFormData(prev => {
      let changed = true;
      // 对于 plan_steps，进行深度比较
      if (field === 'plan_steps') {
        const currentSteps = prev.plan_steps || [];
        const newSteps = value || [];
        
        // 更严格的深度比较：先检查长度，再检查每个步骤的内容
        if (currentSteps.length !== newSteps.length) {
          console.log('SchemaEditor: plan_steps 长度变化，需要更新');
          changed = true;
        } else {
          // 逐个比较每个步骤，特别注意 parameters 字段
          changed = currentSteps.some((currentStep, index) => {
            const newStep = newSteps[index];
            if (!newStep) return true;
            
            // 检查基本字段
            if (currentStep.name !== newStep.name ||
                currentStep.objective !== newStep.objective ||
                currentStep.assigned_to !== newStep.assigned_to ||
                currentStep.persona !== newStep.persona) {
              return true;
            }
            
            // 检查数组/对象字段
            if (JSON.stringify(currentStep.toolsets) !== JSON.stringify(newStep.toolsets) ||
                JSON.stringify(currentStep.phases) !== JSON.stringify(newStep.phases)) {
              return true;
            }
            
            // 特别检查 parameters 字段
            const currentParams = currentStep.parameters || {};
            const newParams = newStep.parameters || {};
            if (JSON.stringify(currentParams) !== JSON.stringify(newParams)) {
              console.log('SchemaEditor: 检测到 parameters 变化:', {
                current: currentParams,
                new: newParams
              });
              return true;
            }
            
            return false;
          });
        }
        
        if (!changed) {
          console.log('SchemaEditor: plan_steps 无变化，跳过更新');
        } else {
          console.log('SchemaEditor: plan_steps 有变化，更新状态');
        }
      }

      if (field === 'user_query_placeholders') {
        const currentPlaceholders = prev.user_query_placeholders || [];
        const newPlaceholders = value || [];
        if (currentPlaceholders.length !== newPlaceholders.length) {
          console.log('SchemaEditor: user_query_placeholders 长度变化，需要更新', value);
          changed = true;
        } else {
          changed = currentPlaceholders.some((currentPlaceholder, index) => {
            const newPlaceholder = newPlaceholders[index];
            if (!newPlaceholder) return true;
            return currentPlaceholder.name !== newPlaceholder.name ||
                   currentPlaceholder.default !== newPlaceholder.default ||
                   currentPlaceholder.description !== newPlaceholder.description ||
                   currentPlaceholder.ref_attachments !== newPlaceholder.ref_attachments ||
                   currentPlaceholder.required !== newPlaceholder.required;
          });
        }

        if (!changed) {
          console.log('SchemaEditor: user_query_placeholders 无变化，跳过更新');
        } else {
          console.log('SchemaEditor: user_query_placeholders 有变化，更新状态');
        }
      } else { // 对于其他字段，简单比较
        changed = prev[field] !== value;
      }

      if (changed) {
        console.log('SchemaEditor: 检测到', field, '变化，更新状态', value);
        const after = { ...prev, [field]: value };
        formDataRef.current = after;
        return after;
      }
      return prev;
    });
    
    // 触发延迟回调
    triggerOnChange();
  }, [triggerOnChange]);

  const toggleAspectExpansion = useCallback((index: number) => {
    setExpandedAspects(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  }, []);

  const addPlaceholder = useCallback(() => {
    if (!newPlaceholder.name) {
      toast.error('Placeholder name cannot be empty');
      return;
    }
    const newPlaceholders = [...(formData.user_query_placeholders || []), newPlaceholder];
    updateFormData('user_query_placeholders', newPlaceholders);
    setNewPlaceholder({ name: '', default: '', description: '', ref_attachments: false, required: false });
    setShowAddPlaceholder(false);
  }, [newPlaceholder, formData.user_query_placeholders, updateFormData]);

  const removePlaceholder = useCallback((index: number) => {
    const newPlaceholders = (formData.user_query_placeholders || []).filter((_, i) => i !== index);
    updateFormData('user_query_placeholders', newPlaceholders);
  }, [formData.user_query_placeholders, updateFormData]);

  const updatePlaceholder = useCallback((index: number, field: keyof Placeholder, value: any) => {
    const newPlaceholders = [...(formData.user_query_placeholders || [])];
    if (newPlaceholders[index]) {
      newPlaceholders[index] = { ...newPlaceholders[index], [field]: value };
      updateFormData('user_query_placeholders', newPlaceholders);
    }
  }, [formData.user_query_placeholders, updateFormData]);

  const handleImportFromClipboard = useCallback(async () => {
    try {
      if (!navigator.clipboard) {
        throw new Error('Clipboard API not available');
      }
      const text = await navigator.clipboard.readText();
      if (!text.trim()) {
        throw new Error('Clipboard content is empty');
      }
      const data = JSON.parse(text);
      const validatedData = sopSchemaSchema.parse(data);
      setFormData(validatedData);
      form.setFieldsValue(validatedData);
      if (onChange) {
        onChange(validatedData);
      }
      toast.success("Import from clipboard successful");
    } catch (error) {
      console.error('Import error:', error);
      if (error instanceof SyntaxError) {
        toast.error("Import failed: Invalid JSON format");
      } else if (error instanceof Error) {
        toast.error(`Import failed: ${error.message}`);
      } else {
        toast.error("Import failed: Unknown error");
      }
    }
  }, [form, onChange]);

  const handleImportFromFile = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string;
        if (!text.trim()) {
          throw new Error('File content is empty');
        }
        const data = JSON.parse(text);
        const validatedData = sopSchemaSchema.parse(data);
        setFormData(validatedData);
        form.setFieldsValue(validatedData);
        if (onChange) {
          onChange(validatedData);
        }
        toast.success("File import successful");
      } catch (error) {
        console.error('Import error:', error);
        if (error instanceof SyntaxError) {
          toast.error("Import failed: Invalid JSON format in file");
        } else if (error instanceof Error) {
          toast.error(`Import failed: ${error.message}`);
        } else {
          toast.error("Import failed: Unknown error");
        }
      }
    };
    reader.onerror = () => {
      toast.error("File reading failed");
    };
    reader.readAsText(file);
    return false;
  }, [form, onChange]);

  const placeholderPreview = useMemo(() => {
    if (!formData.user_query) return null;
    return (
      <div className="mt-4 p-4 bg-gray-50 rounded border">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Preview:</h4>
        <PlaceholderHighlight
          text={formData.user_query}
          placeholders={formData.user_query_placeholders}
          className="text-sm"
        />
      </div>
    );
  }, [formData.user_query, formData.user_query_placeholders]);

  // 主组件即将渲染

  return (
    <ErrorBoundary fallback={
      <div className="text-center py-10">
        <h3 className="text-lg font-semibold text-red-600">SOP Editor Core Component Failed to Load</h3>
        <p className="text-gray-600">Please try refreshing or contact technical support.</p>
      </div>
    }>
      <div className={`space-y-6 w-full max-w-none ${className}`}>
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">SOP Editor</h2>
          <Space>
            <Button 
              type="outline" 
              onClick={handleImportFromClipboard}
              style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
            >
              <FileText size={16} />
              Import from Clipboard
            </Button>
            <Upload beforeUpload={handleImportFromFile} showUploadList={false} accept=".json">
              <Button 
                type="outline" 
                style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
              >
                <UploadIcon size={16} />
                Import from File
              </Button>
            </Upload>
          </Space>
        </div>

        <Row gutter={32}>
          <Col span={16}>
            <div className="space-y-6">
              <Card title="Basic Information" className="w-full">
                <Form form={form} layout="vertical">
                  <Form.Item 
                    label="Name" 
                    field="name" 
                    required 
                    rules={[{ required: true, message: 'Name cannot be empty' }]}
                  >
                    <Input 
                      placeholder="Please enter SOP name" 
                      value={formData.name} 
                      onChange={(value) => updateFormData('name', value)} 
                    />
                  </Form.Item>
                  <Form.Item 
                    label="Usage Scenario" 
                    field="used_when" 
                    required 
                    rules={[{ required: true, message: 'Usage scenario cannot be empty' }]}
                  >
                    <Input.TextArea 
                      placeholder="Please describe the usage scenario for this SOP" 
                      autoSize={{ minRows: 2, maxRows: 4 }} 
                      value={formData.used_when} 
                      onChange={(value) => updateFormData('used_when', value)} 
                    />
                  </Form.Item>
                  <Form.Item 
                    label="Progress Plan" 
                    field="progress_plan" 
                    required 
                    rules={[{ required: true, message: 'Progress plan cannot be empty' }]}
                  >
                    <Input.TextArea 
                      placeholder="Please enter progress plan" 
                      autoSize={{ minRows: 3, maxRows: 6 }} 
                      value={formData.progress_plan} 
                      onChange={(value) => updateFormData('progress_plan', value)} 
                    />
                  </Form.Item>
                </Form>
              </Card>

              <Card title="User Query Template" className="w-full">
                <Form layout="vertical">
                  <Form.Item 
                    label="Query Template" 
                    required 
                    rules={[{ required: true, message: 'Query template cannot be empty' }]}
                  >
                    <Input.TextArea 
                      placeholder="Please enter user query template, use {placeholder_name} format to define placeholders" 
                      autoSize={{ minRows: 4, maxRows: 8 }} 
                      value={formData.user_query} 
                      onChange={(value) => updateFormData('user_query', value)} 
                    />
                  </Form.Item>
                  {placeholderPreview}
                </Form>
              </Card>

              <Card title="Placeholder Configuration" className="w-full">
                <div className="space-y-4">

                  {/* 现有占位符列表 */}
                  {(formData.user_query_placeholders || []).map((placeholder, index) => {
                    if (!placeholder || !placeholder.name) return null;
                    return (
                      <div key={index} className="border rounded-lg p-4 bg-white relative">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="text-sm font-medium text-gray-600">Placeholder #{index + 1}</h4>
                          <Button 
                            type="text" 
                            status="danger" 
                            onClick={() => removePlaceholder(index)}
                            size="small"
                            style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                          >
                            <Trash2 size={16} />
                          </Button>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                            <Input 
                              placeholder="Placeholder name" 
                              value={placeholder.name || ''} 
                              onChange={(value) => updatePlaceholder(index, 'name', value)} 
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Default Value</label>
                            <Input 
                              placeholder="Default value" 
                              value={placeholder.default || ''} 
                              onChange={(value) => updatePlaceholder(index, 'default', value)} 
                            />
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                          <Input.TextArea 
                            placeholder="Description" 
                            value={placeholder.description || ''} 
                            onChange={(value) => updatePlaceholder(index, 'description', value)} 
                            autoSize={{ minRows: 2, maxRows: 4 }}
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4 mt-4">
                          <div>
                            <Checkbox 
                              checked={placeholder.ref_attachments || false}
                              onChange={(checked) => updatePlaceholder(index, 'ref_attachments', checked)}
                            >
                              Ref Attachments
                            </Checkbox>
                          </div>
                          <div>
                            <Checkbox 
                              checked={placeholder.required || false}
                              onChange={(checked) => updatePlaceholder(index, 'required', checked)}
                            >
                              Required
                            </Checkbox>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  {(formData.user_query_placeholders || []).length === 0 && !showAddPlaceholder && (
                    <div className="text-center py-8 text-gray-500">
                      <p>No placeholder configuration</p>
                    </div>
                  )}

                  {/* 添加新占位符按钮和表单 */}
                  {showAddPlaceholder && (
                    <Card
                      size="small"
                      className="border border-gray-200 bg-gray-50"
                      title={
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-600">New Placeholder</span>
                          <Button
                            type="text"
                            size="small"
                            onClick={() => setShowAddPlaceholder(false)}
                            style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                          >
                            <ChevronRight size={14} />
                          </Button>
                        </div>
                      }
                    >
                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                            <Input 
                              size="small"
                              placeholder="Placeholder name" 
                              value={newPlaceholder.name} 
                              onChange={(value) => setNewPlaceholder({ ...newPlaceholder, name: value })} 
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Default Value</label>
                            <Input 
                              size="small"
                              placeholder="Default value" 
                              value={newPlaceholder.default} 
                              onChange={(value) => setNewPlaceholder({ ...newPlaceholder, default: value })} 
                            />
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                          <Input.TextArea 
                            placeholder="Description" 
                            value={newPlaceholder.description} 
                            onChange={(value) => setNewPlaceholder({ ...newPlaceholder, description: value })} 
                            autoSize={{ minRows: 2, maxRows: 4 }}
                          />
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <Checkbox 
                              checked={newPlaceholder.ref_attachments || false}
                              onChange={(checked) => setNewPlaceholder({ ...newPlaceholder, ref_attachments: checked })}
                            >
                              Ref Attachments
                            </Checkbox>
                          </div>
                          <div>
                            <Checkbox 
                              checked={newPlaceholder.required || false}
                              onChange={(checked) => setNewPlaceholder({ ...newPlaceholder, required: checked })}
                            >
                              Required
                            </Checkbox>
                          </div>
                        </div>
                        
                        <div className="flex justify-end">
                          <Button 
                            type="primary" 
                            size="small"
                            onClick={addPlaceholder}
                            style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                          >
                            <Plus size={14} />
                            Add Placeholder
                          </Button>
                        </div>
                      </div>
                    </Card>
                  )}

                  <Button
                    type="dashed"
                    size="small"
                    onClick={() => setShowAddPlaceholder(!showAddPlaceholder)}
                    className="w-full"
                    style={{ display: 'flex', alignItems: 'center', gap: '4px', justifyContent: 'center' }}
                  >
                    <Plus size={14} />
                    Add Placeholder
                  </Button>
                </div>
              </Card>

              <Card title="Plan Steps" className="w-full">
                <React.Suspense fallback={
                  <div className="flex items-center justify-center py-8">
                    <Spin />
                    <p className='ml-2'>Loading step editor...</p>
                  </div>
                }>
                  <StepEditor 
                    steps={formData.plan_steps} 
                    onChange={(steps) => updateFormData('plan_steps', steps)} 
                  />
                </React.Suspense>
              </Card>



              {/* SOP编辑结果自动暂存，通过模态框的确认按钮进行最终保存 */}
              <div className="flex justify-end">
                <div className="text-sm text-gray-500">
                  编辑结果将自动暂存，点击模态框的"确认"按钮进行最终保存
                </div>
              </div>
            </div>
          </Col>

          <Col span={8}>
            <div className="sticky top-4 space-y-4">
              {/* SOP评分展示 - 简洁版 */}
              {evaluateResult && 
               evaluateResult.score !== undefined && 
               evaluateResult.aspects && 
               Array.isArray(evaluateResult.aspects) && 
               evaluateResult.aspects.length > 0 && (
                <Card className="w-full">
                  <div className="space-y-3">
                    {/* 总分展示 - 简洁版 */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-sm font-bold">{evaluateResult.score}</span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">SOP Score</div>
                          <div className="text-xs text-gray-500">{evaluateResult.score}/15 ({Math.round((evaluateResult.score / 15) * 100)}%)</div>
                        </div>
                      </div>
                    </div>

                    {/* 各方面评分 - 简洁版 */}
                    <div className="space-y-2">
                      {evaluateResult.aspects.filter(aspect => 
                        aspect && 
                        typeof aspect === 'object' && 
                        aspect.aspect && 
                        typeof aspect.score === 'number' &&
                        aspect.justification
                      ).map((aspect, index) => {
                        const isExpanded = expandedAspects.has(index);
                        
                        return (
                          <div key={index} className="border rounded p-2 bg-gray-50">
                            <div 
                              className="flex items-center justify-between cursor-pointer hover:bg-gray-100 rounded p-1"
                              onClick={() => toggleAspectExpansion(index)}
                            >
                              <div className="flex items-center space-x-2">
                                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold ${
                                  aspect.score >= 4 ? 'bg-green-500' : 
                                  aspect.score >= 3 ? 'bg-yellow-500' : 'bg-red-500'
                                }`}>
                                  {aspect.score}
                                </div>
                                <span className="text-sm font-medium text-gray-800">{aspect.aspect}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <span className="text-xs text-gray-500">{aspect.score}/5</span>
                                {isExpanded ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
                              </div>
                            </div>
                            
                            {isExpanded && (
                              <div className="mt-2 p-2 bg-white rounded text-xs text-gray-700 leading-relaxed whitespace-pre-wrap border-l-2 border-blue-200">
                                {aspect.justification.trim()}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </Card>
              )}
              
              {/* 无评分数据时的提示 */}
              {evaluateResult && 
               (!evaluateResult.aspects || 
                !Array.isArray(evaluateResult.aspects) || 
                evaluateResult.aspects.length === 0) && (
                <Card className="w-full">
                  <div className="text-center py-4 text-gray-500">
                    <div className="text-sm">No evaluation data available</div>
                  </div>
                </Card>
              )}
              
              <JsonPreview data={formData} />
            </div>
          </Col>
        </Row>
      </div>
    </ErrorBoundary>
  );
});

SchemaEditor.displayName = 'SchemaEditor';

export default SchemaEditor;