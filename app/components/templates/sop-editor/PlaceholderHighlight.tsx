"use client";

import React, { useMemo } from 'react';
import { Placeholder } from '@/app/lib/sop-schema';

interface PlaceholderHighlightProps {
  text: string;
  placeholders: Placeholder[];
  className?: string;
}

const PlaceholderHighlight: React.FC<PlaceholderHighlightProps> = ({
  text,
  placeholders,
  className = ''
}) => {
  // 早期返回处理边界情况
  if (!text || !placeholders || placeholders.length === 0) {
    return <span className={`${className} whitespace-pre-wrap`}>{text}</span>;
  }

  // 使用useMemo缓存有效占位符的过滤和正则表达式计算
  const { validPlaceholders, pattern } = useMemo(() => {
    // 过滤出有效的占位符
    const valid = placeholders.filter(p => p && p.name && typeof p.name === 'string');
    if (valid.length === 0) {
      return { validPlaceholders: [], pattern: null };
    }
    
    // 创建正则表达式模式来匹配所有占位符
    const placeholderNames = valid.map(p => p.name);
    const regex = new RegExp(`\\{(${placeholderNames.join('|')})\\}`, 'g');
    
    return { validPlaceholders: valid, pattern: regex };
  }, [placeholders]);

  if (validPlaceholders.length === 0) {
    return <span className={`${className} whitespace-pre-wrap`}>{text}</span>;
  }
  
  // 检查pattern是否有效
  if (!pattern) {
    return <span className={`${className} whitespace-pre-wrap`}>{text}</span>;
  }
  
  // Split text by placeholders and create highlighted spans
  const parts = [];
  let lastIndex = 0;
  let match;

  while ((match = pattern.exec(text)) !== null) {
    // Add text before the placeholder
    if (match.index > lastIndex) {
      parts.push(
        <span key={`text-${lastIndex}`} className="whitespace-pre-wrap">
          {text.slice(lastIndex, match.index)}
        </span>
      );
    }

    // Add the highlighted placeholder
    const placeholderName = match[1];
    const placeholder = validPlaceholders.find(p => p && p.name === placeholderName);
    
    parts.push(
      <span
        key={`placeholder-${match.index}`}
        className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded text-sm font-medium border border-blue-200"
        title={placeholder?.description || placeholderName}
      >
        {match[0]}
      </span>
    );

    lastIndex = pattern.lastIndex;
  }

  // Add remaining text
  if (lastIndex < text.length) {
    parts.push(
      <span key={`text-${lastIndex}`} className="whitespace-pre-wrap">
        {text.slice(lastIndex)}
      </span>
    );
  }

  return <span className={`${className} whitespace-pre-wrap`}>{parts}</span>;
};

export default PlaceholderHighlight;