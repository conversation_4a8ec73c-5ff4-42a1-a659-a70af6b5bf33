"use client";

import React, { useMemo, useCallback, useState } from 'react';
import { Card, Button, Switch, Space, Typography, Divider } from '@arco-design/web-react';
import { Eye, EyeOff, Copy, Download, RotateCcw } from 'lucide-react';
import { toast } from "sonner";
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer';
import { SOPSchema } from '@/app/lib/sop-schema';

const { Title, Text } = Typography;

interface JsonDiffViewerProps {
  /** 原始数据 */
  originalData: SOPSchema;
  /** 修改后的数据 */
  modifiedData: SOPSchema;
  /** 自定义CSS类名 */
  className?: string;
  /** 组件标题 */
  title?: string;
  /** 是否显示工具栏 */
  showToolbar?: boolean;
  /** 差异检测方法 */
  diffMethod?: DiffMethod;
}

/**
 * JSON差异查看器组件
 * 支持忽略对象键顺序、空格等非内容变化的智能差异对比
 */
const JsonDiffViewer: React.FC<JsonDiffViewerProps> = React.memo(({
  originalData,
  modifiedData,
  className = '',
  title = 'JSON 变更对比',
  showToolbar = true,
  diffMethod = DiffMethod.CHARS
}) => {
  // 组件状态
  const [ignoreKeyOrder, setIgnoreKeyOrder] = useState(true);
  const [ignoreWhitespace, setIgnoreWhitespace] = useState(true);
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const [splitView, setSplitView] = useState(true);

  /**
   * 深度排序对象键，确保对象键的顺序一致
   */
  const sortObjectKeys = useCallback((obj: any): any => {
    if (obj === null || obj === undefined) {
      return obj;
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sortObjectKeys);
    }
    
    if (typeof obj === 'object' && obj.constructor === Object) {
      const sortedObj: any = {};
      const keys = Object.keys(obj).sort();
      for (const key of keys) {
        sortedObj[key] = sortObjectKeys(obj[key]);
      }
      return sortedObj;
    }
    
    return obj;
  }, []);

  /**
   * 标准化 JSON 字符串
   * 根据设置忽略键顺序和空格变化
   */
  const normalizeJson = useCallback((data: SOPSchema): string => {
    try {
      let processedData = data;
      
      // 如果需要忽略键顺序，则对对象进行排序
      if (ignoreKeyOrder) {
        processedData = sortObjectKeys(data) as SOPSchema;
      }
      
      // 生成 JSON 字符串
      let jsonString = JSON.stringify(processedData, null, ignoreWhitespace ? 0 : 2);
      
      // 如果需要忽略空格，则移除多余的空格和换行
      if (ignoreWhitespace) {
        jsonString = JSON.stringify(JSON.parse(jsonString), null, 2);
      }
      
      return jsonString;
    } catch (error) {
      console.error('JSON 标准化失败:', error);
      return '{}';
    }
  }, [ignoreKeyOrder, ignoreWhitespace, sortObjectKeys]);

  // 标准化后的 JSON 字符串
  const { originalJson, modifiedJson } = useMemo(() => {
    return {
      originalJson: normalizeJson(originalData),
      modifiedJson: normalizeJson(modifiedData)
    };
  }, [originalData, modifiedData, normalizeJson]);

  // 检查是否有差异
  const hasDifferences = useMemo(() => {
    return originalJson !== modifiedJson;
  }, [originalJson, modifiedJson]);

  // 差异统计
  const diffStats = useMemo(() => {
    const originalLines = originalJson.split('\n');
    const modifiedLines = modifiedJson.split('\n');
    
    // 简单的行级差异统计
    let addedLines = 0;
    let removedLines = 0;
    let modifiedLines_count = 0;
    
    const maxLines = Math.max(originalLines.length, modifiedLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i] || '';
      const modifiedLine = modifiedLines[i] || '';
      
      if (originalLine && !modifiedLine) {
        removedLines++;
      } else if (!originalLine && modifiedLine) {
        addedLines++;
      } else if (originalLine !== modifiedLine) {
        modifiedLines_count++;
      }
    }
    
    return {
      added: addedLines,
      removed: removedLines,
      modified: modifiedLines_count,
      total: maxLines
    };
  }, [originalJson, modifiedJson]);

  // 复制差异内容
  const handleCopyDiff = useCallback(async () => {
    try {
      const diffContent = `=== 原始版本 ===\n${originalJson}\n\n=== 修改版本 ===\n${modifiedJson}`;
      await navigator.clipboard.writeText(diffContent);
      toast.success("差异内容已复制到剪贴板");
    } catch (error) {
      console.error('复制失败:', error);
      toast.error("复制失败");
    }
  }, [originalJson, modifiedJson]);

  // 下载差异报告
  const handleDownloadDiff = useCallback(() => {
    try {
      const diffReport = {
        timestamp: new Date().toISOString(),
        statistics: diffStats,
        settings: {
          ignoreKeyOrder,
          ignoreWhitespace,
        },
        original: originalData,
        modified: modifiedData,
        originalJson,
        modifiedJson
      };
      
      const blob = new Blob([JSON.stringify(diffReport, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `json-diff-report-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success("差异报告已下载");
    } catch (error) {
      console.error('下载失败:', error);
      toast.error("下载失败");
    }
  }, [diffStats, ignoreKeyOrder, ignoreWhitespace, originalData, modifiedData, originalJson, modifiedJson]);

  // 重置设置
  const handleResetSettings = useCallback(() => {
    setIgnoreKeyOrder(true);
    setIgnoreWhitespace(true);
    setShowLineNumbers(true);
    setSplitView(true);
    toast.success("设置已重置");
  }, []);

  return (
    <Card
      className={`h-full ${className}`}
      title={
        <div className="flex items-center gap-3">
          <Title heading={5} style={{ margin: 0 }}>
            {title}
          </Title>
          {!hasDifferences && (
            <Text type="success" className="text-sm">
              ✓ 无差异
            </Text>
          )}
          {hasDifferences && (
            <Text type="warning" className="text-sm">
              ⚠ 发现差异
            </Text>
          )}
        </div>
      }
      extra={
        showToolbar && (
          <Space size="small" className="flex items-center">
            <Button
              type="text"
              size="small"
              onClick={handleCopyDiff}
              style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
            >
              <Copy size={16} />
              复制
            </Button>
            <Button
              type="text"
              size="small"
              onClick={handleDownloadDiff}
              style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
            >
              <Download size={16} />
              下载
            </Button>
            <Button
              type="text"
              size="small"
              onClick={handleResetSettings}
              style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
            >
              <RotateCcw size={16} />
              重置
            </Button>
          </Space>
        )
      }
    >
      <div className="h-full flex flex-col">
        {/* 控制面板 */}
        {showToolbar && (
          <div className="mb-4 p-3 bg-gray-50 rounded border">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="flex items-center gap-3 min-w-0">
                <Text className="text-sm whitespace-nowrap">忽略键顺序</Text>
                <Switch
                  size="small"
                  checked={ignoreKeyOrder}
                  onChange={setIgnoreKeyOrder}
                />
              </div>
              <div className="flex items-center gap-3 min-w-0">
                <Text className="text-sm whitespace-nowrap">忽略空格</Text>
                <Switch
                  size="small"
                  checked={ignoreWhitespace}
                  onChange={setIgnoreWhitespace}
                />
              </div>
              <div className="flex items-center gap-3 min-w-0">
                <Text className="text-sm whitespace-nowrap">显示行号</Text>
                <Switch
                  size="small"
                  checked={showLineNumbers}
                  onChange={setShowLineNumbers}
                />
              </div>
              <div className="flex items-center gap-3 min-w-0">
                <Text className="text-sm whitespace-nowrap">分屏显示</Text>
                <Switch
                  size="small"
                  checked={splitView}
                  onChange={setSplitView}
                />
              </div>
            </div>
            
            {/* 差异统计 */}
            {hasDifferences && (
              <>
                <Divider style={{ margin: '12px 0' }} />
                <div className="flex items-center gap-4 text-sm">
                  <Text type="success">新增: {diffStats.added} 行</Text>
                  <Text type="error">删除: {diffStats.removed} 行</Text>
                  <Text type="warning">修改: {diffStats.modified} 行</Text>
                  <Text>总计: {diffStats.total} 行</Text>
                </div>
              </>
            )}
          </div>
        )}

        {/* 差异展示区域 */}
        <div className="flex-1 overflow-hidden">
          {!hasDifferences ? (
            <div className="h-full flex items-center justify-center bg-green-50 border border-green-200 rounded">
              <div className="text-center">
                <div className="text-green-600 text-4xl mb-2">✓</div>
                <Text className="text-green-800 text-lg font-medium">数据无差异</Text>
                <Text className="text-green-600 text-sm mt-1">
                  两个版本的 JSON 数据完全一致
                </Text>
              </div>
            </div>
          ) : (
            <div 
              className="h-full border rounded" 
              style={{ 
                overflow: 'auto',
                minWidth: 0,
                width: '100%'
              }}
            >
              <ReactDiffViewer
                oldValue={originalJson}
                newValue={modifiedJson}
                splitView={splitView}
                showDiffOnly={false}
                hideLineNumbers={!showLineNumbers}
                compareMethod={diffMethod}
                leftTitle="原始版本"
                rightTitle="修改版本"
                styles={{
                  variables: {
                    light: {
                      codeFoldGutterBackground: '#f7f7f7',
                      codeFoldBackground: '#f1f8ff',
                      diffViewerBackground: '#fff',
                      addedBackground: '#e6ffed',
                      addedColor: '#24292e',
                      removedBackground: '#ffeef0',
                      removedColor: '#24292e',
                      wordAddedBackground: '#acf2bd',
                      wordRemovedBackground: '#fdb8c0',
                      addedGutterBackground: '#cdffd8',
                      removedGutterBackground: '#fdbdbd',
                      gutterBackground: '#f7f7f7',
                      gutterBackgroundDark: '#f3f1f1',
                      highlightBackground: '#fffbdd',
                      highlightGutterBackground: '#fff5b4',
                    },
                  },
                  diffContainer: {
                    width: '100%',
                    overflowX: 'auto',
                    minWidth: 0,
                  },
                  splitView: {
                    width: '100%',
                    overflowX: 'auto',
                  },
                  line: {
                    padding: '10px 2px',
                    whiteSpace: 'pre',
                    wordBreak: 'keep-all',
                    overflowWrap: 'normal',
                    '&:hover': {
                      background: '#f7f7f7',
                    },
                  },
                  marker: {
                    fontSize: '12px',
                    minWidth: 'auto',
                  },
                  content: {
                    fontSize: '13px',
                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                    whiteSpace: 'pre',
                    wordBreak: 'keep-all',
                    overflowWrap: 'normal',
                    minWidth: 0,
                    width: 'max-content',
                  },
                  contentText: {
                    whiteSpace: 'pre',
                    wordBreak: 'keep-all',
                    overflowWrap: 'normal',
                  },
                }}
              />
            </div>
          )}
        </div>
      </div>
    </Card>
  );
});

// 设置displayName以便于调试
JsonDiffViewer.displayName = 'JsonDiffViewer';

export default JsonDiffViewer;
