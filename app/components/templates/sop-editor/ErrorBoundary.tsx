"use client";

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, Button, Alert } from '@arco-design/web-react';
import { AlertTriangle, RefreshCw, Bug } from 'lucide-react';
import { toast } from "sonner";

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * SOP编辑器错误边界组件
 * 捕获并优雅处理React组件树中的JavaScript错误
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // 更新state以显示错误UI
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    console.error('SOP编辑器错误边界捕获到错误:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // 调用自定义错误处理函数
    this.props.onError?.(error, errorInfo);

    // 显示用户友好的错误提示
    toast.error(`组件发生错误: ${error.message}`);
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleReportError = () => {
    const { error, errorInfo } = this.state;
    if (error && errorInfo) {
      // 这里可以集成错误报告服务
      console.log('报告错误:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
      });
      toast.success('错误已报告，感谢您的反馈');
    }
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误UI
      return (
        <Card className="m-4">
          <div className="text-center py-8">
            <AlertTriangle className="mx-auto mb-4 text-red-500" size={48} />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              组件加载出错
            </h3>
            <p className="text-gray-600 mb-6">
              {this.state.error?.message || '发生了未知错误，请稍后重试'}
            </p>
            
            <div className="space-y-4">
              <div className="flex justify-center gap-4">
                <Button
                  type="primary"
                  onClick={this.handleRetry}
                  style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                >
                  <RefreshCw size={16} />
                  重新加载
                </Button>
                <Button
                  type="outline"
                  onClick={this.handleReportError}
                  style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                >
                  <Bug size={16} />
                  报告问题
                </Button>
              </div>

              {/* 开发环境下显示详细错误信息 */}
              {this.props.showDetails && this.state.error && (
                <Alert
                  type="error"
                  title="详细错误信息（仅开发环境）"
                  content={
                    <div className="text-left">
                      <p className="font-mono text-sm mb-2">
                        <strong>错误:</strong> {this.state.error.message}
                      </p>
                      {this.state.error.stack && (
                        <details className="mt-2">
                          <summary className="cursor-pointer font-semibold">
                            错误堆栈
                          </summary>
                          <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
                            {this.state.error.stack}
                          </pre>
                        </details>
                      )}
                      {this.state.errorInfo?.componentStack && (
                        <details className="mt-2">
                          <summary className="cursor-pointer font-semibold">
                            组件堆栈
                          </summary>
                          <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
                            {this.state.errorInfo.componentStack}
                          </pre>
                        </details>
                      )}
                    </div>
                  }
                />
              )}
            </div>
          </div>
        </Card>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;