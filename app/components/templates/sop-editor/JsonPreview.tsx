"use client";

import React, { useMemo, useCallback } from 'react';
import { Card, Button } from '@arco-design/web-react';
import { Copy, Download } from 'lucide-react';
import { toast } from "sonner";
import { SOPSchema } from '@/app/lib/sop-schema';

interface JsonPreviewProps {
  data: SOPSchema;
  className?: string;
}

/**
 * JSON预览组件 - 使用React.memo优化性能，避免不必要的重渲染
 * @param data - SOP数据结构
 * @param className - 自定义CSS类名
 */
const JsonPreview: React.FC<JsonPreviewProps> = React.memo(({ data, className = '' }) => {
  // 使用useMemo缓存JSON字符串，只有当data变化时才重新计算
  const jsonString = useMemo(() => {
    try {
      // 深度比较确保实时更新
      const serializedData = JSON.stringify(data, null, 2);
      // console.log('JsonPreview: 数据更新，重新生成JSON字符串', data);
      return serializedData;
    } catch (error) {
      console.error('JSON序列化失败:', error);
      return '{}'; // 返回空对象作为fallback
    }
  }, [data]);

  // 使用useCallback缓存事件处理函数，避免不必要的重新创建
  const handleCopy = useCallback(async () => {
    try {
      if (!navigator.clipboard) {
        throw new Error('剪贴板API不可用');
      }
      await navigator.clipboard.writeText(jsonString);
      toast.success("JSON已复制到剪贴板");
    } catch (error) {
      console.error('复制失败:', error);
      // 提供更具体的错误信息
      if (error instanceof Error) {
        toast.error(`复制失败: ${error.message}`);
      } else {
        toast.error("复制失败：未知错误");
      }
    }
  }, [jsonString]);

  const handleDownload = useCallback(() => {
    try {
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${data.name || 'sop-schema'}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success("JSON文件已下载");
    } catch (error) {
      console.error('下载失败:', error);
      // 提供更具体的错误信息
      if (error instanceof Error) {
        toast.error(`下载失败: ${error.message}`);
      } else {
        toast.error("下载失败：未知错误");
      }
    }
  }, [jsonString, data.name]);

  return (
    <Card
      className={`h-full ${className}`}
      title="JSON 预览"
      extra={
        <div className="flex gap-2">
          <Button
            type="text"
            size="small"
            onClick={handleCopy}
            style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
          >
            <Copy size={16} />
            复制
          </Button>
          <Button
            type="text"
            size="small"
            onClick={handleDownload}
            style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
          >
            <Download size={16} />
            下载
          </Button>
        </div>
      }
    >
      <div className="h-full overflow-auto">
        <pre className="text-sm bg-gray-50 p-4 rounded border h-full overflow-auto whitespace-pre-wrap break-words">
          {jsonString}
        </pre>
      </div>
    </Card>
  );
});

// 设置displayName以便于调试
JsonPreview.displayName = 'JsonPreview';

export default JsonPreview;