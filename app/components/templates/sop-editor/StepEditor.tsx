
"use client";

import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { Card, Form, Input, Button, Select, Tag } from '@arco-design/web-react';
import { ChevronDown, ChevronRight, Plus, Trash2, X, Settings, Code } from 'lucide-react';
import { PlanStep } from '@/app/lib/sop-schema';
import PhaseEditor from './PhaseEditor';

// Parameters 编辑器组件
interface ParametersEditorProps {
  parameters: Record<string, string>;
  stepIndex: number;
  onAdd: (stepIndex: number, key: string, value: string) => void;
  onUpdate: (stepIndex: number, oldKey: string, newKey: string, value: string) => void;
  onRemove: (stepIndex: number, key: string) => void;
}

const ParametersEditor: React.FC<ParametersEditorProps> = ({
  parameters,
  stepIndex,
  onAdd,
  onUpdate,
  onRemove
}) => {
  const [newParamKey, setNewParamKey] = useState('');
  const [newParamValue, setNewParamValue] = useState('');
  const [isAddingNew, setIsAddingNew] = useState(false);

  const handleAddParameter = () => {
    if (newParamKey.trim() && newParamValue.trim()) {
      onAdd(stepIndex, newParamKey.trim(), newParamValue.trim());
      setNewParamKey('');
      setNewParamValue('');
      setIsAddingNew(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleAddParameter();
    }
  };

  const handleCancelAdd = () => {
    setNewParamKey('');
    setNewParamValue('');
    setIsAddingNew(false);
  };

  const parameterEntries = Object.entries(parameters);

  return (
    <div className="space-y-3">
      {/* 现有参数列表 */}
      {parameterEntries.length > 0 ? (
        <div className="space-y-3">
          {parameterEntries.map(([key, value]) => (
            <div key={key} className="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-gray-300 transition-colors">
              <div className="flex items-start gap-3">
                <div className="flex-1 space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name
                    </label>
                    <Input
                      placeholder="e.g., max_tokens, temperature"
                      value={key}
                      onChange={(newKey) => onUpdate(stepIndex, key, newKey, value)}
                      size="small"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Value
                    </label>
                    <Input.TextArea
                      placeholder="Parameter value or description"
                      value={value}
                      onChange={(newValue) => onUpdate(stepIndex, key, key, newValue)}
                      autoSize={{ minRows: 2, maxRows: 4 }}
                    />
                  </div>
                </div>
                <Button
                  type="text"
                  status="danger"
                  onClick={() => onRemove(stepIndex, key)}
                  size="small"
                  style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    marginTop: '20px',
                    padding: '4px 8px'
                  }}
                  title="Delete parameter"
                >
                  <Trash2 size={14} />
                </Button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-6 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
          <div className="text-sm">No parameters configured</div>
          <div className="text-xs mt-1">Click "Add Parameter" to get started</div>
        </div>
      )}

      {/* 添加新参数区域 */}
      {isAddingNew ? (
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Parameter Name <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="e.g., task, question, etc."
                value={newParamKey}
                onChange={setNewParamKey}
                onKeyPress={handleKeyPress}
                size="small"
                autoFocus
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Parameter Value <span className="text-red-500">*</span>
              </label>
              <Input.TextArea
                placeholder="Parameter value"
                value={newParamValue}
                onChange={setNewParamValue}
                onKeyPress={handleKeyPress}
                autoSize={{ minRows: 2, maxRows: 4 }}
              />
            </div>
            <div className="flex gap-2 justify-end pt-2">
              <Button
                size="small"
                onClick={handleCancelAdd}
              >
                Cancel
              </Button>
              <Button
                type="primary"
                size="small"
                onClick={handleAddParameter}
                disabled={!newParamKey.trim() || !newParamValue.trim()}
                style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
              >
                <Plus size={14} className="mr-1" />
                Add Parameter
              </Button>
            </div>
            <div className="text-xs text-gray-500 mt-2">
              💡 Tip: Press Ctrl+Enter to quickly add the parameter
            </div>
          </div>
        </div>
      ) : (
        <Button
          type="outline"
          onClick={() => setIsAddingNew(true)}
          style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '6px',
            width: '100%',
            justifyContent: 'center',
            padding: '8px 16px',
            borderStyle: 'dashed'
          }}
        >
          <Plus size={16} />
          Add Parameter
        </Button>
      )}
    </div>
  );
};

interface StepEditorProps {
  steps: PlanStep[];
  onChange: (steps: PlanStep[]) => void;
}

const StepEditor: React.FC<StepEditorProps> = ({ steps, onChange }) => {
  const safeSteps = useMemo(() => {
    return Array.isArray(steps) ? steps : [];
  }, [steps]);

  // 本地状态管理步骤数据
  const [localSteps, setLocalSteps] = useState<PlanStep[]>(safeSteps);

  // 使用 ref 来跟踪是否正在同步，避免循环更新
  const isSyncingRef = useRef(false);

  // 保存上一次的外部 steps 引用，用于检测真正的外部变化
  const prevSafeStepsRef = useRef<PlanStep[]>(safeSteps);
  
  // 当外部 steps 变化时，更新本地状态（但要避免循环更新）
  useEffect(() => {
    // 如果正在同步中，跳过更新
    if (isSyncingRef.current) {
      return;
    }
    
    // 检查是否是真正的外部变化（引用变化）
    if (prevSafeStepsRef.current === safeSteps) {
      // 引用相同，不是外部变化，跳过
      return;
    }
    
    // 真正的外部变化，更新本地状态
    setLocalSteps(safeSteps);
    prevSafeStepsRef.current = safeSteps;
  }, [safeSteps]);

  const latestLocalStepsRef = useRef(localSteps);

  // 每次状态更新时同步到 ref，保证防抖同步到父组件的时候能拿到最新的数据
  useEffect(() => {
    latestLocalStepsRef.current = localSteps;
  }, [localSteps]);

  // 手动同步到父组件的函数，移除实时防抖同步
  const syncToParent = useCallback(() => {
    const currentLocalSteps = latestLocalStepsRef.current; // 始终获取最新值
    // console.log('=== 引用检查 ===');
    // console.log('localSteps === safeSteps:', currentLocalSteps === safeSteps);
    // console.log('localSteps[2] === safeSteps[2]:', currentLocalSteps[2] === safeSteps[2]);
    // console.log('localSteps[2].parameters === safeSteps[2].parameters:', 
    //   currentLocalSteps[2]?.parameters === safeSteps[2]?.parameters);
  
    // 使用更高效的浅比较，避免频繁的 JSON.stringify
    const hasChanges = currentLocalSteps.length !== safeSteps.length || 
    currentLocalSteps.some((step, index) => {
        const safeStep = safeSteps[index];
        if (!safeStep) return true;
        return step.name !== safeStep.name || 
               step.objective !== safeStep.objective ||
               step.assigned_to !== safeStep.assigned_to ||
               step.persona !== safeStep.persona ||
               JSON.stringify(step.toolsets) !== JSON.stringify(safeStep.toolsets) ||
               JSON.stringify(step.phases) !== JSON.stringify(safeStep.phases) ||
               JSON.stringify(step.parameters) !== JSON.stringify(safeStep.parameters);
    });
    // console.log(`hasChanges: ${hasChanges}, ${JSON.stringify(safeSteps[2].parameters)} -> ${JSON.stringify(currentLocalSteps[2].parameters)}`);
    if (hasChanges) {
      // 设置同步标志，防止循环更新
      isSyncingRef.current = true;
      onChange(currentLocalSteps);
      // 延迟重置标志，给上层组件时间处理更新
      setTimeout(() => {
        isSyncingRef.current = false;
      }, 50);
    }
  }, [safeSteps, onChange]);

  // 使用 useRef 来存储防抖计时器
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // 防抖同步函数
  const debouncedSyncToParent = useCallback(() => {
    // 清除之前的计时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    // 设置新的计时器
    debounceTimerRef.current = setTimeout(() => {
      syncToParent();
    }, 300);
  }, [syncToParent]);

  // 清理计时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  const [collapsedSteps, setCollapsedSteps] = useState<Set<number>>(new Set());
  const [newToolset, setNewToolset] = useState<{ [key: number]: string }>({});
  
  // 管理属性组的展开收起状态
  const [expandedStandardAttrs, setExpandedStandardAttrs] = useState<Set<number>>(new Set());
  const [expandedParameters, setExpandedParameters] = useState<Set<number>>(new Set());

  const toggleStepCollapse = useCallback((index: number) => {
    setCollapsedSteps(prev => {
      const newCollapsed = new Set(prev);
      if (newCollapsed.has(index)) {
        newCollapsed.delete(index);
      } else {
        newCollapsed.add(index);
      }
      return newCollapsed;
    });
  }, []);

  const toggleStandardAttrs = useCallback((stepIndex: number) => {
    setExpandedStandardAttrs(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(stepIndex)) {
        newExpanded.delete(stepIndex);
      } else {
        newExpanded.add(stepIndex);
      }
      return newExpanded;
    });
  }, []);

  const toggleParameters = useCallback((stepIndex: number) => {
    setExpandedParameters(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(stepIndex)) {
        newExpanded.delete(stepIndex);
      } else {
        newExpanded.add(stepIndex);
      }
      return newExpanded;
    });
  }, []);

  const addStep = useCallback(() => {
    const newStep: PlanStep = {
      name: '',
      assigned_to: '',
      persona: '',
      toolsets: [],
      objective: '',
      phases: [],
      parameters: {},
    };
    setLocalSteps(prev => [...prev, newStep]);
    // 触发延迟同步
    debouncedSyncToParent();
  }, [debouncedSyncToParent]);

  const removeStep = useCallback((index: number) => {
    setLocalSteps(prev => prev.filter((_, i) => i !== index));
    // 触发延迟同步
    debouncedSyncToParent();
  }, [debouncedSyncToParent]);

  const updateStep = useCallback((index: number, field: keyof PlanStep, value: any) => {
    // 立即更新本地状态，确保 UI 实时响应
    setLocalSteps(prev => {
      const newSteps = [...prev];
      if (newSteps[index]) {
        newSteps[index] = { ...newSteps[index], [field]: value };
      }
      return newSteps;
    });
    // 延迟同步到父组件（不影响 UI 响应）
    debouncedSyncToParent();
  }, [debouncedSyncToParent]);

  const addToolset = useCallback((stepIndex: number) => {
    const toolsetValue = newToolset[stepIndex]?.trim();
    if (!toolsetValue) return;

    setLocalSteps(prev => {
      const newSteps = [...prev];
      if (newSteps[stepIndex] && newSteps[stepIndex].toolsets) {
        if (!newSteps[stepIndex].toolsets.includes(toolsetValue)) {
          newSteps[stepIndex].toolsets.push(toolsetValue);
        }
      }
      return newSteps;
    });
    setNewToolset(prev => ({ ...prev, [stepIndex]: '' }));
    // 触发延迟同步
    debouncedSyncToParent();
  }, [newToolset, debouncedSyncToParent]);

  const removeToolset = useCallback((stepIndex: number, toolsetIndex: number) => {
    setLocalSteps(prev => {
      const newSteps = [...prev];
      if (newSteps[stepIndex] && newSteps[stepIndex].toolsets) {
        newSteps[stepIndex].toolsets = newSteps[stepIndex].toolsets.filter(
          (_, i) => i !== toolsetIndex
        );
      }
      return newSteps;
    });
    // 触发延迟同步
    debouncedSyncToParent();
  }, [debouncedSyncToParent]);

  const handleToolsetInputKeyPress = useCallback((e: React.KeyboardEvent, stepIndex: number) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addToolset(stepIndex);
    }
  }, [addToolset]);

  // Parameters 管理函数
  const addParameter = useCallback((stepIndex: number, key: string, value: string) => {
    if (!key.trim()) return;
    
    setLocalSteps(prev => {
      const newSteps = [...prev];
      if (newSteps[stepIndex]) {
        const currentParams = newSteps[stepIndex].parameters || {};
        newSteps[stepIndex].parameters = {
          ...currentParams,
          [key]: value
        };
      }
      return newSteps;
    });
    // 触发延迟同步
    debouncedSyncToParent();
  }, [debouncedSyncToParent]);

  const updateParameter = useCallback((stepIndex: number, oldKey: string, newKey: string, value: string) => {
    setLocalSteps(prev => {
      console.log("updating parameter", stepIndex, oldKey, newKey, value);
      const newSteps = [...prev];
      if (newSteps[stepIndex]) {
        const currentParams = { ...(newSteps[stepIndex].parameters || {}) };
        
        // 如果 key 改变了，删除旧的 key
        if (oldKey !== newKey && oldKey in currentParams) {
          delete currentParams[oldKey];
        }
        
        // 设置新的 key-value
        if (newKey.trim()) {
          currentParams[newKey] = value;
        }
        
        console.log("setting new parameters", currentParams);
        newSteps[stepIndex] = { 
          ...newSteps[stepIndex],
          parameters: currentParams,
        };
      }
      return newSteps;
    });
    // 触发延迟同步
    debouncedSyncToParent();
  }, [debouncedSyncToParent]);

  const removeParameter = useCallback((stepIndex: number, key: string) => {
    setLocalSteps(prev => {
      const newSteps = [...prev];
      if (newSteps[stepIndex] && newSteps[stepIndex].parameters) {
        const currentParams = { ...newSteps[stepIndex].parameters };
        delete currentParams[key];
        newSteps[stepIndex].parameters = currentParams;
      }
      return newSteps;
    });
    // 触发延迟同步
    debouncedSyncToParent();
  }, [debouncedSyncToParent]);

  // 组件即将渲染

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Plan Steps</h3>
        <Button
          type="primary"
          onClick={addStep}
          style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
        >
          <Plus size={16} />
          Add Step
        </Button>
      </div>

      {localSteps.map((step, stepIndex) => (
        <Card
          key={stepIndex}
          className="border border-gray-200"
          title={
            <div className="flex items-center justify-between w-full">
              <div
                className="flex items-center cursor-pointer flex-1"
                onClick={() => toggleStepCollapse(stepIndex)}
              >
                {collapsedSteps.has(stepIndex) ? (
                  <ChevronRight size={16} className="mr-2" />
                ) : (
                  <ChevronDown size={16} className="mr-2" />
                )}
                <span className="text-base font-medium">
                  Step {stepIndex + 1}: {step.name || 'Unnamed Step'}
                </span>
              </div>
              <Button
                type="text"
                status="danger"
                onClick={(e) => {
                  e.stopPropagation();
                  removeStep(stepIndex);
                }}
                style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
              >
                <Trash2 size={16} />
              </Button>
            </div>
          }
        >
          {!collapsedSteps.has(stepIndex) && (
             <div className="space-y-6">
               {/* 主要属性 - 始终显示 */}
               <Form layout="vertical">
                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                   <Form.Item label="Step Name" required>
                     <Input
                       placeholder="Please enter step name"
                       value={step.name || ''}
                       onChange={(value) => updateStep(stepIndex, 'name', value)}
                     />
                   </Form.Item>

                   <Form.Item label="Assigned To" required>
                     <Input
                       placeholder="Please enter assignee"
                       value={step.assigned_to || ''}
                       onChange={(value) => updateStep(stepIndex, 'assigned_to', value)}
                     />
                   </Form.Item>
                 </div>
               </Form>

               {/* 标准属性组 */}
               <div className="border border-gray-200 rounded-lg">
                 <div 
                   className="flex items-center justify-between p-2 cursor-pointer hover:bg-gray-50 transition-colors"
                   onClick={() => toggleStandardAttrs(stepIndex)}
                 >
                   <div className="flex items-center gap-2">
                     <Settings size={16} className="text-gray-600" />
                     <span className="font-medium text-gray-700">Mewtwo</span>
                     <span className="text-sm text-gray-500">(persona, toolsets, objective, phases)</span>
                   </div>
                   {expandedStandardAttrs.has(stepIndex) ? (
                     <ChevronDown size={16} className="text-gray-500" />
                   ) : (
                     <ChevronRight size={16} className="text-gray-500" />
                   )}
                 </div>
                 
                 {expandedStandardAttrs.has(stepIndex) && (
                   <div className="p-4 border-t border-gray-200 bg-gray-50">
                     <Form layout="vertical">
                       <Form.Item label="Persona" required>
                         <Input
                           placeholder="Please enter persona description"
                           value={step.persona || ''}
                           onChange={(value) => updateStep(stepIndex, 'persona', value)}
                         />
                       </Form.Item>

                       <Form.Item label="Toolsets">
                         <div className="space-y-2">
                           <div className="flex flex-wrap gap-2 mb-2">
                             {(step.toolsets || []).map((toolset, toolsetIndex) => (
                               <Tag
                                 key={toolsetIndex}
                                 closable
                                 onClose={() => removeToolset(stepIndex, toolsetIndex)}
                                 className="flex items-center"
                               >
                                 {toolset}
                               </Tag>
                             ))}
                           </div>
                           <div className="flex gap-2">
                             <Input
                               placeholder="Enter tool name and press Enter to add"
                               value={newToolset[stepIndex] || ''}
                               onChange={(value) =>
                                 setNewToolset({ ...newToolset, [stepIndex]: value })
                               }
                               onKeyPress={(e) => handleToolsetInputKeyPress(e, stepIndex)}
                             />
                             <Button
                               type="outline"
                               onClick={() => addToolset(stepIndex)}
                               style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                             >
                               <Plus size={16} />
                               Add
                             </Button>
                           </div>
                         </div>
                       </Form.Item>

                       <Form.Item label="Objective" required>
                         <Input.TextArea
                           placeholder="Please enter step objective"
                           value={step.objective}
                           onChange={(value) => updateStep(stepIndex, 'objective', value)}
                           autoSize={{ minRows: 3, maxRows: 6 }}
                         />
                       </Form.Item>
                     </Form>

                     <div className="border-t pt-4 mt-4">
                       <PhaseEditor
                         phases={step.phases || []}
                         onChange={(phases) => updateStep(stepIndex, 'phases', phases)}
                         stepIndex={stepIndex}
                       />
                     </div>
                   </div>
                 )}
               </div>

               {/* Parameters 属性组 */}
               <div className="border border-gray-200 rounded-lg">
                 <div 
                   className="flex items-center justify-between p-2 cursor-pointer hover:bg-gray-50 transition-colors"
                   onClick={() => toggleParameters(stepIndex)}
                 >
                   <div className="flex items-center gap-2">
                     <Code size={16} className="text-gray-600" />
                     <span className="font-medium text-gray-700">Other Agents</span>
                     <span className="text-sm text-gray-500">(key-value pairs)</span>
                   </div>
                   {expandedParameters.has(stepIndex) ? (
                     <ChevronDown size={16} className="text-gray-500" />
                   ) : (
                     <ChevronRight size={16} className="text-gray-500" />
                   )}
                 </div>
                 
                 {expandedParameters.has(stepIndex) && (
                   <div className="p-4 border-t border-gray-200 bg-blue-50">
                     <ParametersEditor
                       parameters={step.parameters || {}}
                       stepIndex={stepIndex}
                       onAdd={addParameter}
                       onUpdate={updateParameter}
                       onRemove={removeParameter}
                     />
                   </div>
                 )}
               </div>
             </div>
          )}
        </Card>
      ))}

      {localSteps.length === 0 && (
        <div className="text-center py-12 text-gray-500">
          <p className="mb-4">No plan steps</p>
          <Button
            type="primary"
            size="large"
            onClick={addStep}
            style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
          >
            <Plus size={20} />
            Add First Step
          </Button>
        </div>
      )}
    </div>
  );
};

export default StepEditor;
