"use client";

import React, { useState, useMemo, useCallback } from 'react';
import { Card, Form, Input, Button, Space, Divider, Switch } from '@arco-design/web-react';
import { ChevronDown, ChevronRight, Plus, Trash2 } from 'lucide-react';
import { Phase, Reference } from '@/app/lib/sop-schema';

interface PhaseEditorProps {
  phases: Phase[];
  onChange: (phases: Phase[]) => void;
  stepIndex: number;
}

const PhaseEditor: React.FC<PhaseEditorProps> = ({ phases = [], onChange, stepIndex }) => {
  // 确保phases始终是数组，使用useMemo缓存安全的phases
  const safePhases = useMemo(() => Array.isArray(phases) ? phases : [], [phases]);
  const [collapsedPhases, setCollapsedPhases] = useState<Set<number>>(new Set());

  // 使用useCallback优化事件处理函数
  const togglePhaseCollapse = useCallback((index: number) => {
    setCollapsedPhases(prev => {
      const newCollapsed = new Set(prev);
      if (newCollapsed.has(index)) {
        newCollapsed.delete(index);
      } else {
        newCollapsed.add(index);
      }
      return newCollapsed;
    });
  }, []);

  const addPhase = useCallback(() => {
    const newPhase: Phase = {
      name: '',
      objective: '',
      actions: '',
      references: [],
      deliverable: [],
    };
    onChange([...safePhases, newPhase]);
  }, [safePhases, onChange]);

  const removePhase = useCallback((index: number) => {
    const newPhases = safePhases.filter((_, i) => i !== index);
    onChange(newPhases);
  }, [safePhases, onChange]);

  const updatePhase = useCallback((index: number, field: keyof Phase, value: any) => {
    const newPhases = [...safePhases];
    if (newPhases[index]) {
      newPhases[index] = { ...newPhases[index], [field]: value };
      onChange(newPhases);
    }
  }, [safePhases, onChange]);

  const addDeliverable = useCallback((phaseIndex: number) => {
    const newPhases = [...safePhases];
    if (newPhases[phaseIndex] && newPhases[phaseIndex].deliverable) {
      newPhases[phaseIndex].deliverable.push('');
      onChange(newPhases);
    }
  }, [safePhases, onChange]);

  const removeDeliverable = useCallback((phaseIndex: number, deliverableIndex: number) => {
    const newPhases = [...safePhases];
    if (newPhases[phaseIndex] && newPhases[phaseIndex].deliverable) {
      newPhases[phaseIndex].deliverable = newPhases[phaseIndex].deliverable.filter(
        (_, i) => i !== deliverableIndex
      );
      onChange(newPhases);
    }
  }, [safePhases, onChange]);

  const updateDeliverable = useCallback((phaseIndex: number, deliverableIndex: number, value: string) => {
    const newPhases = [...safePhases];
    if (newPhases[phaseIndex] && newPhases[phaseIndex].deliverable && newPhases[phaseIndex].deliverable[deliverableIndex] !== undefined) {
      newPhases[phaseIndex].deliverable[deliverableIndex] = value;
      onChange(newPhases);
    }
  }, [safePhases, onChange]);

  const addReference = useCallback((phaseIndex: number) => {
    const newPhases = [...safePhases];
    if (newPhases[phaseIndex]) {
      const newReference: Reference = {
        id: '',
        error: '',
        binary: false,
        content: '',
        filepath: '',
      };
      if (!newPhases[phaseIndex].references) {
        newPhases[phaseIndex].references = [];
      }
      newPhases[phaseIndex].references.push(newReference);
      onChange(newPhases);
    }
  }, [safePhases, onChange]);

  const removeReference = useCallback((phaseIndex: number, referenceIndex: number) => {
    const newPhases = [...safePhases];
    if (newPhases[phaseIndex] && newPhases[phaseIndex].references) {
      newPhases[phaseIndex].references = newPhases[phaseIndex].references.filter(
        (_, i) => i !== referenceIndex
      );
      onChange(newPhases);
    }
  }, [safePhases, onChange]);

  const updateReference = useCallback((phaseIndex: number, referenceIndex: number, field: keyof Reference, value: any) => {
    const newPhases = [...safePhases];
    if (newPhases[phaseIndex] && newPhases[phaseIndex].references && newPhases[phaseIndex].references[referenceIndex]) {
      newPhases[phaseIndex].references[referenceIndex] = {
        ...newPhases[phaseIndex].references[referenceIndex],
        [field]: value
      };
      onChange(newPhases);
    }
  }, [safePhases, onChange]);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">Phases</h4>
        <Button
          type="outline"
          size="small"
          onClick={addPhase}
          style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
        >
          <Plus size={16} />
          Add Phase
        </Button>
      </div>

      {safePhases.map((phase, phaseIndex) => (
        <Card
          key={phaseIndex}
          className="border border-gray-200"
          size="small"
          title={
            <div className="flex items-center justify-between w-full">
              <div
                className="flex items-center cursor-pointer flex-1"
                onClick={() => togglePhaseCollapse(phaseIndex)}
              >
                {collapsedPhases.has(phaseIndex) ? (
                  <ChevronRight size={16} className="mr-2" />
                ) : (
                  <ChevronDown size={16} className="mr-2" />
                )}
                <span className="text-sm font-medium">
                  Phase {phaseIndex + 1}: {phase.name || 'Unnamed Phase'}
                </span>
              </div>
              <Button
                type="text"
                size="small"
                status="danger"
                onClick={(e) => {
                  e.stopPropagation();
                  removePhase(phaseIndex);
                }}
                style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
              >
                <Trash2 size={14} />
              </Button>
            </div>
          }
        >
          {!collapsedPhases.has(phaseIndex) && (
            <div className="space-y-4">
              <Form layout="vertical" size="small">
                <Form.Item label="Phase Name" required>
                  <Input
                    placeholder="Please enter phase name"
                    value={phase.name || ''}
                    onChange={(value) => updatePhase(phaseIndex, 'name', value)}
                  />
                </Form.Item>

                <Form.Item label="Objective" required>
                  <Input.TextArea
                    placeholder="Please enter phase objective"
                    value={phase.objective || ''}
                    onChange={(value) => updatePhase(phaseIndex, 'objective', value)}
                    autoSize={{ minRows: 2, maxRows: 4 }}
                  />
                </Form.Item>

                <Form.Item label="Actions" required>
                  <Input.TextArea
                    placeholder="Please enter specific actions"
                    value={phase.actions || ''}
                    onChange={(value) => updatePhase(phaseIndex, 'actions', value)}
                    autoSize={{ minRows: 2, maxRows: 4 }}
                  />
                </Form.Item>

                <Form.Item label="Deliverables">
                  <div className="space-y-2">
                    {(phase.deliverable || []).map((deliverable, deliverableIndex) => (
                      <div key={deliverableIndex} className="flex items-center gap-2">
                        <Input
                          placeholder="Please enter deliverable"
                          value={deliverable || ''}
                          onChange={(value) =>
                            updateDeliverable(phaseIndex, deliverableIndex, value)
                          }
                        />
                        <Button
                          type="text"
                          size="small"
                          status="danger"
                          onClick={() => removeDeliverable(phaseIndex, deliverableIndex)}
                          style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                        >
                          <Trash2 size={14} />
                        </Button>
                      </div>
                    ))}
                    <Button
                      type="dashed"
                      size="small"
                      onClick={() => addDeliverable(phaseIndex)}
                      className="w-full"
                      style={{ display: 'flex', alignItems: 'center', gap: '4px', justifyContent: 'center' }}
                    >
                      <Plus size={14} />
                      Add Deliverable
                    </Button>
                  </div>
                </Form.Item>

                <Form.Item label="References">
                  <div className="space-y-3">
                    {(phase.references || []).map((reference, referenceIndex) => (
                      <Card
                        key={referenceIndex}
                        size="small"
                        className="border border-gray-100"
                        title={
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium text-gray-600">
                              Reference {referenceIndex + 1}
                            </span>
                            <Button
                              type="text"
                              size="small"
                              status="danger"
                              onClick={() => removeReference(phaseIndex, referenceIndex)}
                              style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                            >
                              <Trash2 size={12} />
                            </Button>
                          </div>
                        }
                      >
                        <div className="space-y-3">
                          <div className="grid grid-cols-2 gap-3">
                            <Form.Item label="ID">
                              <Input
                                size="small"
                                placeholder="Reference ID"
                                value={reference.id || ''}
                                onChange={(value) => updateReference(phaseIndex, referenceIndex, 'id', value)}
                              />
                            </Form.Item>
                            <Form.Item label="File Path">
                              <Input
                                size="small"
                                placeholder="File path"
                                value={reference.filepath || ''}
                                onChange={(value) => updateReference(phaseIndex, referenceIndex, 'filepath', value)}
                              />
                            </Form.Item>
                          </div>
                          
                          <Form.Item label="Content">
                            <Input.TextArea
                              placeholder="Reference content"
                              value={reference.content || ''}
                              onChange={(value) => updateReference(phaseIndex, referenceIndex, 'content', value)}
                              autoSize={{ minRows: 2, maxRows: 4 }}
                            />
                          </Form.Item>

                          <div className="grid grid-cols-2 gap-3">
                            <Form.Item label="Error Message">
                              <Input
                                size="small"
                                placeholder="Error message (if any)"
                                value={reference.error || ''}
                                onChange={(value) => updateReference(phaseIndex, referenceIndex, 'error', value)}
                              />
                            </Form.Item>
                            <Form.Item label="Binary File">
                              <Switch
                                size="small"
                                checked={reference.binary || false}
                                onChange={(checked) => updateReference(phaseIndex, referenceIndex, 'binary', checked)}
                              />
                            </Form.Item>
                          </div>
                        </div>
                      </Card>
                    ))}
                    <Button
                      type="dashed"
                      size="small"
                      onClick={() => addReference(phaseIndex)}
                      className="w-full"
                      style={{ display: 'flex', alignItems: 'center', gap: '4px', justifyContent: 'center' }}
                    >
                      <Plus size={14} />
                      Add Reference
                    </Button>
                  </div>
                </Form.Item>
              </Form>
            </div>
          )}
        </Card>
      ))}

      {safePhases.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p className="mb-4">No phases</p>
          <Button
            type="primary"
            onClick={addPhase}
            style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
          >
            <Plus size={16} />
            Add First Phase
          </Button>
        </div>
      )}
    </div>
  );
};

export default PhaseEditor;