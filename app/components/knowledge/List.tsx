"use client";

import React, { useMemo, useState, useEffect } from "react";
import {
  Table,
  TableColumnProps,
  Button,
  Form,
  Grid,
  Input,
  Select,
  Tag,
  Popconfirm,
} from "@arco-design/web-react";
import { PageHeader } from "@/app/components/PageHeader";
import {
  ListKnowledgesetRequest,
  Knowledgeset,
} from "@/app/bam/aime/namespaces/knowledgeset";
import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@/app/api/request";
import { IconPlus, IconEdit, IconDelete } from "@arco-design/web-react/icon";
import KnowledgesetModal from "./Modal";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

/** 知识集类型枚举 */
export enum KnowledgeCollectionType {
  /** 系统知识 */
  System = 1,
  /** 场景知识 */
  Scene = 2,
  /** 工具知识 */
  Tool = 3,
}

/** 知识集状态枚举 */
export enum KnowledgeCollectionStatus {
  /** 已创建 */
  Created = 1,
  /** 已上线 */
  Online = 2,
  /** 已禁用 */
  Disable = 3,
}

export const StatusMap = {
  [KnowledgeCollectionStatus.Created]: "已创建",
  [KnowledgeCollectionStatus.Online]: "已上线",
  [KnowledgeCollectionStatus.Disable]: "已禁用",
};

export const TypesMap = {
  [KnowledgeCollectionType.System]: "系统知识",
  [KnowledgeCollectionType.Scene]: "场景知识",
  [KnowledgeCollectionType.Tool]: "工具知识",
};

const columns: TableColumnProps[] = [
  {
    title: "ID",
    dataIndex: "id",
    width: 200,
  },
  {
    title: "名称",
    dataIndex: "name",
    width: 140,
  },
  {
    title: "描述",
    dataIndex: "description",
    width: 300,
  },
  {
    title: "key",
    dataIndex: "key",
    width: 300,
  },
  {
    title: "tags",
    dataIndex: "tags",
    width: 150,
    render: (col, item) => {
      return item.tags?.map((tag: string, index: number) => (
        <Tag
          color="blue"
          key={index}
          style={{ marginRight: 2, marginBottom: 2 }}
        >
          {tag}
        </Tag>
      ));
    },
  },
  {
    title: "类型",
    dataIndex: "type_name",
    width: 100,
    render: (col, item) => {
      return <Tag color="blue">{item.type_name}</Tag>;
    },
  },
  {
    title: "创建者",
    dataIndex: "creator",
    width: 140,
  },
  {
    title: "创建时间",
    dataIndex: "created_at",
    width: 180,
    render: (_, record) => {
      const date = new Date(record.created_at);
      return date.toLocaleString();
    },
  },
  {
    title: "更新时间",
    dataIndex: "updated_at",
    width: 180,
    render: (_, record) => {
      const date = new Date(record.updated_at);
      return date.toLocaleString();
    },
  },
];

export const KnowledgesetList = () => {
  const router = useRouter();
  const [requestData, setRequestData] = useState<ListKnowledgesetRequest>({
    page_num: 1,
    page_size: 10,
  });
  const [visible, setVisible] = useState(false);
  const [targetItem, setTargetItem] = useState<Knowledgeset | undefined>();
  const [isEdit, setIsEdit] = useState(false);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onFormChange = (_changeValue: any, values: ListKnowledgesetRequest) => {
    setRequestData({
      ...requestData,
      ...values,
    });
  };

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["knowledgeSetList", requestData],
    queryFn: async () => {
      const response = await apiClient.ListKnowledgesets(requestData);
      return response ||  {};
    },
  });

  const { data: configData } = useQuery({
    queryKey: ["GetKnowledgesetMetadataConf"],
    queryFn: async () => apiClient.GetKnowledgesetMetadataConf(),
  });

  const tableData = useMemo(() => {
    return data?.knowledge_sets?.map((item) => {
      return {
        ...item,
      };
    });
  }, [data]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleTableItemClick = (item: any) => {
    const target = data?.knowledge_sets?.find((i) => i.id === item.id);
    if (target) {
      setTargetItem(target);
    }
    setIsEdit(true);
    setVisible(true);
  };

  const handleAdd = () => {
    setIsEdit(false);
    setVisible(true);
  };

  const handleOk = () => {
    setVisible(false);
    refetch();
  };

  const handleViewVersions = (item: Knowledgeset) => {
    router.push(`/knowledge/version?id=${item.id}`);
  };

  // 删除知识集
  const handleDeleteKnowledgeset = async (knowledgesetId: string) => {
    try {
      await apiClient.DeleteKnowledgeset({ knowledge_set_id: knowledgesetId });
      toast.success("删除知识集成功");
      refetch(); // 重新获取知识集列表
    } catch (error) {
      console.error("删除知识集失败:", error);
      toast.error((error as any)?.message || "删除知识集失败");
    }
  };

  return (
    <div>
      <PageHeader title="知识集列表" />
      <Form
        layout="vertical"
        className="mt-6"
        onValuesChange={onFormChange as any}
      >
        <Grid.Row gutter={24}>
          <Grid.Col span={4}>
            <Form.Item label="ID" field="id">
              <Input placeholder="请输入知识集ID" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4}>
            <Form.Item label="名称" field="name">
              <Input placeholder="请输入知识集名称" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4}>
            <Form.Item label="类型" field="type">
              <Select mode="multiple" placeholder="请选择类型" allowClear>
                {configData?.knowledge_set_types?.map((key) => {
                  return (
                    <Select.Option key={key.value} value={key.value}>
                      {key.name}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Grid.Col>
        </Grid.Row>
      </Form>
      <div className="mb-6 overflow-hidden">
        <Button
          type="primary"
          className="float-right"
          icon={<IconPlus />}
          onClick={handleAdd}
        >
          添加知识集
        </Button>
      </div>
      <Table
        columns={columns.concat({
          title: "操作",
          dataIndex: "operation",
          render: (_, item) => (
            <div className="flex gap-2">
              <Button
                type="secondary"
                size="mini"
                icon={<IconEdit />}
                onClick={() => handleTableItemClick(item)}
              >
                编辑
              </Button>
              <Button
                type="primary"
                size="mini"
                onClick={() => handleViewVersions(item)}
              >
                版本列表
              </Button>
              <Popconfirm
                title="确定要删除该知识集吗？"
                content="删除后无法恢复"
                okText="确定"
                cancelText="取消"
                position="br"
                onOk={() => handleDeleteKnowledgeset(item.id)}
              >
                <Button
                  type="secondary"
                  status="danger"
                  size="mini"
                  icon={<IconDelete />}
                >
                  删除
                </Button>
              </Popconfirm>
            </div>
          ),
          fixed: "right",
          width: 240,
        })}
        data={tableData}
        loading={isLoading}
        pagination={{
          total: +(data?.total || 0),
          current: +requestData.page_num || 1,
          pageSize: +requestData.page_size || 10,
          onChange: (pageNumber, pageSize) => {
            setRequestData({
              ...requestData,
              page_num: pageNumber,
              page_size: pageSize,
            });
          },
        }}
      />
      <KnowledgesetModal
        visible={visible}
        data={targetItem}
        knowledgeTypes={configData?.knowledge_set_types}
        isEdit={isEdit}
        onCancel={() => setVisible(false)}
        onOk={handleOk}
        title={isEdit ? "修改知识集" : "添加知识集"}
      />
    </div>
  );
};
