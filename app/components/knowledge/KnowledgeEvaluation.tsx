"use client";

import { PageHeader } from "@/app/components/PageHeader";
import { apiClient } from '@/app/api/request';
import { Button, Input, Switch, Card, Space, Typography, Select, Message } from '@arco-design/web-react';
import { userAtom } from '@/app/store/auth';
import { IconUpload } from '@arco-design/web-react/icon';
import { useStore } from '@nanostores/react';
import { useState } from 'react';
import { toast } from 'sonner';
import { ensureContainerActive, fetchTraceWithRetry } from '@/app/experience/extract/utils';
import { processAgentTrace, processAgentTraceCompressed } from "@/app/experience/extract/compress";
import { HelpTooltip } from "@/app/components/ui/help-tooltip";
import { COMPRESSION_HELP_CONTENT } from "@/app/constants/compression-help";

import Editor from '@monaco-editor/react';

interface KnowledgeEvaluationProps {}

interface IChatResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

const DEFAULT_SYSTEM_PROMPT = `你是一个知识评估专家，需要分析AI Agent执行任务过程中知识召回与执行的效果。

## 分析要求

按照以下要求分析该任务中的知识召回与执行过程：

### 特别说明
如果用户提供了 <reason> 标签内容，表示用户认为任务执行中存在不符合预期的地方。请重点关注分析这些不符合预期的地方是否由知识问题导致，包括：
- 是否因为缺少相关知识导致无法正确处理该问题
- 是否因为召回了错误或无关的知识导致执行偏差
- 是否因为知识冲突或歧义导致执行结果不符合预期
- 是否因为知识内容不准确导致执行错误

如果用户未提供 <reason> 内容，则按照常规要求进行全面分析。

### 一、逐项判断以下四类问题是否存在（需明确回答"是"或"否"）：
1. 是否由于缺少必要知识，导致任务无法完成 
2. 是否召回了与当前任务完全无关的知识 
3. 是否召回了存在歧义或冲突的知识（在同一个 mewtwo 召回的知识中，存在逻辑冲突的知识条目）
4. 是否召回了明确知识，但最终任务未按照知识要求执行 

### 二、如存在以上任一问题，提供详细信息：
每个问题条目应该对应一个具体的问题实例，而不是问题类型。例如：
• 如果发现多个无关知识，应该分别创建多个条目，每个条目对应一个具体的无关知识
• 如果发现知识冲突，应该创建一个条目，包含冲突的多个知识IDs
• 如果发现执行偏差，应该创建一个条目，包含未按要求执行的知识ID

每个条目的详细信息包括：
• 问题类型编号（如问题 2）
• 相关知识 IDs（必须提供，支持多个知识条目，用逗号分隔。当同一个问题涉及多个知识条目时使用，例如逻辑冲突、多个无关知识等）
• 召回该知识的时序节点（如在哪一步被召回）
• 问题具体表现（如哪条知识与任务无关、冲突点、执行偏差等） 

### 三、分析要求：
• 在分析 agent 行为时，需区分组件：
  - **dynamic_planner** 只受自身知识影响；
  - 每个 **mewtwo** 实例的知识是相互隔离的，仅对本次 mewtwo 调用生效；
  - 对于 **lark_creation** 这类以独立agent执行的工具，注意它的开始和结束事件，工具内部的事件不受外部mewtwo知识的影响
• 分析需严格结合事件的时间顺序；
• 知识只能影响其被加载后的事件，不影响之前的行为
• 对于知识冲突的判断，需要特别关注同一个 mewtwo 实例在同一轮执行中召回的知识条目之间是否存在逻辑矛盾

## 输出格式

请以JSON格式输出分析结果：

\`\`\`json
{
  "summary": {
    "problem_1": "是/否",
    "problem_2": "是/否", 
    "problem_3": "是/否",
    "problem_4": "是/否"
  },
  "details": [
    {
      "problem_type": "问题编号（1-4）",
      "knowledge_ids": "知识ID列表（多个ID用逗号分隔）",
      "recall_timing": "召回时序节点描述",
      "problem_description": "问题具体表现描述"
    }
  ],
  "recommendations": "改进建议"
}
\`\`\`

请严格只输出JSON，不要输出任何解释或多余内容。`;

const SUMMARY_SYSTEM_PROMPT = `你是一个知识评估总结专家，负责汇总多个分析agent的评估结果。

## 任务要求

你将收到两个分析agent的评估结果，需要将它们合并成一个统一的分析报告。

### 汇总规则

1. **问题总结汇总**：
   - 如果任一分析结果中某个问题为"是"，则最终结果中该问题为"是"
   - 只有当所有分析结果中某个问题都为"否"时，最终结果中该问题才为"否"

2. **问题详情合并**：
   - 合并所有分析结果中的问题详情
   - 去重：相同的问题类型、知识ID和描述的问题只保留一个
   - 保持原有的详细信息结构

3. **改进建议汇总**：
   - 合并所有分析结果的改进建议
   - 去重并整理成连贯的建议内容

## 输出格式

请以JSON格式输出合并后的分析结果：

\`\`\`json
{
  "summary": {
    "problem_1": "是/否",
    "problem_2": "是/否", 
    "problem_3": "是/否",
    "problem_4": "是/否"
  },
  "details": [
    {
      "problem_type": "问题编号（1-4）",
      "knowledge_ids": "知识ID列表（多个ID用逗号分隔）",
      "recall_timing": "召回时序节点描述",
      "problem_description": "问题具体表现描述"
    }
  ],
  "recommendations": "改进建议"
}
\`\`\`

请严格只输出JSON，不要输出任何解释或多余内容。`;

const DEFAULT_QUERY_PREFIX = '有如下的 agent 执行轨迹：\n<trace>\n';

const QUERY_SUFFIX = `</trace>

## trace 内容结构介绍

这是一个AI Agent执行任务的轨迹日志（Trace）。其核心架构由一个 **Planner（规划器）** 和多个 **Execution（执行器）**构成，通过多轮（Round）的"规划-执行"循环来完成复杂任务。

1. **输入** (\`UserMessage\`):
    - 流程的起点，封装了用户的原始需求、对话和输入附件

2. **规划** (\`Plan\`):
    - Planner的核心产物
    - 对用户任务进行理解和拆解，形成结构化执行蓝图
    - 关键要素包括：
        - \`Rationale\`: 阐述制定该计划的思考过程
        - \`ProgressPlan\`: 将任务分解为有序的、可执行的阶段性步骤
        - \`Actor\` & \`TaskDescription\`: 为每个步骤指派执行Agent并提供明确任务指令

3. **执行** (\`Execution\`):
    - Actor执行任务的具体记录
    - 包含最终的\`Output\`（成果摘要）和\`Reference\`（产出物链接）
    - \`Detail\`部分记录了由"思考(\`Think\`)-动作(\`Action\`)"对组成的执行链
    - 大多数由mewtwo这个Actor负责执行与完成

## 知识召回说明

在trace中，知识召回通常表现为：
- \`<knowledge>\` 标签：表示一个知识条目被召回
- 知识ID：每个知识条目都有唯一的标识符
- 召回时机：知识在特定步骤被加载和使用

请仔细分析每个知识条目的召回时机、使用效果以及与任务执行的相关性。`;

export function KnowledgeEvaluation({}: KnowledgeEvaluationProps) {
  const [sessionId, setSessionId] = useState('');
  const [query, setQuery] = useState('');
  const [result, setResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingTrace, setIsFetchingTrace] = useState(false);
  const [systemPrompt, setSystemPrompt] = useState(DEFAULT_SYSTEM_PROMPT);
  const [isPromptExpanded, setIsPromptExpanded] = useState(false);
  const [isResultExpanded, setIsResultExpanded] = useState(true);
  const [parsedResult, setParsedResult] = useState<any>(null);
  const [model, setModel] = useState('gemini-2.5-pro');
  const [temperature, setTemperature] = useState(0.2);
  const [reason, setReason] = useState('');
  const [isCompressionEnabled, setIsCompressionEnabled] = useState(false);
  const [isMultiAgentEnabled, setIsMultiAgentEnabled] = useState(false);
  const [agentCount, setAgentCount] = useState(2);
  const [analysisProgress, setAnalysisProgress] = useState<{
    step: 'idle' | 'analyzing' | 'summarizing' | 'completed' | 'error';
    currentAgent: number;
    totalAgents: number;
    message: string;
    error?: string;
  }>({
    step: 'idle',
    currentAgent: 0,
    totalAgents: 0,
    message: ''
  });
  const user = useStore(userAtom);

  // 分割trace数据的函数 - 按照data列表项数量分割为N个部分
  const splitTraceData = (traceData: string, count: number = agentCount): { success: boolean; data?: string[]; error?: string } => {
    try {
      if (isCompressionEnabled) {
        // 对于压缩的YAML格式，需要解析后按data列表项分割
        const parsed = JSON.parse(traceData);
        if (parsed && parsed.data && Array.isArray(parsed.data)) {
          const dataItems = parsed.data;
          if (dataItems.length === 0) {
            return {
              success: false,
              error: 'trace数据中的data数组为空，无法进行分割'
            };
          }
          
          if (dataItems.length < count) {
            return {
              success: false,
              error: `trace数据中的data数组长度(${dataItems.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          // 计算每个Agent处理的数据项数量
          const itemsPerAgent = Math.floor(dataItems.length / count);
          const remainder = dataItems.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            // 前remainder个Agent多分配一个数据项
            const currentItemsCount = itemsPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentItemsCount;
            
            const agentData = {
              ...parsed,
              data: dataItems.slice(startIndex, endIndex)
            };
            
            result.push(JSON.stringify(agentData, null, 2));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        } else {
          // 如果不是标准格式，按行分割作为备选
          const lines = traceData.split('\n');
          if (lines.length === 0) {
            return {
              success: false,
              error: 'trace数据为空，无法进行分割'
            };
          }
          
          if (lines.length < count) {
            return {
              success: false,
              error: `trace数据行数(${lines.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          const linesPerAgent = Math.floor(lines.length / count);
          const remainder = lines.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            const currentLinesCount = linesPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentLinesCount;
            result.push(lines.slice(startIndex, endIndex).join('\n'));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        }
      } else {
        // 对于JSON格式，按照data列表项分割
        const parsed = JSON.parse(traceData);
        if (parsed && parsed.data && Array.isArray(parsed.data)) {
          const dataItems = parsed.data;
          if (dataItems.length === 0) {
            return {
              success: false,
              error: 'trace数据中的data数组为空，无法进行分割'
            };
          }
          
          if (dataItems.length < count) {
            return {
              success: false,
              error: `trace数据中的data数组长度(${dataItems.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          // 计算每个Agent处理的数据项数量
          const itemsPerAgent = Math.floor(dataItems.length / count);
          const remainder = dataItems.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            const currentItemsCount = itemsPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentItemsCount;
            
            const agentData = {
              ...parsed,
              data: dataItems.slice(startIndex, endIndex)
            };
            
            result.push(JSON.stringify(agentData, null, 2));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        } else if (Array.isArray(parsed)) {
          // 如果直接是数组，按数组项分割
          if (parsed.length === 0) {
            return {
              success: false,
              error: 'trace数据为空数组，无法进行分割'
            };
          }
          
          if (parsed.length < count) {
            return {
              success: false,
              error: `trace数组长度(${parsed.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          const itemsPerAgent = Math.floor(parsed.length / count);
          const remainder = parsed.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            const currentItemsCount = itemsPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentItemsCount;
            result.push(JSON.stringify(parsed.slice(startIndex, endIndex), null, 2));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        } else {
          // 如果不是标准格式，按属性分割作为备选
          const keys = Object.keys(parsed);
          if (keys.length === 0) {
            return {
              success: false,
              error: 'trace数据为空对象，无法进行分割'
            };
          }
          
          if (keys.length < count) {
            return {
              success: false,
              error: `trace对象属性数量(${keys.length})小于Agent数量(${count})，无法进行分割`
            };
          }
          
          const keysPerAgent = Math.floor(keys.length / count);
          const remainder = keys.length % count;
          
          const result: string[] = [];
          let startIndex = 0;
          
          for (let i = 0; i < count; i++) {
            const currentKeysCount = keysPerAgent + (i < remainder ? 1 : 0);
            const endIndex = startIndex + currentKeysCount;
            
            const agentData = keys.slice(startIndex, endIndex).reduce((acc, key) => {
              acc[key] = parsed[key];
              return acc;
            }, {} as any);
            
            result.push(JSON.stringify(agentData, null, 2));
            startIndex = endIndex;
          }
          
          return {
            success: true,
            data: result
          };
        }
      }
    } catch (e) {
      console.error('分割trace数据失败:', e);
      return {
        success: false,
        error: `分割trace数据失败: ${(e as Error).message}`
      };
    }
  };

  const handleFetchTrace = async () => {
    if (!sessionId) {
      toast.error('请输入 Session ID');
      return null;
    }
    setIsFetchingTrace(true);
    try {
      let res = await fetchTraceWithRetry(sessionId);
      let traceData: string;
      
      if (isCompressionEnabled) {
        traceData = processAgentTraceCompressed(res);
      } else {
        traceData = JSON.stringify(res, null, 2);
      }
      
      const newQuery = `${DEFAULT_QUERY_PREFIX}${traceData}${QUERY_SUFFIX}`;
      setQuery(newQuery);
      setIsFetchingTrace(false);
      return newQuery;
    } catch (e) {
      console.error(e);
      toast.error(`获取 trace 失败: ${(e as Error).message}`);
      setIsFetchingTrace(false);
      return null;
    }
  };

  // 解析JSON响应的辅助函数
  const parseJsonResponse = (content: string): any => {
    try {
      let cleaned = content.trim();
      // 去除 BOM
      cleaned = cleaned.replace(/^\uFEFF/, '');
      // 去掉开头的 ```json 或 ```
      if (cleaned.startsWith('```json')) {
        cleaned = cleaned.substring(7);
      } else if (cleaned.startsWith('```')) {
        cleaned = cleaned.substring(3);
      }
      // 去掉结尾的 ```
      if (cleaned.endsWith('```')) {
        cleaned = cleaned.substring(0, cleaned.length - 3);
      }
      // 去除首尾空白字符
      cleaned = cleaned.trim();
      return JSON.parse(cleaned);
    } catch (e) {
      console.error('JSON 解析失败:', e);
      console.error('原始内容:', content.substring(0, 500) + '...');
      return null;
    }
  };

  // 多agent评估函数
  const runMultiAgentEvaluation = async (traceData: string) => {
    const startTime = Date.now();
    setIsLoading(true);
    setResult('');
    setParsedResult(null);
    setAnalysisProgress({
      step: 'analyzing',
      currentAgent: 0,
      totalAgents: agentCount + 1,
      message: `正在启动${agentCount}个分析agent...`
    });

    console.log(`开始多Agent评估，Agent数量: ${agentCount}，开始时间: ${new Date().toISOString()}`);

    try {
      // 分割trace数据
      const splitResult = splitTraceData(traceData, agentCount);
      if (!splitResult.success) {
        throw new Error(splitResult.error || 'trace数据分割失败');
      }
      const traceParts = splitResult.data!;
      
      // 构建reason部分
      const reasonPrefix = reason.trim() ? `<reason>
${reason.trim()}
</reason>

` : '';

      // 并行调用N个分析agent
      setAnalysisProgress(prev => ({
        ...prev,
        message: `正在并行启动${agentCount}个分析agent...`
      }));

      // 创建所有分析agent的并行调用
      const analysisAgentPromises = traceParts.map((tracePart, index) => {
        console.log(`启动分析Agent ${index + 1}/${agentCount}`);
        
        return apiClient.ChatStream({
          model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `${reasonPrefix}${DEFAULT_QUERY_PREFIX}${tracePart}${QUERY_SUFFIX}` },
          ],
          temperature,
          max_tokens: 64000,
        }).then(res => {
          const content = (res as IChatResponse)?.choices?.[0]?.message?.content ?? '';
          console.log(`分析Agent ${index + 1} 完成`);
          
          // 更新进度：显示完成的agent数量
          setAnalysisProgress(prev => ({
            ...prev,
            currentAgent: prev.currentAgent + 1,
            message: `分析Agent ${index + 1} 完成 (${prev.currentAgent + 1}/${agentCount})`
          }));
          
          return {
            index,
            agentId: index + 1,
            content,
            timestamp: new Date().toISOString()
          };
        }).catch(error => {
          console.error(`分析Agent ${index + 1} 失败:`, error);
          throw new Error(`分析Agent ${index + 1} 调用失败: ${error.message}`);
        });
      });

      // 等待所有分析agent完成
      setAnalysisProgress(prev => ({
        ...prev,
        message: `等待${agentCount}个分析agent完成...`
      }));

      const agentResults = await Promise.all(analysisAgentPromises);
      
      // 所有分析agent完成后，显示汇总阶段
      setAnalysisProgress(prev => ({
        ...prev,
        message: `所有${agentCount}个分析agent已完成，开始汇总...`
      }));

      // 解析所有分析结果
      const parsedResults = agentResults.map(result => {
        const parsed = parseJsonResponse(result.content);
        if (!parsed) {
          throw new Error(`第${result.index + 1}个分析agent返回结果解析失败`);
        }
        return { index: result.index, data: parsed };
      });

      // 所有分析agent完成后，调用总结agent
      setAnalysisProgress({
        step: 'summarizing',
        currentAgent: agentCount,
        totalAgents: agentCount + 1,
        message: '正在调用总结Agent汇总分析结果...'
      });

      console.log(`开始汇总${agentCount}个分析Agent的结果`);

      // 构建汇总查询
      const resultsText = parsedResults.map(result => 
        `## 第${result.index + 1}个分析结果：\n\`\`\`json\n${JSON.stringify(result.data, null, 2)}\n\`\`\``
      ).join('\n\n');

      const summaryQuery = `以下是${agentCount}个分析agent的评估结果，请按照要求汇总：

${resultsText}

请将以上${agentCount}个分析结果汇总成一个统一的分析报告。`;

      const summaryResult = await apiClient.ChatStream({
        model,
        messages: [
          { role: 'system', content: SUMMARY_SYSTEM_PROMPT },
          { role: 'user', content: summaryQuery },
        ],
        temperature,
        max_tokens: 64000,
      }).then(res => {
        console.log('总结Agent完成');
        // 更新汇总进度
        setAnalysisProgress(prev => ({
          ...prev,
          currentAgent: prev.totalAgents,
          message: '总结Agent完成，正在处理结果...'
        }));
        return res;
      }).catch(error => {
        console.error('总结Agent失败:', error);
        throw new Error(`总结Agent调用失败: ${error.message}`);
      });

      const summaryContent = (summaryResult as IChatResponse)?.choices?.[0]?.message?.content ?? '';
      const finalResult = parseJsonResponse(summaryContent);

      if (finalResult) {
        setParsedResult(finalResult);
        
        // 构建详细结果显示
        const detailedResults = parsedResults.map(result => {
          const agentResult = agentResults.find(r => r.index === result.index);
          return `分析Agent ${result.index + 1}结果 (完成时间: ${agentResult?.timestamp || 'N/A'})：
${JSON.stringify(result.data, null, 2)}`;
        }).join('\n\n');

        const endTime = Date.now();
        const totalTime = ((endTime - startTime) / 1000).toFixed(2);

        setResult(`${agentCount}个Agent并行评估完成！

## 并行处理详情：
- 分析Agent数量: ${agentCount}
- 总耗时: ${totalTime}秒
- 并行启动时间: ${new Date().toISOString()}
- 所有分析Agent并行处理完成

${detailedResults}

## 汇总结果：
${JSON.stringify(finalResult, null, 2)}`);

        console.log(`多Agent评估完成，总耗时: ${totalTime}秒，Agent数量: ${agentCount}`);
      } else {
        throw new Error('总结agent返回结果解析失败');
      }

      setAnalysisProgress({
        step: 'completed',
        currentAgent: agentCount + 1,
        totalAgents: agentCount + 1,
        message: '评估完成！'
      });

    } catch (error) {
      console.error('多agent评估失败:', error);
      setResult(`多Agent评估失败: ${(error as Error).message}`);
      setParsedResult(null);
      
      setAnalysisProgress({
        step: 'error',
        currentAgent: 0,
        totalAgents: 0,
        message: '评估失败',
        error: (error as Error).message
      });
    } finally {
      setIsLoading(false);
      setTimeout(() => {
        setAnalysisProgress({
          step: 'idle',
          currentAgent: 0,
          totalAgents: 0,
          message: ''
        });
      }, 2000);
    }
  };

  // 单agent评估函数（原有逻辑）
  const runSingleAgentEvaluation = async (queryToRun: string) => {
    setIsLoading(true);
    setResult('');
    setParsedResult(null);
    
    // 构建最终的查询内容
    let finalQuery = queryToRun;
    if (reason.trim()) {
      finalQuery = `<reason>
${reason.trim()}
</reason>

${queryToRun}`;
    }
    
    try {
      const res = await apiClient.ChatStream({
        model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: finalQuery },
        ],
        temperature,
        max_tokens: 64000,
      });
      const content = (res as IChatResponse)?.choices?.[0]?.message?.content ?? '';
      setResult(content);
      
      const parsed = parseJsonResponse(content);
      setParsedResult(parsed);
    } catch (error) {
      console.error(`Error running query:`, error);
      setResult('Error: Could not fetch result.');
      setParsedResult(null);
    }
    setIsLoading(false);
  };

  const handleRun = async () => {
    if (isLoading || !model) return;
    
    if (isMultiAgentEnabled) {
      // 多agent模式
      const queryToRun = query || (await handleFetchTrace());
      if (!queryToRun) {
        return;
      }
      
      // 提取trace数据
      let traceData: string;
      
      // 尝试多种方式提取trace数据
      const traceMatch = queryToRun.match(/<trace>\n([\s\S]*?)\n<\/trace>/);
      if (traceMatch) {
        // 标准格式：<trace>...</trace>
        traceData = traceMatch[1];
      } else {
        // 尝试其他可能的格式
        const alternativeMatch = queryToRun.match(/<trace>([\s\S]*?)<\/trace>/);
        if (alternativeMatch) {
          traceData = alternativeMatch[1].trim();
        } else {
          // 如果都没有找到，检查是否是纯trace数据（没有<trace>标签）
          const lines = queryToRun.split('\n');
          const hasTraceTag = lines.some(line => line.includes('<trace>') || line.includes('</trace>'));
          
          if (!hasTraceTag) {
            // 可能是纯trace数据，尝试直接解析
            try {
              const parsed = JSON.parse(queryToRun);
              if (parsed && (parsed.data || Array.isArray(parsed))) {
                traceData = queryToRun;
              } else {
                throw new Error('不是有效的trace数据格式');
              }
            } catch (e) {
              console.error('无法提取trace数据，query内容：', queryToRun.substring(0, 500) + '...');
              toast.error('无法从query中提取trace数据。请确保：\n1. query中包含<trace>标签，或\n2. query是完整的trace JSON数据');
              return;
            }
          } else {
            console.error('无法提取trace数据，query内容：', queryToRun.substring(0, 500) + '...');
            toast.error('无法从query中提取trace数据，请确保<trace>标签格式正确');
            return;
          }
        }
      }
      await runMultiAgentEvaluation(traceData);
    } else {
      // 单agent模式
      const queryToRun = query || (await handleFetchTrace());
      if (!queryToRun) {
        return;
      }
      await runSingleAgentEvaluation(queryToRun);
    }
  };

  const renderProblemSummary = (summary: any) => {
    const problemLabels = {
      problem_1: '缺少必要知识导致任务无法完成',
      problem_2: '召回与任务无关的知识',
      problem_3: '召回存在歧义或冲突的知识',
      problem_4: '召回明确知识但未按知识要求执行'
    };

    return (
      <div className="space-y-2">
        <Typography.Title heading={6}>问题总结</Typography.Title>
        {Object.entries(summary).map(([key, value]) => (
          <div key={key} className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${value === '是' ? 'bg-red-500' : 'bg-green-500'}`}></div>
            <span className="text-sm">{problemLabels[key as keyof typeof problemLabels]}: {String(value)}</span>
          </div>
        ))}
      </div>
    );
  };

  const renderProblemDetails = (details: any[]) => {
    if (!details || details.length === 0) {
      return <div className="text-gray-500">未发现问题</div>;
    }

    // 问题类型映射
    const problemTypeMap: Record<string, string> = {
      '1': '缺少必要知识导致任务无法完成',
      '2': '召回与任务无关的知识',
      '3': '召回存在歧义或冲突的知识',
      '4': '召回明确知识但未按知识要求执行'
    };

    return (
      <div className="space-y-4">
        <Typography.Title heading={6}>问题详情</Typography.Title>
        {details.map((detail, index) => (
          <Card key={index} className="border-l-4 border-red-500">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <span className="font-semibold">问题类型:</span>
                <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">
                  {problemTypeMap[detail.problem_type] || `问题 ${detail.problem_type}`}
                </span>
              </div>
              <div>
                <span className="font-semibold">相关知识:</span>
                <span className="ml-2 font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                  {detail.knowledge_ids || detail.knowledge_id || '未提供'}
                </span>
              </div>
              <div>
                <span className="font-semibold">召回时序:</span>
                <span className="ml-2">{detail.recall_timing}</span>
              </div>
              <div>
                <span className="font-semibold">问题描述:</span>
                <div className="mt-1 p-2 bg-red-50 rounded">
                  {detail.problem_description}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <>
      <PageHeader
        title="知识评估"
        description="通过分析 Agent 执行轨迹，评估知识召回与执行效果"
      />
      <div className="p-6">
                <div className="mb-4">
          <h2 className="text-lg font-semibold mb-2">System Prompt</h2>
        <textarea
          className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
          value={systemPrompt}
          onChange={(e) => setSystemPrompt(e.target.value)}
          rows={isPromptExpanded ? Math.max(10, systemPrompt.split('\n').length) : 3}
        />
        <button
          className="text-sm text-blue-500 mt-1"
          onClick={() => setIsPromptExpanded(!isPromptExpanded)}
        >
          {isPromptExpanded ? '收起' : '展开'}
        </button>
      </div>

        <div className="flex items-center gap-4 mb-4">
          <Input
            placeholder="Enter Session ID"
            value={sessionId}
            onChange={setSessionId}
            className="w-1/2"
          />
          <Button type="primary" onClick={handleFetchTrace} loading={isFetchingTrace}>
            {isFetchingTrace ? '拉取中...' : '拉取轨迹'}
          </Button>
        </div>

        <div className="flex items-center gap-4 mb-4 flex-wrap">
          <div className="flex items-center gap-2 flex-shrink-0">
            <Switch checked={isCompressionEnabled} onChange={setIsCompressionEnabled} />
            <label onClick={() => setIsCompressionEnabled(v => !v)} className="cursor-pointer select-none whitespace-nowrap">
              上下文压缩
            </label>
            <HelpTooltip content={COMPRESSION_HELP_CONTENT} />
          </div>
          
          <div className="flex items-center gap-2 flex-shrink-0">
            <Switch checked={isMultiAgentEnabled} onChange={setIsMultiAgentEnabled} />
            <label onClick={() => setIsMultiAgentEnabled(v => !v)} className="cursor-pointer select-none whitespace-nowrap">
              多Agent评估
            </label>
            <HelpTooltip content="启用多Agent评估模式，通过多个分析Agent并行分析trace的不同部分，然后由总结Agent汇总结果，提供更全面的评估" />
          </div>
          
          {isMultiAgentEnabled && (
            <div className="flex items-center gap-2 flex-shrink-0">
              <label className="cursor-pointer select-none whitespace-nowrap">Agent数量</label>
              <Input
                type="number"
                min="2"
                max="10"
                value={String(agentCount)}
                onChange={(value) => setAgentCount(Math.max(2, Math.min(10, Number(value) || 2)))}
                className="w-16"
                size="small"
              />
              <HelpTooltip content="设置并行分析Agent的数量，范围2-10个。数量越多，分析越细致，但耗时更长" />
            </div>
          )}
        </div>

        <div className="mb-4">
          <Input.TextArea
            value={reason}
            placeholder="请输入任务不符合预期的地方（可选）"
            onChange={setReason}
            rows={2}
            className="w-full"
          />
        </div>

        <div className="flex items-center gap-4 mb-4">
          <div className="w-1/4">
            <Select
              placeholder="Select a model"
              onChange={setModel}
              value={model}
              showSearch
            >
              {[
                { label: 'Gemini 2.5 Pro', value: 'gemini-2.5-pro' },
                { label: 'GPT-4o', value: 'gpt-4o' },
                { label: 'GPT-4', value: 'gpt-4' },
                { label: 'GPT-3.5-turbo', value: 'gpt-3.5-turbo' },
              ].map((option) => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </div>
          <div className="w-1/4">
            <Input
              type="number"
              min="0"
              max="2"
              step="0.1"
              value={String(temperature)}
              onChange={(value) => setTemperature(Number(value))}
              prefix="Temperature:"
            />
          </div>
          <div className="flex-1"></div>
          <Button type="primary" onClick={handleRun} loading={isLoading}>
            {isLoading ? (isMultiAgentEnabled ? '多Agent评估中...' : '评估中...') : '开始评估'}
          </Button>
        </div>

        {/* 多Agent评估进度显示 */}
        {isMultiAgentEnabled && analysisProgress.step !== 'idle' && (
          <div className={`mb-4 p-4 rounded-lg border ${
            analysisProgress.step === 'error' 
              ? 'bg-red-50 border-red-200' 
              : 'bg-blue-50 border-blue-200'
          }`}>
            <div className="flex items-center justify-between mb-2">
              <span className={`font-medium ${
                analysisProgress.step === 'error' ? 'text-red-800' : 'text-blue-800'
              }`}>
                {analysisProgress.step === 'analyzing' && '分析阶段'}
                {analysisProgress.step === 'summarizing' && '汇总阶段'}
                {analysisProgress.step === 'completed' && '评估完成'}
                {analysisProgress.step === 'error' && '评估失败'}
              </span>
              {analysisProgress.step !== 'error' && (
                <span className="text-sm text-blue-600">
                  {analysisProgress.currentAgent}/{analysisProgress.totalAgents}
                </span>
              )}
            </div>
            {analysisProgress.step !== 'error' && (
              <div className="w-full bg-blue-200 rounded-full h-2 mb-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${(analysisProgress.currentAgent / analysisProgress.totalAgents) * 100}%` 
                  }}
                ></div>
              </div>
            )}
            {analysisProgress.step === 'analyzing' && (
              <div className="text-xs text-blue-600 mt-1">
                💡 所有分析Agent正在并行处理中...
              </div>
            )}
            <p className={`text-sm ${
              analysisProgress.step === 'error' ? 'text-red-700' : 'text-blue-700'
            }`}>
              {analysisProgress.message}
            </p>
            {analysisProgress.step === 'error' && analysisProgress.error && (
              <div className="mt-2 p-2 bg-red-100 rounded border border-red-300">
                <p className="text-xs text-red-800 font-medium">错误详情：</p>
                <p className="text-xs text-red-700 mt-1">{analysisProgress.error}</p>
              </div>
            )}
          </div>
        )}

        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <p className="font-semibold">Query:</p>
            {isMultiAgentEnabled && (
              <Button 
                size="small" 
                type="secondary"
                onClick={() => {
                  if (query && !query.includes('<trace>')) {
                    // 如果query中没有<trace>标签，自动添加
                    const formattedQuery = `${DEFAULT_QUERY_PREFIX}${query}${QUERY_SUFFIX}`;
                    setQuery(formattedQuery);
                    toast.success('已自动添加<trace>标签');
                  } else if (query && query.includes('<trace>')) {
                    toast.info('query已包含<trace>标签');
                  } else {
                    toast.warning('请先输入query内容');
                  }
                }}
              >
                格式化Query
              </Button>
            )}
          </div>
          <div style={{ border: '1px solid var(--color-border-2)', borderRadius: 'var(--border-radius-medium)' }}>
            <Editor
              height="240px"
              language={isCompressionEnabled ? "yaml" : "json"}
              value={query}
              onChange={(value) => setQuery(value || '')}
              options={{
                wordWrap: 'on',
                minimap: { enabled: true },
                scrollBeyondLastLine: false,
                automaticLayout: true,
                readOnly: false,
              }}
            />
          </div>
          {isMultiAgentEnabled && (
            <p className="text-xs text-gray-500 mt-1">
              💡 多Agent模式使用{agentCount}个分析Agent并行处理，需要query包含&lt;trace&gt;标签或完整的trace JSON数据
            </p>
          )}
        </div>

        {parsedResult && (
          <div className="mt-4">
            <div className="flex justify-between items-center mb-4">
              <Typography.Title heading={6}>评估结果</Typography.Title>
            </div>
          
          {parsedResult.summary && renderProblemSummary(parsedResult.summary)}
          
          {parsedResult.details && renderProblemDetails(parsedResult.details)}
          
          {parsedResult.recommendations && (
            <div className="space-y-2">
              <Typography.Title heading={6}>改进建议</Typography.Title>
              <Card>
                <div className="whitespace-pre-wrap">{parsedResult.recommendations}</div>
              </Card>
            </div>
          )}
        </div>
      )}

      {result && !parsedResult && (
        <div className="mt-6">
          <Typography.Title heading={5}>原始响应</Typography.Title>
          <div className="bg-gray-100 p-4 rounded-md">
            <pre className="whitespace-pre-wrap text-sm">{result}</pre>
          </div>
        </div>
      )}

      {/* 多Agent模式下的详细结果显示 */}
      {isMultiAgentEnabled && parsedResult && (
        <div className="mt-6">
          <Typography.Title heading={5}>多Agent评估详情</Typography.Title>
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <p className="text-sm text-green-700 mb-2">
              ✓ 已通过{agentCount}个分析Agent和总结Agent完成全面评估
            </p>
            <p className="text-xs text-green-600">
              使用{agentCount}个分析Agent并行处理trace的不同部分，总结Agent汇总所有结果，提供更全面的知识评估
            </p>
          </div>
        </div>
      )}
    </div>
    </>
  );
}
