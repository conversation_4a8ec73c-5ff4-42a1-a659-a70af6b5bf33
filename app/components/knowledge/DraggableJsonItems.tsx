import { Button } from "@arco-design/web-react";
import { JSONEditor } from "@/components/ui/json-editor";
import { 
  DndContext, 
  closestCenter,
  KeyboardSensor, 
  PointerSensor, 
  useSensor, 
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { IconDelete, IconDragDotVertical } from "@arco-design/web-react/icon";
import { useState } from "react";

// 定义JSON表单项的数据结构
export interface JsonItem {
  id: string;
  value: string;
}

// 可排序的JSON表单项组件
function SortableJsonItem({ id, value, onChange, onRemove }: { 
  id: string; 
  value: string; 
  onChange: (id: string, value: string) => void;
  onRemove: (id: string) => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style} className="mb-4 bg-white rounded border p-2">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center">
          <div {...attributes} {...listeners} className="cursor-grab mr-2">
            <IconDragDotVertical />
          </div>
          <div className="font-medium">JSON 项</div>
        </div>
        <Button 
          type="secondary" 
          status="danger" 
          icon={<IconDelete />} 
          size="mini"
          onClick={() => onRemove(id)}
        />
      </div>
      <JSONEditor 
        value={value} 
        onChange={(newValue) => onChange(id, newValue)} 
        height="150px"
      />
    </div>
  );
}

export interface DraggableJsonItemsProps {
  // 初始 JSON 项数组
  initialItems?: JsonItem[];
  // 当 JSON 项数组变化时的回调函数
  onChange?: (items: JsonItem[]) => void;
  // 添加按钮的文本
  addButtonText?: string;
  // 空状态提示文本
  emptyText?: string;
}

export default function DraggableJsonItems({
  initialItems = [],
  onChange,
  addButtonText = "添加 JSON 项",
  emptyText = "暂无 JSON 数据项，点击上方按钮添加"
}: DraggableJsonItemsProps) {
  // 用于管理JSON表单项的状态
  const [jsonItems, setJsonItems] = useState<JsonItem[]>(initialItems);

  // 传感器配置，用于拖拽功能
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 添加新的JSON表单项
  const addJsonItem = () => {
    const newItem: JsonItem = {
      id: `item-${Date.now()}`,
      value: '{}'
    };
    const newItems = [...jsonItems, newItem];
    setJsonItems(newItems);
    onChange?.(newItems);
  };

  // 更新JSON表单项的值
  const updateJsonItem = (id: string, value: string) => {
    const newItems = jsonItems.map(item => 
      item.id === id ? { ...item, value } : item
    );
    setJsonItems(newItems);
    onChange?.(newItems);
  };

  // 删除JSON表单项
  const removeJsonItem = (id: string) => {
    const newItems = jsonItems.filter(item => item.id !== id);
    setJsonItems(newItems);
    onChange?.(newItems);
  };

  // 处理拖拽结束事件
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      const newItems = arrayMove(
        jsonItems,
        jsonItems.findIndex(item => item.id === active.id),
        jsonItems.findIndex(item => item.id === over.id)
      );
      setJsonItems(newItems);
      onChange?.(newItems);
    }
  };

  // 验证所有JSON表单项是否为有效的JSON
  const validateJsonItems = (): boolean => {
    for (const item of jsonItems) {
      try {
        JSON.parse(item.value);
      } catch (e) {
        return false;
      }
    }
    return true;
  };

  // 获取处理后的JSON数据
  const getJsonData = (): any[] => {
    return jsonItems.map(item => {
      try {
        return JSON.parse(item.value);
      } catch {
        return {};
      }
    });
  };

  return (
    <div className="mt-4">
      <div className="flex items-center justify-between mb-2">
        <div className="text-base font-medium">JSON 数据项</div>
        <Button type="primary" size="small" onClick={addJsonItem}>
          {addButtonText}
        </Button>
      </div>
      
      {/* 拖拽排序区域 */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={jsonItems.map(item => item.id)}
          strategy={verticalListSortingStrategy}
        >
          <div className="space-y-2">
            {jsonItems.map((item) => (
              <SortableJsonItem
                key={item.id}
                id={item.id}
                value={item.value}
                onChange={updateJsonItem}
                onRemove={removeJsonItem}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>
      
      {jsonItems.length === 0 && (
        <div className="text-center py-8 border border-dashed rounded-md">
          <div className="text-gray-500">{emptyText}</div>
        </div>
      )}
    </div>
  );
}

// 导出辅助方法
export const JsonItemsHelper = {
  // 验证所有JSON表单项是否为有效的JSON
  validateJsonItems: (items: JsonItem[]): boolean => {
    for (const item of items) {
      try {
        JSON.parse(item.value);
      } catch (e) {
        return false;
      }
    }
    return true;
  },

  // 获取处理后的JSON数据
  getJsonData: (items: JsonItem[]): any[] => {
    return items.map(item => {
      try {
        return JSON.parse(item.value);
      } catch {
        return {};
      }
    });
  }
};