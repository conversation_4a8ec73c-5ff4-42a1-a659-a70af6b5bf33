import { Modal, ModalProps, Form, Input, Select } from "@arco-design/web-react";
import { Knowledgeset } from "@/app/bam/aime/namespaces/knowledgeset";
import { useEffect } from "react";
import { apiClient } from "@/app/api/request";
import { toast } from "sonner";
import { useQuery } from "@tanstack/react-query";

export interface KnowledgesetModalProps extends ModalProps {
  isEdit?: boolean;
  data?: Knowledgeset;
  knowledgeTypes?: { value: string; name: string }[];
}

export default function KnowledgesetModal(props: KnowledgesetModalProps) {
  const { data, isEdit = false, knowledgeTypes } = props || {};
  const [form] = Form.useForm();

  useEffect(() => {
    if (data && props.visible && form && isEdit) {
      form.setFieldsValue({
        name: data.name,
        description: data.description,
        type: data.type,
        knowledge_set_id: data.id,
        key: data.key,
        tags: data.tags,
      });
    } else if (!props.visible) {
      form.clearFields();
    }
  }, [data, props.visible, form, isEdit]);

  const { data: metadataConf } = useQuery({
    queryKey: ["GetKnowledgesetMetadataConf"],
    queryFn: async () => {
      const response = await apiClient.GetKnowledgesetMetadataConf();
      return response;
    },
    staleTime: 1000 * 60 * 5,
  });

  const handleOk = async () => {
    try {
      const values = await form.validate();

      if (isEdit) {
        await apiClient.UpdateKnowledgeset({
          knowledge_set_id: data?.id || "",
          name: values.name,
          description: values.description,
          type: values.type,
          key: values.key,
          tags: values.tags,
        } as any);
        toast.success("修改知识集成功");
      } else {
        await apiClient.CreateKnowledgeset({
          name: values.name,
          description: values.description,
          type: values.type,
          key: values.key,
          tags: values.tags,
        } as any);
        toast.success("添加知识集成功");
      }
      props?.onOk?.();
    } catch (error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      toast.error((error as any).message || "操作失败");
      props?.onOk?.();
    }
  };

  return (
    <Modal {...props} style={{ width: 600 }} onOk={handleOk}>
      <Form
        layout="vertical"
        className="mt-6"
        style={{ maxHeight: 600, overflow: "auto" }}
        form={form}
      >
        <Form.Item
          label="key"
          field="key"
          rules={[{ required: true, message: "请输入知识集key" }]}
        >
          <Input placeholder="请输入知识集key" allowClear />
        </Form.Item>
        <Form.Item
          label="名称"
          field="name"
          rules={[{ required: true, message: "请输入知识集名称" }]}
        >
          <Input placeholder="请输入知识集名称" allowClear />
        </Form.Item>
        <Form.Item
          label="描述"
          field="description"
          rules={[{ required: true, message: "请输入知识集描述" }]}
        >
          <Input.TextArea
            placeholder="请输入知识集描述"
            allowClear
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </Form.Item>

        <Form.Item
          label="类型"
          field="type"
          rules={[{ required: true, message: "请选择知识集类型" }]}
        >
          <Select placeholder="请选择知识集类型" allowClear>
            {knowledgeTypes?.map((type) => (
              <Select.Option key={type.value} value={type.value}>
                {type.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item shouldUpdate noStyle>
          {(values) => {
            return values.type &&
              metadataConf?.knowledge_set_tags?.[values.type]?.length ? (
              <Form.Item label="tags" field="tags">
                <Select
                  mode="multiple"
                  allowClear
                  placeholder="请选择知识集标签"
                  options={
                    metadataConf?.knowledge_set_tags?.[values.type] || []
                  }
                />
              </Form.Item>
            ) : null;
          }}
        </Form.Item>

        {isEdit && (
          <Form.Item label="知识集ID" field="knowledge_set_id">
            <Input disabled />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
}
