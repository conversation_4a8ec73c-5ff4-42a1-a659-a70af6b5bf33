import {
  Drawer,
  DrawerProps,
  Form,
  Input,
  Button,
  Space,
  Select,
  Typography,
  Message,
} from "@arco-design/web-react";
import { KnowledgesetVersion } from "@/app/bam/aime/namespaces/knowledgeset";
import { use, useEffect, useState } from "react";
import { apiClient } from "@/app/api/request";
import { toast } from "sonner";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  IconDelete,
  IconDragDotVertical,
  IconDown,
  IconUp,
} from "@arco-design/web-react/icon";
import { useQuery } from "@tanstack/react-query";

// 定义JSON表单项的数据结构
interface JsonItem {
  id: string;
  key: string;
  title: string;
  content: string;
  enable_if: string;
  use_when: string;
  recall_method?: string[];
  tags: string[];
  isDirty?: boolean; // 标记是否有未保存的更改
  isSaving?: boolean; // 标记是否正在保存
  isCheckingEnableIf?: boolean; // 标记是否正在校验 enable_if
}

// 可排序的JSON表单项组件
function SortableJsonItem({
  id,
  item,
  onChange,
  onRemove,
  onSave,
  isReadOnly,
  tags,
}: {
  id: string;
  item: JsonItem;
  index: number;
  onChange: (id: string, field: keyof JsonItem, value: any) => void;
  onRemove: (id: string) => void;
  onSave: (id: string) => Promise<void>;
  tags?: string[];
  isReadOnly?: boolean;
}) {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id, disabled: isReadOnly });
  const [error, setError] = useState<string>("");
  const [success, setSuccess] = useState<string>("");

  const [height, setHeight] = useState<string | number>(0);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const { data: metadataConf } = useQuery({
    queryKey: ["GetKnowledgesetMetadataConf"],
    queryFn: async () => {
      const response = await apiClient.GetKnowledgesetMetadataConf();
      return response;
    },
    staleTime: 1000 * 60 * 5,
  });

  // const recallMethod = useMemo(() => {
  //   return (
  //     item?.recall_method || metadataConf?.knowledge_recall_method?.[0]?.value
  //   );
  // }, [metadataConf, item.recall_method]);

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="mb-3 bg-white rounded border pl-4 pr-4 pt-2 pb-2"
    >
      {/* 标题和操作按钮 */}
      <div
        className={`flex items-center justify-between cursor-pointer ${
          height && "mb-4"
        }`}
        onClick={() => setHeight(height === 0 ? "auto" : 0)}
      >
        <div className="flex items-center flex-auto">
          {!isReadOnly && (
            <div {...attributes} {...listeners} className="cursor-grab mr-2">
              <IconDragDotVertical />
            </div>
          )}
          <div className="font-medium text-base flex items-center">
            <div className="w-[200px] flex items-center">
              <Typography.Ellipsis
                className="text-sm !mb-0 max-w-[180px]"
                showTooltip
              >
                {item.key || "-"}
              </Typography.Ellipsis>
              <span className="ml-1">
                {height === 0 ? <IconDown /> : <IconUp />}
              </span>
            </div>

            {height === 0 && (
              <>
                <div className="inline-flex items-center gap-2 ml-5 w-[400px]">
                  <div className="text-xs text-gray-500">Title:</div>
                  <Typography.Ellipsis
                    className="text-sm !mb-0 max-w-[300px]"
                    showTooltip
                  >
                    {item.title || "-"}
                  </Typography.Ellipsis>
                </div>
                <div className="inline-flex  items-center gap-2 ml-2 w-[150px]">
                  <div className="text-xs text-gray-500">Content:</div>
                  <Typography.Ellipsis
                    className="text-sm !mb-0 max-w-[130px]"
                    showTooltip
                  >
                    {item.content || "-"}
                  </Typography.Ellipsis>
                </div>
              </>
            )}
          </div>
        </div>
        <div onClick={(e) => e.stopPropagation()}>
          {!isReadOnly && (
            <Space>
              <Button
                type="primary"
                size="mini"
                loading={item.isSaving}
                onClick={() => onSave(id)}
              >
                保存
              </Button>
              <Button
                type="secondary"
                status="danger"
                icon={<IconDelete />}
                size="mini"
                onClick={() => onRemove(id)}
              />
            </Space>
          )}
        </div>
      </div>

      {/* 表单字段 */}
      <Form
        layout="vertical"
        labelAlign="left"
        style={{ height, transition: "height 0.3s", overflow: "hidden" }}
      >
        <div className="grid grid-cols-2 gap-4">
          <Form.Item label="Key" required>
            <Input
              value={item.key}
              onChange={(value) => onChange(id, "key", value)}
              placeholder="请输入唯一标识符"
              disabled={isReadOnly}
            />
          </Form.Item>
          <Form.Item label="Title" required>
            <Input
              value={item.title}
              onChange={(value) => onChange(id, "title", value)}
              placeholder="请输入标题"
              disabled={isReadOnly}
            />
          </Form.Item>
        </div>

        <Form.Item label="Content" required>
          <Input.TextArea
            value={item.content}
            onChange={(value) => onChange(id, "content", value)}
            placeholder="请输入内容"
            autoSize={{ minRows: 4, maxRows: 15 }}
            disabled={isReadOnly}
          />
        </Form.Item>

        <Form.Item label="Enable If" required>
          <div className="flex flex-col">
            <Input.TextArea
              value={item.enable_if}
              onChange={(value) => onChange(id, "enable_if", value)}
              placeholder="请输入启用条件"
              autoSize={{ minRows: 3, maxRows: 10 }}
              disabled={isReadOnly}
            />
            {!isReadOnly && (
              <div className="flex justify-end mt-2">
                {error && <div className="text-red-500 mr-2">{error}</div>}
                {success && (
                  <div className="text-green-400 mr-2">{success}</div>
                )}
                <Button
                  type="primary"
                  size="mini"
                  loading={item.isCheckingEnableIf}
                  onClick={async () => {
                    // 处理空输入情况
                    if (!item.enable_if || item.enable_if.trim() === "") {
                      toast.warning("请先输入启用条件");
                      return;
                    }
                    // 更新状态为校验中
                    onChange(id, "isCheckingEnableIf", true);

                    try {
                      const result = await apiClient.CheckEnableIf({
                        enable_if_content: item.enable_if,
                      });
                      if (result.is_valid) {
                        setError("");
                        setSuccess("校验通过");
                        toast.success("校验通过");
                      } else {
                        setError(result.err_message || "校验失败");
                        setSuccess("");
                        toast.error(result.err_message || "校验失败");
                      }
                    } catch (error) {
                      console.error(error);
                      toast.error("校验请求失败");
                    } finally {
                      // 更新状态为校验完成
                      onChange(id, "isCheckingEnableIf", false);
                    }
                  }}
                >
                  校验
                </Button>
              </div>
            )}
          </div>
        </Form.Item>

        <Form.Item label="召回方式" required>
          <Select
            value={item?.recall_method}
            options={metadataConf?.knowledge_recall_method?.map((item) => ({
              label: item.name,
              value: item.value,
            }))}
            onChange={(value) => onChange(id, "recall_method", value)}
            placeholder="请选择召回方式"
            disabled={isReadOnly}
            mode="multiple"
          />
        </Form.Item>

        <Form.Item
          label="Use When"
          required={item?.recall_method?.includes("llm")}
        >
          <Input.TextArea
            value={item.use_when}
            onChange={(value) => onChange(id, "use_when", value)}
            placeholder="请输入使用条件"
            autoSize={{ minRows: 3, maxRows: 10 }}
            disabled={isReadOnly}
          />
        </Form.Item>

        <Form.Item label="Tags">
          <Select
            mode="multiple"
            value={item.tags}
            options={tags || []}
            onChange={(value) => onChange(id, "tags", value)}
            placeholder="请选择标签"
            allowCreate
            disabled={isReadOnly}
          />
        </Form.Item>
      </Form>
    </div>
  );
}

export interface KnowledgesetVersionModalProps extends DrawerProps {
  isEdit?: boolean;
  isReadOnly?: boolean;
  data?: KnowledgesetVersion;
  knowledgeSetId: string;
  tags?: string[];
  showUseWhen?: boolean;
}

export default function KnowledgesetVersionModal(
  props: KnowledgesetVersionModalProps
) {
  const {
    data,
    isEdit = false,
    knowledgeSetId,
    isReadOnly = false,
    tags = [],
    showUseWhen = false,
  } = props || {};
  const [form] = Form.useForm();
  // 用于管理JSON表单项的状态
  const [jsonItems, setJsonItems] = useState<JsonItem[]>([]);

  // 传感器配置，用于拖拽功能
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (data && props.visible && form && (isEdit || isReadOnly)) {
      form.setFieldsValue({
        version: data.version,
        description: data.description,
        knowledge_set_version_id: data.id,
        use_when: data.use_when || "",
      });

      // 加载知识数据
      setIsLoading(true);
      apiClient
        .GetKnowledgesetVersion({
          knowledge_set_version_id: data.id,
        })
        .then((res) => {
          const knowledges = res.knowledge_set_version?.knowledges || [];
          const items = knowledges.map((knowledge) => ({
            id: knowledge.id,
            key: knowledge.key,
            title: knowledge.title,
            content: knowledge.content,
            enable_if: knowledge.enable_if || "",
            use_when: knowledge.use_when || "",
            tags: knowledge.tags || [],
            isDirty: false,
            isSaving: false,
            isCheckingEnableIf: false,
            recall_method: knowledge.recall_method || undefined,
          }));
          setJsonItems(items);
        })
        .catch((err) => {
          console.error(err);
          toast.error("加载知识数据失败");
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else if (!props.visible) {
      form.clearFields();
      // 清空JSON表单项
      setJsonItems([]);
    }
  }, [data, props.visible, form, isEdit, isReadOnly]);

  // 添加新的JSON表单项
  const addJsonItem = () => {
    const newItem: JsonItem = {
      id: `item-${Date.now()}`,
      key: "",
      title: "",
      content: "",
      enable_if: "",
      use_when: "",
      tags: [],
      isDirty: true,
      isCheckingEnableIf: false,
    };
    setJsonItems([...jsonItems, newItem]);
  };

  // 更新JSON表单项的特定字段
  const updateJsonItem = (id: string, field: keyof JsonItem, value: any) => {
    setJsonItems(
      jsonItems.map((item) =>
        item.id === id ? { ...item, [field]: value, isDirty: true } : item
      )
    );
  };

  // 删除JSON表单项
  const removeJsonItem = async (id: string) => {
    setJsonItems(jsonItems.filter((item) => item.id !== id));
    if (id.startsWith("item-")) {
      Message.success("删除成功");
      return;
    }
    await apiClient.DeleteKnowledge({
      knowledge_id: id,
    });
    Message.success("删除成功");
  };

  // 保存单个JSON表单项
  const saveJsonItem = async (id: string) => {
    const itemIndex = jsonItems.findIndex((item) => item.id === id);
    if (itemIndex === -1) return;

    const item = jsonItems[itemIndex];

    // 验证必填字段
    if (
      !item.key ||
      !item.title ||
      !item.content ||
      (item.recall_method?.includes("llm") && !item.use_when)
    ) {
      toast.error("Key、Title 和 Content 为必填项");
      return;
    }

    try {
      // 更新状态为保存中
      setJsonItems((items) =>
        items.map((i, idx) =>
          idx === itemIndex ? { ...i, isSaving: true } : i
        )
      );

      if (item.id.startsWith("item-")) {
        // 新创建的项，调用创建 API
        const res = await apiClient.CreateKnowledge({
          knowledge_set_id: knowledgeSetId,
          knowledge_version_id: data?.id || "",
          key: item.key,
          title: item.title,
          content: item.content,
          enable_if: item.enable_if,
          use_when: item.use_when,
          tags: item.tags,
          recall_method: item.recall_method as string[],
         } as any);

        // 更新 ID 和状态
        setJsonItems((items) =>
          items.map((i, idx) =>
            idx === itemIndex
              ? {
                  ...i,
                  id: res.knowledge_id,
                  isDirty: false,
                  isSaving: false,
                }
              : i
          )
        );
      } else {
        // 已有的项，调用更新 API
        await apiClient.UpdateKnowledge({
          knowledge_id: item.id,
          key: item.key,
          title: item.title,
          content: item.content,
          enable_if: item.enable_if,
          use_when: item.use_when,
          tags: item.tags,
          recall_method: item.recall_method as string[],
        } as any);

        // 更新状态
        setJsonItems((items) =>
          items.map((i, idx) =>
            idx === itemIndex ? { ...i, isDirty: false, isSaving: false } : i
          )
        );
      }

      toast.success("保存成功");
    } catch (error) {
      console.error(error);
      toast.error("保存失败");

      // 恢复状态
      setJsonItems((items) =>
        items.map((i, idx) =>
          idx === itemIndex ? { ...i, isSaving: false } : i
        )
      );
    }
  };

  // 处理拖拽结束事件
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setJsonItems((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  // 处理表单提交
  const handleOk = async () => {
    try {
      const values = await form.validate();

      // 处理表单数据
      const formData = {
        ...values,
      };

      if (isEdit) {
        // 编辑版本
        await apiClient.UpdateKnowledgesetVersion({
          knowledge_set_version_id: data?.id || "",
          description: formData.description,
          use_when: formData.use_when,
          knowledges: jsonItems.map((item) => {
            return {
              knowledge_id: item.id.startsWith("item-") ? undefined : item.id,
              key: item.key,
              title: item.title,
              content: item.content,
              enable_if: item.enable_if,
              use_when: item.use_when,
              tags: item.tags,
              recall_method: item.recall_method,
            };
          }),
          knowledge_set_id: knowledgeSetId,
        });
        toast.success("修改知识集版本成功");
      } else {
        // 创建新版本
        await apiClient.CreateKnowledgesetVersion({
          knowledge_set_id: knowledgeSetId,
          // version: formData.version,
          description: formData.description,
          use_when: formData.use_when,
        });

        toast.success("添加知识集版本成功");
      }
      props?.onOk?.({} as any);
    } catch (error) {
      console.error(error);
      toast.error("操作失败");
    }
  };

  const renderFooter = () => {
    if (isReadOnly) {
      return <Button onClick={(e) => props?.onCancel?.(e)}>关闭</Button>;
    }
    return (
      <>
        <Button onClick={(e) => props?.onCancel?.(e)}>取消</Button>
        <Button type="primary" onClick={handleOk}>
          确认
        </Button>
      </>
    );
  };

  return (
    <Drawer
      {...props}
      style={{ width: 1000 }}
      onOk={handleOk}
      footer={renderFooter()}
    >
      <Form
        layout={isEdit ? "inline" : "vertical"}
        className="mt-4"
        style={{ overflow: "auto" }}
        form={form}
      >
        <Form.Item
          label="描述"
          field="description"
          rules={[{ required: true, message: "请输入版本描述" }]}
        >
          <Input.TextArea
            placeholder="请输入版本描述"
            allowClear
            autoSize={{ minRows: 1, maxRows: 6 }}
            disabled={isReadOnly}
          />
        </Form.Item>

        {(isEdit || isReadOnly) && (
          <>
            <Form.Item label="版本ID" field="knowledge_set_version_id">
              <Input disabled />
            </Form.Item>
            <Form.Item
              label="版本号"
              field="version"
              rules={[{ required: true, message: "请输入版本号" }]}
              disabled
            >
              <Input
                placeholder="请输入版本号，例如：v1.0.0"
                allowClear
                disabled
              />
            </Form.Item>
          </>
        )}

        {showUseWhen && (
          <Form.Item label="useWhen" field="use_when"  rules={[{ required: true, message: "请输入useWhen" }]}>
            <Input.TextArea
              style={{ minWidth: "716px" }}
              placeholder="请输入useWhen"
              allowClear
              autoSize={{ minRows: 3, maxRows: 6 }}
              disabled={isReadOnly}
            />
          </Form.Item>
        )}
      </Form>
      {/* JSON表单项区域 */}
      {(isEdit || isReadOnly) && (
        <div className="mt-4">
          <div className="flex items-center justify-between mb-3">
            <div className="text-base font-medium">知识列表</div>
            {!isReadOnly && (
              <Button type="primary" size="mini" onClick={addJsonItem}>
                添加知识
              </Button>
            )}
          </div>

          {/* 拖拽排序区域 */}
          {isLoading ? (
            <div className="text-center py-8 border border-dashed rounded-md">
              <div className="text-gray-500">正在加载知识项数据...</div>
            </div>
          ) : (
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={jsonItems.map((item) => item.id)}
                strategy={verticalListSortingStrategy}
              >
                <div className="space-y-2">
                  {jsonItems.map((item, index) => (
                    <SortableJsonItem
                      key={item.id}
                      id={item.id}
                      index={index}
                      item={item}
                      tags={tags}
                      onChange={updateJsonItem}
                      onRemove={removeJsonItem}
                      onSave={saveJsonItem}
                      isReadOnly={isReadOnly}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          )}

          {!isLoading && jsonItems.length === 0 && (
            <div className="text-center py-8 border border-dashed rounded-md">
              <div className="text-gray-500">
                暂无知识项数据
                {!isReadOnly && "，点击上方按钮添加"}
              </div>
            </div>
          )}
        </div>
      )}
    </Drawer>
  );
}
