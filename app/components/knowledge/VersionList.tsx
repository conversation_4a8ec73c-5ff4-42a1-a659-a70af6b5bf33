"use client";

import React, { useMemo, useState, useEffect } from "react";
import {
  Table,
  TableColumnProps,
  Button,
  Tag,
  Popconfirm,
  Link,
  Select,
  Spin,
} from "@arco-design/web-react";
import { PageHeader } from "@/app/components/PageHeader";
import {
  ListKnowledgesetVersionRequest,
  KnowledgesetVersion,
  KnowledgesetVersionStatus,
  RelationAgent,
  Knowledgeset,
  ListKnowledgesetRequest,
} from "@/app/bam/aime/namespaces/knowledgeset";
import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@/app/api/request";
import { IconPlus, IconEdit, IconCopy } from "@arco-design/web-react/icon";
import KnowledgesetVersionModal from "./VersionModal";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";

export const KnowledgesetVersionList = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const knowledgeSetId = searchParams.get("id") as string;
  const [requestData, setRequestData] =
    useState<ListKnowledgesetVersionRequest>({
      knowledge_set_id: knowledgeSetId,
      page_num: 1,
      page_size: 10,
    });
  const [visible, setVisible] = useState(false);
  const [targetItem, setTargetItem] = useState<
    KnowledgesetVersion | undefined
  >();
  const [isEdit, setIsEdit] = useState(false);
  const [isReadOnly, setIsReadOnly] = useState(false);

  // 知识集列表相关状态
  const [knowledgeSetListParams, setKnowledgeSetListParams] =
    useState<ListKnowledgesetRequest>({
      page_num: 1,
      page_size: 10,
      name: "",
    });
  const [knowledgeSets, setKnowledgeSets] = useState<Knowledgeset[]>([]);
  const [knowledgeSetLoading, setKnowledgeSetLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [selectedKnowledgeSetId, setSelectedKnowledgeSetId] =
    useState(knowledgeSetId);

  // 获取知识集信息
  const { data: knowledgeSetData } = useQuery({
    queryKey: ["knowledgeSet", knowledgeSetId],
    queryFn: async () => {
      const response = await apiClient.GetKnowledgeset({
        knowledge_set_id: knowledgeSetId,
      });
      return response.knowledge_set;
    },
  });

  const { data: metadataConf } = useQuery({
    queryKey: ["GetKnowledgesetMetadataConf"],
    queryFn: async () => {
      const response = await apiClient.GetKnowledgesetMetadataConf();
      return response;
    },
  });

  // 获取知识集列表
  const fetchKnowledgeSets = async (
    params: ListKnowledgesetRequest,
    isLoadMore = false
  ) => {
    try {
      setKnowledgeSetLoading(true);
      const response = await apiClient.ListKnowledgesets({
        ...params,
      });
      const newData = response.knowledge_sets || [];

      if (isLoadMore) {
        setKnowledgeSets((prev) => [...prev, ...newData]);
      } else {
        setKnowledgeSets(newData);
      }

      setHasMore(newData.length === params.page_size);
      return newData;
    } catch (error) {
      console.error("获取知识集列表失败", error);
      toast.error("获取知识集列表失败");
      return [];
    } finally {
      setKnowledgeSetLoading(false);
    }
  };

  // 初始加载知识集列表
  useEffect(() => {
    fetchKnowledgeSets(knowledgeSetListParams);
  }, []);

  // 确保当前知识集在列表中
  useEffect(() => {
    if (knowledgeSetData && knowledgeSets.length > 0) {
      const currentKnowledgeSetExists = knowledgeSets.some(
        (item) => item.id === knowledgeSetId
      );
      if (!currentKnowledgeSetExists) {
        setKnowledgeSets((prev) => [
          {
            ...knowledgeSetData,
            id: knowledgeSetId,
          },
          ...prev,
        ]);
      }
    }
  }, [knowledgeSetData, knowledgeSets, knowledgeSetId]);

  const currentKnowledgeSet = useMemo(() => {
    return knowledgeSets.find((item) => item.id === knowledgeSetId);
  }, [knowledgeSets, knowledgeSetId]);

  // 获取知识集版本列表
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["knowledgeSetVersionList", requestData],
    queryFn: async () => {
      const response = await apiClient.ListKnowledgesetVersions(requestData);
      return response || {};
    },
  });

  const tableData = useMemo(() => {
    return data?.knowledge_set_versions?.map((item) => {
      return {
        ...item,
        key: item.id,
      };
    });
  }, [data]);

  // 渲染状态标签
  const renderStatusTag = (status: KnowledgesetVersionStatus) => {
    switch (status) {
      case KnowledgesetVersionStatus.KnowledgeVersionStatusCreated:
        return <Tag color="blue">创建</Tag>;
      case KnowledgesetVersionStatus.KnowledgeVersionStatusOnline:
        return <Tag color="green">已发布</Tag>;
      case KnowledgesetVersionStatus.KnowledgeVersionStatusDisable:
        return <Tag color="red">已禁用</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  // 处理编辑版本
  const handleEdit = (item: KnowledgesetVersion) => {
    setTargetItem(item);
    setIsEdit(true);
    setIsReadOnly(false);
    setVisible(true);
  };

  // 处理查看详情
  const handleViewDetails = (item: KnowledgesetVersion) => {
    setTargetItem(item);
    setIsEdit(false);
    setIsReadOnly(true);
    setVisible(true);
  };

  // 处理添加版本
  const handleAdd = () => {
    setIsEdit(false);
    setIsReadOnly(false);
    setTargetItem(undefined);
    setVisible(true);
  };

  // 处理模态框确认
  const handleOk = () => {
    setVisible(false);
    refetch();
  };

  // 处理返回知识集列表
  const handleBack = () => {
    router.push("/knowledge/list");
  };

  // 处理知识集选择变化
  const handleKnowledgeSetChange = (value: string) => {
    if (value === knowledgeSetId) return;
    setRequestData({
      ...requestData,
      knowledge_set_id: value,
    });
    setSelectedKnowledgeSetId(value);
    router.push(`/knowledge/version?id=${value}`);
  };

  // 处理知识集搜索
  const handleKnowledgeSetSearch = (value: string) => {
    setKnowledgeSetListParams({
      ...knowledgeSetListParams,
      name: value,
      page_num: 1,
    });
    fetchKnowledgeSets({
      ...knowledgeSetListParams,
      name: value,
      page_num: 1,
    });
  };

  // 处理知识集下拉框滚动加载更多
  const handleKnowledgeSetPopupScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const { scrollTop, scrollHeight, clientHeight } = target;
    // 滚动到底部时加载更多
    if (
      scrollTop + clientHeight >= scrollHeight - 20 &&
      !knowledgeSetLoading &&
      hasMore
    ) {
      const nextPage = +knowledgeSetListParams.page_num + 1;
      setKnowledgeSetListParams({
        ...knowledgeSetListParams,
        page_num: nextPage,
      });
      fetchKnowledgeSets(
        {
          ...knowledgeSetListParams,
          page_num: nextPage,
        },
        true
      );
    }
  };

  // 处理发布版本
  const handlePublish = async (item: KnowledgesetVersion) => {
    try {
      await apiClient.UpdateKnowledgesetVersion({
        knowledge_set_version_id: item.id,
        status: KnowledgesetVersionStatus.KnowledgeVersionStatusOnline,
        knowledge_set_id: knowledgeSetId,
      });
      toast.success("发布成功");
      refetch();
    } catch (error) {
      console.error(error);
      toast.error("发布失败");
    }
  };

  // 处理禁用版本
  const handleDisable = async (item: KnowledgesetVersion) => {
    try {
      await apiClient.UpdateKnowledgesetVersion({
        knowledge_set_version_id: item.id,
        status: KnowledgesetVersionStatus.KnowledgeVersionStatusDisable,
        knowledge_set_id: knowledgeSetId,
      });
      toast.success("禁用成功");
      refetch();
    } catch (error) {
      console.error(error);
      toast.error("禁用失败");
    }
  };

  // 处理复制版本
  const handleCopy = async (item: KnowledgesetVersion) => {
    try {
      await apiClient.CopyKnowledgesetVersion({
        source_id: item.id,
      });
      toast.success("复制成功");
      refetch();
    } catch (error) {
      console.error(error);
      toast.error("复制失败");
    }
  };

  // 渲染关联部署Agent标签
  const renderRelationAgentTags = (relationAgents?: RelationAgent[]) => {
    if (!relationAgents || relationAgents.length === 0) {
      return <span className="text-gray-400">-</span>;
    }

    return (
      <div className="flex flex-wrap gap-1">
        {relationAgents.map((agent, index) => {
          // 根据Agent配置类型和状态设置标签颜色
          let color = "default";
          if (
            agent.agent_config_type === "base" &&
            agent.agent_config_version_status === "online"
          ) {
            color = "green";
          } else if (
            agent.agent_config_type === "abtest" &&
            agent.agent_config_version_status === "online"
          ) {
            color = "gold";
          }

          return (
            <Tag
              key={index}
              color={color}
              style={{ marginBottom: "4px", cursor: "pointer" }}
              onClick={() => {
                router.push(
                  `/agents/version/detail?id=${agent.agent_config_version_id}&config_id=${agent.agent_config_id}`
                );
              }}
            >
              {agent.agent_name} ({agent.agent_config_type})
            </Tag>
          );
        })}
      </div>
    );
  };

  // 表格列定义
  const columns: TableColumnProps[] = [
    {
      title: "ID",
      dataIndex: "id",
      width: 200,
    },
    {
      title: "版本号",
      dataIndex: "version",
      width: 120,
    },
    {
      title: "描述",
      dataIndex: "description",
      width: 200,
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 100,
      render: (_, record) => renderStatusTag(record.status),
    },
    {
      title: "关联部署agent",
      dataIndex: "relation_agents",
      width: 200,
      render: (_, record) => renderRelationAgentTags(record.relation_agents),
    },
    {
      title: "创建者",
      dataIndex: "creator",
      width: 120,
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      width: 180,
      render: (_, record) => {
        const date = new Date(record.created_at);
        return date.toLocaleString();
      },
    },
    {
      title: "更新时间",
      dataIndex: "updated_at",
      width: 180,
      render: (_, record) => {
        const date = new Date(record.updated_at);
        return date.toLocaleString();
      },
    },
  ];

  return (
    <div>
      <PageHeader
        title={
          <div className="flex items-center">
            <span className="mr-2">知识集版本列表</span>
            <Select
              showSearch
              value={!knowledgeSetLoading ? selectedKnowledgeSetId : ""}
              placeholder="选择知识集"
              style={{ width: 300 }}
              onChange={handleKnowledgeSetChange}
              onSearch={handleKnowledgeSetSearch}
              onPopupScroll={handleKnowledgeSetPopupScroll}
              loading={knowledgeSetLoading}
              filterOption={false}
              allowClear={false}
            >
              {knowledgeSets.map((item) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
              {knowledgeSetLoading && hasMore && (
                <div className="flex justify-center">
                  <Spin />
                </div>
              )}
            </Select>
          </div>
        }
        // description={knowledgeSetData?.description || ""}
        extra={
          <Link type="primary" onClick={handleBack}>
            返回知识集列表
          </Link>
        }
      />
      <div className="mb-6 overflow-hidden mt-6">
        <Button
          type="primary"
          className="float-right"
          icon={<IconPlus />}
          onClick={handleAdd}
        >
          添加版本
        </Button>
      </div>
      <Table
        columns={columns.concat({
          title: "操作",
          dataIndex: "operation",
          render: (_, item: KnowledgesetVersion) => (
            <div className="flex gap-2">
              {[
                KnowledgesetVersionStatus.KnowledgeVersionStatusCreated,
                KnowledgesetVersionStatus.KnowledgeVersionStatusDisable,
              ].includes(item.status) && (
                <Button
                  type="secondary"
                  size="mini"
                  icon={<IconEdit />}
                  onClick={() => handleEdit(item)}
                >
                  编辑
                </Button>
              )}
              {item.status ===
                KnowledgesetVersionStatus.KnowledgeVersionStatusOnline && (
                <Button
                  type="secondary"
                  size="mini"
                  onClick={() => handleViewDetails(item)}
                >
                  查看详情
                </Button>
              )}
              {[
                KnowledgesetVersionStatus.KnowledgeVersionStatusCreated,
                KnowledgesetVersionStatus.KnowledgeVersionStatusDisable,
              ].includes(item.status) && (
                <Popconfirm
                  title="确定要发布此版本吗？"
                  onOk={() => handlePublish(item)}
                >
                  <Button type="primary" size="mini">
                    发布
                  </Button>
                </Popconfirm>
              )}
              {item.status ===
                KnowledgesetVersionStatus.KnowledgeVersionStatusOnline && (
                <Popconfirm
                  title="确定要禁用此版本吗？"
                  onOk={() => handleDisable(item)}
                >
                  <Button type="primary" status="danger" size="mini">
                    禁用
                  </Button>
                </Popconfirm>
              )}

              <Button
                type="outline"
                size="mini"
                icon={<IconCopy />}
                onClick={() => handleCopy(item)}
              >
                复制
              </Button>
            </div>
          ),
          fixed: "right",
          width: 240,
        })}
        data={tableData}
        loading={isLoading}
        pagination={{
          total: +(data?.total || 0),
          current: +requestData.page_num || 1,
          pageSize: +requestData.page_size || 10,
          onChange: (pageNumber, pageSize) => {
            setRequestData({
              ...requestData,
              page_num: pageNumber,
              page_size: pageSize,
            });
          },
        }}
      />
      <KnowledgesetVersionModal
        visible={visible}
        data={targetItem}
        tags={metadataConf?.knowledge_tags?.[knowledgeSetData?.type as string]}
        knowledgeSetId={knowledgeSetId}
        isEdit={isEdit}
        isReadOnly={isReadOnly}
        showUseWhen={
          currentKnowledgeSet?.type === 'scenario'
        }
        onCancel={() => setVisible(false)}
        onOk={handleOk}
        title={
          isReadOnly
            ? "查看版本详情"
            : isEdit
            ? "修改知识集版本"
            : "添加知识集版本"
        }
      />
    </div>
  );
};
