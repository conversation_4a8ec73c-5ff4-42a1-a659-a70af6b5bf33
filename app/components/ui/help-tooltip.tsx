"use client";

import { useState } from "react";
import { HelpCircle } from "lucide-react";

interface HelpTooltipProps {
  content: string;
  className?: string;
}

export function HelpTooltip({ content, className = "" }: HelpTooltipProps) {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div className="relative inline-block">
      <HelpCircle 
        className={`h-4 w-4 text-gray-400 hover:text-gray-600 cursor-help ${className}`}
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
      />
      {isVisible && (
        <div className="absolute z-50 w-80 p-3 mt-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg left-1/2 transform -translate-x-1/2">
          <div className="whitespace-pre-wrap">{content}</div>
          {/* 小三角箭头 */}
          <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
        </div>
      )}
    </div>
  );
}
