"use client";

import {
  <PERSON><PERSON>,
  <PERSON>,
  FileText,
  BarChart2,
  ActivitySquare,
  Sparkles,
  User,
  List,
  Cpu,
  ChevronRight,
  ChartGantt,
  HandHelping,
  BookOpen,
  MessageSquare,
  Stethoscope,
  PieChart,
  FileSearch,
  BrainCircuit,
  Clock,
  NotebookPenIcon,
  BrainCog,
  Eye,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAuth } from "./AuthProvider";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useEffect, useState, memo, useCallback } from "react";
import { UserRole } from "../bam/aime/namespaces/session";
import { apiClient } from "../api/request";
// import { ThemeToggle } from "@/components/ui/theme-toggle";
import { XCloudSite, XCloudSiteTag } from "@tod-m/materials";
import { Envs } from "../api/const";

type NavItemType = {
  name: string;
  href: string;
  icon: React.ElementType;
  children?: NavItemType[];
  external?: boolean;
  requiredPermission?: string;
};

const navItems: NavItemType[] = [
  {
    name: "Agents",
    href: "/agents",
    icon: Bot,
    requiredPermission: UserRole.UserRoleAimoAgentDeveloper,
    children: [
      { name: "List", href: "/agents/list", icon: List },
      { name: "Env", href: "/agents/env", icon: Cpu },
      { name: "Version", href: "/agents/version", icon: FileText },
    ],
  },
  {
    name: "Playground",
    href: "/playground",
    icon: Play,
    children: [
      // {
      //   name: "Chat",
      //   href: "/playground/chat",
      //   icon: MessageSquare,
      //   requiredPermission: UserRole.UserRoleAimoTraceSelf,
      // },
      {
        name: "Chat",
        href: "/playground/chat2",
        icon: MessageSquare,
        requiredPermission: UserRole.UserRoleAimoTraceSelf,
      },
      {
        name: "MCP",
        href: "/playground/mcp",
        icon: Cpu,
        requiredPermission: UserRole.UserRoleAimoMCPPlayground,
      },
  ],
  },
  {
    name: "单步评测",
    href: "/job",
    icon: ChartGantt,
    children: [
      {
        name: "评测集",
        href: "/job/cases",
        icon: FileText,
        requiredPermission: UserRole.UserRoleAimoTraceSelf,
      },
      {
        name: "评测任务",
        href: "/job",
        icon: ChartGantt,
        requiredPermission: UserRole.UserRoleAimoTraceSelf,
      },
    ],
  },
  {
    name: "Trace",
    href: "/trace",
    icon: ActivitySquare,
    children: [
      {
        name: "Session",
        href: "/trace/session",
        icon: ActivitySquare,
        requiredPermission: UserRole.UserRoleAimoTraceSelf,
      },
      {
        name: "Events",
        href: "/trace/events",
        icon: List,
        requiredPermission: UserRole.UserRoleAimoTraceSelf,
      },
      {
        name: "LLM",
        href: "/trace/llm",
        icon: Cpu,
        requiredPermission: UserRole.UserRoleAimoTraceSelf,
      },
      {
        name: "LLM-Graph",
        href: "/trace/graph",
        icon: ChartGantt,
        requiredPermission: UserRole.UserRoleAimoTraceSelf,
      },
      {
        name: "Documents",
        href: "/trace/documents",
        icon: FileText,
        requiredPermission: UserRole.UserRoleAimoTraceSelf,
      },
      {
        name: "Diagnose",
        href: "/trace/diagnose",
        icon: Stethoscope,
        requiredPermission: UserRole.UserRoleAimoTraceDiagnose,
      },
    ],
  },
  {
    name: "MCP",
    href: "/mcp",
    icon: HandHelping,
    requiredPermission: UserRole.UserRoleAimoMCPDeveloper,
    children: [{ name: "List", href: "/mcp/list", icon: List }],
  },
  {
    name: "Experience",
    href: "/experience",
    icon: BookOpen,
    requiredPermission: UserRole.UserRoleAimoAgentDeveloper,
    children: [
      {
        href: "/experience/statistics",
        icon: PieChart,
        name: "数据统计",
      },
      {
        href: "/experience/list",
        icon: Sparkles,
        name: "经验库列表",
      },
      {
        href: "/experience/wait",
        icon: Clock,
        name: "待入库经验",
      },
      {
        href: "/experience/extract",
        icon: BrainCircuit,
        name: "智能提取",
      },
      {
        href: "/experience/evaluation",
        icon: BrainCog,
        name: "经验效果评价",
      },
      {
        href: "/experience/job",
        icon: ChartGantt,
        name: "经验 CI 任务",
      },
      {
        href: "/experience/visual",
        icon: Eye,
        name: "可视化分析",
      },
    ],
  },
  {
    name: "Knowledge",
    href: "/knowledge",
    icon: NotebookPenIcon,
    requiredPermission: UserRole.UserRoleAimoAgentDeveloper,
    children: [
      { name: "列表", href: "/knowledge/list", icon: List },
      { name: "评估", href: "/knowledge/evaluation", icon: BarChart2 },
      { name: "分析", href: "/knowledge/analysis", icon: FileSearch },
      { name: "召回测试", href: "/knowledge/query", icon: BrainCircuit }
    ],
  },
  {
    name: "Evaluation",
    href: "/evaluation",
    icon: BarChart2,
  },
  {
    name: "Templates",
    href: "/templates",
    icon: FileText,
    requiredPermission: UserRole.UserRoleAimoAgentDeveloper,
  },
   {
    name: "Prompt",
    href: "/prompt",
    icon: FileText,
    requiredPermission: UserRole.UserRoleAimoAgentDeveloper,
    children: [
      { name: "List", href: "/prompt/list", icon: List },
      { name: "Version", href: "/prompt/version", icon: FileText },
    ],
  },
  // { name: "Knowledge", href: "https://tosv.byted.org/obj/ttclient-android/agent/index.html", icon: BookOpen, external: true },
];

// 可复用的子菜单组件
const SubMenu = memo(
  ({ children, isOpen }: { children: React.ReactNode; isOpen: boolean }) => {
    return (
      <div
        className={cn(
          "grid transition-all duration-300 ease-in-out",
          isOpen ? "grid-rows-[1fr] opacity-100" : "grid-rows-[0fr] opacity-0"
        )}
      >
        <div className="overflow-hidden">
          <div
            className={cn(
              "border-l border-border/50 ml-5 pl-3 py-1 mt-1 mb-1 space-y-1 transition-transform duration-300",
              isOpen ? "translate-x-0" : "-translate-x-2"
            )}
          >
            {children}
          </div>
        </div>
      </div>
    );
  }
);

SubMenu.displayName = "SubMenu";

// 单独的导航项按钮组件
const NavButton = memo(
  ({
    href,
    icon: Icon,
    name,
    isActive,
    isChild = false,
  }: {
    href: string;
    icon: React.ElementType;
    name: string;
    isActive: boolean;
    isChild?: boolean;
  }) => {
    return (
      <Button
        asChild
        variant={isActive ? "secondary" : "ghost"}
        className={cn(
          "w-full justify-start transition-colors",
          isChild ? "pl-7 h-8 text-sm" : "h-10",
          isActive
            ? "bg-[#E0F2F1] font-medium text-[#00796B] hover:bg-[#E0F2F1] hover:text-[#00796B]"
            : "text-muted-foreground hover:text-foreground"
        )}
      >
        <Link href={href}>
          <div
            className={cn(
              "flex items-center justify-center",
              isChild ? "w-4 h-4" : "w-5 h-5"
            )}
          >
            <Icon size={isChild ? 16 : 18} />
          </div>
          <span>{name}</span>
        </Link>
      </Button>
    );
  }
);

NavButton.displayName = "NavButton";

// 外部链接按钮组件
const ExternalNavButton = memo(
  ({
    href,
    icon: Icon,
    name,
  }: {
    href: string;
    icon: React.ElementType;
    name: string;
  }) => {
    return (
      <Button
        asChild
        variant="ghost"
        className="w-full justify-start h-10 text-muted-foreground hover:text-foreground transition-colors"
      >
        <a href={href} target="_blank" rel="noopener noreferrer">
          <div className="flex items-center justify-center w-5 h-5">
            <Icon size={18} />
          </div>
          <span>{name}</span>
        </a>
      </Button>
    );
  }
);

ExternalNavButton.displayName = "ExternalNavButton";

// 父菜单按钮组件
const ParentNavButton = memo(
  ({
    icon: Icon,
    name,
    isOpen,
    isActive,
    onClick,
  }: {
    icon: React.ElementType;
    name: string;
    isOpen: boolean;
    isActive: boolean;
    onClick: () => void;
  }) => {
    return (
      <Button
        variant={isActive ? "secondary" : "ghost"}
        className={cn(
          "w-full justify-between h-10 group transition-all",
          isActive
            ? "bg-[#E0F2F1] font-medium text-[#00796B] hover:bg-[#E0F2F1] hover:text-[#00796B]"
            : "text-muted-foreground hover:text-foreground"
        )}
        onClick={onClick}
      >
        <div className="flex items-center">
          <div className="flex items-center justify-center w-5 h-5 mr-2">
            <Icon
              size={18}
              className={cn(
                "transition-colors duration-300",
                isOpen && !isActive && "text-foreground"
              )}
            />
          </div>
          <span
            className={cn(
              "transition-colors duration-300",
              isOpen && !isActive && "text-foreground"
            )}
          >
            {name}
          </span>
        </div>
        <div
          className={cn(
            "h-5 w-5 flex items-center justify-center rounded-full transition-all duration-300",
            isOpen && "bg-accent/40"
          )}
        >
          <ChevronRight
            size={14}
            className={cn(
              "transform transition-transform duration-300",
              isOpen && "rotate-90"
            )}
          />
        </div>
      </Button>
    );
  }
);

ParentNavButton.displayName = "ParentNavButton";

export function Sidebar() {
  const pathname = usePathname();
  const { isAuthenticated, user } = useAuth();
  const [openMenus, setOpenMenus] = useState<Record<string, boolean>>({});
  const [userPermissions, setUserPermissions] = useState<
    Record<string, boolean>
  >({});
  const [permissionsLoaded, setPermissionsLoaded] = useState(false);
  const [region, setRegion] = useState<string>("cn");

  // 根据当前域名设置默认区域
  useEffect(() => {
    if (typeof window !== "undefined") {
      const hostname = window.location.hostname;
      if (hostname.includes("tiktok-row.net")) {
        setRegion("i18n");
      } else {
        setRegion("online");
      }
    }
  }, []);

  // 处理区域切换
  const handleRegionChange = useCallback((value: string) => {
    setRegion(value);
    if (typeof window !== "undefined") {
      const currentUrl = window.location.href;
      let newUrl;
      if (value === "online") {
        newUrl = currentUrl.replace(/\/\/[^\/]+/, "//aime.bytedance.net");
      } else {
        newUrl = currentUrl.replace(/\/\/[^\/]+/, "//aime.tiktok-row.net");
      }

      window.location.href = newUrl;
    }
  }, []);

  // 加载用户权限
  useEffect(() => {
    const loadUserPermissions = async () => {
      if (isAuthenticated) {
        try {
          const roles = await apiClient.GetUserRoles();

          setUserPermissions(
            roles.roles.reduce((acc, role) => {
              acc[role] = true;
              return acc;
            }, {} as Record<string, boolean>),
          );
        } catch (error) {
          console.error("加载用户权限失败:", error);
          setUserPermissions({});
        }
        setPermissionsLoaded(true);
      }
    };

    loadUserPermissions();
  }, [isAuthenticated]);

  // 初始化时，根据当前路径打开相应的菜单
  useEffect(() => {
    setOpenMenus((prev) => {
      const newOpenMenus = { ...prev };
      navItems.forEach((item) => {
        if (item.children && pathname.startsWith(item.href)) {
          newOpenMenus[item.href] = true;
        }
      });
      return newOpenMenus;
    });
  }, [pathname]);

  const toggleMenu = (href: string) => {
    setOpenMenus((prev) => ({
      ...prev,
      [href]: !prev[href],
    }));
  };

  // 检查用户是否有权限访问某个菜单项
  const hasPermissionForItem = (item: NavItemType): boolean => {
    if (!item.requiredPermission) return true; // 不需要权限的项目
    return userPermissions[item.requiredPermission] || false;
  };

  // 导航项渲染函数
  const renderNavItem = (item: NavItemType, isChild = false) => {
    // 权限检查
    if (!hasPermissionForItem(item)) {
      return null;
    }

    const isActive = pathname === item.href;
    const hasChildren = item.children && item.children.length > 0;
    const isOpen = !!openMenus[item.href];
    const isParentActive = !!(hasChildren && pathname.startsWith(item.href));

    // 过滤有权限的子菜单项
    const visibleChildren = hasChildren
      ? item.children?.filter((child) => hasPermissionForItem(child))
      : undefined;

    // 如果父菜单项有子菜单，但所有子菜单都没有权限，则隐藏整个父菜单
    if (hasChildren && (!visibleChildren || visibleChildren.length === 0)) {
      return null;
    }

    // 如果没有子菜单，直接渲染单个按钮
    if (!hasChildren || !visibleChildren) {
      // 如果是外部链接，使用ExternalNavButton
      if (item.external) {
        return (
          <ExternalNavButton
            key={item.href}
            href={item.href}
            icon={item.icon}
            name={item.name}
          />
        );
      }

      return (
        <NavButton
          key={item.href}
          href={item.href}
          icon={item.icon}
          name={item.name}
          isActive={isActive}
          isChild={isChild}
        />
      );
    }

    // 如果有子菜单，渲染父菜单和子菜单
    return (
      <div key={item.href} className="w-full">
        <ParentNavButton
          icon={item.icon}
          name={item.name}
          isOpen={isOpen}
          isActive={isParentActive}
          onClick={() => toggleMenu(item.href)}
        />

        <SubMenu isOpen={isOpen}>
          {visibleChildren.map((child) => renderNavItem(child, true))}
        </SubMenu>
      </div>
    );
  };

  // 如果权限还没加载完成，显示加载状态
  if (!permissionsLoaded) {
    return (
      <div className="fixed top-0 left-0 w-[250px] h-screen z-10 border-r bg-background/90 backdrop-blur-sm shadow-sm flex flex-col p-0">
        <div className="flex items-center justify-between gap-2 p-4 border-b h-14">
          <div className="flex items-center gap-2">
            <div className="flex items-center justify-center w-8 h-8 rounded-md bg-[#E0F2F1] dark:bg-[#004D40]/30">
              <Sparkles
                size={18}
                className="text-[#00796B] dark:text-[#4DB6AC]"
                strokeWidth={2.5}
              />
            </div>
            <h2 className="text-lg font-semibold text-[#00796B] dark:text-[#4DB6AC]">
              AIME Operator
            </h2>
          </div>
        </div>
        <div className="flex items-center justify-center flex-1">
          <div className="text-sm text-muted-foreground">加载中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed top-0 left-0 w-[250px] h-screen z-10 border-r bg-background/90 backdrop-blur-sm shadow-sm flex flex-col p-0">
      <div className="flex items-center justify-between gap-2 p-4 border-b h-14">
        <div className="flex items-center gap-2">
          <div className="flex items-center justify-center w-8 h-8 rounded-md bg-[#E0F2F1] dark:bg-[#004D40]/30">
            <Sparkles
              size={18}
              className="text-[#00796B] dark:text-[#4DB6AC]"
              strokeWidth={2.5}
            />
          </div>
          <h2 className="text-lg font-semibold text-[#00796B] dark:text-[#4DB6AC]">
            AIME Operator
          </h2>
        </div>
        <div className="flex items-center content-center">
          <XCloudSite
            initValue="online"
            filterSite={(site) => {
             return [Envs.I18N, Envs.ONLINE].includes(site as any)
            }}
            value={region as any}
            onChange={handleRegionChange as any}
            // eslint-disable-next-line react/no-children-prop
            children={(site) => (
              <div className="site-tag-select flex items-center content-center">
                <XCloudSiteTag site={site as any} show={{flag: true, tag: true} as any}/>
              </div>
            )}
          ></XCloudSite>
        </div>
      </div>

      <ScrollArea className="flex-1 px-3 py-4 overflow-y-auto">
        <div className="flex flex-col space-y-1 pb-4">
          {navItems.map((item) => renderNavItem(item))}
        </div>
      </ScrollArea>

      {isAuthenticated && (
        <div className="border-t p-4 flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user?.avatarUrl} />
            <AvatarFallback className="bg-primary/10 text-primary">
              <User size={16} />
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <span className="text-sm font-medium">
              {user?.username || "用户"}
            </span>
            <span className="text-xs text-muted-foreground">已登录</span>
          </div>
        </div>
      )}
    </div>
  );
}
