"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { hasSpecificPermission } from "@/app/api/auth";
import { useAuth } from "./AuthProvider";
import { UserRole } from "../bam/aime/namespaces/session";

interface PermissionGuardProps {
  children: React.ReactNode;
  requiredPermission: UserRole;
  fallbackPath?: string;
}

export function PermissionGuard({
  children,
  requiredPermission,
  fallbackPath = "/no-permission",
}: PermissionGuardProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [hasPermission, setHasPermission] = useState(false);
  const [permissionLoading, setPermissionLoading] = useState(true);

  useEffect(() => {
    const checkPermission = async () => {
      if (!isAuthenticated || isLoading) {
        setPermissionLoading(false);
        return;
      }

      try {
        const permission = await hasSpecificPermission(requiredPermission);
        setHasPermission(permission);

        if (!permission) {
          router.push(fallbackPath);
        }
      } catch (error) {
        console.error("权限检查失败:", error);
        setHasPermission(false);
        router.push(fallbackPath);
      } finally {
        setPermissionLoading(false);
      }
    };

    checkPermission();
  }, [isAuthenticated, isLoading, requiredPermission, router, fallbackPath]);

  if (isLoading || permissionLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-lg font-medium text-gray-700 mb-2">
            检查权限中...
          </div>
          <div className="text-sm text-gray-500">请稍候</div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // AuthProvider 会处理未认证的情况
  }

  if (!hasPermission) {
    return null; // 会重定向到 fallbackPath
  }

  return <>{children}</>;
}
