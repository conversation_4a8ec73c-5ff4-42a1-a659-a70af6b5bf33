import { Flex, Heading, Text } from "@radix-ui/themes";
import { ReactNode } from "react";

interface PageHeaderProps {
  title: string | ReactNode;
  description?: string;
  extra?: ReactNode;
}

export function PageHeader({ title, description, extra }: PageHeaderProps) {
  return (
    <Flex direction="column" gap="2" mb="2">
      <Flex justify="between" align="center">
        <Heading size="7" weight="bold">
          {title}
        </Heading>
        {extra && (
          <div>
            {extra}
          </div>
        )}
      </Flex>
      {description && (
        <Text size="2" color="gray">
          {description}
        </Text>
      )}
    </Flex>
  );
}
