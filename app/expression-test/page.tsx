"use client";

import React, { useState } from "react";
import { 
  Card, 
  Button, 
  Input, 
  Form, 
  Grid, 
  Select, 
  Space,
  Typography,
  Message
} from "@arco-design/web-react";
import { evaluateExpression } from "@/lib/expression-evaluator";
import { agentOptions, variantOptions, toolsOptions } from "@/lib/option-config";

const { Text } = Typography;

// 示例表达式
const exampleExpressions = [
  "agent in ['mewtwo', 'planner']",
  "agent in ['dynamic_planner'] and variant != 'gpt'",
  "agent in ['mewtwo'] and 'browser' in tools",
  "(agent == 'dynamic_planner' and variant != 'gpt') or (agent == 'mewtwo' and 'cosy' in tools)",
  "variant in ['newbie']",
  "false",
  "true"
];

export default function ExpressionTestPage() {
  const [testExpression, setTestExpression] = useState('');
  const [testAgent, setTestAgent] = useState('');
  const [testVariant, setTestVariant] = useState('');
  const [testTools, setTestTools] = useState<string[]>([]);
  const [testResult, setTestResult] = useState<{ result: boolean; error?: string } | null>(null);
  const [isTesting, setIsTesting] = useState(false);

  // 测试表达式
  const handleTestExpression = async () => {
    if (!testExpression.trim()) {
      Message.warning('请输入要测试的表达式');
      return;
    }

    setIsTesting(true);
    
    try {
      const context: any = {};
      if (testAgent) context.agent = testAgent;
      if (testVariant) context.variant = testVariant;
      if (testTools.length > 0) context.tools = testTools;

      // 添加延迟以显示加载状态
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const result = evaluateExpression(testExpression, context);
      setTestResult({ result });
    } catch (error) {
      setTestResult({ 
        result: false, 
        error: error instanceof Error ? error.message : '未知错误' 
      });
    } finally {
      setIsTesting(false);
    }
  };

  // 使用示例表达式
  const useExampleExpression = (expression: string) => {
    setTestExpression(expression);
  };

  return (
    <div className="h-full flex flex-col p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">表达式测试工具</h1>
        <p className="text-gray-600">
          此工具用于测试 enableIf 条件表达式的求值结果。您可以输入表达式并设置不同的上下文参数来测试表达式是否匹配。
        </p>
      </div>

      <Card className="mb-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold mb-2">示例表达式</h2>
          <div className="flex flex-wrap gap-2">
            {exampleExpressions.map((expr, index) => (
              <Button
                key={index}
                size="small"
                onClick={() => useExampleExpression(expr)}
              >
                {expr}
              </Button>
            ))}
          </div>
        </div>

        <div className="mb-4">
          <h2 className="text-lg font-semibold mb-2">表达式</h2>
          <Input.TextArea
            value={testExpression}
            onChange={(value) => setTestExpression(value)}
            placeholder="请输入要测试的表达式，例如：agent in ['mewtwo'] and variant != 'gpt'"
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </div>

        <Grid.Row gutter={16} className="mb-4">
          <Grid.Col span={8}>
            <h3 className="text-md font-medium mb-2">Agent</h3>
            <Select 
              placeholder="请选择Agent" 
              allowClear
              value={testAgent}
              onChange={(value) => setTestAgent(value || '')}
              style={{ width: '100%' }}
            >
              {agentOptions.map(option => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </Grid.Col>
          <Grid.Col span={8}>
            <h3 className="text-md font-medium mb-2">Variant</h3>
            <Select 
              placeholder="请选择Variant" 
              allowClear
              value={testVariant}
              onChange={(value) => setTestVariant(value || '')}
              style={{ width: '100%' }}
            >
              {variantOptions.map(option => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </Grid.Col>
          <Grid.Col span={8}>
            <h3 className="text-md font-medium mb-2">Tools</h3>
            <Select 
              placeholder="请选择Tools" 
              allowClear
              mode="multiple"
              value={testTools}
              onChange={(value) => setTestTools(value || [])}
              style={{ width: '100%' }}
            >
              {toolsOptions.map(option => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </Grid.Col>
        </Grid.Row>

        <div className="flex justify-end">
          <Button 
            type="primary" 
            onClick={handleTestExpression}
            loading={isTesting}
          >
            测试表达式
          </Button>
        </div>
      </Card>

      {testResult && (
        <Card>
          <h2 className="text-lg font-semibold mb-2">测试结果</h2>
          <div className={`p-4 rounded-md ${testResult.result ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {testResult.error ? (
              <div>
                <p className="font-medium">错误:</p>
                <p>{testResult.error}</p>
              </div>
            ) : (
              <div>
                <p className="font-medium">表达式求值结果:</p>
                <p className="text-xl font-bold">{testResult.result ? 'true' : 'false'}</p>
              </div>
            )}
          </div>
          
          <div className="mt-4 p-4 bg-gray-100 rounded-md">
            <p className="font-medium mb-2">测试上下文:</p>
            <pre className="text-sm">
              {JSON.stringify({
                agent: testAgent || '(未设置)',
                variant: testVariant || '(未设置)',
                tools: testTools.length > 0 ? testTools : '(未设置)'
              }, null, 2)}
            </pre>
          </div>
        </Card>
      )}

      <Card className="mt-6">
        <h2 className="text-lg font-semibold mb-2">表达式语法说明</h2>
        <div className="space-y-2">
          <p><code>agent in ['value1', 'value2']</code> - 检查 agent 是否在指定列表中</p>
          <p><code>variant == 'value'</code> - 检查 variant 是否等于指定值</p>
          <p><code>variant != 'value'</code> - 检查 variant 是否不等于指定值</p>
          <p><code>'tool' in tools</code> - 检查 tools 列表中是否包含指定工具</p>
          <p><code>condition1 and condition2</code> - 逻辑与操作</p>
          <p><code>condition1 or condition2</code> - 逻辑或操作</p>
          <p><code>(condition1) or (condition2)</code> - 使用括号分组</p>
          <p><code>true</code> - 始终为真</p>
          <p><code>false</code> - 始终为假</p>
        </div>
      </Card>
    </div>
  );
}