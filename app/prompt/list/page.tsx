"use client";

import { apiClient } from "@/app/api/request";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Edit, Trash2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { PaginationInfo } from "@/components/common/PaginationInfo";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useAgents } from "@/app/hooks/useAgents";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// 常量定义
const pageSize = 10;

// 表单结构定义
const formSchema = z.object({
  name: z.string().min(1, "名称不能为空"),
  description: z.string().optional(),
  agent_id: z.string().optional(),
});

// Prompt类型定义
interface Prompt {
  id: string;
  name: string;
  description: string;
  agent_id?: string;
  creator: string;
  created_at: string;
  updated_at: string;
}

// 日期时间格式化工具函数
function formatDateTime(dateString: string) {
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    }).format(date);
  } catch {
    return dateString;
  }
}

// Prompt表单组件
function PromptFormDialog({
  open,
  onOpenChange,
  editingPrompt,
  onSubmit,
  isSubmitting,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editingPrompt: Prompt | null;
  onSubmit: (values: z.infer<typeof formSchema>) => void;
  isSubmitting: boolean;
}) {
  const { data: agents = [] } = useAgents();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: editingPrompt?.name || "",
      description: editingPrompt?.description || "",
      agent_id: editingPrompt?.agent_id || "",
    },
  });

  useEffect(() => {
    if (editingPrompt) {
      form.reset({
        name: editingPrompt.name,
        description: editingPrompt.description,
        agent_id: editingPrompt.agent_id,
      });
    } else {
      form.reset({
        name: "",
        description: "",
        agent_id: "",
      });
    }
  }, [form, editingPrompt, open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {editingPrompt ? "编辑 Prompt" : "创建 Prompt"}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>名称</FormLabel>
                  <FormControl>
                    <Input {...field} disabled={isSubmitting} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>描述</FormLabel>
                  <FormControl>
                    <Textarea {...field} disabled={isSubmitting} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="agent_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>关联 Agent</FormLabel>
                  <FormControl>
                    <Select
                      value={field.value}
                      onValueChange={value => {
                        field.onChange(value);
                      }}
                      disabled={isSubmitting}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择 Agent" />
                      </SelectTrigger>
                      <SelectContent>
                        {agents.length > 0 ? (
                          agents.map(agent => (
                            <SelectItem key={agent.id} value={agent.id}>
                              {agent.name}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-center text-gray-500">
                            暂无 Agent
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  {editingPrompt ? "保存中..." : "创建中..."}
                </div>
              ) : editingPrompt ? (
                "保存"
              ) : (
                "创建"
              )}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

// 主页面组件
export default function PromptsListPage() {
  const router = useRouter();
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<Prompt | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedAgentId, setSelectedAgentId] = useState<string>("all");
  const { data: agents = [] } = useAgents();

  // 获取Prompt列表数据
  const fetchPrompts = async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.ListPrompt({
        page_num: currentPage,
        page_size: pageSize,
        agent_id: selectedAgentId === "all" ? undefined : selectedAgentId,
      });
      setPrompts(response.prompts);
      setTotalCount(Number(response.total));
      setTotalPages(Math.max(1, Math.ceil(Number(response.total) / pageSize)));
    } catch {
      toast.error("获取 Prompt 列表失败");
    } finally {
      setIsLoading(false);
    }
  };

  // 页面首次加载和页码变化时获取数据
  useEffect(() => {
    fetchPrompts();
  }, [currentPage, selectedAgentId]);

  // 处理 agent 筛选变化
  const handleAgentFilterChange = (value: string) => {
    setSelectedAgentId(value);
    setCurrentPage(1); // 重置页码
  };

  // 页码变化处理
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 创建Prompt
  const handleCreate = async (values: z.infer<typeof formSchema>) => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    try {
      await apiClient.CreatePrompt({
        name: values.name,
        description: values.description || "",
        agent_id: values.agent_id,
      });
      toast.success("创建成功");
      setIsDialogOpen(false);
      await fetchPrompts();
    } catch {
      toast.error("创建失败");
    } finally {
      setIsSubmitting(false);
    }
  };

  // 编辑Prompt
  const handleEdit = async (values: z.infer<typeof formSchema>) => {
    if (!editingPrompt || isSubmitting) return;
    setIsSubmitting(true);
    try {
      await apiClient.UpdatePrompt({
        prompt_id: editingPrompt.id,
        name: values.name,
        description: values.description || "",
      });
      toast.success("更新成功");
      setIsDialogOpen(false);
      await fetchPrompts();
    } catch {
      toast.error("更新失败");
    } finally {
      setIsSubmitting(false);
    }
  };

  // 删除Prompt
  const handleDelete = async () => {
    try {
      // TODO: 添加删除Prompt的API调用
      toast.success("删除成功");
      await fetchPrompts();
    } catch {
      toast.error("删除失败");
    }
  };

  // 表单提交处理
  const handleFormSubmit = (values: z.infer<typeof formSchema>) => {
    if (editingPrompt) {
      handleEdit(values);
    } else {
      handleCreate(values);
    }
  };

  // 编辑按钮点击处理
  const handleEditClick = (prompt: Prompt) => {
    setEditingPrompt(prompt);
    setIsDialogOpen(true);
  };

  // 创建按钮点击处理
  const handleCreateClick = () => {
    setEditingPrompt(null);
    setIsDialogOpen(true);
  };

  // 对话框开关状态变化处理
  const handleDialogOpenChange = (open: boolean) => {
    if (!open) {
      setIsDialogOpen(false);
      setEditingPrompt(null);
    } else {
      setIsDialogOpen(true);
    }
  };

  const handleRowClick = (promptId: string) => {
    router.push(`/prompt/version?prompt_id=${promptId}`);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Prompt 列表</h1>
        <div className="flex items-center gap-4">
          <Select
            value={selectedAgentId}
            onValueChange={handleAgentFilterChange}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="筛选 Agent" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部</SelectItem>
              {agents.map(agent => (
                <SelectItem key={agent.id} value={agent.id}>
                  {agent.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button onClick={handleCreateClick}>创建 Prompt</Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
        </div>
      ) : prompts.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <p className="text-gray-500">
            暂无数据，请点击右上角&ldquo;创建 Prompt&rdquo;按钮添加
          </p>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>名称</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>关联 Agent</TableHead>
                <TableHead>创建者</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>更新时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {prompts.map(prompt => (
                <TableRow
                  key={prompt.id}
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => handleRowClick(prompt.id)}
                >
                  <TableCell className="font-medium">{prompt.name}</TableCell>
                  <TableCell className="max-w-md truncate">
                    {prompt.description || "暂无描述"}
                  </TableCell>
                  <TableCell>
                    {prompt.agent_id ? (
                      <Badge
                        variant="outline"
                        className="bg-gray-50 font-normal"
                      >
                        {agents.find(agent => agent.id === prompt.agent_id)
                          ?.name || prompt.agent_id}
                      </Badge>
                    ) : (
                      <span className="text-gray-400">未关联</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="bg-gray-50 font-normal">
                      {prompt.creator}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDateTime(prompt.created_at)}</TableCell>
                  <TableCell>{formatDateTime(prompt.updated_at)}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={e => {
                          e.stopPropagation();
                          handleEditClick(prompt);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-red-500"
                        onClick={e => {
                          e.stopPropagation();
                          handleDelete();
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {prompts.length > 0 && (
        <PaginationInfo
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          totalCount={totalCount}
          onPageChange={handlePageChange}
        />
      )}

      <PromptFormDialog
        open={isDialogOpen}
        onOpenChange={handleDialogOpenChange}
        editingPrompt={editingPrompt}
        onSubmit={handleFormSubmit}
        isSubmitting={isSubmitting}
      />
    </div>
  );
}
