"use client";

import { useState, useEffect, useRef, type ReactNode } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { apiClient } from "@/app/api/request";
import {
  VariableMetadata,
  VariableType,
} from "@/app/bam/aime/namespaces/prompt";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON><PERSON>, keymap } from "@codemirror/view";
import { EditorState } from "@codemirror/state";
import { basicSetup } from "codemirror";
import {
  indentUnit,
  indentOnInput,
  bracketMatching,
  syntaxHighlighting,
  defaultHighlightStyle,
} from "@codemirror/language";
import {
  closeBrackets,
  autocompletion,
  CompletionContext,
} from "@codemirror/autocomplete";
import { defaultKeymap, indentWithTab } from "@codemirror/commands";
import { markdown } from "@codemirror/lang-markdown";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { usePromptVersion } from "../hooks/usePromptVersion";

const formSchema = z.object({
  description: z.string().optional(),
  content: z.string().min(1, "内容不能为空"),
  enabled: z.boolean(),
  variables: z.array(
    z.object({
      key: z.string().min(1, "变量名不能为空"),
      type: z.nativeEnum(VariableType),
      description: z.string().optional(),
      defaultValue: z.string().optional(),
      required: z.boolean(),
    }),
  ),
});

type FormValues = z.infer<typeof formSchema>;

interface VersionFormProps {
  promptId: string;
  versionId?: string;
  onSuccess?: () => void;
}

export function VersionForm({
  promptId,
  versionId,
  onSuccess,
}: VersionFormProps) {
  const { version, isLoading: isLoadingVersion } = usePromptVersion(
    versionId || null,
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("edit");
  const [previewContent, setPreviewContent] = useState("");
  const [variableHighlights, setVariableHighlights] = useState<
    Array<{
      variable: string;
      value: string;
      start: number;
      end: number;
    }>
  >([]);
  const editorRef = useRef<HTMLDivElement>(null);
  const viewRef = useRef<EditorView | null>(null);
  const editorContentRef = useRef<string>("");

  // 当版本数据加载完成后，更新表单默认值
  useEffect(() => {
    if (version && version.content) {
      editorContentRef.current = atob(version.content);
      form.reset({
        description: version.description || "",
        content: editorContentRef.current,
        enabled: version.enabled,
        variables: version.variable
          ? Object.entries(version.variable).map(([key, metadata]) => ({
              key,
              type: metadata.type,
              description: "",
              defaultValue: "",
              required: false,
            }))
          : [],
      });

      // 如果编辑器已初始化，更新其内容
      if (viewRef.current) {
        const transaction = viewRef.current.state.update({
          changes: {
            from: 0,
            to: viewRef.current.state.doc.length,
            insert: editorContentRef.current,
          },
        });
        viewRef.current.dispatch(transaction);
      } else {
        // 如果编辑器未初始化且当前是编辑模式，则初始化编辑器
        if (activeTab === "edit") {
          initializeEditor();
        }
      }
    }
  }, [version, activeTab]);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      description: "",
      content: "",
      enabled: true,
      variables: [],
    },
  });

  const getVariableCompletions = (context: CompletionContext) => {
    const variables = form.getValues("variables");
    const word = context.matchBefore(/{{[^}]*$/);
    if (!word) return null;

    return {
      from: word.from,
      options: variables.map(v => ({
        label: `{{${v.key}}}`,
        detail: `${v.description || ""}`,
        type: v.type,
      })),
    };
  };

  const initializeEditor = () => {
    if (!editorRef.current) return;

    // 清理之前的编辑器实例
    if (viewRef.current) {
      viewRef.current.destroy();
      viewRef.current = null;
    }

    const state = EditorState.create({
      doc: editorContentRef.current,
      extensions: [
        basicSetup,
        markdown(),
        indentUnit.of("  "),
        indentOnInput(),
        bracketMatching(),
        closeBrackets(),
        syntaxHighlighting(defaultHighlightStyle),
        autocompletion({
          override: [getVariableCompletions],
        }),
        keymap.of([...defaultKeymap, indentWithTab]),
        EditorView.updateListener.of(update => {
          if (update.docChanged) {
            const content = update.state.doc.toString();
            editorContentRef.current = content;
            form.setValue("content", content);
          }
        }),
        EditorView.theme({
          "&": {
            backgroundColor: "white",
            color: "#333",
            height: "400px",
            overflow: "auto",
          },
          ".cm-scroller": {
            overflow: "auto",
            height: "100%",
          },
          ".cm-content": {
            fontFamily: "monospace",
            fontSize: "14px",
            lineHeight: "1.5",
            padding: "8px 0",
          },
          ".cm-gutters": {
            backgroundColor: "#f5f5f5",
            borderRight: "1px solid #ddd",
            minHeight: "100%",
          },
          ".cm-activeLineGutter": {
            backgroundColor: "#e8e8e8",
          },
          ".cm-activeLine": {
            backgroundColor: "#f5f5f5",
          },
          ".cm-selectionBackground": {
            backgroundColor: "#d4d4d4",
          },
          ".cm-matchingBracket": {
            backgroundColor: "#e8e8e8",
            outline: "1px solid #999",
          },
          ".cm-completionIcon": {
            display: "none",
          },
          ".cm-completionDetail": {
            marginLeft: "0.5em",
            fontStyle: "italic",
          },
          ".cm-tooltip.cm-tooltip-autocomplete": {
            border: "1px solid #ddd",
            background: "white",
            fontSize: "14px",
            "& > ul": {
              fontFamily: "monospace",
              maxHeight: "200px",
            },
            "& > ul > li": {
              padding: "2px 1em 2px 3px",
            },
            "& > ul > li[aria-selected]": {
              background: "#e8e8e8",
              color: "inherit",
            },
          },
        }),
      ],
    });

    const view = new EditorView({
      state,
      parent: editorRef.current,
    });

    viewRef.current = view;

    return () => {
      view.destroy();
      viewRef.current = null;
    };
  };

  // 移除初始化时机的useEffect，改为在适当的时机调用initializeEditor
  useEffect(() => {
    // 非编辑模式，不初始化编辑器
    if (activeTab !== "edit") return;

    // 在数据加载完成后初始化编辑器
    if (!isLoadingVersion && !viewRef.current && editorRef.current) {
      // 短暂延时确保DOM已完全渲染
      const timer = setTimeout(() => {
        initializeEditor();
      }, 50);

      return () => clearTimeout(timer);
    }

    return () => {
      if (viewRef.current) {
        viewRef.current.destroy();
        viewRef.current = null;
      }
    };
  }, [activeTab, isLoadingVersion]);

  const handlePreview = () => {
    const content = form.getValues("content");
    const variables = form.getValues("variables");
    let previewText = content;
    const highlights: Array<{
      variable: string;
      value: string;
      start: number;
      end: number;
    }> = [];

    variables.forEach(variable => {
      const placeholder = `{{${variable.key}}}`;
      const value = variable.defaultValue || `[${variable.key}]`;
      let startIndex = 0;
      while (true) {
        const index = previewText.indexOf(placeholder, startIndex);
        if (index === -1) break;
        highlights.push({
          variable: variable.key,
          value,
          start: index,
          end: index + placeholder.length,
        });
        startIndex = index + placeholder.length;
      }
      previewText = previewText.replace(new RegExp(placeholder, "g"), value);
    });

    setPreviewContent(previewText);
    setVariableHighlights(highlights);
  };

  const renderPreviewContent = () => {
    if (!previewContent) return null;

    const sortedHighlights = [...variableHighlights].sort(
      (a, b) => a.start - b.start,
    );
    const parts: ReactNode[] = [];
    let lastIndex = 0;

    sortedHighlights.forEach((highlight, index) => {
      // 添加高亮前的文本
      if (highlight.start > lastIndex) {
        parts.push(
          <span key={`text-${index}`}>
            {previewContent.slice(lastIndex, highlight.start)}
          </span>,
        );
      }

      // 添加高亮的变量
      parts.push(
        <span
          key={`var-${index}`}
          className="bg-blue-100 text-blue-800 rounded px-1 mx-0.5 cursor-help"
          title={`变量: ${highlight.variable}`}
        >
          {highlight.value}
        </span>,
      );

      lastIndex = highlight.end;
    });

    // 添加最后一段文本
    if (lastIndex < previewContent.length) {
      parts.push(
        <span key="text-last">{previewContent.slice(lastIndex)}</span>,
      );
    }

    return parts;
  };

  const addVariable = () => {
    const currentVariables = form.getValues("variables");
    form.setValue("variables", [
      ...currentVariables,
      {
        key: "",
        type: VariableType.VariableTypeString,
        description: "",
        defaultValue: "",
        required: false,
      },
    ]);
  };

  const removeVariable = (index: number) => {
    const currentVariables = form.getValues("variables");
    currentVariables.splice(index, 1);
    form.setValue("variables", [...currentVariables]);
  };

  const onSubmit = async (values: FormValues) => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    try {
      const variableMap: Record<string, VariableMetadata> = {};
      values.variables.forEach(v => {
        variableMap[v.key] = {
          type: v.type,
        };
      });

      if (versionId && version) {
        await apiClient.UpdatePromptVersion({
          prompt_version_id: versionId,
          description: values.description || "",
          enabled: values.enabled,
          content: btoa(values.content),
          variable: variableMap,
        });
        toast.success("更新成功");
      } else {
        await apiClient.CreatePromptVersion({
          prompt_id: promptId,
          description: values.description || "",
          enabled: values.enabled,
          content: btoa(values.content),
          variable: variableMap,
        });
        toast.success("创建成功");
      }
      onSuccess?.();
    } catch {
      toast.error(versionId ? "更新失败" : "创建失败");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoadingVersion) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>描述</FormLabel>
                <FormControl>
                  <Textarea {...field} disabled={isSubmitting} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Card>
            <CardHeader>
              <CardTitle>模板内容</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4">
                  <TabsTrigger value="edit">编辑</TabsTrigger>
                  <TabsTrigger value="preview" onClick={handlePreview}>
                    预览
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="edit" forceMount>
                  <div className={activeTab === "edit" ? "block" : "hidden"}>
                    <FormField
                      control={form.control}
                      name="content"
                      render={() => (
                        <FormItem>
                          <FormControl>
                            <div
                              ref={editorRef}
                              className="rounded-md border overflow-hidden"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </TabsContent>
                <TabsContent value="preview">
                  <div className="min-h-[400px] p-4 border rounded-md whitespace-pre-wrap font-mono">
                    {renderPreviewContent()}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>变量配置</CardTitle>
                <Button type="button" variant="outline" onClick={addVariable}>
                  添加变量
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {form.watch("variables").map((_, index) => (
                  <div key={index} className="space-y-4 p-4 border rounded-md">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">变量 {index + 1}</h4>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeVariable(index)}
                      >
                        删除
                      </Button>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`variables.${index}.key`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>变量名</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="请输入变量名" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name={`variables.${index}.type`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>类型</FormLabel>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择类型" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem
                                  value={VariableType.VariableTypeString}
                                >
                                  文本
                                </SelectItem>
                                <SelectItem
                                  value={VariableType.VariableTypeInt64}
                                >
                                  整数
                                </SelectItem>
                                <SelectItem
                                  value={VariableType.VariableTypeFloat64}
                                >
                                  浮点数
                                </SelectItem>
                                <SelectItem
                                  value={VariableType.VariableTypeList}
                                >
                                  列表
                                </SelectItem>
                                <SelectItem
                                  value={VariableType.VariableTypeMap}
                                >
                                  对象
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name={`variables.${index}.description`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>描述</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="请输入描述" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name={`variables.${index}.defaultValue`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>默认值</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="请输入默认值" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name={`variables.${index}.required`}
                        render={({ field }) => (
                          <FormItem className="flex items-center justify-between space-y-0">
                            <FormLabel>必填</FormLabel>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <FormField
            control={form.control}
            name="enabled"
            render={({ field }) => (
              <FormItem className="flex items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel>启用</FormLabel>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isSubmitting}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                {versionId ? "保存中..." : "创建中..."}
              </div>
            ) : versionId ? (
              "保存"
            ) : (
              "创建"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
