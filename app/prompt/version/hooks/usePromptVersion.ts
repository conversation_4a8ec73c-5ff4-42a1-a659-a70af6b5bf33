import { useState, useEffect } from "react";
import { apiClient } from "@/app/api/request";
import { PromptVersion } from "@/app/bam/aime/namespaces/prompt";
import { toast } from "sonner";

export function usePromptVersion(versionId: string | null) {
  const [version, setVersion] = useState<PromptVersion | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchVersion = async () => {
      if (!versionId) {
        setVersion(null);
        setIsLoading(false);
        return;
      }

      try {
        const response = await apiClient.GetPromptVersion({
          prompt_version_id: versionId,
          content: true,
        });
        setVersion(response.prompt_version);
      } catch {
        toast.error("获取版本信息失败");
      } finally {
        setIsLoading(false);
      }
    };

    fetchVersion();
  }, [versionId]);

  return { version, isLoading };
} 