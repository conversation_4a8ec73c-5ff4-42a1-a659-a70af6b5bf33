"use client";

import { apiClient } from "@/app/api/request";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Edit, ArrowLeft } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { PaginationInfo } from "@/components/common/PaginationInfo";
import { useSearchParams } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { PromptVersion } from "@/app/bam/aime/namespaces/prompt";
import { VersionForm } from "./components/VersionForm";

// 常量定义
const pageSize = 10;

type ViewMode = "list" | "edit" | "create";

// 日期时间格式化工具函数
function formatDateTime(dateString: string) {
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    }).format(date);
  } catch {
    return dateString;
  }
}

// 主页面组件
export default function PromptVersionsPage() {
  const searchParams = useSearchParams();
  const promptId = searchParams.get("prompt_id");
  const [prompt, setPrompt] = useState<{
    id: string;
    name: string;
    description: string;
  } | null>(null);
  const [versions, setVersions] = useState<PromptVersion[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [editingVersionId, setEditingVersionId] = useState<string | null>(null);

  // 获取Prompt和版本列表数据
  const fetchData = async () => {
    if (!promptId) return;
    try {
      setIsLoading(true);
      const [promptResponse, versionsResponse] = await Promise.all([
        apiClient.GetPrompt({ prompt_id: promptId }),
        apiClient.ListPromptVersion({
          prompt_id: promptId,
          page_num: currentPage,
          page_size: pageSize,
        }),
      ]);
      setPrompt(promptResponse.prompt);
      setVersions(versionsResponse.prompt_versions);
      setTotalCount(Number(versionsResponse.total));
      setTotalPages(
        Math.max(1, Math.ceil(Number(versionsResponse.total) / pageSize)),
      );
    } catch {
      toast.error("获取数据失败");
    } finally {
      setIsLoading(false);
    }
  };

  // 页面首次加载和页码变化时获取数据
  useEffect(() => {
    fetchData();
  }, [promptId, currentPage]);

  // 页码变化处理
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 编辑按钮点击处理
  const handleEditClick = (version: PromptVersion) => {
    setEditingVersionId(version.id);
    setViewMode("edit");
  };

  // 创建按钮点击处理
  const handleCreateClick = () => {
    setViewMode("create");
  };

  // 返回列表处理
  const handleBackToList = () => {
    setViewMode("list");
    setEditingVersionId(null);
    fetchData();
  };

  if (!promptId) {
    return (
      <div className="p-6">
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <p className="text-gray-500">缺少 prompt_id 参数</p>
        </div>
      </div>
    );
  }

  if (viewMode === "create") {
    return (
      <div className="p-6">
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            className="flex items-center gap-2"
            onClick={handleBackToList}
          >
            <ArrowLeft className="h-4 w-4" />
            返回列表
          </Button>
          <h1 className="text-2xl font-bold">创建版本</h1>
        </div>
        <VersionForm promptId={promptId} onSuccess={handleBackToList} />
      </div>
    );
  }

  if (viewMode === "edit" && editingVersionId) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            className="flex items-center gap-2"
            onClick={handleBackToList}
          >
            <ArrowLeft className="h-4 w-4" />
            返回列表
          </Button>
          <h1 className="text-2xl font-bold">编辑版本</h1>
        </div>
        <VersionForm
          promptId={promptId}
          versionId={editingVersionId}
          onSuccess={handleBackToList}
        />
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">
            {prompt?.name || "Prompt"} 版本列表
          </h1>
          {prompt?.description && (
            <p className="text-gray-500 mt-1">{prompt.description}</p>
          )}
        </div>
        <Button onClick={handleCreateClick}>创建版本</Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
        </div>
      ) : versions.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <p className="text-gray-500">
            暂无数据，请点击右上角&ldquo;创建版本&rdquo;按钮添加
          </p>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>版本号</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建者</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>更新时间</TableHead>
                <TableHead className="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {versions.map(version => (
                <TableRow key={version.id}>
                  <TableCell className="font-medium">
                    v{version.version}
                  </TableCell>
                  <TableCell className="max-w-md truncate">
                    {version.description || "暂无描述"}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={version.enabled ? "default" : "secondary"}
                      className="font-normal"
                    >
                      {version.enabled ? "已启用" : "已禁用"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="font-normal">
                      {version.creator}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDateTime(version.created_at)}</TableCell>
                  <TableCell>{formatDateTime(version.updated_at)}</TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => handleEditClick(version)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {versions.length > 0 && (
        <PaginationInfo
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          totalCount={totalCount}
          onPageChange={handlePageChange}
        />
      )}
    </div>
  );
}
