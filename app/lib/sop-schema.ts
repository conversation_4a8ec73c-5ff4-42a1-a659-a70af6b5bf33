
import { z } from "zod";

// 定义SOP数据结构的严格类型验证
// 使用Zod进行运行时类型检查，确保数据完整性
export const referenceSchema = z.object({
  id: z.string(),
  error: z.string(),
  binary: z.boolean(),
  content: z.string(),
  filepath: z.string(),
});

export const placeholderSchema = z.object({
  name: z.string(),
  default: z.string(),
  description: z.string(),
  ref_attachments: z.boolean().optional(),
  required: z.boolean().optional(),
});

export const phaseSchema = z.object({
  name: z.string(),
  objective: z.string(),
  actions: z.string(),
  references: z.array(referenceSchema).default([]),
  deliverable: z.array(z.string()).default([]),
});

export const planStepSchema = z.object({
  name: z.string(),
  assigned_to: z.string(),
  persona: z.string(),
  toolsets: z.array(z.string()).default([]),
  objective: z.string(),
  phases: z.array(phaseSchema).default([]),
  parameters: z.record(z.string(), z.string()).optional().nullable(),
});

export const sopSchemaSchema = z.object({
  name: z.string(),
  used_when: z.string(),
  user_query: z.string(),
  progress_plan: z.string(),
  user_query_placeholders: z.array(placeholderSchema).default([]),
  plan_steps: z.array(planStepSchema).default([]),
});

export type Reference = z.infer<typeof referenceSchema>;
export type Placeholder = z.infer<typeof placeholderSchema>;
export type Phase = z.infer<typeof phaseSchema>;
export type PlanStep = z.infer<typeof planStepSchema>;
export type SOPSchema = z.infer<typeof sopSchemaSchema>;

// 工具函数：安全数组处理，避免重复的防御性检查代码
export const safeArray = <T>(arr: T[] | undefined | null): T[] => {
  return Array.isArray(arr) ? arr : [];
};

// 工具函数：安全映射数组，过滤掉null/undefined结果
export const safeMapArray = <T, R>(
  arr: T[] | undefined | null,
  mapFn: (item: T, index: number) => R | null
): R[] => {
  return safeArray(arr)
    .map(mapFn)
    .filter((item): item is R => item !== null);
};

// 工具函数：安全解析JSON字符串
export const safeParseJSON = <T>(jsonString: string | any, fallback: T): T => {
  if (typeof jsonString !== 'string') {
    return typeof jsonString === 'object' && jsonString !== null ? jsonString : fallback;
  }
  
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn('JSON解析失败:', error);
    return fallback;
  }
};

// 工具函数：验证并清理占位符数据
export const validatePlaceholders = (placeholders: any[]): Placeholder[] => {
  return safeMapArray(placeholders, (item) => {
    if (!item || typeof item !== 'object' || !item.name || typeof item.name !== 'string') {
      return null;
    }
    
    try {
      return placeholderSchema.parse({
        name: String(item.name).trim(),
        default: String(item.default || ''),
        description: String(item.description || ''),
        ref_attachments: Boolean(item.ref_attachments),
        required: item.required !== undefined ? Boolean(item.required) : undefined,
      });
    } catch {
      return null;
    }
  });
};

// TemplateVersion与SOPSchema之间的转换函数 - 使用工具函数优化
export const convertTemplateVersionToSOPSchema = (template: any): SOPSchema => {
  console.log('convertTemplateVersionToSOPSchema: input template:', template);
  
  // 如果template为null或undefined，返回默认值
  if (!template) {
    return {
      name: '',
      used_when: '',
      user_query: '',
      progress_plan: '',
      user_query_placeholders: [],
      plan_steps: [],
    };
  }

  try {
    // 只从exp_sop字段读取所有SOP数据
    const expSop = safeParseJSON(template.exp_sop, {}) as any;
    console.log('convertTemplateVersionToSOPSchema: parsed exp_sop:', expSop);
    
    // 如果exp_sop存在且包含数据，直接使用它
    if (expSop && typeof expSop === 'object') {
      console.log('convertTemplateVersionToSOPSchema: using exp_sop as the only data source');
      
      // 解析计划步骤
      const planSteps: PlanStep[] = safeMapArray(expSop.plan_steps || [], (step: any) => {
        if (!step || typeof step !== 'object') {
          return null;
        }
        
        try {
          return planStepSchema.parse({
            name: String(step.name || ''),
            assigned_to: String(step.assigned_to || ''),
            persona: String(step.persona || ''),
            toolsets: safeArray(step.toolsets).filter(t => t && typeof t === 'string'),
            objective: String(step.objective || ''),
            phases: safeMapArray(step.phases || [], (phase: any) => {
              if (!phase || typeof phase !== 'object') {
                return null;
              }
              
              try {
                return phaseSchema.parse({
                  name: String(phase.name || ''),
                  objective: String(phase.objective || ''),
                  actions: String(phase.actions || ''),
                  references: safeArray(phase.references),
                  deliverable: safeArray(phase.deliverable).filter(d => d && typeof d === 'string'),
                });
              } catch {
                return null;
              }
            }),
            parameters: step.parameters && typeof step.parameters === 'object' ? step.parameters : {},
          });
        } catch {
          return null;
        }
      });

      // 解析占位符
      const placeholders = validatePlaceholders(safeArray(expSop.user_query_placeholders));
      
      const result = {
        name: String(expSop.name || ''),
        used_when: String(expSop.used_when || ''),
        user_query: String(expSop.user_query || ''),
        progress_plan: String(expSop.progress_plan || ''),
        user_query_placeholders: placeholders,
        plan_steps: planSteps,
      };
      
      console.log('convertTemplateVersionToSOPSchema: final result from exp_sop:', result);
      return result;
    } else {
      console.log('convertTemplateVersionToSOPSchema: exp_sop is empty, returning default values');
      
      // 如果exp_sop为空，返回基本的默认值
      return {
        name: String(template.name || ''),
        used_when: '',
        user_query: String(template.prompt_content || ''),
        progress_plan: String(template.plan || ''),
        user_query_placeholders: [],
        plan_steps: [],
      };
    }
  } catch (error) {
    console.error('转换TemplateVersion到SOPSchema时出错:', error, '模板数据:', template);
    return {
      name: String(template.name || ''),
      used_when: '',
      user_query: String(template.prompt_content || ''),
      progress_plan: String(template.plan || ''),
      user_query_placeholders: [],
      plan_steps: [],
    };
  }
};

export const convertSOPSchemaToTemplateVersion = (sopSchema: SOPSchema, originalTemplate?: any): any => {
  console.log('convertSOPSchemaToTemplateVersion: input sopSchema:', sopSchema);
  
  try {
    // 使用Zod验证输入数据
    const validatedSOP = sopSchemaSchema.parse(sopSchema);
    
    // 将完整的SOP数据保存到exp_sop字段
    const expSopData = {
      name: validatedSOP.name,
      used_when: validatedSOP.used_when,
      user_query: validatedSOP.user_query,
      progress_plan: validatedSOP.progress_plan,
      user_query_placeholders: validatedSOP.user_query_placeholders,
      plan_steps: validatedSOP.plan_steps,
    };
    
    console.log('convertSOPSchemaToTemplateVersion: expSopData:', expSopData);
    
    const result = {
      ...originalTemplate,
      name: validatedSOP.name,
      prompt_content: validatedSOP.user_query,
      plan: validatedSOP.progress_plan,
      exp_sop: JSON.stringify(expSopData),
      // 为了兼容性，也保存到分散字段
      plan_steps: JSON.stringify(validatedSOP.plan_steps),
      prompt_variables: JSON.stringify(validatedSOP.user_query_placeholders),
    };
    
    console.log('convertSOPSchemaToTemplateVersion: final result:', result);
    return result;
  } catch (error) {
    console.error('转换SOPSchema到TemplateVersion时出错:', error);
    // 在错误情况下，返回原始数据以避免数据丢失
    return {
      ...originalTemplate,
      name: sopSchema.name || '',
      prompt_content: sopSchema.user_query || '',
      plan: sopSchema.progress_plan || '',
    };
  }
};
