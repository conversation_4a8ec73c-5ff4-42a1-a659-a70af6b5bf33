"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { PageHeader } from "@/app/components/PageHeader";
import { apiClient } from "@/app/api/request";
import { toast } from "sonner";
import { EventSourceMessage } from "@microsoft/fetch-event-source";
import { Play, Square, Trash2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useStore } from "@nanostores/react";

// 复用原始的组件
import EventList from "@/app/trace/events/components/EventList";
import EventDetail from "@/app/trace/events/components/EventDetail";

// 使用独立的 MCP store
import {
  mcpEventsStore,
  mcpEventListStore,
  mcpEventTypesStore,
  MCPEventData,
  setMCPSessionId,
  setMCPLoading,
  setMCPConnected,
  setMCPError,
  setMCPSelectedEvent,
  setMCPSearchText,
  setMCPEventFilter,
  addMCPEvent,
  clearMCPEvents,
} from "./store/mcpStore";

// 导入 EventData 类型
import { type EventData } from "@/app/trace/events/store/eventsStore";

// 转换 MCP 事件数据为 trace 事件格式，以便复用组件
const convertMCPToTraceEvent = (mcpEvent: MCPEventData): EventData => {
  return {
    id: mcpEvent.id,
    offset: mcpEvent.offset,
    event: mcpEvent.event,
    timestamp: mcpEvent.timestamp,
    data: {
      event: mcpEvent.event,
      offset: mcpEvent.offset,
      data: mcpEvent.data,
      timestamp: mcpEvent.timestamp,
    },
    run_id: mcpEvent.run_id,
  };
};

export default function MCPPlaygroundPage() {
  const [sessionId, setSessionId] = useState("");

  const {
    isLoading,
    isConnected,
    error,
    selectedEventId,
    selectedEventData,
    searchText,
    eventFilter,
  } = useStore(mcpEventsStore);

  const events = useStore(mcpEventListStore);
  const eventTypes = useStore(mcpEventTypesStore);

  // 用于控制EventSource连接
  const eventSourceRef = useRef<AbortController | null>(null);

  // 转换事件数据格式以供复用组件使用，并过滤掉 ping 事件
  const convertedEvents = events
    .filter(event => event.event !== "ping") // 过滤掉 ping 事件
    .map(convertMCPToTraceEvent);
  const convertedSelectedEventData = selectedEventData
    ? convertMCPToTraceEvent(selectedEventData)
    : null;

  // 处理新事件
  const handleNewEvent = useCallback(
    (eventStr: string) => {
      try {
        const rawEvent = JSON.parse(eventStr);

        const eventData: MCPEventData = {
          id: crypto.randomUUID(),
          event: rawEvent.event,
          timestamp: rawEvent.timestamp || new Date().toISOString(),
          offset: Number(rawEvent.offset || rawEvent.data?.offset || 0),
          data: rawEvent.data || {},
          run_id: sessionId,
        };

        addMCPEvent(eventData);
      } catch (error) {
        console.error("解析事件数据失败:", error, eventStr);
      }
    },
    [sessionId],
  );

  // 获取过滤后的事件数量（排除ping）
  const getFilteredEventCount = () => {
    return events.filter(event => event.event !== "ping").length;
  };

  // 处理事件点击
  const handleEventClick = (event: EventData) => {
    // 找到对应的 MCP 事件数据
    const mcpEvent = events.find(e => e.id === event.id);
    if (mcpEvent) {
      setMCPSelectedEvent(mcpEvent.id, mcpEvent);
    }
  };

  // 处理搜索变化
  const handleSearchChange = (text: string) => {
    setMCPSearchText(text);
  };

  // 处理过滤器变化
  const handleFilterChange = (filter: string[]) => {
    setMCPEventFilter(filter);
  };

  // 开始监听事件
  const startTracking = async () => {
    if (!sessionId.trim()) {
      toast.error("请输入 Session ID");
      return;
    }

    // 停止之前的连接
    if (eventSourceRef.current) {
      eventSourceRef.current.abort();
    }

    setMCPSessionId(sessionId);
    setMCPLoading(true);
    setMCPError("");
    clearMCPEvents();
    setMCPEventFilter([]); // 清空过滤器

    try {
      const controller = new AbortController();
      eventSourceRef.current = controller;

      await apiClient.TraceMCPEvents(
        { session_id: sessionId },
        {
          signal: controller.signal,
          onopen: async () => {
            setMCPLoading(false);
            setMCPConnected(true);
            setMCPError("");
            toast.success("开始监听 MCP 事件");
          },
          onmessage: (event: EventSourceMessage) => {
            if (event.data) {
              handleNewEvent(event.data);

              try {
                const parsed = JSON.parse(event.data);
                if (parsed.event === "done") {
                  toast.success("连接已完成");
                  if (eventSourceRef.current) {
                    eventSourceRef.current.abort();
                    eventSourceRef.current = null;
                  }
                  setMCPConnected(false);
                  setMCPLoading(false);
                }
              } catch {
                // 忽略解析错误
              }
            }
          },
          onerror: (error: unknown) => {
            console.error("EventSource 错误:", error);
            setMCPLoading(false);
            setMCPConnected(false);
            setMCPError("连接错误");
            toast.error("连接出现错误");
          },
          onclose: () => {
            setMCPConnected(false);
          },
        },
      );
    } catch (error) {
      console.error("启动事件追踪失败:", error);
      setMCPLoading(false);
      setMCPConnected(false);
      setMCPError(error instanceof Error ? error.message : "未知错误");
      toast.error("启动事件追踪失败");
    }
  };

  // 停止监听事件
  const stopTracking = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.abort();
      eventSourceRef.current = null;
    }

    setMCPConnected(false);
    toast.success("已停止监听 MCP 事件");
  };

  // 清空事件
  const clearEvents = () => {
    clearMCPEvents();
    setMCPEventFilter([]);
    toast.success("已清空事件列表");
  };

  // 清理EventSource连接
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.abort();
      }
    };
  }, []);

  return (
    <div className="flex flex-col h-[calc(100vh-60px)] gap-3 p-6">
      <PageHeader
        title="MCP Playground"
        description="监听和调试 MCP (Model Context Protocol) 事件"
      />

      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle>事件监听控制</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-end gap-4">
            <div className="flex-1">
              <Label htmlFor="sessionId" className="mb-2 text-base">
                Session ID
              </Label>
              <Input
                id="sessionId"
                placeholder="请输入 Session ID"
                value={sessionId}
                onChange={e => setSessionId(e.target.value)}
                disabled={isConnected}
                onKeyDown={e => {
                  if (
                    e.key === "Enter" &&
                    !isConnected &&
                    !isLoading &&
                    sessionId.trim()
                  ) {
                    startTracking();
                  }
                }}
                className="rounded-lg px-4 py-2 text-base"
              />
            </div>
            <div className="flex gap-2">
              {!isConnected ? (
                <Button
                  onClick={startTracking}
                  disabled={isLoading || !sessionId.trim()}
                  className="flex items-center gap-2"
                >
                  <Play className="h-4 w-4" />
                  {isLoading ? "连接中..." : "开始监听"}
                </Button>
              ) : (
                <Button
                  onClick={stopTracking}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Square className="h-4 w-4" />
                  停止监听
                </Button>
              )}
              <Button
                onClick={clearEvents}
                variant="outline"
                className="flex items-center gap-2"
                disabled={events.length === 0}
              >
                <Trash2 className="h-4 w-4" />
                清空
              </Button>
            </div>
          </div>
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div
                className={cn("w-2 h-2 rounded-full", {
                  "bg-green-500": isConnected,
                  "bg-yellow-500": isLoading,
                  "bg-gray-400": !isConnected && !isLoading,
                })}
              />
              <span>
                {isConnected ? "已连接" : isLoading ? "连接中" : "未连接"}
              </span>
            </div>
            <span>事件数量: {getFilteredEventCount()}</span>
            {error && <span className="text-red-600">错误: {error}</span>}
          </div>
        </CardContent>
      </Card>

      {/* 真正复用展示组件，通过 props 传递独立的 MCP 数据 */}
      <div className="grid grid-cols-1 md:grid-cols-[1fr_2fr] gap-4 flex-1 overflow-hidden">
        {/* 事件列表 - 复用原始组件 */}
        <div className="border rounded-md overflow-hidden h-full">
          <EventList
            showFilter={false} // MCP 不需要过滤器
            externalEvents={convertedEvents}
            externalEventTypes={eventTypes}
            externalSelectedEventId={selectedEventId}
            externalEventFilter={eventFilter}
            externalSearchText={searchText}
            onExternalEventClick={handleEventClick}
            onExternalFilterChange={handleFilterChange}
            onExternalSearchChange={handleSearchChange}
          />
        </div>

        {/* 事件详情 - 复用原始组件 */}
        <div className="border rounded-md overflow-hidden h-full">
          <EventDetail externalSelectedEventData={convertedSelectedEventData} />
        </div>
      </div>
    </div>
  );
}
