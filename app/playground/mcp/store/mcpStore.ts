import { atom, map } from 'nanostores';

// MCP 事件数据定义
export interface MCPEventData {
  id: string;
  offset: number;
  event: string;
  timestamp: string;
  data: Record<string, unknown>;
  run_id: string;
}

// MCP 状态存储
export const mcpEventsStore = map({
  sessionId: '',
  isLoading: false,
  isConnected: false,
  error: '',
  selectedEventId: null as string | null,
  selectedEventData: null as MCPEventData | null,
  searchText: '',
  eventFilter: [] as string[],
});

// MCP 事件数据存储
export const mcpEventListStore = atom<MCPEventData[]>([]);

// MCP 事件类型集合
export const mcpEventTypesStore = atom<Set<string>>(new Set());

// 设置查询参数
export function setMCPSessionId(sessionId: string) {
  mcpEventsStore.setKey('sessionId', sessionId);
}

// 设置加载状态
export function setMCPLoading(loading: boolean) {
  mcpEventsStore.setKey('isLoading', loading);
}

// 设置连接状态
export function setMCPConnected(connected: boolean) {
  mcpEventsStore.setKey('isConnected', connected);
}

// 设置错误信息
export function setMCPError(error: string) {
  mcpEventsStore.setKey('error', error);
}

// 设置选中事件
export function setMCPSelectedEvent(eventId: string | null, eventData: MCPEventData | null) {
  mcpEventsStore.setKey('selectedEventId', eventId);
  mcpEventsStore.setKey('selectedEventData', eventData);
}

// 设置搜索文本
export function setMCPSearchText(text: string) {
  mcpEventsStore.setKey('searchText', text);
}

// 设置事件过滤器
export function setMCPEventFilter(filter: string[]) {
  mcpEventsStore.setKey('eventFilter', filter);
}

// 添加新事件
export function addMCPEvent(eventData: MCPEventData) {
  const currentEvents = mcpEventListStore.get();
  
  // 检查是否已存在相同offset的事件
  const existingIndex = currentEvents.findIndex(e => e.offset === eventData.offset);
  if (existingIndex !== -1) {
    return;
  }
  
  // 添加新事件
  mcpEventListStore.set([...currentEvents, eventData]);
  
  // 更新事件类型集合
  const currentTypes = mcpEventTypesStore.get();
  if (!currentTypes.has(eventData.event)) {
    mcpEventTypesStore.set(new Set([...currentTypes, eventData.event]));
  }
}

// 清空事件
export function clearMCPEvents() {
  mcpEventListStore.set([]);
  mcpEventTypesStore.set(new Set());
  setMCPSelectedEvent(null, null);
}

// 根据条件过滤事件
export function getMCPFilteredEvents(): MCPEventData[] {
  const { searchText } = mcpEventsStore.get();
  const events = mcpEventListStore.get();
  
  // 首先确保事件按offset排序
  const sortedEvents = [...events].sort((a, b) => a.offset - b.offset);
  
  // 然后应用搜索过滤
  return sortedEvents.filter(event => {
    // 过滤搜索文本
    if (searchText && !JSON.stringify(event.data).toLowerCase().includes(searchText.toLowerCase())) {
      return false;
    }
    
    return true;
  });
} 