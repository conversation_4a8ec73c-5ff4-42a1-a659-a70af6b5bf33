"use client";

import { useState } from "react";
import {
  SendIcon,
  PlusIcon,
  TrashIcon,
  MessageSquareIcon,
  SlidersIcon,
  RefreshCwIcon,
  ZapIcon,
  HelpCircleIcon,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PageHeader } from "@/app/components/PageHeader";
import { Badge } from "@/components/ui/badge";

interface DraftMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

interface ChatResponse {
  id: string;
  content: string;
  model: string;
  timestamp: Date;
  messages: DraftMessage[];
}

interface ModelParam {
  temperature: string;
  maxTokens: string;
  topP: string;
  frequencyPenalty: string;
  presencePenalty: string;
}

export default function ChatPlaygroundPage() {
  const [draftMessages, setDraftMessages] = useState<DraftMessage[]>([
    { role: "system", content: "" },
    { role: "user", content: "" },
  ]);
  const [responses, setResponses] = useState<ChatResponse[]>([]);
  const [selectedModel, setSelectedModel] = useState("gpt-4");
  const [modelParams, setModelParams] = useState<ModelParam>({
    temperature: "0.7",
    maxTokens: "2000",
    topP: "1.0",
    frequencyPenalty: "0.0",
    presencePenalty: "0.0",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("basic");

  const handleAddMessage = () => {
    setDraftMessages([...draftMessages, { role: "user", content: "" }]);
  };

  const handleRemoveMessage = (index: number) => {
    if (draftMessages.length <= 1) return;
    const newMessages = [...draftMessages];
    newMessages.splice(index, 1);
    setDraftMessages(newMessages);
  };

  const handleMessageChange = (
    index: number,
    field: keyof DraftMessage,
    value: string,
  ) => {
    const newMessages = [...draftMessages];
    if (
      field === "role" &&
      (value === "system" || value === "user" || value === "assistant")
    ) {
      newMessages[index].role = value;
    } else if (field === "content") {
      newMessages[index].content = value;
    }
    setDraftMessages(newMessages);
  };

  const handleParamChange = (param: keyof ModelParam, value: string) => {
    setModelParams({
      ...modelParams,
      [param]: value,
    });
  };

  const handleSubmit = async () => {
    const validMessages = draftMessages.filter(msg => msg.content.trim());
    if (validMessages.length === 0) return;

    setIsLoading(true);

    // TODO: 实现实际的API调用
    // 模拟API响应
    setTimeout(() => {
      const response: ChatResponse = {
        id: Math.random().toString(36).substr(2, 9),
        content: `这是对消息列表的模拟响应，使用模型: ${selectedModel}，参数: 温度=${modelParams.temperature}, 最大Token=${modelParams.maxTokens}`,
        model: selectedModel,
        timestamp: new Date(),
        messages: [...validMessages],
      };
      setResponses([...responses, response]);
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="container px-4 py-6 mx-auto max-w-6xl">
      {/* 页面标题 */}
      <PageHeader
        title="(WIP)Chat Playground"
        description="与 AI 模型进行对话和测试"
      />

      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 mt-6">
        {/* 左侧：消息列表 */}
        <div className="lg:col-span-3 flex flex-col rounded-xl border border-border/60 shadow-sm overflow-hidden bg-card">
          <div className="flex items-center justify-between px-4 py-3 bg-muted/30 border-b">
            <div className="flex items-center">
              <MessageSquareIcon className="h-5 w-5 mr-2 text-primary" />
              <h2 className="font-semibold">对话消息</h2>
            </div>
            <div className="text-xs text-muted-foreground">
              添加系统提示和用户消息来构建对话
            </div>
          </div>

          <ScrollArea className="flex-1 h-[400px]">
            <div className="flex flex-col gap-4 p-4">
              {draftMessages.map((msg, index) => (
                <div
                  key={index}
                  className={`
                    relative p-4 rounded-lg transition-all group
                    ${
                      msg.role === "system"
                        ? "bg-secondary/20 border border-secondary/30"
                        : msg.role === "assistant"
                        ? "bg-primary/10 border border-primary/20"
                        : "bg-muted/40 border border-border/50"
                    }
                    hover:border-border
                  `}
                >
                  <div className="flex flex-col gap-3">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <Badge
                          variant={
                            msg.role === "system"
                              ? "secondary"
                              : msg.role === "assistant"
                              ? "outline"
                              : "default"
                          }
                          className="font-medium text-xs"
                        >
                          {msg.role === "system"
                            ? "系统"
                            : msg.role === "assistant"
                            ? "助手"
                            : "用户"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <Select
                          value={msg.role}
                          onValueChange={value =>
                            handleMessageChange(index, "role", value)
                          }
                        >
                          <SelectTrigger className="w-28 h-8 text-xs">
                            <SelectValue placeholder="选择角色" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="system">系统</SelectItem>
                            <SelectItem value="user">用户</SelectItem>
                            <SelectItem value="assistant">助手</SelectItem>
                          </SelectContent>
                        </Select>
                        <Button
                          variant="ghost"
                          size="icon"
                          disabled={draftMessages.length <= 1}
                          onClick={() => handleRemoveMessage(index)}
                          className="h-8 w-8 text-muted-foreground hover:text-destructive opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <Textarea
                      placeholder={`输入${
                        msg.role === "system"
                          ? "系统提示"
                          : msg.role === "assistant"
                          ? "助手回复"
                          : "用户消息"
                      }...`}
                      value={msg.content}
                      onChange={e =>
                        handleMessageChange(index, "content", e.target.value)
                      }
                      className={`
                        min-h-[80px] resize-none transition-colors
                        focus-visible:ring-1 focus-visible:ring-ring
                        ${
                          msg.role === "system"
                            ? "bg-secondary/10 border-secondary/30 focus-visible:border-secondary"
                            : msg.role === "assistant"
                            ? "bg-primary/5 border-primary/20 focus-visible:border-primary"
                            : "bg-background border-muted"
                        }
                      `}
                    />
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>

          <div className="flex items-center justify-between p-4 border-t bg-muted/20">
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddMessage}
              className="text-xs h-8"
            >
              <PlusIcon className="mr-1 h-3.5 w-3.5" />
              添加消息
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isLoading}
              className="text-sm h-9"
            >
              {isLoading ? (
                <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <SendIcon className="mr-2 h-4 w-4" />
              )}
              {isLoading ? "发送中..." : "发送"}
            </Button>
          </div>
        </div>

        {/* 右侧：LLM配置 */}
        <div className="lg:col-span-2 flex flex-col rounded-xl border border-border/60 shadow-sm overflow-hidden bg-card">
          <div className="flex items-center justify-between px-4 py-3 bg-muted/30 border-b">
            <div className="flex items-center">
              <SlidersIcon className="h-5 w-5 mr-2 text-primary" />
              <h2 className="font-semibold">模型与参数</h2>
            </div>
            <div className="text-xs text-muted-foreground">
              调整参数以控制模型输出
            </div>
          </div>

          <div className="p-4 flex flex-col gap-4 h-[400px] overflow-y-auto">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">模型选择</Label>
                <Badge variant="outline" className="font-mono text-xs">
                  {selectedModel}
                </Badge>
              </div>
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger className="w-full bg-muted/30 border-border/60">
                  <SelectValue placeholder="选择模型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gpt-4" className="flex items-center">
                    <ZapIcon className="h-3.5 w-3.5 mr-2 text-blue-500" />
                    GPT-4
                  </SelectItem>
                  <SelectItem value="gpt-4-turbo" className="flex items-center">
                    <ZapIcon className="h-3.5 w-3.5 mr-2 text-blue-600" />
                    GPT-4 Turbo
                  </SelectItem>
                  <SelectItem
                    value="gpt-3.5-turbo"
                    className="flex items-center"
                  >
                    <ZapIcon className="h-3.5 w-3.5 mr-2 text-blue-400" />
                    GPT-3.5 Turbo
                  </SelectItem>
                  <SelectItem
                    value="claude-3-opus"
                    className="flex items-center"
                  >
                    <ZapIcon className="h-3.5 w-3.5 mr-2 text-purple-600" />
                    Claude 3 Opus
                  </SelectItem>
                  <SelectItem
                    value="claude-3-sonnet"
                    className="flex items-center"
                  >
                    <ZapIcon className="h-3.5 w-3.5 mr-2 text-purple-500" />
                    Claude 3 Sonnet
                  </SelectItem>
                  <SelectItem
                    value="claude-3-haiku"
                    className="flex items-center"
                  >
                    <ZapIcon className="h-3.5 w-3.5 mr-2 text-purple-400" />
                    Claude 3 Haiku
                  </SelectItem>
                  <SelectItem value="llama-3-70b" className="flex items-center">
                    <ZapIcon className="h-3.5 w-3.5 mr-2 text-amber-500" />
                    Llama 3 70B
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Tabs
              defaultValue="basic"
              className="w-full"
              value={activeTab}
              onValueChange={setActiveTab}
            >
              <TabsList className="grid w-full grid-cols-2 h-8 mb-2">
                <TabsTrigger value="basic" className="text-xs">
                  基础参数
                </TabsTrigger>
                <TabsTrigger value="advanced" className="text-xs">
                  高级参数
                </TabsTrigger>
              </TabsList>
              <TabsContent value="basic" className="space-y-4 mt-2">
                <div className="space-y-2 bg-muted/20 p-3 rounded-lg border border-border/30">
                  <div className="flex justify-between">
                    <div className="flex items-center gap-1">
                      <Label className="text-sm font-medium">温度</Label>
                      <span className="inline-flex relative group">
                        <HelpCircleIcon className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
                        <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 px-2 py-1 text-xs bg-popover text-popover-foreground rounded shadow-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none w-40 z-50">
                          控制生成文本的随机性和创造性
                        </span>
                      </span>
                    </div>
                    <Badge variant="outline" className="text-xs font-mono">
                      {modelParams.temperature}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Slider
                      defaultValue={[parseFloat(modelParams.temperature)]}
                      min={0}
                      max={2}
                      step={0.1}
                      onValueChange={(value: number[]) =>
                        handleParamChange("temperature", value[0].toString())
                      }
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={modelParams.temperature}
                      onChange={e =>
                        handleParamChange("temperature", e.target.value)
                      }
                      min="0"
                      max="2"
                      step="0.1"
                      className="w-16 h-8 text-xs"
                    />
                  </div>
                </div>

                <div className="space-y-2 bg-muted/20 p-3 rounded-lg border border-border/30">
                  <div className="flex justify-between">
                    <div className="flex items-center gap-1">
                      <Label className="text-sm font-medium">最大Token数</Label>
                      <span className="inline-flex relative group">
                        <HelpCircleIcon className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
                        <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 px-2 py-1 text-xs bg-popover text-popover-foreground rounded shadow-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none w-40 z-50">
                          模型生成的最大令牌数
                        </span>
                      </span>
                    </div>
                    <Badge variant="outline" className="text-xs font-mono">
                      {modelParams.maxTokens}
                    </Badge>
                  </div>
                  <Input
                    type="number"
                    value={modelParams.maxTokens}
                    onChange={e =>
                      handleParamChange("maxTokens", e.target.value)
                    }
                    min="1"
                    max="8000"
                    className="w-full h-8 text-xs"
                  />
                </div>
              </TabsContent>
              <TabsContent value="advanced" className="space-y-4 mt-2">
                <div className="space-y-2 bg-muted/20 p-3 rounded-lg border border-border/30">
                  <div className="flex justify-between">
                    <div className="flex items-center gap-1">
                      <Label className="text-sm font-medium">Top P</Label>
                      <span className="inline-flex relative group">
                        <HelpCircleIcon className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
                        <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 px-2 py-1 text-xs bg-popover text-popover-foreground rounded shadow-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none w-40 z-50">
                          控制输出的多样性
                        </span>
                      </span>
                    </div>
                    <Badge variant="outline" className="text-xs font-mono">
                      {modelParams.topP}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Slider
                      defaultValue={[parseFloat(modelParams.topP)]}
                      min={0}
                      max={1}
                      step={0.01}
                      onValueChange={(value: number[]) =>
                        handleParamChange("topP", value[0].toString())
                      }
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={modelParams.topP}
                      onChange={e => handleParamChange("topP", e.target.value)}
                      min="0"
                      max="1"
                      step="0.01"
                      className="w-16 h-8 text-xs"
                    />
                  </div>
                </div>

                <div className="space-y-2 bg-muted/20 p-3 rounded-lg border border-border/30">
                  <div className="flex justify-between">
                    <div className="flex items-center gap-1">
                      <Label className="text-sm font-medium">频率惩罚</Label>
                      <span className="inline-flex relative group">
                        <HelpCircleIcon className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
                        <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 px-2 py-1 text-xs bg-popover text-popover-foreground rounded shadow-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none w-40 z-50">
                          降低模型重复使用相同词语的可能性
                        </span>
                      </span>
                    </div>
                    <Badge variant="outline" className="text-xs font-mono">
                      {modelParams.frequencyPenalty}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Slider
                      defaultValue={[parseFloat(modelParams.frequencyPenalty)]}
                      min={-2}
                      max={2}
                      step={0.01}
                      onValueChange={(value: number[]) =>
                        handleParamChange(
                          "frequencyPenalty",
                          value[0].toString(),
                        )
                      }
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={modelParams.frequencyPenalty}
                      onChange={e =>
                        handleParamChange("frequencyPenalty", e.target.value)
                      }
                      min="-2"
                      max="2"
                      step="0.01"
                      className="w-16 h-8 text-xs"
                    />
                  </div>
                </div>

                <div className="space-y-2 bg-muted/20 p-3 rounded-lg border border-border/30">
                  <div className="flex justify-between">
                    <div className="flex items-center gap-1">
                      <Label className="text-sm font-medium">存在惩罚</Label>
                      <span className="inline-flex relative group">
                        <HelpCircleIcon className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
                        <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 px-2 py-1 text-xs bg-popover text-popover-foreground rounded shadow-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none w-40 z-50">
                          降低模型讨论相同主题的可能性
                        </span>
                      </span>
                    </div>
                    <Badge variant="outline" className="text-xs font-mono">
                      {modelParams.presencePenalty}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Slider
                      defaultValue={[parseFloat(modelParams.presencePenalty)]}
                      min={-2}
                      max={2}
                      step={0.01}
                      onValueChange={(value: number[]) =>
                        handleParamChange(
                          "presencePenalty",
                          value[0].toString(),
                        )
                      }
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={modelParams.presencePenalty}
                      onChange={e =>
                        handleParamChange("presencePenalty", e.target.value)
                      }
                      min="-2"
                      max="2"
                      step="0.01"
                      className="w-16 h-8 text-xs"
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* 下部分：响应结果 */}
      <div className="mt-6 rounded-xl border border-border/60 shadow-sm overflow-hidden bg-card">
        <div className="flex items-center justify-between px-4 py-3 bg-muted/30 border-b">
          <div className="flex items-center">
            <MessageSquareIcon className="h-5 w-5 mr-2 text-primary" />
            <h2 className="font-semibold">响应结果</h2>
          </div>
          <Badge variant="outline" className="font-mono text-xs">
            {responses.length} 条响应
          </Badge>
        </div>

        <ScrollArea className="h-[400px]">
          {responses.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-muted-foreground p-8">
              <MessageSquareIcon className="h-12 w-12 mb-4 opacity-20" />
              <p className="text-sm">请发送请求以查看响应结果</p>
              <p className="text-xs text-muted-foreground mt-2">
                模型响应将在此显示
              </p>
            </div>
          ) : (
            <div className="p-4 space-y-4">
              {responses.map((response, index) => (
                <div
                  key={response.id}
                  className="rounded-lg border border-border/40 hover:border-border transition-all overflow-hidden"
                >
                  <div className="flex items-center justify-between px-4 py-2.5 bg-muted/30 border-b">
                    <div className="flex items-center gap-2">
                      <Badge
                        variant="default"
                        className="text-xs font-medium bg-primary/80 hover:bg-primary/80"
                      >
                        响应 #{index + 1}
                      </Badge>
                      <span className="text-xs font-medium">
                        {response.model}
                      </span>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {response.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="p-4 space-y-4">
                    <div className="rounded-md bg-muted/20 p-3 border border-border/40">
                      <p className="text-xs font-medium text-muted-foreground mb-2">
                        请求消息:
                      </p>
                      <div className="space-y-2">
                        {response.messages.map((msg, msgIndex) => (
                          <div
                            key={msgIndex}
                            className="ml-2 pl-2 border-l-2 border-muted-foreground/30"
                          >
                            <Badge
                              variant={
                                msg.role === "system"
                                  ? "secondary"
                                  : msg.role === "assistant"
                                  ? "outline"
                                  : "default"
                              }
                              className="mb-1 text-xs"
                            >
                              {msg.role === "system"
                                ? "系统"
                                : msg.role === "assistant"
                                ? "助手"
                                : "用户"}
                            </Badge>
                            <div className="text-sm whitespace-pre-wrap mt-1">
                              {msg.content}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="space-y-1.5">
                      <div className="flex items-center">
                        <p className="text-xs font-medium text-muted-foreground">
                          响应内容:
                        </p>
                        <Badge variant="outline" className="ml-2 text-xs">
                          助手
                        </Badge>
                      </div>
                      <div className="p-4 rounded-md bg-primary/5 border border-primary/20">
                        <p className="whitespace-pre-wrap">
                          {response.content}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>
    </div>
  );
}
