import { MessageSquareIcon } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import ReactMarkdown from "react-markdown";
import "@/app/playground/chat2/components/History/chat-markdown.css";

export function Threads({ res = "", markdown = false }) {
  return (
    <div className="mt-6 rounded-xl border border-border/60 shadow-sm overflow-hidden bg-card">
      <div className="flex items-center justify-between px-4 py-3 bg-muted/30 border-b">
        <div className="flex items-center">
          <MessageSquareIcon className="h-5 w-5 mr-2 text-primary" />
          <h2 className="font-semibold">响应结果</h2>
        </div>
      </div>
      <ScrollArea className="h-[400px] px-[10px] py-[10px] text-sm whitespace-pre-wrap">
        {markdown ? (
          <div className="chat-markdown">
            <ReactMarkdown>{res}</ReactMarkdown>
          </div>
        ) : (
          res
        )}
      </ScrollArea>
    </div>
  );
}
