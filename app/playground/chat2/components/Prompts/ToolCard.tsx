import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { WrenchIcon, SettingsIcon } from "lucide-react";
import { useState } from "react";
import { ChevronDownIcon, ChevronRightIcon } from "@radix-ui/react-icons";

interface ToolCardProps {
  content: string;
  type: 'tool' | 'tools';
}

interface ToolCall {
  index?: number;
  id?: string;
  type?: string;
  function?: {
    name: string;
    arguments: string;
  };
}

export function ToolCard({ content, type }: ToolCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // 解析tool内容
  const parseToolContent = (content: string) => {
    try {
      // 尝试解析JSON格式的tool_calls
      if (content.trim().startsWith('[') || content.trim().startsWith('{')) {
        const parsed = JSON.parse(content);
        return Array.isArray(parsed) ? parsed : [parsed];
      }
      
      // 如果不是JSON格式，返回原始内容
      return [{ raw: content }];
    } catch (error) {
      console.log('Tool content parse error:', error);
      return [{ raw: content }];
    }
  };

  const toolData = parseToolContent(content);
  
  const getCardColor = (type: string) => {
    switch (type) {
      case 'tool': return 'border-purple-500 bg-purple-50';
      case 'tools': return 'border-indigo-500 bg-indigo-50';
      default: return 'border-gray-500 bg-gray-50';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'tool': return '工具调用';
      case 'tools': return '工具配置';
      default: return '工具';
    }
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'tool': return <WrenchIcon className="h-4 w-4" />;
      case 'tools': return <SettingsIcon className="h-4 w-4" />;
      default: return <WrenchIcon className="h-4 w-4" />;
    }
  };

  return (
    <Card className={`rounded-lg border-l-4 ${getCardColor(type)} mb-2 shadow-sm`}>
      <CardHeader 
        className="pb-2 cursor-pointer hover:bg-opacity-80"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {isExpanded ? (
              <ChevronDownIcon className="w-4 h-4" />
            ) : (
              <ChevronRightIcon className="w-4 h-4" />
            )}
            {getIcon(type)}
            <CardTitle className="text-sm font-medium">
              {getTypeLabel(type)}
            </CardTitle>
            <Badge variant="secondary" className="text-xs">
              {toolData.length} 项
            </Badge>
          </div>
          <span className="text-xs text-muted-foreground">
            点击{isExpanded ? '收起' : '展开'}
          </span>
        </div>
      </CardHeader>
      
      {isExpanded && (
        <CardContent className="pt-0 pb-3">
          <div className="space-y-2">
            {toolData.map((tool: any, index: number) => (
              <div key={index} className="bg-background border border-muted rounded p-3">
                {tool.raw ? (
                  // 显示原始内容
                  <pre className="whitespace-pre-wrap text-xs font-mono text-muted-foreground">
                    {tool.raw}
                  </pre>
                ) : (
                  // 显示结构化的tool call信息
                  <div className="space-y-2">
                    {tool.function && (
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="outline" className="text-xs">
                            {tool.function.name}
                          </Badge>
                          {tool.id && (
                            <span className="text-xs text-muted-foreground">
                              ID: {tool.id}
                            </span>
                          )}
                        </div>
                        {tool.function.arguments && (
                          <div className="mt-2">
                            <div className="text-xs text-muted-foreground mb-1">参数:</div>
                            <pre className="whitespace-pre-wrap text-xs font-mono bg-muted/50 p-2 rounded">
                              {typeof tool.function.arguments === 'string' 
                                ? tool.function.arguments 
                                : JSON.stringify(tool.function.arguments, null, 2)
                              }
                            </pre>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
}
