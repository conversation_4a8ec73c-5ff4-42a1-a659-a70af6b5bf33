import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> as Shadcn<PERSON><PERSON>on } from "@/components/ui/button";
import { TrashIcon, ImagePlus, Link } from "lucide-react";
import { Role } from "../../interface";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChatMessagePart,
  ChatMessagePartType,
} from "@/app/bam/aime/namespaces/trace";
import {
  Upload,
  Button,
  Input,
  Tooltip,
  Modal,
  Collapse,
} from "@arco-design/web-react";
import React, { useEffect, useState, useMemo } from "react";
import { v4 as UUID } from "uuid";
import { UploadItem } from "@arco-design/web-react/es/Upload";
import { toast } from "sonner";
import { ToolCard } from "./ToolCard";

const fileToDataURL = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

export interface MessageItemProps {
  message: string;
  activeKey: string;
  onMessageChange: (message: string) => void;
  role: Role;
  onRoleChange: (role: Role) => void;
  onDelate?: () => void;
  onMultiContentChange: (multiContent: Array<ChatMessagePart>) => void;
}

// 解析消息中的tool标签（不包括tools标签）
const parseToolTags = (content: string) => {
  const toolTags: Array<{ type: "tool"; content: string }> = [];
  let remainingContent = content;

  // 只匹配tool标签，不匹配tools标签
  const toolRegex = /<(tool)\b[^>]*>([\s\S]*?)<\/\1>/gi;
  let match;

  while ((match = toolRegex.exec(content)) !== null) {
    const tagType = match[1].toLowerCase() as "tool";
    const tagContent = match[2].trim();

    toolTags.push({
      type: tagType,
      content: tagContent,
    });

    // 从剩余内容中移除已匹配的标签
    remainingContent = remainingContent.replace(match[0], "");
  }

  return {
    toolTags,
    remainingContent: remainingContent.trim(),
  };
};

export function MessageItem(props: MessageItemProps) {
  const {
    message,
    activeKey,
    onMessageChange,
    role,
    onRoleChange,
    onDelate,
    onMultiContentChange,
  } = props;
  const [isUrlModalVisible, setIsUrlModalVisible] = useState(false);
  const [imageUrl, setImageUrl] = useState("");
  const [imgFileList, setImgFileList] = useState<UploadItem[]>([]);

  // 解析消息中的tool标签
  const parsedContent = useMemo(() => {
    return parseToolTags(message);
  }, [message]);

  useEffect(() => {
    if (imgFileList.length) {
      onMultiContentChange(
        imgFileList.map((item) => ({
          type: ChatMessagePartType.ImageURL,
          image_url: { url: item.url },
        }))
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [imgFileList]);

  const handleUrlConfirm = () => {
    if (!imageUrl.trim()) {
      toast.error("图片链接不能为空");
      return;
    }
    // Simple URL validation
    try {
      new URL(imageUrl);
      setImageUrl("");
      setImgFileList([
        ...imgFileList,
        {
          uid: UUID(),
          name: imageUrl,
          url: imageUrl,
        },
      ]);
      setIsUrlModalVisible(false);
    } catch (error) {
      console.log(error);
      toast.error("请输入有效的图片链接");
    }
  };

  // 获取角色显示标签
  const getRoleLabel = (role: string) => {
    switch (role) {
      case "system":
        return "系统";
      case "assistant":
        return "助手";
      case "user":
        return "用户";
      case "tool":
        return "工具调用";
      default:
        return role;
    }
  };

  return (
    <div className="">
      <Collapse.Item
        name={activeKey}
        className="chat2-item"
        header={
          <div className="flex justify-between items-center w-full">
            <div className="flex items-center">
              <Badge variant="default" className="font-medium text-xs">
                {getRoleLabel(role)}
              </Badge>
              {parsedContent.toolTags.length > 0 && (
                <Badge variant="secondary" className="ml-2 text-xs">
                  {parsedContent.toolTags.length} 个工具调用
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Select value={role} onValueChange={onRoleChange}>
                <SelectTrigger className="w-28 h-8 text-xs">
                  <SelectValue placeholder="选择角色" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="system">系统</SelectItem>
                  <SelectItem value="user">用户</SelectItem>
                  <SelectItem value="assistant">助手</SelectItem>
                  <SelectItem value="tool">工具调用</SelectItem>
                </SelectContent>
              </Select>
              <ShadcnButton
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-muted-foreground hover:text-destructive  group-hover:opacity-100 transition-opacity"
                onClick={() => onDelate?.()}
              >
                <TrashIcon className="h-4 w-4" />
              </ShadcnButton>
            </div>
          </div>
        }
      >
        <div className="relative rounded-lg transition-all group bg-muted/40 border border-border/50 hover:border-border">
          <div className="flex flex-col gap-3">
            {/* 工具标签展示区域 */}
            {parsedContent.toolTags.length > 0 && (
              <div className="space-y-2">
                {parsedContent.toolTags.map((toolTag, index) => (
                  <ToolCard
                    key={index}
                    content={toolTag.content}
                    type={toolTag.type}
                  />
                ))}
              </div>
            )}

            {/* 原有的消息输入区域 */}
            <div className="bg-background border border-muted rounded-md p-2 flex flex-col gap-2">
              <Textarea
                placeholder={`输入${
                  role === "system"
                    ? "系统提示"
                    : role === "assistant"
                    ? "助手回复"
                    : "用户消息"
                }...`}
                value={parsedContent.remainingContent || message}
                onChange={(e) => onMessageChange(e.target.value)}
                className="min-h-[80px] resize-none border-none focus-visible:ring-0"
              />

              <Upload
                showUploadList
                listType="picture-list"
                fileList={imgFileList}
                onRemove={(file) => {
                  setImgFileList(
                    imgFileList.filter((item) => item.uid !== file.uid)
                  );
                }}
                customRequest={async (options) => {
                  const { file, onSuccess } = options;
                  try {
                    const base64 = await fileToDataURL(file as File);
                    setImgFileList([
                      ...imgFileList,
                      {
                        uid: UUID(),
                        name: file.name,
                        url: base64,
                      },
                    ]);
                    onSuccess?.({});
                  } catch (error) {
                    console.log(error);
                    toast.error("图片转换失败");
                  }
                }}
                beforeUpload={(file) => {
                  const isImage = file.type.startsWith("image/");
                  if (!isImage) {
                    toast.error("只能上传图片文件！");
                  }

                  return isImage;
                }}
                multiple
              >
                <div className="flex gap-2 mt-2">
                  <Tooltip content="上传图片">
                    <Button
                      icon={<ImagePlus size={16} />}
                      className="flex justify-center items-center"
                      shape="circle"
                      type="secondary"
                    />
                  </Tooltip>
                  <Tooltip content="图片链接">
                    <Button
                      icon={<Link size={16} />}
                      shape="circle"
                      type="secondary"
                      className="flex justify-center items-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsUrlModalVisible(true);
                      }}
                    />
                  </Tooltip>
                </div>
              </Upload>
            </div>
          </div>
        </div>
      </Collapse.Item>
      <Modal
        title="添加图片链接"
        visible={isUrlModalVisible}
        onOk={handleUrlConfirm}
        onCancel={() => setIsUrlModalVisible(false)}
        autoFocus={false}
        focusLock={true}
      >
        <Input
          value={imageUrl}
          onChange={setImageUrl}
          placeholder="请输入图片链接"
        />
      </Modal>
    </div>
  );
}
