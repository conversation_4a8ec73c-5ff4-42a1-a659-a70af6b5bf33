import React, { useMemo } from "react";
import { GroupInfo } from "../../interface";
import { apiClient } from "@/app/api/request";
import { useQuery } from "@tanstack/react-query";
import { ListModelsResponse, Model } from "@/app/bam/aime/namespaces/trace";
import { uniq } from "lodash-es";

export interface ChatContextProps {
  groups: GroupInfo[];
  setGroups: (groups: GroupInfo[]) => void;
  models: ListModelsResponse["models"];
}

export const ChatContext = React.createContext<ChatContextProps>({
  groups: [],
  setGroups: () => void 0,
  models: [],
});

export function ChatProvider(props: React.PropsWithChildren<object>) {
  const { children } = props;

  const { data } = useQuery({
    queryKey: ["list-models"],
    queryFn: () => apiClient.ListModels({}),
    staleTime: Infinity,
  });

  const models = useMemo(() => {
    const models: Model[] = [];
    for (const model of data?.models ?? []) {
      const exists = models?.find((item) => item.type === model?.type);
      if (!exists) {
        models.push(model);
      } else {
        exists?.models?.push(...(model?.models ?? []));
        exists.models = uniq(exists?.models);
      }
    }
    return models;
  }, [data]);

  return (
    <ChatContext.Provider
      value={{
        groups: [],
        setGroups: () => void 0,
        models,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
}
