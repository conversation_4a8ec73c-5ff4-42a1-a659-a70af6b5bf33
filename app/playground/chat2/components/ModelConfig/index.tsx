// import { Badge, Select } from "@radix-ui/themes";
import { useCallback, useContext } from "react";
import {
  Select,
  Button,
  Popover,
  Form,
  InputNumber,
} from "@arco-design/web-react";
import { ModelOptions } from "../../interface";
import { ChatContext } from "../Provider";
const FormItem = Form.Item;

export interface ModelConfigProps {
  config: ModelOptions;
  onChange: (config: ModelOptions) => void;
}

export function ModelConfig(props: ModelConfigProps) {
  const { config, onChange } = props;
  const { models } = useContext(ChatContext);

  const handleModelChange = useCallback(
    (value: string) => {
      onChange?.({
        ...config,
        model: value,
      });
    },
    [config, onChange]
  );

  // 检查当前选择的模型是否是 gpt-5 开头
  const isGPT5Model = config?.model?.startsWith("gpt-5");

  return (
    <div className="px-3 py-5 rounded-xl bg-white">
      <h1 className="!font-bold !text-sm text-slate-600 rt-r-mb-3">模型选择</h1>
      <div className="flex gap-1">
        <Select
          placeholder="请选择模型"
          showSearch
          value={config?.model}
          onChange={handleModelChange}
        >
          {models.map((model, modelIndex) => (
            <Select.OptGroup
              label={model.type}
              key={`${model?.type}_${modelIndex}`}
            >
              {model?.models?.map((item, index) => (
                <Select.Option
                  key={`${model?.type}_${modelIndex}_${index}`}
                  title={item}
                  value={item}
                >
                  <span className="ml-1">{item}</span>
                </Select.Option>
              ))}
            </Select.OptGroup>
          ))}
        </Select>
        <Popover
          position="bottom"
          className="w-[300px]"
          content={
            <Form layout="vertical" autoComplete="off" className="p-[6px]">
              <FormItem label="温度">
                <InputNumber
                  step={0.1}
                  value={config?.temperature}
                  onChange={(value) => {
                    onChange?.({
                      ...config,
                      temperature: value,
                    });
                  }}
                  // disabled={isGPT5Model}
                />
              </FormItem>
              <FormItem label="最大Token数">
                <InputNumber
                  value={config?.max_tokens}
                  onChange={(value) => {
                    onChange?.({
                      ...config,
                      max_tokens: value,
                    });
                  }}
                />
              </FormItem>
              {isGPT5Model && (
                <FormItem label="reasoning_effort">
                  <Select
                    placeholder="请选择推理程度"
                    value={config?.reasoning_effort || "medium"}
                    onChange={(value) => {
                      onChange?.({
                        ...config,
                        reasoning_effort: value,
                        thinking: { type: value, budget_tokens: -1 },
                      });
                    }}
                  >
                    <Select.Option value="minimal">Minimal</Select.Option>
                    <Select.Option value="low">Low</Select.Option>
                    <Select.Option value="medium">Medium</Select.Option>
                    <Select.Option value="high">High</Select.Option>
                  </Select>
                </FormItem>
              )}
            </Form>
          }
        >
          <Button type="outline">参数配置</Button>
        </Popover>
      </div>
    </div>
  );
}
