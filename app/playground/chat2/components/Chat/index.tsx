import { use<PERSON><PERSON>back, useState, useEffect } from "react";
import { SendIcon, SlidersIcon, RefreshCwIcon } from "lucide-react";
import { GroupInfo, ModelOptions } from "../../interface";
import { Messages } from "../Prompts";
import { ModelConfig } from "../ModelConfig";
import { FunctionManager, Tool } from "../FunctionManager";
import { Threads } from "../History";
import {
  ChatCompletionMessage,
  ChatMessagePartType,
  ChatToolCall,
} from "@/app/bam/aime/namespaces/trace";
import { Button } from "@/components/ui/button";
import { IconDelete } from "@arco-design/web-react/icon";
import { apiClient } from "@/app/api/request";
import { toast } from "sonner";
import axios from "axios";

// 解析带有<image_url>和<text>标签的内容
const parseContentWithTags = (
  content: string,
): Array<{ type: string; content: string }> => {
  const parts: Array<{ type: string; content: string }> = [];

  // 使用正则表达式匹配<image_url>和<text>标签
  const regex = /<(image_url|text)>([\s\S]*?)<\/\1>/g;
  let match;

  while ((match = regex.exec(content)) !== null) {
    const type = match[1];
    // 去除内容中的前后空格
    const tagContent = match[2].trim();

    if (type === "image_url") {
      // 移除方括号并获取真实链接
      const url = tagContent.replace(/^\[(.*)\]$/, "$1");
      parts.push({ type, content: url });
    } else {
      parts.push({ type, content: tagContent });
    }
  }

  return parts;
};

// 将图片URL转换为base64格式
const urlToBase64 = async (url: string): Promise<string> => {
  try {
    if (!url) {
      return "";
    }

    let targetUrl = url;
    if (url.startsWith("http://")) {
      targetUrl = url.replace("http://", "https://");
    }

    const response = await axios.get(targetUrl, {
      responseType: "arraybuffer",
    });
    const contentType = response.headers["content-type"];
    const base64 = Buffer.from(response.data, "binary").toString("base64");
    return `data:${contentType};base64,${base64}`;
  } catch (error) {
    console.error("Failed to convert URL to base64:", error);
    throw new Error("Failed to convert image URL to base64");
  }
};

/**
 * 提取 <tool_calls>...</tool_calls> 中的内容，并删除原字符串中的这部分
 * @param {string} input 原始字符串
 * @returns {{ extracted: string, cleaned: string }}
 */
function extractAndRemoveToolCalls(input: string) {
  const regex = /<tool_calls>([\s\S]*?)<\/tool_calls>/;
  const match = input.match(regex);
  let extracted = "";
  let cleaned = input;
  if (match) {
    extracted = match[1].trim();
    // 删除整个 <tool_calls>...</tool_calls> 包括标签本身
    cleaned = input.replace(regex, "").trim();
  }
  return { extracted: extracted && JSON.parse(extracted), cleaned };
}

interface IChatResponse {
  choices: {
    index: number;
    message: {
      content?: string;
      tool_calls?: ChatToolCall[];
    };
  }[];
}

export function ChatGroupItem(props: {
  info: GroupInfo;
  onChange: (info: GroupInfo) => void;
  onDel: () => void;
  index: number;
  showDelete?: boolean;
  autoInsertAssistantMessage?: boolean;
  autoSend?: boolean;
  onAutoSendComplete?: () => void;
  hideResponse?: boolean;
  onCreateEvaluationSet?: () => void;
}) {
  const {
    info,
    onChange,
    showDelete,
    onDel,
    index = 0,
    autoInsertAssistantMessage = false,
    autoSend = false,
    onAutoSendComplete,
    hideResponse = false,
    onCreateEvaluationSet,
  } = props;
  const [isLoading, setIsLoading] = useState(false);

  const handleMessageChange = useCallback(
    (message: ChatCompletionMessage[]) => {
      // Fix: do not mutate info directly, create a new object
      onChange({
        ...info,
        data: {
          ...info.data,
          messages: message,
        },
      });
    },
    [info, onChange],
  );

  const modelOptions: ModelOptions = {
    model: info.data.model,
    max_tokens: info.data.max_tokens,
    temperature: info.data.temperature,
    top_p: info.data.top_p,
    thinking: info.data.thinking,
    reasoning_effort: info.data.reasoning_effort,
  };

  const handleModelChange = useCallback(
    (config: ModelOptions) => {
      onChange({
        ...info,
        data: {
          ...info.data,
          ...config,
        },
      });
    },
    [info, onChange],
  );

  // 处理工具配置变更
  const handleToolsChange = useCallback(
    (tools: Tool[]) => {
      onChange({
        ...info,
        data: {
          ...info.data,
          tools: {
            ...info.data.tools,
            data: tools.map(tool => ({
              type: tool.type,
              function: tool.function
                ? {
                    name: tool.function.name,
                    description: tool.function.description,
                    strict: tool.function.strict,
                    parameters: tool.function.parameters,
                  }
                : undefined,
            })),
          } as any,
        },
      });
    },
    [info, onChange],
  );

  const handSend = useCallback(async () => {
    if (isLoading) return;
    setIsLoading(true);

    // 处理消息，检查是否包含特殊标签
    const messages = await Promise.all(
      info.data.messages.map(async item => {
        // 检查是否已有multi_content
        if (item?.multi_content) {
          return {
            ...item,
            content: undefined,
            multi_content: [
              ...item.multi_content,
              ...(item.content
                ? [
                    {
                      type: ChatMessagePartType.Text,
                      text: item.content,
                    },
                  ]
                : []),
            ],
          };
        }

        // 检查content是否包含特殊标签
        if (
          item?.content &&
          (item.content.includes("<image_url>") ||
            item.content.includes("<text>"))
        ) {
          try {
            // 解析带标签的内容
            const parts = parseContentWithTags(item.content);

            // 转换为multi_content格式
            const multiContent = await Promise.all(
              parts.map(async part => {
                if (part.type === "image_url") {
                  try {
                    // 将图片URL转换为base64
                    const base64Url = await urlToBase64(part.content);
                    if (!base64Url) {
                      return false;
                    }
                    return {
                      type: ChatMessagePartType.ImageURL,
                      image_url: { url: base64Url },
                    };
                  } catch (error) {
                    console.error(
                      "Failed to convert image URL to base64:",
                      error,
                    );
                    toast.error("图片转换失败");
                    // 如果转换失败，仍然使用原始URL
                    return {
                      type: ChatMessagePartType.ImageURL,
                      image_url: { url: part.content },
                    };
                  }
                } else {
                  return {
                    type: ChatMessagePartType.Text,
                    text: part.content,
                  };
                }
              }),
            );

            // 返回转换后的消息
            return {
              ...item,
              content: undefined,
              multi_content: multiContent.filter(item => !!item),
            };
          } catch (error) {
            console.error("Failed to parse content with tags:", error);
            toast.error("消息解析失败");
            return item;
          }
        }
        const { extracted, cleaned } = extractAndRemoveToolCalls(
          item.content || "",
        );
        item.content = cleaned;
        if (extracted) {
          item.tool_calls = extracted;
        }

        return item;
      }),
    );

    try {
      // 构建API请求，包含工具配置
      const requestData: any = {
        model: info.data.model,
        messages,
        max_tokens: info.data.max_tokens,
        temperature: info.data.temperature,
      };

      // 只有 GPT-5 模型才需要 reasoning_effort 和 thinking 参数
      if (info.data.model?.startsWith("gpt-5")) {
        requestData.reasoning_effort = info.data.reasoning_effort;
        requestData.thinking = info.data.thinking;
      }

      const toolsData = ((info.data.tools as any)?.data as Tool[]) || [];
      // 如果有工具配置，添加到请求中
      if (toolsData.length > 0) {
        const tools = toolsData?.map(item => {
          return {
            ...item,
            function: {
              ...item.function!,
              parameters: JSON.stringify(item.function?.parameters || {}),
            },
          };
        });

        requestData.tools = tools;
      }

      const res = await apiClient.ChatStream(requestData);
      const mergedChoices: Record<number, IChatResponse["choices"][0]> = {};
      // 兼容 gpt 平台接口 bug: 可能存在多个 choices（依赖过多，平台侧不作修复）
      for (const choice of (res as IChatResponse)?.choices) {
        if (!mergedChoices[choice.index]) {
          mergedChoices[choice.index] = choice;
        } else {
          const existingChoice = mergedChoices[choice.index];
          if (choice.message.content) {
            if (existingChoice.message.content) {
              existingChoice.message.content += choice.message.content;
            } else {
              existingChoice.message.content = choice.message.content;
            }
          }
          if (choice.message.tool_calls?.length) {
            existingChoice.message.tool_calls = [
              ...(existingChoice.message.tool_calls || []),
              ...choice.message.tool_calls,
            ];
          }
        }
      }
      if (mergedChoices[0]?.message?.tool_calls) {
        (
          res as IChatResponse
        ).choices[0].message.content = `<tool_calls>\n${JSON.stringify(
          mergedChoices[0].message.tool_calls,
          null,
          2,
        )}\n</tool_calls>\n${mergedChoices[0].message.content || ""}`;
      }
      const assistantContent =
        (res as IChatResponse)?.choices?.[0]?.message?.content || "";

      if (autoInsertAssistantMessage) {
        const newMessages = [...messages];
        newMessages.push({
          role: "assistant",
          content: assistantContent,
        });
        onChange({
          ...info,
          data: {
            ...info.data,
            messages: newMessages,
          },
          response: assistantContent,
        });
      } else {
        // 只更新 response 字段
        onChange({
          ...info,
          response: assistantContent,
        });
      }
    } catch (error) {
      console.log(error);
      toast.error("发送失败！");
    }

    setIsLoading(false);
  }, [info, onChange, isLoading, autoInsertAssistantMessage]);

  // 自动发送逻辑
  useEffect(() => {
    if (autoSend && !isLoading) {
      handSend().then(() => {
        onAutoSendComplete?.();
      });
    }
  }, [autoSend, isLoading]);

  // 获取当前工具配置，转换为FunctionManager需要的格式
  const currentTools: Tool[] = (info.data.tools as any)?.data || [];

  return (
    <div className="flex-1 flex flex-col gap-3 relative min-w-[400px]">
      <div className="lg:col-span-3 flex flex-col rounded-xl border border-border/60 shadow-sm overflow-hidden bg-card">
        <div className="flex items-center justify-between px-4 py-3 bg-muted/30 border-b">
          <div className="flex items-center">
            <SlidersIcon className="h-5 w-5 mr-2 text-primary" />
            <h2 className="font-semibold">配置</h2>
          </div>
          <div className="text-xs text-muted-foreground">
            添加系统提示、用户消息、模型配置等
          </div>
        </div>

        {/* 模型配置 */}
        <ModelConfig config={modelOptions} onChange={handleModelChange} />

        {/* 函数管理 - 新增的功能 */}
        <FunctionManager tools={currentTools} onChange={handleToolsChange} />

        {/* 对话消息 */}
        <Messages
          messages={info.data?.messages}
          onChange={handleMessageChange}
        />

        <div className="flex items-center justify-between p-4 border-t bg-muted/20">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              className="text-sm h-9"
              onClick={() => {
                onChange({
                  ...info,
                  data: {
                    ...info.data,
                    messages: [],
                  },
                });
              }}
            >
              清空消息
            </Button>
            {onCreateEvaluationSet && (
              <Button
                variant="outline"
                className="text-sm h-9"
                onClick={onCreateEvaluationSet}
              >
                创建评测集
              </Button>
            )}
            <Button className="text-sm h-9" onClick={handSend}>
              {isLoading ? (
                <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <SendIcon className="mr-2 h-4 w-4" />
              )}
              {isLoading ? "发送中..." : "发送"}
            </Button>
          </div>
        </div>
      </div>
      {!hideResponse && (
        <Threads
          res={info?.response}
          markdown={info?.groupName === "diagnose"}
        />
      )}
      {showDelete ? (
        <div className="absolute top-[-40px] left-[50%] bg-amber-50 h-[34px] text-24 p-[6px] rounded-2xl flex gap-[6px] items-center pl-[10px] pr-[10px]">
          Chat Group {index + 1}
          <div
            onClick={onDel}
            className="text-[rgb(0, 121, 107)] w-[24px] h-[24px] flex items-center justify-center border rounded-2xl border-[#00796B] bg-green-200 cursor-pointer"
          >
            <IconDelete />
          </div>
        </div>
      ) : null}
    </div>
  );
}
