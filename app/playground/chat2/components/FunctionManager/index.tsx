import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { JSONEditor } from "@/components/ui/json-editor";
import { SettingsIcon, TrashIcon, WrenchIcon, PenLineIcon } from "lucide-react";
import { useState, useCallback } from "react";
import { toast } from "sonner";
import { Modal } from "@arco-design/web-react";

// 工具定义接口
export interface Tool {
  type: string;
  function?: {
    name: string;
    description?: string;
    strict?: boolean;
    parameters: string; // JSON字符串格式的参数定义
  };
}

interface FunctionManagerProps {
  tools: Tool[];
  onChange: (tools: Tool[]) => void;
  // 新增：对话内容，用于解析tools标签
  conversationContent?: string;
}

// 默认工具模板
const DEFAULT_TOOL: Tool = {
  type: "function",
  function: {
    name: "example_function",
    description: "示例函数描述",
    strict: false,
    parameters: JSON.stringify(
      {
        type: "object",
        properties: {
          param1: {
            type: "string",
            description: "参数1描述",
          },
        },
        required: ["param1"],
      },
      null,
      2
    ),
  },
};

// 单个工具配置卡片
function ToolConfigCard({
  tool,
  onUpdate,
  onDelete,
}: {
  tool: Tool;
  onUpdate: (tool: Tool) => void;
  onDelete: () => void;
}) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [editingTool, setEditingTool] = useState<Tool>({
    ...tool,
    function: {
      ...tool.function!,
      parameters: JSON.stringify(tool.function?.parameters || {}),
    },
  });

  const handleSave = useCallback(() => {
    // 验证JSON格式
    if (editingTool.function?.parameters) {
      try {
        JSON.parse(editingTool.function.parameters);
      } catch (error) {
        console.log(error);
        toast.error("参数JSON格式不正确");
        return;
      }
    }
    setIsExpanded(false);
    onUpdate(editingTool);
    toast.success("工具配置已保存");
  }, [editingTool, onUpdate]);

  const handleCancel = useCallback(() => {
    setEditingTool({
      ...tool,
      function: {
        ...tool.function!,
        parameters: JSON.stringify(tool.function?.parameters || {}),
      },
    });
    setIsExpanded(false);
  }, [tool]);

  const handleParametersChange = useCallback((value: string) => {
    setEditingTool((prev) => ({
      ...prev,
      function: {
        ...prev.function!,
        parameters: value,
      },
    }));
  }, []);

  return (
    <Card className="border border-border/60 shadow-sm py-3">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <WrenchIcon className="h-4 w-4 text-primary" />
            <CardTitle className="text-sm font-medium">
              {tool.function?.name || "未命名工具"}
            </CardTitle>
            <Badge variant="secondary" className="text-xs">
              {tool.type}
            </Badge>
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <PenLineIcon className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 text-destructive hover:text-destructive"
              onClick={onDelete}
            >
              <TrashIcon className="h-3 w-3" />
            </Button>
          </div>
        </div>
        {tool.function?.description && (
          <p className="text-xs text-muted-foreground mt-1">
            {tool.function.description}
          </p>
        )}
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium text-muted-foreground">
                函数名称
              </label>
              <input
                type="text"
                className="w-full mt-1 px-2 py-1 text-sm border border-border rounded"
                value={editingTool.function?.name || ""}
                onChange={(e) =>
                  setEditingTool((prev) => ({
                    ...prev,
                    function: {
                      ...prev.function!,
                      name: e.target.value,
                    },
                  }))
                }
              />
            </div>

            <div>
              <label className="text-xs font-medium text-muted-foreground">
                描述
              </label>
              <input
                type="text"
                className="w-full mt-1 px-2 py-1 text-sm border border-border rounded"
                value={editingTool.function?.description || ""}
                onChange={(e) =>
                  setEditingTool((prev) => ({
                    ...prev,
                    function: {
                      ...prev.function!,
                      description: e.target.value,
                    },
                  }))
                }
              />
            </div>

            <div>
              <label className="text-xs font-medium text-muted-foreground">
                参数定义 (JSON)
              </label>
              <div className="mt-1 border border-border rounded">
                <JSONEditor
                  value={editingTool?.function?.parameters || "{}"}
                  onChange={handleParametersChange}
                  height="200px"
                />
              </div>
            </div>

            <div className="flex items-center gap-2 pt-2">
              <Button size="sm" onClick={handleSave}>
                保存
              </Button>
              <Button variant="outline" size="sm" onClick={handleCancel}>
                取消
              </Button>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}

export function FunctionManager({ tools, onChange }: FunctionManagerProps) {
  const [showAddModal, setShowAddModal] = useState(false);
  const [newTool, setNewTool] = useState<Tool>(DEFAULT_TOOL);

  const handleConfirmAdd = useCallback(() => {
    // 验证JSON格式
    if (newTool.function?.parameters) {
      try {
        JSON.parse(newTool.function.parameters);
      } catch (error) {
        toast.error("参数JSON格式不正确");
        return;
      }
    }

    onChange([...tools, newTool]);
    setShowAddModal(false);
    toast.success("工具已添加");
  }, [tools, newTool, onChange]);

  const handleUpdateTool = useCallback(
    (index: number, updatedTool: Tool) => {
      const newTools = [...tools];
      newTools[index] = updatedTool;
      onChange(newTools);
    },
    [tools, onChange]
  );

  const handleDeleteTool = useCallback(
    (index: number) => {
      const newTools = tools.filter((_, i) => i !== index);
      onChange(newTools);
      toast.success("工具已删除");
    },
    [tools, onChange]
  );

  const handleNewToolParametersChange = useCallback((value: string) => {
    setNewTool((prev) => ({
      ...prev,
      function: {
        ...prev.function!,
        parameters: value,
      },
    }));
  }, []);

  return (
    <div className="bg-white rounded-xl px-3 py-2">
      <Card className="rounded-xl border border-border/60 shadow-sm overflow-hidden bg-card py-0 gap-0">
        <CardHeader className="px-4 py-3 bg-muted/30 border-b !pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <SettingsIcon className="h-5 w-5 mr-2 text-primary" />
              <CardTitle className="font-semibold">函数</CardTitle>
              <Badge variant="secondary" className="ml-2 text-xs">
                {tools?.length} 个工具
              </Badge>
            </div>
            <Button
              size="sm"
              onClick={() => setShowAddModal(true)}
            >
              添加工具
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-4">
          {/* 现有工具列表 */}
          {tools?.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <SettingsIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p className="text-sm">暂无工具配置</p>
              <p className="text-xs mt-1">点击"添加工具"开始配置函数调用</p>
            </div>
          ) : (
            <div className="space-y-3 max-h-[450px] overflow-auto">
              {tools?.map?.((tool, index) => (
                <ToolConfigCard
                  key={index}
                  tool={tool}
                  onUpdate={(updatedTool) =>
                    handleUpdateTool(index, updatedTool)
                  }
                  onDelete={() => handleDeleteTool(index)}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 添加工具模态框 */}
      <Modal
        title="添加新工具"
        visible={showAddModal}
        onOk={handleConfirmAdd}
        onCancel={() => setShowAddModal(false)}
        autoFocus={false}
        focusLock={true}
        style={{ width: 600 }}
      >
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              函数名称
            </label>
            <input
              type="text"
              className="w-full mt-1 px-3 py-2 border border-border rounded"
              value={newTool.function?.name || ""}
              onChange={(e) =>
                setNewTool((prev) => ({
                  ...prev,
                  function: {
                    ...prev.function!,
                    name: e.target.value,
                  },
                }))
              }
              placeholder="输入函数名称"
            />
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">
              描述
            </label>
            <input
              type="text"
              className="w-full mt-1 px-3 py-2 border border-border rounded"
              value={newTool.function?.description || ""}
              onChange={(e) =>
                setNewTool((prev) => ({
                  ...prev,
                  function: {
                    ...prev.function!,
                    description: e.target.value,
                  },
                }))
              }
              placeholder="输入函数描述"
            />
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">
              参数定义 (JSON Schema)
            </label>
            <div className="mt-1 border border-border rounded">
              <JSONEditor
                value={newTool.function?.parameters || "{}"}
                onChange={handleNewToolParametersChange}
                height="200px"
              />
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
}
