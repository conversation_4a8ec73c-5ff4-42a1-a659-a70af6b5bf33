"use client";
import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { ChatGroupItem } from "./components/Chat";
import { GroupInfo } from "./interface";
import { v4 as uuid } from "uuid";
import { Chat<PERSON><PERSON>ider } from "./components/Provider";
import { PageHeader } from "@/app/components/PageHeader";
import { PlusIcon } from "lucide-react";
import { Tooltip } from "@arco-design/web-react";
import { $chatData, CHAT_DATA_STORAGE_KEY } from "@/app/store/playground";
import { useStore } from "@nanostores/react";
import { useRouter } from "next/navigation";
import { Message } from "@arco-design/web-react";

const defaultGroup: GroupInfo = {
  groupName: "default",
  groupId: uuid(),
  data: {
    model: "",
    messages: [],
    tools: [],
    reasoning_effort: "medium",
    thinking: { type: "medium", budget_tokens: -1 },
  },
};

export default function Threads() {
  const router = useRouter();
  const [groups, setGroups] = useState<GroupInfo[]>(() => {
    const cache = localStorage.getItem(CHAT_DATA_STORAGE_KEY);
    if (cache) {
      try {
        const parsed = JSON.parse(cache);
        if (Array.isArray(parsed)) {
          return parsed;
        }
      } catch {
        // ignore
      }
    }
    return [defaultGroup];
  });

  const chatData = useStore($chatData);

  const chatGroup = useMemo(() => {
    const data =
      chatData ||
      JSON.parse(localStorage.getItem(CHAT_DATA_STORAGE_KEY) || "{}");
    if (data) {
      localStorage.setItem(CHAT_DATA_STORAGE_KEY, JSON.stringify(data));
      const metadata = JSON.parse(data.metadata || "{}");
      return {
        groupName: "default",
        groupId: uuid(),
        data: {
          model: data.model_name,
          messages: data.messages,
          max_tokens: metadata.max_tokens,
          temperature: metadata.temperature,
          top_p: metadata.top_p,
          reasoning_effort: metadata.reasoning_effort,
          thinking: metadata.thinking,
          tools: data.tools,
        },
        response: data.response,
      };
    }
    return null;
  }, [chatData]);

  useEffect(() => {
    if (chatData && chatGroup) {
      setGroups([chatGroup]);
    }
  }, [chatData, chatGroup]);

  useEffect(() => {
    const data = groups.map((item) => ({
      ...item,
      data: {
        ...item.data,
        messages: item.data.messages.map((msg) => ({
          ...msg,
          multi_content: undefined,
        }))
      },
    }));
    localStorage.setItem(CHAT_DATA_STORAGE_KEY, JSON.stringify(data));
  }, [groups]);

  const handleChange = useCallback(
    (group: GroupInfo) => {
      const index = groups.findIndex((g) => g.groupId === group.groupId);
      if (index > -1) {
        const newGroups = groups?.slice();
        newGroups[index] = group;
        setGroups(newGroups);
      }
    },
    [groups]
  );

  const handleAdd = useCallback(() => {
    const newGroups = groups?.slice();
    newGroups.push({
      ...(chatGroup || defaultGroup),
      groupId: new Date().getTime().toString(),
    });
    setGroups(newGroups);
  }, [groups, chatGroup]);

  const handleDel = useCallback(
    (group: GroupInfo) => {
      const index = groups.findIndex((g) => g.groupId === group.groupId);
      if (index > -1) {
        const newGroups = groups?.slice();
        newGroups.splice(index, 1);
        setGroups(newGroups);
      }
    },
    [groups]
  );

  // 创建评测集
  const handleCreateEvaluationSet = useCallback(() => {
    // 直接跳转到评测集表单页面，带上缓存标识参数
    router.push('/job/case-form?isFromCache=true');
  }, [router]);

  return (
    <ChatProvider>
      {/* 页面标题和清空缓存按钮 */}
      <div style={{ display: "flex", alignItems: "center", gap: 16 }}>
        <PageHeader
          title="Chat Playground"
          description="与 AI 模型进行对话和测试"
        />
        <button
          style={{
            height: 36,
            padding: "0 16px",
            border: "1px solid #00796b",
            borderRadius: 6,
            background: "#fff",
            color: "#00796b",
            cursor: "pointer",
          }}
          onClick={() => {
            localStorage.removeItem(CHAT_DATA_STORAGE_KEY);
            setGroups([defaultGroup]);
          }}
        >
          清空缓存
        </button>
         <button
          style={{
            height: 36,
            padding: "0 16px",
            border: "1px solid #00796b",
            borderRadius: 6,
            background: "#fff",
            color: "#00796b",
            cursor: "pointer",
          }}
          onClick={() => {
           handleCreateEvaluationSet();
          }}
        >
          创建评测集
        </button>
      </div>

      <div className="p-6 flex flex-nowrap gap-4 overflow-auto pt-[50px] !overscroll-auto">
        {groups.map((group, index) => {
          return (
            <ChatGroupItem
              key={index}
              index={index}
              info={group}
              showDelete={index > 0}
              onDel={() => handleDel(group)}
              onChange={(group) => handleChange(group)}
              onCreateEvaluationSet={index === 0 ? handleCreateEvaluationSet : undefined}
            />
          );
        })}
        <Tooltip content="新增 Playground">
          <div
            onClick={handleAdd}
            className="flex justify-center min-w-[80px] h-[80px] items-center rounded-[8px] border  border-[#00796b] cursor-pointer"
          >
            <PlusIcon />
          </div>
        </Tooltip>
      </div>
    </ChatProvider>
  );
}
