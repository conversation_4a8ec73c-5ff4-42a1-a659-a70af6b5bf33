import { BadgeProps } from "@radix-ui/themes";

export const badgeColors: BadgeProps["color"][] = [
  "gray",
  "gold",
  "bronze",
  "brown",
  "yellow",
  "amber",
  "orange",
  "tomato",
  "red",
  "ruby",
  "crimson",
  "pink",
  "plum",
  "purple",
  "violet",
  "iris",
  "indigo",
  "blue",
  "cyan",
  "teal",
  "jade",
  "green",
  "grass",
  "lime",
  "mint",
  "sky",
];

// 扩展后的parseRoleContentFromTags方法，支持tool和tools标签
export function parseRoleContentFromTags(str: string) {
  // 扩展正则表达式以支持tool和tools标签
  const regex =  /<(system|user|assistant|tool|tools)([^>]*)>([\s\S]*?)<\/\1>/g;

  const result = [];
  let match;
  try {
    while ((match = regex.exec(str)) !== null) {
      const role = match[1];
      const attrStr = match[2]; // 属性字符串
      const content = match[3];
      // 默认结构
      const item: any = {
        role,
        content,
      };
      // 仅当是 tool 标签时，尝试提取 tool_call_id
      if (role === 'tool') {
        
        // 匹配 tool_call_id="xxx" 或 tool_call_id='xxx'
        const toolCallIdMatch = attrStr.match(/tool_call_id\s*=\s*['"]([^'"]+)['"]/);
        if (toolCallIdMatch) {
          item.tool_call_id = toolCallIdMatch[1];
        }
      }
      result.push(item);
    }
  } catch (error) {
    console.log(error);
  }

  return result;
}

// 专门解析tools标签内容的方法
export function parseToolsFromContent(content: string) {
  const toolsArray: any[] = [];
  
  try {
    // 匹配tools标签
    const toolsRegex = /<tools\b[^>]*>([\s\S]*?)<\/tools>/gi;
    let match;
    
    while ((match = toolsRegex.exec(content)) !== null) {
      const toolsContent = match[1].trim();
      
      if (toolsContent) {
        try {
          // 尝试解析JSON内容
          const parsedTools = JSON.parse(toolsContent);
          
          // 如果是数组，直接添加
          if (Array.isArray(parsedTools)) {
            toolsArray.push(...parsedTools);
          } else {
            // 如果是单个对象，包装成数组
            toolsArray.push(parsedTools);
          }
        } catch (jsonError) {
          console.warn('Failed to parse tools JSON:', jsonError);
          // 如果JSON解析失败，尝试作为单个工具对象处理
          toolsArray.push({
            type: "function",
            function: {
              name: "parsed_tool",
              description: "从tools标签解析的工具",
              parameters: toolsContent
            }
          });
        }
      }
    }
  } catch (error) {
    console.error('Error parsing tools from content:', error);
  }
  
  return toolsArray;
}
