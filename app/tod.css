.tod-action-group {
  display: inline-flex;
}
.tod-action-group-space {
  display: inline-flex;
  align-items: center;
  margin-right: 12px;
}
.tod-action-group-space:last-child {
  margin-right: 0;
}
.tod-action-group--small .tod-action-group-space,
.tod-action-group--mini .tod-action-group-space {
  margin-right: 8px;
}
.tod-action-group--small .tod-action-group-space:last-child,
.tod-action-group--mini .tod-action-group-space:last-child {
  margin-right: 0;
}
.avatar-common-style {
  flex-shrink: 0;
  align-items: center;
  flex-wrap: nowrap;
}
.tod-base-avatar,
.tod-avatar {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  flex-wrap: nowrap;
}
.tod-base-avatar .tod-pure-avatar,
.tod-avatar .tod-pure-avatar {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  flex-wrap: nowrap;
}
.tod-base-avatar-group,
.tod-avatar-group {
  flex-shrink: 0;
  align-items: center;
  flex-wrap: nowrap;
}
.tod-base-avatar-group.arco-volc3-avatar-group,
.tod-avatar-group.arco-volc3-avatar-group,
.tod-base-avatar-group.arco-avatar-group,
.tod-avatar-group.arco-avatar-group {
  display: flex;
}
.tod-base-avatar-group-popover .arco-volc3-avatar-group-popover > .tod-avatar,
.tod-avatar-group-popover .arco-volc3-avatar-group-popover > .tod-avatar,
.tod-base-avatar-group-popover .arco-avatar-group-popover > .tod-avatar,
.tod-avatar-group-popover .arco-avatar-group-popover > .tod-avatar {
  margin-bottom: 4px;
}
.tod-base-avatar-group-popover .arco-volc3-avatar-group-popover > .tod-avatar-department,
.tod-avatar-group-popover .arco-volc3-avatar-group-popover > .tod-avatar-department,
.tod-base-avatar-group-popover .arco-avatar-group-popover > .tod-avatar-department,
.tod-avatar-group-popover .arco-avatar-group-popover > .tod-avatar-department {
  display: block;
  margin-bottom: 4px;
}
.tod-base-avatar-group-mix,
.tod-avatar-group-mix {
  display: flex;
  align-items: center;
}
.tod-base-avatar-group-mix .tod-avatar-group > .tod-avatar:last-of-type .rd-overflow-text,
.tod-avatar-group-mix .tod-avatar-group > .tod-avatar:last-of-type .rd-overflow-text {
  display: none;
}
.tod-base-avatar-group-mix-fake,
.tod-avatar-group-mix-fake {
  margin-left: 4px;
}
.tod-base-avatar-group-mix-fake .tod-avatar,
.tod-avatar-group-mix-fake .tod-avatar {
  display: none;
}
.tod-base-avatar-department,
.tod-avatar-department {
  display: inline-block;
  padding: 1px 6px 1px 1px;
  border: 1px solid #eaedf1;
  border-radius: 30px;
}
.tod-base-avatar .rd-overflow-text-desc,
.tod-avatar .rd-overflow-text-desc {
  color: var(--color-fill-2);
}
.tod-base-avatar .tod-pure-avatar .arco-avatar-text {
  color: var(--color-white);
  text-align: center;
  vertical-align: middle;
  white-space: pre;
}
.tod-avatar-department .arco-avatar-text {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tod-avatar-user .arco-avatar-image {
  line-height: initial !important;
}
.tod-avatar-tooltip-username {
  color: var(--color-text-4);
}
.tod-avatar-tooltip-node.tod-avatar {
  display: inline-flex;
}
.tod-avatar-tooltip-spin {
  display: inline;
}
.tod-avatar-tooltip-spin .arco-spin-children::after {
  background-color: transparent;
}
.tod-avatar-tooltip-content {
  display: flex;
  width: auto;
  transition: all 1000ms ease-out;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.tod-avatar-tooltip-content.loading {
  max-width: 50px;
}
.arco-popover .tod-avatar-department {
  border: none;
  padding: 0;
}
.tod-avatar-department.no-border {
  border: none;
  padding: 0;
}
.tod-avatar-username {
  color: var(--color-text-2);
}
.tod-avatar-user-lark {
  cursor: pointer;
}
.tod-avatar-user-lark .tod-overflow-text {
  cursor: pointer;
}
.tod-avatar-user-lark:hover .tod-overflow-text,
.tod-avatar-user-lark:hover .tod-avatar-username {
  color: rgb(var(--primary-6));
}
.arco-avatar-group.tod-avatar-group {
  position: relative;
  z-index: 0;
  line-height: initial;
}
.arco-avatar-group.tod-avatar-group .arco-avatar-group-max-count-avatar {
  border: 1px solid var(--color-border-2);
  color: var(--color-text-2);
  background: #fff;
  font-weight: 500;
  border-radius: 20px;
}
.tod-avatar-popover .arco-avatar-group-popover {
  display: flex;
  flex-direction: column;
}
.ve-o-avatar-group-popover .arco-avatar-group-popover > .tod-avatar-department {
  margin-bottom: 6px;
}
.ve-o-avatar-group-popover .arco-avatar-group-popover > .tod-avatar-department:last-child {
  margin-bottom: 0;
}
.ve-o-avatar-group-popover .arco-avatar-group-popover > .tod-avatar-user {
  margin-bottom: 6px;
}
.ve-o-avatar-group-popover .arco-avatar-group-popover > .tod-avatar-user:last-child {
  margin-bottom: 0;
}
.tod-avatar-popover .arco-popover-content {
  color: var(--color-text-1);
  overflow: auto;
  padding: 8px 16px;
}
.tod-avatar-popover .arco-popover-content .ve-o-avatar:last-child {
  margin-bottom: 0;
}
.tod-avatar-popover.mini .arco-popover-content {
  font-size: 12px;
  max-height: calc(20px * 5 + 16px);
}
.tod-avatar-popover.small .arco-popover-content {
  font-size: 13px;
  max-height: calc(24px * 5 + 16px);
}
.tod-avatar-popover.default .arco-popover-content {
  font-size: 13px;
  max-height: calc(28px * 5 + 16px);
}
.tod-avatar-popover.large .arco-popover-content {
  font-size: 16px;
  max-height: calc(44px * 5 + 16px);
}
.tod-avatar-border {
  display: inline-flex;
  align-items: center;
  padding: 1px 6px 1px 1px;
  border: 1px solid var(--color-border-2);
  border-radius: 30px;
  background-color: var(--color-bg-2);
}
.tod-avatar-user-lark-tenant-tag {
  margin-left: 5px;
}
.tod-byte-tree-selector mark {
  background-color: transparent;
}
.tod-byte-tree-selector-tip {
  color: rgb(var(--gray-8));
  background: rgb(var(--link-1));
  position: absolute;
  top: 0;
  z-index: 10000;
  left: 0;
  right: 0;
  padding: 4px 10px;
}
.tod-byte-tree-selector-tip .icon {
  color: rgb(var(--arcoblue-6));
  padding-right: 4px;
  font-size: 18px;
  padding-top: 5px;
}
.tod-byte-tree-selector-tree > div > div {
  overflow-x: auto;
}
.tod-byte-tree-selector-tree .arco-tree-node-title:hover {
  background-color: var(--color-bg-4);
}
.tod-byte-tree-selector-tree .arco-tree-node .arco-tree-node-title-text {
  width: max-content;
  display: block;
}
.tod-byte-tree-selector-tree .arco-tree-node .arco-tree-node-title-text .tod-byte-tree-selector-node .high-light-node {
  color: rgb(var(--gray-6));
}
.tod-byte-tree-selector-tree .arco-tree-node .arco-tree-node-title-text .tod-byte-tree-selector-node .high-light-node .divider {
  display: inline-block;
  width: 2px;
  margin: 0 4px;
  color: rgb(var(--gray-4));
  font-weight: bolder;
}
.tod-byte-tree-selector-tree .arco-tree-node .arco-tree-node-title-text .tod-byte-tree-selector-node .high-light-node XByteTreeSelectorMark {
  background-color: yellow;
  color: rgb(var(--gray-10));
  font-weight: 500;
}
.tod-byte-tree-selector-tree .arco-tree-node {
  align-items: center;
}
.tod-byte-tree-selector-tree .arco-tree-node .flavor-extra {
  margin-right: 12px;
}
.tod-byte-tree-selector-tree .arco-tree-node .star {
  opacity: 0;
  pointer-events: none;
}
.tod-byte-tree-selector-tree .arco-tree-node:hover .star {
  opacity: 1;
  pointer-events: initial;
}
.tod-byte-tree-selector-listSelect .arco-menu .arco-menu-item .arco-icon {
  margin-right: 0;
}
.tod-byte-tree-selector-listSelect .list-select-item .extra-node-hover-display {
  opacity: 0;
  pointer-events: none;
}
.tod-byte-tree-selector-listSelect .list-select-item:hover .extra-node-hover-display {
  opacity: 1;
  pointer-events: initial;
}
.cloud-ui-action-group__dropdown_menu {
  max-height: 400px;
}
.cloud-ui-action-group__more {
  user-select: none;
  font-size: 16px;
  vertical-align: middle;
  cursor: pointer;
  height: 24px;
}
.cloud-ui-action-group__more:hover {
  color: #3370ff;
}
/** **/
.cloud-ui-anchors {
  padding: 24px;
  background-color: #fff;
  display: flex;
  align-items: stretch;
}
.cloud-ui-anchors__sider {
  width: 200px;
}
.cloud-ui-anchors__content {
  width: calc(100% - 200px);
  flex: auto;
}
.arco-card.cloud-layout-card__header > .arco-card-header {
  padding-top: 16px;
  padding-bottom: 0;
}
.arco-card.cloud-layout-card__header > .arco-card-body {
  padding-left: 20px;
  padding-right: 20px;
}
.cloud-ui-card-group__row {
  align-items: stretch;
}
.cloud-ui-card-group__row_border {
  margin: -6px;
  align-items: stretch !important;
}
.cloud-ui-card-group__row_col_border {
  padding: 6px;
}
.cloud-ui-card-group__row_col_border_top {
  border-top: 1px solid var(--color-neutral-3);
}
.cloud-ui-card-group__row_col_border_left {
  border-left: 1px solid var(--color-neutral-3);
}
.cloud-ui-datetime__relative {
  color: rgb(var(--gray-6));
}
.cloud-ui-detail-card__status {
  display: flex;
  align-items: center;
  padding: 6px 0;
}
.cloud-ui-detail-card__status_tag {
  flex: 1;
}
.cloud-ui-detail-card__status_extra {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.cloud-ui-detail-card__header {
  display: flex;
  align-items: center;
  padding: 6px 0;
}
.cloud-ui-detail-card__header_title,
.cloud-ui-detail-card__header_extra {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.cloud-ui-detail-card__header_title {
  font-weight: 500;
  font-size: 16px;
  color: var(--color-text-1);
}
.cloud-ui-detail-card__header_extra {
  font-size: 14px;
  flex: 0 0 auto;
  margin-left: 8px;
}
.cloud-ui-detail-card__body {
  padding: 6px 0;
  color: rgb(var(--gray-6));
}
.cloud-ui-detail-card__action {
  text-align: right;
  padding: 16px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
}
.cloud-ui-detail-card__action_shadow {
  padding: 6px 0;
  visibility: hidden;
  pointer-events: none;
}
.cloud-ui-filter {
  margin: 16px 0 8px 0;
}
.cloud-ui-filter__form_item.arco-form-item {
  margin-bottom: 8px;
}
.cloud-ui-filter__form_item.arco-form-item .arco-form-label-item label {
  color: var(--color-text-3);
}
.cloud-ui-filter__form_item.arco-form-item .arco-form-label-item {
  width: unset !important;
  flex: 0 0 auto;
  min-width: 90px;
}
.cloud-ui-filter__form_item.arco-form-item .arco-form-item-wrapper {
  width: unset !important;
  flex: 1;
}
.cloud-ui-filter__button {
  padding-left: 20px;
  padding-right: 20px;
}
.cloud-ui-form__groups_header {
  padding-left: 16px;
  user-select: none;
  text-align: left;
}
.cloud-ui-form__groups_header svg {
  margin-right: 4px;
}
.cloud-ui-form__groups_content {
  overflow: hidden;
  max-height: 0;
  transition: all 0.3s ease;
}
.cloud-ui-form__groups_content.active {
  height: auto;
  max-height: 999px;
}
.cloud-ui-form__groups_content > div {
  padding: 16px 0;
}
.cloud-ui-form__item .arco-form-item-control-children > div {
  width: 100%;
}
.cloud-ui-layout__content .cloud-ui-form__page {
  margin: -24px -16px;
}
.cloud-ui-form__buttons_wrapper {
  height: 85px;
  line-height: 85px;
  background: var(--color-bg-1);
}
.cloud-ui-form__affix.arco-affix {
  box-shadow: #ffffff 0px 20px 0px 20px, #0000000d 0px 16px 4px 20px;
  box-sizing: content-box;
}
.cloud-ui-form__page_header {
  font-size: 20px;
  font-weight: bold;
  display: flex;
  align-items: center;
  padding: 28px 30px;
  border-bottom: 1px solid #e8e9ea;
}
.cloud-ui-form__page_header_icon {
  flex: 0 0 auto;
  font-size: 16px;
  padding-right: 14px;
  font-weight: bold;
}
.cloud-ui-form__page_header_icon > span {
  padding: 10px;
  border-radius: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
}
.cloud-ui-form__page_header_icon > span:hover {
  background-color: #f5f5f5;
}
.cloud-ui-form__page_header_icon .arco-icon {
  stroke-width: 6;
}
.cloud-ui-form__page_header_title {
  flex: auto;
  line-height: 1.5;
  user-select: none;
}
.cloud-ui-form__page_header_title h3 {
  font-size: 20px;
  line-height: 28px;
}
.cloud-ui-form__page_header_title_subtitle {
  font-weight: normal;
  font-size: 14px;
  color: #4E5969;
}
.cloud-ui-form__page_steps {
  padding: 24px;
}
.cloud-ui-form__page_form {
  padding: 24px 16px;
  padding-top: 40px;
}
.cloud-ui-heart__wrapper .cloud-ui-heart__like {
  color: #e36159;
}
.cloud-ui-heart__wrapper .cloud-ui-heart__like:hover {
  color: rgba(227, 97, 89, 0.85);
}
.cloud-ui-heart__wrapper .cloud-ui-heart__unlike {
  color: rgb(var(--gray-8));
}
.cloud-ui-heart__wrapper .cloud-ui-heart__unlike:hover {
  color: #e36159;
}
.cloud-ui-heart__wrapper .cloud-ui-heart__like,
.cloud-ui-heart__wrapper .cloud-ui-heart__unlike {
  cursor: pointer;
  font-size: 18px;
}
.cloud-ui-heart .arco-icon {
  fill: none;
}
.cloud-ui-header {
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.05);
}
.cloud-ui-header__header {
  margin: 0 -16px;
  display: flex;
  flex-flow: column nowrap;
  justify-content: flex-start;
  align-items: stretch;
  padding: 0 16px 24px 16px;
  padding-bottom: 0;
  margin-bottom: 16px;
  background-color: var(--color-bg-2);
}
.cloud-ui-header__header_title {
  color: var(--color-text-2);
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: center;
}
.cloud-ui-header__header_title_main {
  font-size: 20px;
  font-weight: bold;
  flex: auto;
  display: flex;
  overflow: hidden;
  white-space: nowrap;
}
.cloud-ui-header__header_title_main_title {
  font-size: 24px;
  color: #1D2129;
  padding-right: 24px;
  text-overflow: ellipsis;
  overflow: hidden;
  box-sizing: border-box;
  transition: width 0.3s ease;
}
.cloud-ui-header__header_title_main_title_shadow {
  position: absolute;
  z-index: -1;
  color: transparent;
  padding-right: 24px;
  font-size: 24px;
}
.cloud-ui-header__header_title_main_droplist_trigger {
  font-size: 16px;
  font-weight: normal;
  padding-right: 24px;
  position: relative;
  cursor: pointer;
  font-size: 14px;
  color: var(--color-text-2);
  user-select: none;
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
}
.cloud-ui-header__header_title_main_droplist_trigger:hover {
  color: #3370ff;
}
.cloud-ui-header__header_title_main_droplist_trigger > svg {
  margin-left: 2px;
}
.cloud-ui-header__header_title_main > div:first-child {
  flex: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  white-space: nowrap;
  position: relative;
}
.cloud-ui-header__header_title_main > div {
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: center;
}
.cloud-ui-header__header_title_sub_title {
  color: #999;
  font-size: 16px;
  font-weight: normal;
}
.cloud-ui-header__header_title_searchbar {
  flex: 0 0 auto;
  width: 240px;
  height: 36px;
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
}
.cloud-ui-header__header_title_button_group {
  margin-left: 32px;
  flex: 0 0 auto;
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
}
.cloud-ui-header__header_title_button_group > button {
  font-weight: normal;
  user-select: none;
  min-width: 96px;
}
.cloud-ui-header__header_title_button_group > button:nth-of-type(n+2) {
  margin-left: 16px;
}
.cloud-ui-header__header_title_button_group_more {
  user-select: none;
  margin-left: 16px;
  font-size: 20px;
  vertical-align: middle;
  height: 32px;
  cursor: pointer;
}
.cloud-ui-header__header_title_button_group_more:hover {
  color: #3370ff;
}
.cloud-ui-header__header_button_group_item {
  padding: 0;
}
.cloud-ui-header__header_button_group_item > button.arco-btn {
  color: var(--arco-volc3-color-text-1, var(--color-text-1));
  width: 100%;
}
.cloud-ui-header__header_button_group_item > button.arco-btn:hover {
  background-color: transparent !important;
}
.cloud-ui-layout {
  background-color: var(--color-secondary);
  min-width: 1200px;
  color: var(--color-text-2);
  height: calc(100vh - 50px);
  position: relative;
  /** sider **/
}
.cloud-ui-layout.cloud-ui-layout_wrapper {
  position: relative;
}
.cloud-ui-layout.cloud-ui-layout_wrapper.collapsed {
  transition: all 0.3s ease;
  padding-left: 0;
}
.cloud-ui-layout.arco-layout-has-sider {
  padding-left: 200px;
}
.cloud-ui-layout .cloud-ui-layout__header {
  padding: 16px 16px 0 16px;
  position: relative;
  z-index: 1;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
}
.cloud-ui-layout .cloud-ui-layout__header > div {
  flex: 1;
}
.cloud-ui-layout .cloud-ui-layout__header_droplist {
  text-align: right;
  height: 22px;
  line-height: 22px;
}
.cloud-ui-layout .cloud-ui-layout__header_droplist_trigger {
  cursor: pointer;
  font-size: 14px;
  color: var(--color-text-2);
  user-select: none;
}
.cloud-ui-layout .cloud-ui-layout__header_droplist_trigger:nth-of-type(n+2) {
  margin-left: 20px;
}
.cloud-ui-layout .cloud-ui-layout__header_droplist_trigger:hover {
  color: #3370ff;
}
.cloud-ui-layout .cloud-ui-layout__header_droplist_trigger:nth-of-type(n+2) {
  margin-left: 20px;
}
.cloud-ui-layout .cloud-ui-layout__header_droplist_trigger:hover {
  color: #3370ff;
}
.cloud-ui-layout .cloud-ui-layout__header .cloud-ui-header__header {
  margin: -24px -16px 0 -16px;
}
.cloud-ui-layout .cloud-ui-layout__sider {
  font-size: 14px;
  width: 200px;
  position: absolute;
  left: 0;
  height: 100%;
  overflow: hidden;
}
.cloud-ui-layout .cloud-ui-layout__sider ::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}
.cloud-ui-layout .cloud-ui-layout__sider_menu_item_disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
}
.cloud-ui-layout .cloud-ui-layout__sider_menu_trigger {
  position: absolute;
  top: 40vh;
  cursor: pointer;
  z-index: 1;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: auto 100%;
  height: 54px;
  transform: translateY(-50%);
  padding: 12px 1px 12px 3px;
}
.cloud-ui-layout .cloud-ui-layout__sider_menu_trigger.unfold {
  color: #4E5969;
  right: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='58' viewBox='0 0 20 58' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2 13.217a4 4 0 0 1 1.88-3.392L18 1v54L3.734 45.192A4 4 0 0 1 2 41.896V13.217z' fill='%23F2F3F5' filter='url(%23filter0_d)'/%3E%3Cdefs%3E%3Cfilter id='filter0_d' x='0' y='0' width='20' height='58' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeColorMatrix in='SourceAlpha' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'/%3E%3CfeOffset dy='1'/%3E%3CfeGaussianBlur stdDeviation='1'/%3E%3CfeColorMatrix values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0'/%3E%3CfeBlend in2='BackgroundImageFix' result='effect1_dropShadow'/%3E%3CfeBlend in='SourceGraphic' in2='effect1_dropShadow' result='shape'/%3E%3C/filter%3E%3C/defs%3E%3C/svg%3E");
}
.cloud-ui-layout .cloud-ui-layout__sider_menu_trigger.fold {
  color: #fff;
  left: 0;
  animation: 0.2s ease 0.3s both slideLeft;
  animation-fill-mode: both;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='58' viewBox='0 0 20 58' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 13.217a4 4 0 0 0-1.88-3.392L2 1v54l14.266-9.808A4 4 0 0 0 18 41.896V13.217z' fill='%2387919D' filter='url(%23filter0_d)'/%3E%3Cdefs%3E%3Cfilter id='filter0_d' x='0' y='0' width='20' height='58' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeColorMatrix in='SourceAlpha' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'/%3E%3CfeOffset dy='1'/%3E%3CfeGaussianBlur stdDeviation='1'/%3E%3CfeColorMatrix values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0'/%3E%3CfeBlend in2='BackgroundImageFix' result='effect1_dropShadow'/%3E%3CfeBlend in='SourceGraphic' in2='effect1_dropShadow' result='shape'/%3E%3C/filter%3E%3C/defs%3E%3C/svg%3E");
  transform: translateX(-2px) translateY(-50%);
}
.cloud-ui-layout .cloud-ui-layout__main {
  padding: 16px 20px 0 20px;
  overflow: auto;
}
.cloud-ui-layout .cloud-ui-layout__main.with-bg > div {
  background-color: var(--color-bg-1);
}
.cloud-ui-layout .cloud-ui-layout__main_wrapper {
  background-color: var(--color-bg-2);
  flex: 0 0 auto;
  display: flex;
  flex-flow: column nowrap;
  justify-content: flex-start;
  align-items: stretch;
}
.cloud-ui-layout .cloud-ui-layout__content {
  padding: 24px 20px;
  color: var(--color-text-2);
}
.cloud-ui-layout .cloud-ui-layout__content .cloud-ui-layout__detail_content:first-child:not(:last-child) {
  margin-top: -40px;
}
.cloud-ui-layout .cloud-ui-layout__content .cloud-ui-layout__detail_content:last-child {
  margin-bottom: -24px;
}
.cloud-ui-layout .cloud-ui-layout__content .arco-tabs-pane {
  padding-left: 20px;
  padding-right: 20px;
}
.cloud-ui-layout .cloud-ui-layout__content .arco-tabs-header-nav-line.arco-tabs-header-nav-horizontal > .arco-tabs-header-scroll .arco-tabs-header-title:first-of-type {
  margin-left: 20px;
}
.cloud-ui-layout__breadcrumb {
  color: #4E5969;
}
.cloud-ui-layout__breadcrumb:last-child {
  color: #1D2129;
}
@keyframes slideLeft {
  from {
    left: -16px;
  }
  to {
    left: 0;
  }
}
@keyframes slideRight {
  from {
    right: -16px;
  }
  to {
    right: 0;
  }
}
.cloud-ui-shotcut__item {
  line-height: 1.5;
  transition: all 0.1s ease;
}
.cloud-ui-shotcut__item a,
.cloud-ui-shotcut__item span {
  cursor: pointer;
  color: #1D2129;
  text-decoration: none;
  font-size: 14px;
  height: 36px;
  line-height: 36px;
  display: flex;
  align-items: center;
  padding: 0 0;
}
.cloud-ui-shotcut__item a > svg,
.cloud-ui-shotcut__item span > svg {
  margin-left: 4px;
}
.cloud-ui-shotcut__item a:hover,
.cloud-ui-shotcut__item span:hover {
  background-color: transparent;
  color: #3370ff;
}
.cloud-ui-star__wrapper .cloud-ui-star__like {
  color: #FF7D00;
}
.cloud-ui-star__wrapper .cloud-ui-star__like:hover {
  color: rgba(255, 125, 0, 0.85);
}
.cloud-ui-star__wrapper .cloud-ui-star__unlike {
  color: rgb(var(--gray-8));
}
.cloud-ui-star__wrapper .cloud-ui-star__unlike:hover {
  color: #FF7D00;
}
.cloud-ui-star__wrapper .cloud-ui-star__like,
.cloud-ui-star__wrapper .cloud-ui-star__unlike {
  cursor: pointer;
  font-size: 18px;
}
.cloud-ui-star .arco-icon {
  fill: none;
}
.cloud-ui-statistic-card__statistic {
  width: 100%;
}
.cloud-ui-statistic-card__card {
  width: 100%;
}
.cloud-ui-statistic-card__header {
  display: flex;
  align-items: center;
}
.cloud-ui-statistic-card__header_title {
  flex: 1 1 auto;
}
.cloud-ui-statistic-card__badge {
  margin-right: 8px;
  line-height: 0 !important;
}
.cloud-ui-statistic-card__badge_dot {
  width: 10px;
  height: 10px;
}
.baseStyles {
  border: none;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
  display: inline-block;
}
.cloud-ui-text {
  border: none;
  vertical-align: baseline;
  line-height: 1.5em;
}
.cloud-ui-text__div {
  display: block;
  width: 100%;
}
.cloud-ui-text__content {
  border: none;
  position: relative;
  vertical-align: bottom;
}
.cloud-ui-text__content::before {
  pointer-events: none;
  content: attr(data-shadow);
  color: transparent;
  z-index: -1;
  white-space: nowrap;
  max-height: 1.5em;
  display: inline-block;
  overflow: hidden;
  vertical-align: bottom;
}
.cloud-ui-text__popup .arco-tooltip-content {
  max-height: 300px;
  overflow: auto;
}
.cloud-ui-text__popup .arco-tooltip-content::-webkit-scrollbar {
  display: none;
}
.cloud-ui-text__solid {
  width: 100%;
  vertical-align: bottom;
  border: none;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
}
.cloud-ui-text__shadow {
  border: none;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
  display: inline-block;
  position: fixed;
  left: 0;
  top: 10px;
  opacity: 0;
  z-index: -1;
  pointer-events: none;
  user-select: none;
}
.cloud-ui-text_fulltext {
  cursor: pointer;
}
.cloud-ui-text_fulltext_popup {
  position: absolute;
  left: -4px;
  right: 0;
  top: -4px;
  display: none;
  background-color: #fff;
  box-shadow: 0 1px 1px 3px #f5f5f5;
  padding: 4px;
  word-break: break-all;
  z-index: 9999;
}
.cloud-ui-global-actions__header_droplist {
  text-align: right;
  height: 22px;
  line-height: 22px;
}
.cloud-ui-global-actions__header_droplist_trigger {
  cursor: pointer;
  font-size: 14px;
  color: var(--color-text-2);
  user-select: none;
}
.cloud-ui-global-actions__header_droplist_trigger:nth-of-type(n+2) {
  margin-left: 20px;
}
.cloud-ui-global-actions__header_droplist_trigger:hover {
  color: #3370ff;
}
.cloud-ui-global-actions__header_droplist_trigger:nth-of-type(n+2) {
  margin-left: 20px;
}
.cloud-ui-global-actions__header_droplist_trigger:hover {
  color: #3370ff;
}
/** skip **/
.tod-byte-tree-tree-spin > .arco-spin-children {
  height: 100%;
}
.tod-byte-tree-tab-pane-tree .arco-tree-node {
  position: relative;
}
.tod-byte-tree-tab-pane-tree .arco-tree-node .star {
  box-sizing: content-box;
  position: absolute;
  top: calc(50% - 15px);
  right: 17px;
  padding: 6px;
  opacity: 0;
  pointer-events: none;
}
.tod-byte-tree-tab-pane-tree .arco-tree-node:hover .star {
  opacity: 1;
  pointer-events: initial;
}
.tod-byte-tree-tab-pane-tree .arco-tree-node .arco-tree-node-title {
  flex: 1;
  padding-right: 50px;
  width: 0px;
}
.tod-byte-tree-tab-pane-tree .arco-tree-node .arco-tree-node-title .arco-tree-node-title-text {
  display: block;
}
.tod-byte-tree-tab-pane-tree.tod-byte-tree-scroll-x > div {
  width: 500px;
}
.tod-byte-tree-tab-pane-tree.tod-byte-tree-scroll-x > div > div {
  width: 500px;
}
.psm_list_item-wrap {
  margin-bottom: 2px;
  flex: 1 1 auto;
  overflow-x: hidden;
  cursor: pointer;
  padding: 4px 8px;
  transition: 300ms;
  background-color: var(--color-bg-2);
  display: flex;
}
.psm_list_item-wrap:hover,
.psm_list_item-wrap.active {
  background-color: var(--color-bg-5);
}
.psm_list_item-wrap .content {
  flex: 1;
  cursor: pointer;
  width: 80%;
}
.psm_list_item-wrap .content .name {
  line-height: 20px;
  margin-bottom: 4px;
  word-wrap: break-word;
}
.psm_list_item-wrap .content .parent-path {
  font-size: 12px;
  line-height: 18px;
  color: rgb(var(--gray-6));
  word-wrap: break-word;
}
.psm_list_item-wrap .extra {
  flex: 0 0 18px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
}
.psm_list_item-wrap .extra .recent {
  font-family: Nunito Sans, sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: rgba(var(--primary-6));
  margin-right: 20px;
}
.psm_list_item-wrap .extra .heart {
  margin-top: -2px;
}
.tab-wrap {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}
.tab-wrap .arco-tabs-content {
  padding: 0;
  height: 100%;
}
.tab-wrap .arco-tabs-content-inner {
  height: 100%;
}
.tab-wrap .arco-tabs-pane {
  height: 100%;
}
.item-wrap {
  flex: 1 1 auto;
  overflow-x: hidden;
  align-items: center;
  cursor: default;
  padding: 4px 8px;
  transition: 300ms;
  display: flex;
}
.item-wrap:hover,
.item-wrap.active {
  background-color: var(--color-bg-2);
}
.item-wrap .content {
  flex: 1;
}
.item-wrap .content .name {
  line-height: 18px;
  margin-bottom: 4px;
  word-break: break-all;
}
.item-wrap .content .parent-path {
  font-size: 12px;
  line-height: 14px;
  color: rgb(var(--gray-6));
}
.item-wrap .extra {
  color: var(--color-text-2);
}
.wrap {
  max-height: 100%;
  display: flex;
  flex-direction: column;
}
.wrap .header {
  flex-shrink: 0;
  font-weight: 500;
  line-height: 20px;
  padding: 4px 12px 4px 8px;
}
.wrap .header .tod-bytetree-selector-drawer-action-btn {
  color: var(--color-text-2);
  height: 24px;
  width: 24px;
  flex-shrink: 0;
}
.wrap .content {
  margin-top: 8px;
  overflow-y: auto;
}
.singleWrap .tod-bytetree-selector-drawer-content,
.multipleWrap .tod-bytetree-selector-drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.singleWrap .mutiple-content,
.multipleWrap .mutiple-content {
  border: 1px solid var(--color-border-2);
  padding-left: 8px;
}
.singleWrap .tod-bytetree-selector-drawer-alert,
.multipleWrap .tod-bytetree-selector-drawer-alert {
  margin-left: -24px;
  margin-right: -24px;
  width: unset;
  margin-bottom: 20px;
  margin-top: -20px;
}
.multipleWrap  .arco-drawer-inner .arco-drawer-header {
  height: 56px;
  line-height: 56px;
}
.multipleWrap .arco-drawer-inner .arco-drawer-close-icon {
  top: 22px;
  font-size: 16px;
}
.multipleWrap .left {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 538px;
}
.multipleWrap .right {
  height: 100%;
  overflow: hidden;
  flex: 1;
  padding: 8px;
  border-left: 1px solid rgba(var(--gray-3));
}
.tagBox {
  margin-left: 10px;
  color: rgba(var(--gray-6));
}
.tod-byte-tree-pro-left-wrapper {
  height: 100%;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.mutiple-content {
  padding: 0;
  height: 100%;
  width: 100%;
  min-width: 400px;
  overflow: hidden;
}
.mutiple-content .left {
  height: 100%;
}
.arco-rc-tooltip-button {
  padding: 20px;
}
.no-render-children .arco-tree-node-switcher {
  visibility: hidden;
}
.arco-dropdown-menu-item .mark-highlight mark {
  color: rgb(var(--gray-10));
  background-color: yellow;
  padding: 0;
  margin: 0;
  font-weight: 500;
}
.tod-byte-tree-node-clickable-item {
  cursor: pointer;
}
.tod-byte-tree .arco-tree-node .arco-tree-node-title {
  flex: 1;
  width: 0;
  height: 32px;
}
.tod-byte-tree-permission-modal .iam-auth-arco .m-t-8 {
  margin-top: 8px;
}
.tod-byte-tree-permission-modal .iam-auth-arco .f-s-12 {
  font-size: 12px;
}
.tod-byte-tree-permission-modal .iam-auth-arco .color-grey {
  color: rgb(var(--color-text-2));
}
.tod-byte-tree-permission-modal .iam-auth-arco .iam-message-info {
  word-break: break-all;
  margin-bottom: 6px;
}
.tod-byte-tree-permission-modal .iam-auth-arco .iam-message-alert {
  max-height: 260px;
  overflow-y: auto;
  margin-bottom: 6px;
  padding: 12px;
  background-color: rgb(var(--warning-1));
}
.tod-byte-tree-permission-modal .iam-auth-arco .code {
  background: var(--color-fill-1);
  padding: 10px;
  overflow: auto;
  font-weight: 400;
}
.tod-byte-tree-permission-modal .iam-auth-arco .arco-alert-with-title {
  display: flex;
}
.tod-byte-tree-permission-modal .iam-auth-arco .arco-icon-hover::before {
  display: none;
}
.tod-card {
  padding: 16px 20px;
  position: relative;
  box-sizing: border-box;
}
.tod-card-size-small {
  border-radius: var(--border-radius-medium);
}
.tod-card-size-default {
  border-radius: var(--border-radius-large);
}
.tod-card:hover {
  box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.05);
}
.tod-card-border {
  border: 1px solid var(--color-border-2);
}
.tod-card-box-shadow {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1);
}
.tod-card-gray {
  background-color: var(--color-bg-4);
}
.tod-card-warning {
  background-color: var(--color-warning-light-1);
}
.tod-card-error {
  background-color: var(--color-danger-light-1);
}
.tod-card-info {
  background-color: var(--color-primary-light-1);
}
.tod-card-success {
  background-color: var(--color-success-light-1);
}
.tod-card-checkable {
  cursor: pointer;
}
.tod-card-checked {
  background-color: var(--color-primary-light-1);
  border: 1px solid rgb(var(--primary-6));
}
.tod-card-checkable:hover .tod-card-checkbox,
.tod-card-checked .tod-card-checkbox {
  opacity: 1;
}
.tod-card-disabled,
.tod-card-disabled:hover {
  cursor: not-allowed;
  box-shadow: unset;
}
.tod-card-disabled .tod-card-checkbox,
.tod-card-disabled:hover .tod-card-checkbox {
  opacity: 0;
}
.tod-card-disabled.tod-card-checked,
.tod-card-disabled.tod-card-checked:hover {
  cursor: not-allowed;
  background-color: unset;
  border-color: var(--color-border-2);
}
.tod-card-disabled.tod-card-checked .tod-card-checkbox,
.tod-card-disabled.tod-card-checked:hover .tod-card-checkbox {
  opacity: 1;
  background: linear-gradient(45deg, transparent 50%, var(--color-primary-light-3) 50%);
}
.tod-card-checkbox {
  opacity: 0;
  width: 30px;
  height: 30px;
  position: absolute;
  right: -1px;
  top: -1px;
  font-size: 16px;
  text-align: right;
  color: var(--color-white);
  background: linear-gradient(45deg, transparent 50%, var(--color-fill-1) 50%);
  border-top-right-radius: inherit;
}
.tod-card-checkbox.tod-card-checkbox-checked {
  background: linear-gradient(45deg, transparent 50%, rgba(var(--primary-6)) 50%);
}
.tod-card-checkbox-icon {
  display: inline-flex;
}
.tod-card-grid {
  display: grid;
  grid-gap: 20px 20px;
}
@media screen and (max-width: 1440px) {
  .tod-card-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
@media screen and (min-width: 1440px) and (max-width: 1680px) {
  .tod-card-grid {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
@media screen and (min-width: 1680px) and (max-width: 1920px) {
  .tod-card-grid {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}
@media screen and (min-width: 1920px) {
  .tod-card-grid {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
}
.tod-codeBlock {
  display: inline-block;
  background: var(--color-bg-4);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(213, 219, 227, 0.5);
  border-radius: 4px;
}
.tod-codeBlock--default .tod-codeBlock-content {
  padding: 12px 16px;
}
.tod-codeBlock--simple .tod-codeBlock-content {
  padding: 0;
}
.tod-codeBlock--simple.tod-codeBlock--small {
  padding: 1px 6px;
}
.tod-codeBlock--simple.tod-codeBlock--small .tod-codeBlock-content--operation {
  font-size: 12px;
}
.tod-codeBlock--simple.tod-codeBlock--default {
  padding: 7px 16px;
}
.tod-codeBlock .tod-codeBlock-content--operation {
  margin-left: 6px;
}
.tod-codeBlock-title {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  font-size: 12px;
  color: #1d2129;
  border-bottom: 1px solid #e6eaf0;
}
.tod-codeBlock-title .arco-select {
  width: auto;
}
.tod-codeBlock-title .arco-select-arrow-icon {
  font-size: 12px;
  margin-left: 10px;
}
.tod-codeBlock-title .arco-select-size-default.arco-select-single .arco-select-view,
.tod-codeBlock-title .arco-tabs-header-nav-text .arco-tabs-header-title {
  font-size: 12px;
  font-weight: normal;
}
.tod-codeBlock-title .arco-tabs-header-nav-text .arco-tabs-header-title:last-child {
  margin-right: 0;
}
.tod-codeBlock-title .arco-select {
  margin-left: -11px;
}
.tod-codeBlock-title-left {
  flex: 1;
}
.tod-codeBlock-tooltip {
  display: inline-flex;
  justify-content: center;
  line-height: 16px;
}
.tod-codeBlock-tooltip__icon {
  font-size: 16px;
  margin-right: 8px;
}
.tod-codeBlock-content {
  display: flex;
  line-height: 18px;
  color: #4e5969;
  font-size: 12px;
  word-break: break-all;
}
.tod-codeBlock-content-code {
  padding-right: 10px;
  overflow-y: auto;
}
.tod-codeBlock-content--operation {
  font-size: 14px;
  cursor: pointer;
  color: #41464f;
}
.tod-codeBlock-line {
  display: flex;
}
.tod-codeBlock-line-index {
  margin-right: 24px;
  white-space: nowrap;
}
.tod-conditions-body,
.tod-conditions-group-body {
  display: flex;
}
.tod-conditions-body .arco-btn,
.tod-conditions-group-body .arco-btn {
  padding: 0;
}
.tod-conditions-content,
.tod-conditions-group-content {
  flex: auto;
  position: relative;
}
.tod-conditions-group-content.view {
  position: relative;
}
.tod-conditions-group-content.view > div:only-child {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
}
.tod-conditions-relation {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 12px;
}
.tod-conditions-relation-upper-line,
.tod-conditions-relation-lower-line {
  flex: auto;
  border: 1px solid #EAEDF1;
  border-right: none;
  width: 12px;
  margin-left: 12px;
}
.tod-conditions-relation-upper-line {
  border-radius: 4px 0;
  border-bottom: none;
  margin-top: 1em;
}
.tod-conditions-relation-lower-line {
  border-radius: 0 0 0 4px;
  border-top: none;
  margin-top: 4px;
  margin-bottom: 1em;
}
.tod-conditions-relation-lower-line.view {
  margin-top: 0;
}
.tod-conditions-body > .tod-conditions-relation > .tod-conditions-relation-upper-line.view {
  margin-top: 2em;
}
.tod-conditions-body > .tod-conditions-relation > .tod-conditions-relation-lower-line.view {
  margin-bottom: 1.5em;
}
.tod-conditions-relation-select {
  width: 80px;
}
.tod-conditions-relation-toggle {
  position: relative;
  cursor: pointer;
}
.tod-conditions-relation-card {
  display: inline-block;
  width: 28px;
  height: 24px;
  border: 1px solid;
  border-radius: 2px;
  background-color: var(--color-bg-white);
  text-align: center;
  color: var(--color-text-3);
  font-size: 12px;
  line-height: 24px;
  user-select: none;
  transition: top 0.2s ease, left 0.2s ease;
}
.tod-conditions-relation-card.small {
  width: 24px;
  height: 20px;
  line-height: 20px;
  font-size: 10px;
}
.tod-conditions-relation-card.first {
  color: rgb(var(--primary-6));
}
.tod-conditions-relation-card.second {
  z-index: -1;
  color: rgb(var(--success-6));
  position: absolute;
  left: -4px;
  top: 4px;
}
.tod-conditions-relation-toggle.flipped .tod-conditions-relation-card.first {
  position: absolute;
  left: -4px;
  top: 4px;
}
.tod-conditions-relation-toggle.flipped .tod-conditions-relation-card.second {
  z-index: 1;
  position: relative;
  left: 0;
  top: 0;
}
.tod-conditions-group-content .tod-conditions-group-clear {
  position: absolute;
  right: 1em;
}
.tod-conditions-group-wrapper > .tod-conditions-group-delete {
  position: absolute;
  right: 0.5em;
  margin-top: -2.5em;
}
.tod-timezone-selector-text {
  font-size: 12px;
  color: var(--color-neutral-6);
  margin-top: 8px;
  margin-bottom: 10px;
}
.tod-form-group-title {
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 22px;
  color: #41464f;
}
.tod-form-group .arco-volc3-collapse,
.tod-form-group .arco-collapse {
  border: none;
}
.tod-form-group .arco-volc3-form-label-item > label,
.tod-form-group .arco-form-label-item > label {
  color: #80838a;
}
.tod-form-group-item {
  margin-bottom: 12px;
  background: #fcfdfe;
}
.tod-form-group-item.arco-volc3-collapse-item,
.tod-form-group-item.arco-collapse-item {
  border-bottom: none;
}
.tod-form-group-item .arco-volc3-collapse-item-header,
.tod-form-group-item .arco-collapse-item-header {
  border: 1px solid #eaedf1;
  border-radius: 4px;
}
.tod-form-group-item.arco-volc3-collapse-item-active > .arco-collapse-item-header,
.tod-form-group-item.arco-collapse-item-active > .arco-collapse-item-header,
.tod-form-group-item.arco-volc3-collapse-item-active > .arco-volc3-collapse-item-header,
.tod-form-group-item.arco-collapse-item-active > .arco-volc3-collapse-item-header {
  border: 1px solid #eaedf1;
  border-bottom-color: transparent;
  border-radius: 4px 4px 0 0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  z-index: 1;
}
.tod-form-group-item.arco-volc3-collapse-item-active > .arco-collapse-item-header .arco-collapse-item-header-title,
.tod-form-group-item.arco-collapse-item-active > .arco-collapse-item-header .arco-collapse-item-header-title,
.tod-form-group-item.arco-volc3-collapse-item-active > .arco-volc3-collapse-item-header .arco-collapse-item-header-title,
.tod-form-group-item.arco-collapse-item-active > .arco-volc3-collapse-item-header .arco-collapse-item-header-title,
.tod-form-group-item.arco-volc3-collapse-item-active > .arco-collapse-item-header .arco-volc3-collapse-item-header-title,
.tod-form-group-item.arco-collapse-item-active > .arco-collapse-item-header .arco-volc3-collapse-item-header-title,
.tod-form-group-item.arco-volc3-collapse-item-active > .arco-volc3-collapse-item-header .arco-volc3-collapse-item-header-title,
.tod-form-group-item.arco-collapse-item-active > .arco-volc3-collapse-item-header .arco-volc3-collapse-item-header-title {
  font-weight: normal;
}
.tod-form-group-item .arco-collapse-item-content-box,
.tod-form-group-item .arco-volc3-collapse-item-content-box {
  padding: 16px 20px;
  background: #fcfdfe;
  border: 1px solid #eaedf1;
  border-top: none;
  border-radius: 0 0 4px 4px;
}
.tod-form-group-space {
  display: flex;
  align-items: flex-end;
  margin-bottom: 12px;
}
.tod-form-group-space .arco-space,
.tod-form-group-space .arco-volc3-space {
  flex: 1;
}
.tod-form-group-space-del {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  font-size: 16px;
  border-radius: 4px;
  vertical-align: bottom;
  color: #80838a;
  margin-left: 8px;
  margin-bottom: 8px;
  cursor: pointer;
}
.tod-form-group-space-del:hover {
  background: rgba(160, 162, 167, 0.12);
}
.tod-form-group-space .arco-space-item,
.tod-form-group-space .arco-volc3-space-item {
  flex: 1 1 0%;
}
.tod-form-group-space .arco-space-item .arco-form-item,
.tod-form-group-space .arco-volc3-space-item .arco-form-item,
.tod-form-group-space .arco-space-item .arco-volc3-form-item,
.tod-form-group-space .arco-volc3-space-item .arco-volc3-form-item {
  margin-bottom: 0;
}
.tod-form-group-space .arco-space-item .arco-volc3-select-single .arco-volc3-select-view-selector,
.tod-form-group-space .arco-volc3-space-item .arco-volc3-select-single .arco-volc3-select-view-selector,
.tod-form-group-space .arco-space-item .arco-select-single .arco-select-view-selector,
.tod-form-group-space .arco-volc3-space-item .arco-select-single .arco-select-view-selector {
  position: relative;
  display: inline-flex;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}
.tod-form-group-space .arco-space-item .arco-volc3-select-single .arco-volc3-select-view-selector .arco-volc3-select-view-value-mirror,
.tod-form-group-space .arco-volc3-space-item .arco-volc3-select-single .arco-volc3-select-view-selector .arco-volc3-select-view-value-mirror,
.tod-form-group-space .arco-space-item .arco-select-single .arco-select-view-selector .arco-volc3-select-view-value-mirror,
.tod-form-group-space .arco-volc3-space-item .arco-select-single .arco-select-view-selector .arco-volc3-select-view-value-mirror,
.tod-form-group-space .arco-space-item .arco-volc3-select-single .arco-volc3-select-view-selector .arco-select-view-value-mirror,
.tod-form-group-space .arco-volc3-space-item .arco-volc3-select-single .arco-volc3-select-view-selector .arco-select-view-value-mirror,
.tod-form-group-space .arco-space-item .arco-select-single .arco-select-view-selector .arco-select-view-value-mirror,
.tod-form-group-space .arco-volc3-space-item .arco-select-single .arco-select-view-selector .arco-select-view-value-mirror {
  opacity: 0;
}
.tod-form-group-del {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  vertical-align: middle;
  font-size: 16px;
  border-radius: 4px;
  color: #80838a;
  cursor: pointer;
}
.tod-form-group-del:hover {
  background: rgba(160, 162, 167, 0.12);
}
.tod-form-group-add-item {
  display: flex;
  padding-top: 2px;
  font-weight: 500;
  font-size: 13px;
  color: #1664ff;
  cursor: pointer;
}
.tod-form-group-add-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  font-size: 12px;
  background: rgba(162, 193, 255, 0.2);
  border-radius: 20px;
}
.tod-form-group-tab {
  padding: 0 20px 16px 20px;
}
.tod-i18n-input {
  box-sizing: border-box;
}
.tod-i18n-input--disabled {
  cursor: not-allowed;
}
.tod-i18n-input--disabled .tod-i18n-input-box {
  pointer-events: none;
  background-color: #f6f8fa;
  border-color: #eaedf1;
  color: #80838a;
  -webkit-text-fill-color: #80838a;
}
.tod-i18n-input--disabled-del {
  color: #80838a !important;
}
.tod-i18n-input-box {
  border-radius: 4px;
  padding: 0 12px;
  border: 1px solid #dde2e9;
  transition: border-color 0.1s cubic-bezier(0, 0, 1, 1);
}
.tod-i18n-input-box:hover {
  border-color: #4281ff;
}
.tod-i18n-input-box--focus {
  box-shadow: 0 0 0 2px rgba(22, 100, 255, 0.3);
  border-color: #0055ff;
}
.tod-i18n-input-box--error {
  border-color: #fa9ea3;
  background-color: #feeced;
}
.tod-i18n-input-box--error:hover {
  border-color: #db373f;
}
.tod-i18n-input-box--error.tod-i18n-input-box--focus {
  box-shadow: 0 0 0 2px rgba(219, 55, 63, 0.3);
  border-color: #c43138;
  background: #fff;
}
.tod-i18n-input-box .arco-volc3-input,
.tod-i18n-input-box .arco-input {
  line-height: 22px;
  padding-top: 6px;
  padding-bottom: 6px;
  padding-left: 10px;
}
.tod-i18n-input-box .arco-volc3-textarea,
.tod-i18n-input-box .arco-textarea {
  padding-top: 3px;
  padding-bottom: 3px;
  padding-left: 4px;
  line-height: 22px;
  resize: none;
}
.tod-i18n-input-box .arco-input-inner-wrapper .arco-input,
.tod-i18n-input-box .arco-volc3-input-inner-wrapper .arco-volc3-input {
  padding-left: 0;
}
.tod-i18n-input-box .arco-textarea-clear-wrapper .arco-textarea,
.tod-i18n-input-box .arco-volc3-textarea-clear-wrapper .arco-volc3-textarea {
  padding-right: 22px;
}
.tod-i18n-input-box .arco-volc3-input-inner-wrapper,
.tod-i18n-input-box .arco-input-inner-wrapper,
.tod-i18n-input-box .arco-volc3-input,
.tod-i18n-input-box .arco-volc3-textarea,
.tod-i18n-input-box .arco-input,
.tod-i18n-input-box .arco-textarea {
  padding-right: 0;
  background: none;
  border: none !important;
  border-radius: 0;
  background-color: transparent !important;
}
.tod-i18n-input-box .arco-volc3-input-inner-wrapper:focus,
.tod-i18n-input-box .arco-input-inner-wrapper:focus,
.tod-i18n-input-box .arco-volc3-input:focus,
.tod-i18n-input-box .arco-volc3-textarea:focus,
.tod-i18n-input-box .arco-input:focus,
.tod-i18n-input-box .arco-textarea:focus {
  box-shadow: none;
  border-color: transparent;
}
.tod-i18n-input-box .arco-volc3-input-inner-wrapper:hover,
.tod-i18n-input-box .arco-input-inner-wrapper:hover,
.tod-i18n-input-box .arco-volc3-input:hover,
.tod-i18n-input-box .arco-volc3-textarea:hover,
.tod-i18n-input-box .arco-input:hover,
.tod-i18n-input-box .arco-textarea:hover {
  border-color: transparent;
  background: transparent;
}
.tod-i18n-input-box .arco-input-inner-wrapper-focus,
.tod-i18n-input-box .arco-volc3-input-inner-wrapper-focus {
  box-shadow: none !important;
}
.tod-i18n-input-box--noRequired .tod-i18n-input-tag {
  width: 24px;
}
.tod-i18n-input-inputArea {
  flex: 1;
  display: inline-flex;
  align-items: center;
}
.tod-i18n-input-wrapper {
  display: flex;
}
.tod-i18n-input-wrapper:nth-child(2n) {
  border-top: 1px dashed #d4dbe2;
  border-bottom: 1px dashed transparent;
}
.tod-i18n-input-wrapper-textArea .tod-i18n-input-tag {
  margin-top: 4px;
}
.tod-i18n-input-del {
  padding: 0 4px 0 14px;
  font-size: 14px;
  color: #80838a !important;
  cursor: pointer;
}
.tod-i18n-input-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 20px;
  margin-top: 7px;
  padding: 0 4px;
  line-height: 20px;
  font-size: 12px;
  color: #41464f;
  background: #fcfdfe;
  border: 1px solid #eaedf1;
  border-radius: 4px;
  word-break: normal;
  box-sizing: border-box;
}
.tod-i18n-input-tag-require {
  color: #f53f3f;
}
.tod-i18n-input-add-lang {
  display: inline-flex;
  margin-top: 10px;
  font-size: 13px;
  font-weight: 500;
  color: #1664ff;
  cursor: pointer;
}
.tod-i18n-input-add-lang-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  font-size: 12px;
  background: rgba(162, 193, 255, 0.2);
  border-radius: 20px;
}
.tod-i18n-input-add-lang--disabled {
  color: #a0c0ff;
  pointer-events: none;
}
.tod-i18n-input-errorMsg {
  font-size: 12px;
  line-height: 20px;
  color: #db373f;
}
.arco-volc3-form-item-status-error .tod-i18n-input .tod-i18n-input-box {
  border-color: #fa9ea3;
  background-color: #feeced;
}
.arco-volc3-form-item-status-error .tod-i18n-input .tod-i18n-input-box:hover {
  border-color: #db373f;
}
.arco-volc3-form-item-status-error .tod-i18n-input .tod-i18n-input-box.tod-i18n-input-box--focus {
  box-shadow: 0 0 0 2px rgba(219, 55, 63, 0.3);
  border-color: #c43138;
  background: #fff;
}
.arco-volc3-form-item-status-error .tod-i18n-input .arco-volc3-input:not(.arco-volc3-input-disabled),
.arco-volc3-form-item-status-error .tod-i18n-input .arco-volc3-textarea:not(.arco-volc3-textarea-disabled) {
  border: none;
  background-color: transparent;
  box-shadow: none;
}
.arco-volc3-form-item-status-error .tod-i18n-input .arco-volc3-input:not(.arco-volc3-input-disabled):focus,
.arco-volc3-form-item-status-error .tod-i18n-input .arco-volc3-textarea:not(.arco-volc3-textarea-disabled):focus,
.arco-volc3-form-item-status-error .tod-i18n-input .arco-volc3-input:not(.arco-volc3-input-disabled):hover,
.arco-volc3-form-item-status-error .tod-i18n-input .arco-volc3-textarea:not(.arco-volc3-textarea-disabled):hover,
.arco-volc3-form-item-status-error .tod-i18n-input .arco-volc3-input:not(.arco-volc3-input-disabled):focus:hover,
.arco-volc3-form-item-status-error .tod-i18n-input .arco-volc3-textarea:not(.arco-volc3-textarea-disabled):focus:hover {
  border: none;
  background-color: transparent;
  box-shadow: none;
}
.arco-form-item-status-error .tod-i18n-input .tod-i18n-input-box {
  border-color: #fa9ea3;
  background-color: #feeced;
}
.arco-form-item-status-error .tod-i18n-input .tod-i18n-input-box:hover {
  border-color: #db373f;
}
.arco-form-item-status-error .tod-i18n-input .tod-i18n-input-box.tod-i18n-input-box--focus {
  box-shadow: 0 0 0 2px rgba(219, 55, 63, 0.3);
  border-color: #c43138;
  background: #fff;
}
.arco-form-item-status-error .tod-i18n-input .arco-input:not(.arco-input-disabled),
.arco-form-item-status-error .tod-i18n-input .arco-textarea:not(.arco-textarea-disabled) {
  border: none;
  background-color: transparent;
  box-shadow: none;
}
.arco-form-item-status-error .tod-i18n-input .arco-input:not(.arco-input-disabled):focus,
.arco-form-item-status-error .tod-i18n-input .arco-textarea:not(.arco-textarea-disabled):focus,
.arco-form-item-status-error .tod-i18n-input .arco-input:not(.arco-input-disabled):hover,
.arco-form-item-status-error .tod-i18n-input .arco-textarea:not(.arco-textarea-disabled):hover,
.arco-form-item-status-error .tod-i18n-input .arco-input:not(.arco-input-disabled):focus:hover,
.arco-form-item-status-error .tod-i18n-input .arco-textarea:not(.arco-textarea-disabled):focus:hover {
  border: none;
  background-color: transparent;
  box-shadow: none;
}
.tod-i18n-input-form-item.tod-i18n-input-form-item--show-extra .arco-volc3-form-message,
.tod-i18n-input-form-item.tod-i18n-input-form-item--show-extra .arco-form-message {
  min-height: 20px;
  height: 20px;
  line-height: 20px;
}
.tod-i18n-input-form-item.tod-i18n-input-form-item--show-extra .arco-volc3-form-message + .arco-form-extra,
.tod-i18n-input-form-item.tod-i18n-input-form-item--show-extra .arco-form-message + .arco-form-extra,
.tod-i18n-input-form-item.tod-i18n-input-form-item--show-extra .arco-volc3-form-message + .arco-volc3-form-extra,
.tod-i18n-input-form-item.tod-i18n-input-form-item--show-extra .arco-form-message + .arco-volc3-form-extra {
  margin-bottom: 8px !important;
}
/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.tod-input-tag-remote-department-option-tag {
  margin-right: 8px;
  background-color: transparent !important;
  color: var(--color-text-2) !important;
}
/**
 * 每位工程师都有保持代码优雅的义务
 * Each engineer has a duty to keep the code elegant
 */
.f-s-12 {
  font-size: 12px;
}
.f-s-13 {
  font-size: 13px;
}
.f-s-14 {
  font-size: 14px;
}
.f-s-15 {
  font-size: 15px;
}
.f-s-16 {
  font-size: 16px;
}
.f-s-17 {
  font-size: 17px;
}
.f-s-18 {
  font-size: 18px;
}
.color-danger {
  color: #ee4d38;
}
.bg-danger {
  background-color: #ee4d38;
}
.color-warning {
  color: #ff8b07;
}
.bg-warning {
  background-color: #ff8b07;
}
.color-info {
  color: #3370ff;
}
.bg-info {
  background-color: #3370ff;
}
.color-success {
  color: #0fbf60;
}
.bg-success {
  background-color: #0fbf60;
}
.color-grey {
  color: #939aa3;
}
.color-f-black {
  color: #282f38;
}
.tod-input-tag-remote-employee-item-wrap {
  height: 50px !important;
}
.tod-input-tag-remote-employee-item-wrap:hover {
  background-color: var(--color-bg-4);
}
.tod-input-tag-remote-employee-input-tag-wrap .tod-input-tag-remote-employee-input-tag {
  margin: 0 0 0 0;
}
.tod-input-tag-remote-employee-input-tag-wrap .tod-input-tag-remote-employee-input-tag .tod-input-tag-remote-employee-input-tag-avatar {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  vertical-align: middle;
  margin: -2px 4px 0 0;
}
.tod-input-tag-remote-employee-input-tag-wrap .tod-input-tag-remote-employee-input-tag .tod-input-tag-remote-employee-input-tag-email {
  margin: 0 0 0 4px;
  color: var(--color-text-2);
}
.tod-input-tag-remote-employee-option-item-wrap {
  height: 50px !important;
}
.tod-input-tag-remote-employee-option-item-wrap .tod-input-tag-remote-employee-option-item {
  display: flex;
  align-items: center;
  height: 100%;
}
.tod-input-tag-remote-employee-option-item-wrap .tod-input-tag-remote-employee-option-item .tod-input-tag-remote-employee-option-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 15px;
}
.tod-input-tag-remote-employee-option-item-wrap .tod-input-tag-remote-employee-option-item .tod-input-tag-remote-employee-option-user-info {
  height: 100%;
  line-height: 1;
}
.tod-input-tag-remote-employee-option-item-wrap .tod-input-tag-remote-employee-option-item .tod-input-tag-remote-employee-option-user-info p {
  margin: 0;
  font-weight: 400;
}
.tod-input-tag-remote-employee-option-item-wrap .tod-input-tag-remote-employee-option-item .tod-input-tag-remote-employee-option-user-info p.tod-input-tag-remote-employee-option-user-info-name {
  margin: 8px 0 4px;
}
.tod-input-tag-remote-employee-option-item-wrap .tod-input-tag-remote-employee-option-item .tod-input-tag-remote-employee-option-user-info .tod-input-tag-remote-employee-option-user-info-name-inner {
  margin-right: 4px;
}
.tod-input-tag-remote-employee-option-item-wrap .tod-input-tag-remote-employee-option-item .tod-input-tag-remote-employee-option-user-info .tod-input-tag-remote-employee-option-user-info-summary {
  margin: 8px 0 6px;
  font-size: 12px;
  color: var(--color-text-2);
}
.tod-input-tag-remote-employee-option-item-wrap .tod-input-tag-remote-employee-option-item .tod-input-tag-remote-employee-option-user-info .tod-input-tag-remote-employee-option-user-info-name-resign {
  margin: 0 4px;
  color: rgb(var(--red-1));
  padding-left: 4;
}
.arco-select-popup .arco-select-option-disabled .tod-input-tag-remote-employee-option-user-info-summary {
  color: inherit !important;
}
.tod-input-tag-remote-employee-multi-tenant-item-wrap {
  height: 72px !important;
  font-size: 13px;
  line-height: 20px;
}
.tod-input-tag-remote-employee-multi-tenant-item-wrap:hover {
  background-color: var(--color-bg-4);
}
.tod-input-tag-remote-employee-multi-tenant-input-tag-wrap .tod-input-tag-remote-employee-multi-tenant-input-tag {
  margin: 0 0 0 0;
}
.tod-input-tag-remote-employee-multi-tenant-input-tag-wrap .tod-input-tag-remote-employee-multi-tenant-input-tag .tod-input-tag-remote-employee-multi-tenant-input-tag-avatar {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  vertical-align: middle;
  margin: -2px 4px 0 0;
}
.tod-input-tag-remote-employee-multi-tenant-input-tag-wrap .tod-input-tag-remote-employee-multi-tenant-input-tag .tod-input-tag-remote-employee-multi-tenant-input-tag-email {
  margin: 0 0 0 4px;
  color: var(--color-text-2);
}
.tod-input-tag-remote-employee-multi-tenant-option-item-wrap {
  height: 72px !important;
}
.tod-input-tag-remote-employee-multi-tenant-option-item-wrap .tod-input-tag-remote-employee-multi-tenant-option-item {
  display: flex;
  align-items: center;
  height: 100%;
}
.tod-input-tag-remote-employee-multi-tenant-option-item-wrap .tod-input-tag-remote-employee-multi-tenant-option-item .tod-input-tag-remote-employee-multi-tenant-option-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 15px;
}
.tod-input-tag-remote-employee-multi-tenant-option-item-wrap .tod-input-tag-remote-employee-multi-tenant-option-item .tod-input-tag-remote-employee-multi-tenant-option-user-info {
  height: 100%;
  line-height: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.tod-input-tag-remote-employee-multi-tenant-option-item-wrap .tod-input-tag-remote-employee-multi-tenant-option-item .tod-input-tag-remote-employee-multi-tenant-option-user-info p {
  margin: 0;
  font-weight: 400;
}
.tod-input-tag-remote-employee-multi-tenant-option-item-wrap .tod-input-tag-remote-employee-multi-tenant-option-item .tod-input-tag-remote-employee-multi-tenant-option-user-info .tod-input-tag-remote-employee-multi-tenant-option-user-info-name-inner {
  display: flex;
  gap: 8px;
  align-items: center;
}
.tod-input-tag-remote-employee-multi-tenant-option-item-wrap .tod-input-tag-remote-employee-multi-tenant-option-item .tod-input-tag-remote-employee-multi-tenant-option-user-info .tod-input-tag-remote-employee-multi-tenant-option-user-info-summary {
  font-size: 13px;
  color: var(--color-text-2);
}
.tod-input-tag-remote-employee-multi-tenant-option-item-wrap .tod-input-tag-remote-employee-multi-tenant-option-item .tod-input-tag-remote-employee-multi-tenant-option-user-info .tod-input-tag-remote-employee-multi-tenant-option-user-info-name-resign {
  margin: 0 4px;
  color: rgb(var(--red-1));
  padding-left: 4;
}
.tod-input-tag-remote-employee-multi-tenant-option-item-wrap .tod-input-tag-remote-employee-multi-tenant-option-item .tod-input-tag-remote-employee-multi-tenant-option-user-info .tod-input-tag-remote-employee-multi-tenant-option-user-info-name-resign .tod-input-tag-remote-employee-multi-tenant-option-user-info-name-tenant-tag {
  line-height: 18px;
  font-size: 10px;
  height: 18px;
  background-color: transparent;
  border: 1px solid var(--color-text-4);
}
.arco-select-popup .arco-select-option-disabled .tod-input-tag-remote-employee-multi-tenant-option-user-info-summary {
  color: inherit !important;
}
.tod-lark-group-lark {
  display: inline-block;
  padding: 0 3px;
}
.tod-lark-group-lark .lark-name {
  word-break: keep-all;
  margin-left: 4px;
  cursor: pointer;
}
.tod-lark-group-select-item {
  display: flex;
  align-items: center;
}
.tod-lark-group-select-item .avatar {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 6px;
  object-fit: cover;
}
.tod-lark-group-select-item .group-name {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  overflow-wrap: break-word;
  white-space: nowrap;
  word-break: break-all;
  max-width: 100%;
  vertical-align: bottom;
}
.tod-lark-group-check-login .intro-text {
  color: rgba(var(--gray-6));
  margin: 0 6px 0 0;
}
.tod-lark-group-check-login .click-link {
  cursor: pointer;
}
.tod-list-wrapper.tod-list-pagination-bordered {
  border-radius: 4px;
}
.tod-list-wrapper.tod-list-pagination-bordered .tod-list {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.tod-list-wrapper.tod-list-pagination-bordered .tod-list-pagination {
  padding-bottom: 16px;
  padding-right: 8px;
  width: 100%;
  margin-top: 0;
  padding-top: 16px;
  justify-content: end;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border: 1px solid var(--color-border-2);
  border-top: none;
}
.tod-list-wrapper .tod-list-card .tod-list-item {
  border: 1px solid var(--color-border-2);
  border-radius: 4px;
  margin-bottom: 12px !important;
}
.tod-list-wrapper .tod-list-card .tod-list-item.arco-list-item .arco-list-item-main {
  box-shadow: none;
}
.tod-list-wrapper .tod-list-card .tod-list-item:last-child {
  margin-bottom: 0;
}
.tod-list-wrapper .tod-list-card .arco-list-header {
  font-size: 18px;
  padding-left: 0;
  border-bottom: none;
}
.tod-list-wrapper .arco-list-default .tod-list-item-extra-content {
  padding: 12px 0;
}
.tod-list-wrapper .arco-list-small .tod-list-item-extra-content {
  padding: 8px 0;
}
.tod-list-wrapper .arco-list-large .tod-list-item-extra-content {
  padding: 16px 0;
}
.tod-list-wrapper .tod-list.arco-list-no-border .arco-list-content .arco-list-item:last-child {
  margin-bottom: 1px;
}
.tod-list-wrapper .tod-list.arco-list-no-border .arco-list-content .arco-list-item:last-child .arco-list-item-main {
  box-shadow: 0 1px 0 rgba(213, 219, 227, 0.5);
}
.log-ansi.arco,
.log-ansi.arco-theme {
  --log-ansi-border: var(--color-border-1);
  --log-ansi-bg-1: var(--color-bg-1);
  --log-ansi-bg-2: var(--color-bg-2);
  --log-ansi-bg-3: var(--color-bg-3);
  --log-ansi-bg-4: var(--color-bg-4);
  --log-ansi-bg-highlight: rgb(var(--yellow-6));
  --log-ansi-bg-highlight-focus: rgb(var(--orange-6));
  --log-ansi-text-1: var(--color-text-1);
  --log-ansi-text-2: var(--color-text-2);
  --log-ansi-text-3: var(--color-text-3);
  --log-ansi-text-4: var(--color-text-4);
  --log-ansi-text-highlight: var(--color-black);
  --log-ansi-hover: var(--color-neutral-3);
  --log-ansi-link: rgb(var(--link-6));
}
/** Dark Theme **/
.log-ansi.dark {
  --log-ansi-border: #e5e6eb;
  --log-ansi-bg-1: #17171A;
  --log-ansi-bg-2: #232324;
  --log-ansi-bg-3: #2a2a2b;
  --log-ansi-bg-4: #313132;
  --log-ansi-bg-highlight: #fbe94b;
  --log-ansi-bg-highlight-focus: #f9925a;
  --log-ansi-text-1: #e6e6e6;
  --log-ansi-text-2: #b3b3b3;
  --log-ansi-text-3: #808080;
  --log-ansi-text-4: #4d4d4d;
  --log-ansi-text-highlight: #020814;
  --log-ansi-hover: #4d4d4d;
  --log-ansi-link: #74a2ff;
}
.log-ansi {
  --log-ansi-border: #e5e6eb;
  --log-ansi-bg-header: #e6e6e6;
  --log-ansi-bg-1: #ffffff;
  --log-ansi-bg-2: #fcfdfe;
  --log-ansi-bg-3: #fafbfc;
  --log-ansi-bg-4: #f6f8fa;
  --log-ansi-bg-highlight: #fadc19;
  --log-ansi-bg-highlight-focus: #f77234;
  --log-ansi-text-1: #020814;
  --log-ansi-text-2: #41464f;
  --log-ansi-text-3: #80838a;
  --log-ansi-text-4: #ccced0;
  --log-ansi-text-highlight: #020814;
  --log-ansi-hover: #f1f3f5;
  --log-ansi-link: #1a6eff;
}
/** 4-bit color: xterm **/
.log-ansi {
  --log-ansi-black: #000000;
  --log-ansi-red: #cd0000;
  --log-ansi-green: #00cd00;
  --log-ansi-yellow: #cdcd00;
  --log-ansi-blue: #0000ee;
  --log-ansi-magenta: #cd00cd;
  --log-ansi-cyan: #00cdcd;
  --log-ansi-white: #e5e5e5;
  --log-ansi-bright-black: #7f7f7f;
  --log-ansi-bright-red: #ff0000;
  --log-ansi-bright-green: #00ff00;
  --log-ansi-bright-yellow: #ffff00;
  --log-ansi-bright-blue: #5c5cff;
  --log-ansi-bright-magenta: #ff00ff;
  --log-ansi-bright-cyan: #00ffff;
  --log-ansi-bright-white: #ffffff;
}
.log-ansi {
  position: relative;
  border: 1px solid var(--log-ansi-border);
  border-radius: 4px;
}
.log-ansi-scroller {
  background-color: var(--log-ansi-bg-1);
  color: var(--log-ansi-text-1);
}
.log-ansi-affix {
  z-index: 10;
  position: absolute;
  right: 32px;
  bottom: 16px;
  display: flex;
  flex-direction: column;
  row-gap: 0.5rem;
}
/** css override for arco button **/
.log-ansi .log-ansi-header .log-ansi-header-search button {
  color: var(--log-ansi-text-2);
}
.log-ansi .log-ansi-header .log-ansi-header-search button:hover {
  background-color: var(--log-ansi-bg-header);
}
.log-ansi .log-ansi-affix button {
  background-color: var(--log-ansi-bg-4);
  color: var(--log-ansi-text-2);
}
.log-ansi .log-ansi-affix button:disabled {
  color: var(--log-ansi-text-4);
}
.log-ansi-header {
  display: flex;
  column-gap: 1rem;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--log-ansi-border);
}
.log-ansi-header-meta {
  flex: 1;
  display: flex;
  align-items: stretch;
  column-gap: 1rem;
}
.log-ansi-header-meta-title {
  font-weight: 500;
  font-size: 1.1rem;
}
.log-ansi-header-meta-description {
  color: var(--log-ansi-text-2);
}
.log-ansi-header-search {
  display: flex;
  align-items: center;
  color: var(--log-ansi-text-2);
}
.log-ansi-header-search-counter {
  padding: 0 5px;
}
.log-ansi-line {
  font-size: 12px;
  /* font-family: ui-monospace, SFMono-Regular, 'SF Mono,Menlo', Consolas, 'Liberation Mono', monospace; */
  font-family: Menlo, 'RobotoMono', Monaco, 'Courier New', monospace;
  display: flex;
  background-color: var(--log-ansi-bg-1);
}
.log-ansi-line:hover {
  background-color: var(--log-ansi-hover);
}
.log-ansi-line a {
  color: var(--log-ansi-link);
}
.log-ansi-line-highlight {
  background-color: var(--log-ansi-bg-highlight);
  color: var(--log-ansi-text-highlight);
}
.log-ansi-line-highlight-focus {
  background-color: var(--log-ansi-bg-highlight-focus);
}
.log-ansi-line-number {
  flex: 0 0 48px;
  text-align: right;
  padding: 0 12px;
  user-select: none;
  color: var(--log-ansi-text-2);
}
.log-ansi-line-content {
  display: flex;
}
.log-ansi-line-log-content {
  display: inline-block;
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
}
/* stylelint-disable indentation */
/* stylelint-disable declaration-colon-newline-after */
.tod-oncall-trigger {
  position: fixed;
  top: calc(60vh + 19px);
  right: 0;
  z-index: 1002;
  display: flex;
  align-items: center;
  box-sizing: content-box;
  height: 22px;
  padding: 9px 25px 9px 5px;
  color: var(--color-white);
  white-space: nowrap;
  background: rgb(var(--primary-6));
  border-radius: 4px 0 0 4px;
  transform: translateX(calc(100% - 26px));
  cursor: pointer;
  transition: transform 0.3s ease;
  user-select: none;
}
.tod-oncall-trigger.tod-oncall-trigger-draggable {
  cursor: move;
  top: 45%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 25px;
  height: unset;
  padding: 10px 6px;
  line-height: 1.5;
  box-sizing: border-box;
  white-space: unset;
  transition: unset;
}
.tod-oncall-trigger.tod-oncall-trigger-draggable .tod-oncall-trigger-icon {
  font-size: 13px;
}
.tod-oncall-trigger.tod-oncall-trigger-draggable .tod-oncall-trigger-text {
  word-wrap: break-word;
  font-size: 13px;
  width: 100%;
  margin: 4px 0 0;
  text-align: center;
}
.tod-oncall-trigger-icon {
  font-size: 16px;
}
.tod-oncall-trigger-text {
  margin-right: 5px;
  margin-left: 5px;
  font-weight: 500;
  font-size: 13px;
}
.tod-oncall-trigger:hover {
  transform: translateX(20px);
}
.tod-oncall-modal.arco-modal {
  width: unset;
}
.tod-oncall-modal .arco-modal-content {
  padding: 0;
}
.tod-oncall-content {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 1000px;
  height: 640px;
  padding: 20px 24px;
  background: linear-gradient(82.57deg, rgba(255, 255, 255, 0.25) 34.68%, rgba(255, 255, 255, 0) 100%), radial-gradient(1146.89% 52.11% at -51.19% 40.7%, rgba(244, 255, 255, 0.46) 7.54%, #f5faff 100%);
  border-radius: 8px;
}
.tod-oncall-content-title.arco-typography {
  margin: 0;
  font-weight: 500;
  font-size: 18px;
  line-height: 26px;
}
.tod-oncall-content-main {
  display: flex;
  flex: 1;
  margin-top: 20px;
}
.tod-oncall-content-block {
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(203, 220, 237, 0.3);
}
.tod-oncall-content-left {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 240px;
  margin-right: 12px;
  padding: 8px;
  background: #fff;
  border: 1px solid var(--color-border-2);
}
.tod-oncall-content-right {
  flex: 1;
  overflow: hidden;
}
.tod-oncall-content-item {
  margin: 0;
  padding: 12px;
  background: linear-gradient(130.43deg, #ebfaf8 0%, rgba(255, 255, 255, 0) 23.46%);
  border-radius: 4px;
}
.tod-oncall-content-item dt {
  display: flex;
  align-items: center;
  height: 22px;
  margin-bottom: 4px;
  color: var(--color-text-1);
  font-weight: 500;
  font-size: 13px;
  line-height: 22px;
}
.tod-oncall-content-item dt > svg {
  margin-right: 8px;
  font-size: 16px;
}
.tod-oncall-content-item dd {
  margin: 0;
  margin-top: 8px;
  cursor: pointer;
}
.tod-oncall-content-doc {
  margin-bottom: 8px;
}
.tod-oncall-content-doc_item {
  position: relative;
  padding-left: 12px;
}
.tod-oncall-content-doc_item::before {
  position: absolute;
  top: 8px;
  left: 2px;
  display: block;
  width: 4px;
  height: 4px;
  background: var(--color-text-3);
  border-radius: 2px;
  content: '';
}
.tod-oncall-content-doc_item:hover::before {
  background: rgb(var(--primary-6));
}
.tod-oncall-content-doc_item_link_wrap {
  padding: 0;
}
.tod-oncall-content-doc_item_link_wrap:focus-visible {
  box-shadow: none;
}
.tod-oncall-content-doc_item_link.arco-typography {
  margin: 0;
  padding: 0;
  color: var(--color-text-1);
}
.tod-oncall-content-doc_item_link.arco-typography:hover {
  color: rgb(var(--primary-6));
}
.tod-oncall-content-doc_item_link.arco-typography:active {
  color: rgb(var(--primary-7));
}
.tod-oncall-content-doc_center {
  padding-left: 12px;
}
.tod-oncall-content-doc_center_link {
  padding: 0;
  color: rgb(var(--primary-6));
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
}
.tod-oncall-content-doc_center_link:hover {
  color: rgb(var(--primary-5));
}
.tod-oncall-content-doc_center_link:active {
  color: rgb(var(--primary-7));
}
.tod-oncall-content-doc_center_link:focus-visible {
  box-shadow: none;
}
.tod-oncall-content-group_item {
  display: flex;
  gap: 8px;
  align-items: center;
}
.tod-oncall-content-group_item_text.arco-typography {
  flex: 1;
  margin: 0;
  color: var(--color-text-1);
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
}
.tod-oncall-content-group_item_text.arco-typography:hover {
  color: rgb(var(--primary-6));
}
.tod-oncall-content-group_item_text.arco-typography:active {
  color: rgb(var(--primary-7));
}
body[arco-theme='dark'] .tod-oncall-content {
  background: var(--color-bg-1);
}
body[arco-theme='dark'] .tod-oncall-content-left {
  background: var(--color-bg-2);
}
body[arco-theme='dark'] .tod-oncall-content-item {
  background: linear-gradient(130.43deg, #1f443e 0%, rgba(42, 41, 40, 0) 23.46%);
}
.tod-overflow-items__group_item-vertical {
  margin-bottom: 4px;
}
.tod-overflow-items__group_item-vertical:last-child {
  margin-bottom: 0;
}
.tod-overflow-sub-title {
  width: 100%;
}
.tod-overflow-sub-title-space {
  display: flex;
  height: 20px;
  margin-right: 20px;
  white-space: nowrap;
  font-size: 12px;
}
.tod-overflow-sub-title-label {
  margin-right: 2px;
  color: var(--color-text-3);
}
.tod-overflow-sub-title-value {
  color: var(--color-text-2);
}
.tod-overflow-sub-title-more__trigger {
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  height: 20px;
  padding: 0 6px;
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-2);
  background: var(--color-bg-1);
  cursor: pointer;
  border: 1px solid var(--color-border-3);
  border-radius: 20px;
  box-sizing: border-box;
}
.tod-overflow-sub-title-more-popover .arco-volc3-popover-content,
.tod-overflow-sub-title-more-popover .arco-popover-content {
  padding: 8px 0 8px 12px;
}
.tod-overflow-sub-title-more__droplist {
  max-height: 263px;
  padding-right: 8px;
  overflow-y: auto;
}
.tod-overflow-sub-title-more__droplist-item {
  margin-bottom: 8px;
}
.tod-overflow-sub-title-more__droplist-item:last-child {
  margin-bottom: 0;
}
.tod-oi-sub-overflow-wrap {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
}
.tod-oi-sub-overflow-hidden-item,
.tod-oi-sub-overflow-item-mirror {
  position: absolute !important;
  white-space: nowrap;
  visibility: hidden;
  pointer-events: none;
}
.tod-overflow-text {
  word-break: break-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: default;
}
.tod-overflow-text-mid {
  word-break: break-all;
  white-space: nowrap;
  overflow-wrap: normal;
  cursor: default;
}
.tod-psm-input-help,
.tod-psm-input-error-icon.arco-icon {
  color: rgb(var(--red-6));
}
.tod-psm-network-status {
  box-sizing: border-box;
}
.tod-psm-network-status .arco-popover-content {
  width: 400px;
}
.tod-psm-network-status a {
  text-decoration: none;
  color: rgb(var(--primary-6));
  font-weight: 500;
  font-size: 12px;
}
.tod-psm-network-status-content-item:nth-of-type(1) {
  margin-top: 10px;
}
.tod-psm-network-status-content-item {
  padding-bottom: 12px;
}
.tod-psm-network-status-content-item-title {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  line-height: 22px;
  font-weight: 500;
  font-size: 14px;
  color: var(--color-text-1);
}
.tod-psm-network-status-content-item-title-text {
  margin-left: 8px;
  display: inline-block;
}
.tod-psm-network-status-content-item-desc {
  margin-left: 26px;
  color: var(--color-text-3);
  font-size: 12px;
  line-height: 20px;
}
.tod-psm-network-status-empty {
  color: var(--color-text-3);
}
.tod-psm-network-status-empty .arco-popover-content {
  padding: 0;
}
.tod-psm-network-status-empty-content {
  padding: 0;
  background: var(--color-bg-4);
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}
.tod-psm-network-status-empty-footer {
  padding: 8px 12px;
}
.tod-psm-network-status-empty .arco-popover-arrow.arco-trigger-arrow {
  background: var(--color-bg-4);
}
.tod-priority-tag {
  display: inline-flex;
  align-items: center;
  height: 20px;
  padding: 0 5px;
  border-radius: 2px;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
}
.tod-priority-tag-icon {
  margin-right: 4px;
  font-size: 12px;
}
.tod-priority-tag-icon svg {
  display: inline-block;
  color: inherit;
  font-style: normal;
  width: 1em;
  height: 1em;
  vertical-align: -2px;
}
.tod-priority-tag--default {
  color: #80838a;
  background: #eceded;
}
.tod-priority-tag--P0 {
  color: #db373f;
  background: #feeced;
}
.tod-priority-tag--P1 {
  color: #f2a200;
  background: #fdf3de;
}
.tod-priority-tag--P2 {
  color: #1cb267;
  background: #e2f5eb;
}
.tod-priority-tag--P3 {
  color: #1664ff;
  background: #ecf2ff;
}
.tod-priority-tag--urgent {
  color: #db373f;
  background: #feeced;
}
.tod-scm-select-tag {
  margin-right: 8px;
}
.tod-scm-select-version {
  color: var(--color-text-1);
  font-weight: 500;
}
.tod-scm-select-divider {
  margin: 8px;
  padding: 2px;
  border-radius: 100%;
  background-color: var(--color-text-3);
}
.tod-scm-select-content {
  overflow: hidden;
  text-overflow: ellipsis;
}
.arco-select-option-selected .tod-scm-select-version {
  color: rgb(var(--primary-6));
}
.arco-select-option-selected .tod-scm-select-divider {
  background-color: rgb(var(--primary-6));
}
.arco-select-option-disabled .tod-scm-select-version {
  color: var(--color-text-4);
}
.arco-select-option-disabled .tod-scm-select-tag {
  filter: opacity(0.5);
}
.arco-select-option-disabled .tod-scm-select-divider {
  color: var(--color-text-4);
}
.tod-scm-select-options-tab {
  margin-bottom: 8px;
}
.tod-scm-select-tooltip .arco-tooltip-content {
  background-color: #fff;
  color: var(--color-text-2);
}
.tod-scm-select-tooltip .arco-trigger-arrow.arco-tooltip-arrow {
  background-color: #fff;
  z-index: auto;
}
.tod-scm-select-tag.arco-tag-checked.arco-tag-bordered.arco-tag-green {
  color: rgb(var(--green-7));
  border-color: rgb(var(--green-2));
  background-color: transparent;
  min-width: 66px;
}
.tod-scm-select-tag.arco-tag-checked.arco-tag-bordered.arco-tag-green:hover {
  border-color: rgb(var(--green-2));
}
.tod-scm-select-tag.arco-tag-checked.arco-tag-bordered.arco-tag-gold {
  color: rgb(var(--orange-5));
  border-color: rgb(var(--orange-2));
  background-color: transparent;
  min-width: 66px;
}
.tod-scm-select-tag.arco-tag-checked.arco-tag-bordered.arco-tag-gold:hover {
  border-color: rgb(var(--orange-2));
}
.tod-scm-select-tag.arco-tag-checked.arco-tag-bordered.arco-tag-gray {
  color: rgb(var(--gray-7));
  border-color: rgb(var(--gray-2));
  background-color: transparent;
  min-width: 66px;
}
.tod-scm-select-tag.arco-tag-checked.arco-tag-bordered.arco-tag-gray:hover {
  border-color: rgb(var(--gray-2));
}
.tod-scm-select-arch-tag.arco-tag-checked.arco-tag-bordered.arco-tag-green {
  color: rgb(var(--success-6));
  border-color: rgba(var(--success-6), 0.2);
  background-color: rgb(var(--success-1));
  padding: 0 3px;
}
.tod-scm-select-arch-tag.arco-tag-checked.arco-tag-bordered.arco-tag-green:hover {
  border-color: rgba(var(--success-6), 0.2);
}
.tod-scm-select-arch-tag.arco-tag-checked.arco-tag-bordered.arco-tag-cyan {
  color: rgb(var(--teal-6));
  border-color: rgba(var(--teal-6), 0.2);
  background-color: rgb(var(--teal-1));
  padding: 0 3px;
}
.tod-scm-select-arch-tag.arco-tag-checked.arco-tag-bordered.arco-tag-cyan:hover {
  border-color: rgba(var(--teal-6), 0.2);
}
.tod-scm-select-more-btn.arco-btn-text:not(.arco-btn-disabled) {
  color: var(--color-text-3);
  width: 100%;
}
.tod-search-form.arco-form-inline {
  flex-flow: row wrap;
}
.tod-search-form.arco-form-inline.tod-search-form-inline {
  flex-flow: row nowrap;
}
.tod-search-form.arco-form-inline .arco-form-layout-inline {
  margin-right: 12px;
}
.tod-search-form.arco-form-inline .arco-form-item {
  flex-shrink: 0;
}
.tod-search-form-container.tod-search-form-inline {
  display: flex;
  align-items: center;
}
.tod-search-form-filter-dropdown {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 20px 0 20px;
  background: var(--color-bg-1);
  box-shadow: 0 0 14px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.tod-search-form-filter-dropdown .arco-form-item.arco-form-layout-inline {
  margin: 0 0 20px 0;
}
.tod-search-form-filter-count {
  display: inline-block;
  padding: 0 6px;
  margin-left: 6px;
  min-width: 20px;
  color: rgb(var(--primary-7));
  background-color: rgba(var(--link-3), 0.2);
  mix-blend-mode: normal;
  border-radius: 20px;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
}
.tod-search-form-reset.arco-btn-size-default,
.tod-search-form-reset > button {
  padding: 0 8px;
}
.tod-search-form-filter.arco-btn-size-default,
.tod-search-form-filter > button {
  padding: 0 10px;
}
.tod-stage-container {
  display: inline-block;
  box-sizing: border-box;
}
.tod-stage-wrapper {
  display: flex;
  align-items: center;
}
.tod-stage-stageWrapper {
  padding: 3px 3px;
  background: #fff;
  border-radius: 36px;
}
.tod-stage-stageGap {
  display: flex;
  flex: 1 0 auto;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin: 0 -4px 0 -4px;
  background: #f2f3f5;
}
.tod-stage-stageNode {
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
  padding: 4px 16px;
  line-height: 22px;
  white-space: nowrap;
  background: #fff;
  border: 1px solid #41464f00;
  border-radius: 30px;
}
.tod-stage-stageNode:hover .tod-stage-name {
  color: #020814 !important;
}
.tod-stage-innerGap {
  width: 1px;
  height: 12px;
  margin: 0 6px 0 8px;
}
.tod-stage-name {
  font-weight: 500;
  font-size: 14px;
}
.tod-stage-line {
  width: 1px;
  height: 10px;
  margin: 0 8px;
  background: #dde2e9;
}
.tod-stage-detail {
  color: #41464f;
  font-size: 12px;
}
.tod-stage-actionArea {
  margin-left: 8px;
}
.tod-stage-time {
  font-size: 12px;
}
.tod-stage-iconWrapper {
  display: flex;
  align-items: center;
  margin-right: 8px;
}
.tod-stage-node {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-weight: 500;
  font-size: 16px;
  border-radius: 20px;
}
.tod-stage-running-animation {
  animation: Rotate 1000ms linear infinite;
}
@keyframes Rotate {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.tod-table-text-button {
  color: rgb(var(--primary-6));
  font-size: 12px;
  cursor: pointer;
}
.tod-table-text-button:not(:last-child) {
  margin-right: 12px;
}
.tod-table-text-button-disabled {
  color: rgb(var(--gray-4));
  cursor: default;
}
.tod-table {
  position: relative;
  overflow: hidden;
}
.tod-table-resizer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
}
.tod-table-resizer-item {
  position: absolute;
  top: 0;
  left: 0;
  height: 0;
}
.tod-table-resizer-line {
  position: absolute;
  width: 12px;
  height: 100px;
  top: 0;
  right: -7px;
  cursor: col-resize;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.1s ease;
}
.tod-table-resizer-line.visible {
  visibility: visible;
}
.tod-table-resizer-line.highlight {
  opacity: 1;
}
.tod-table-resizer-bar {
  width: 2px;
  height: 100%;
  margin-left: 5px;
  background-color: rgb(var(--primary-6));
}
.tod-table-add-row {
  margin-top: 12px;
}
.tod-table-a-button {
  margin-right: 4px;
  color: rgb(var(--primary-6));
}
.tod-table-a-button-disabled {
  color: rgb(var(--gray-4));
}
.tod-table-editable-row:hover .tod-table-editable-cell {
  padding: 5px 11px;
  border: 1px solid var(--color-border-3);
  border-radius: 4px;
}
.tod-table-editable-cell {
  box-sizing: border-box;
  padding: 6px 12px;
  min-height: 32px;
}
.tod-text-link a {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}
.tod-text-link a:hover {
  color: rgb(var(--primary-6));
}
.tod-text-link-important a {
  color: rgb(var(--primary-6));
}
.tod-text-link-with-popover a,
.tod-text-link-with-popover span {
  cursor: pointer;
  border-bottom: 1px dashed;
}
.tod-text-link-expected a {
  border-bottom: 1px solid;
}
.tod-text-link-popover .arco-popover-content {
  max-height: 100px;
  overflow-y: scroll;
}
.tod-timezone-select-button {
  box-shadow: none !important;
  border: none !important;
  background-color: unset !important;
}
.tod-timezone-select-button-open,
.tod-timezone-select-button:hover {
  background-color: var(--color-bg-4) !important;
}
.tod-timezone-select-item {
  margin: 0 -12px;
  padding: 0 12px;
}
.tod-tree .tod-tree-tag {
  height: 16px;
  margin-right: 6px;
  padding: 0 6px;
  color: var(--color-text-2);
  font-size: 12px;
  border-radius: 4px;
  background-color: var(--color-bg-4);
}
.tod-tree .tod-tree-tag-after {
  margin-right: 0px;
  margin-left: 6px;
}
.tod-tree.arco-tree-size-mini .tod-tree-tag {
  margin-right: 2px;
  transform: scale(0.83);
}
.tod-tree.arco-tree-size-mini .tod-tree-tag-after {
  margin-left: 2px;
  transform: scale(0.83);
}
.tod-tree .tod-tree-tag-wrap-right {
  margin-left: 6px;
}
.tod-tree .tod-tree-tag-wrap-left {
  margin-right: 6px;
}
.tod-tree.arco-tree-size-mini .tod-tree-tag-wrap-left {
  margin-right: 2px;
}
.tod-tree.arco-tree-size-mini .tod-tree-tag-wrap-right {
  margin-left: 2px;
}
.tod-tree-label {
  display: inline-flex;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  color: rgb(var(--gray-8));
  background-color: rgb(var(--gray-2));
  margin-left: 8px;
  font-size: 12px;
}
.tod-tree-more {
  position: absolute;
  display: inline-flex;
  align-items: center;
  right: 8px;
  top: 0;
  height: 100%;
}
.tod-tree-actions {
  display: flex;
  opacity: 0;
}
.arco-tree-node:hover .tod-tree-actions {
  opacity: 1;
}
.arco-tree-node-selected .tod-tree-actions {
  opacity: 1;
}
.tod-tree-action-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 4px;
}
.tod-tree-diff {
  font-size: 12px;
}
.tod-tree-diff-addition {
  margin-left: 4px;
  color: rgb(var(--success-6));
}
.tod-tree-diff-deletion {
  margin-left: 4px;
  color: rgb(var(--danger-6));
}
.tod-tree .tod-tree-icon {
  stroke: none;
}
.tod-tree-directory-icon-content {
  fill: var(--color-text-2);
}
.arco-tree-node-expanded .tod-tree-directory-icon-content {
  fill: var(--color-text-3);
}
.arco-tree-node-selected .tod-tree-directory-icon-content {
  fill: rgb(var(--primary-7));
}
.tod-tree-file-icon-content {
  fill: var(--color-text-3);
  stroke: var(--color-text-3);
}
.arco-tree-node-selected .tod-tree-file-icon-content {
  fill: rgb(var(--primary-7));
  stroke: rgb(var(--primary-7));
}
.scroll-bar {
  scrollbar-width: 11px;
}
.scroll-bar ::-webkit-scrollbar-thumb {
  border-radius: 10px;
  transition: all 1s;
  border: 2px solid transparent;
  background-clip: padding-box;
}
.scroll-bar ::-webkit-scrollbar {
  width: 11px;
  height: 11px;
}
.scroll-bar ::-webkit-scrollbar:hover {
  width: 11px;
  height: 11px;
}
.tod-user-profile__main {
  border-radius: 8px;
  box-shadow: 0px 8px 24px 8px rgba(31, 35, 41, 0.04), 0px 6px 12px 0px rgba(31, 35, 41, 0.04), 0px 4px 8px -8px rgba(31, 35, 41, 0.06);
}
body[arco-theme='dark'] .tod-user-profile__main {
  border: 1px solid var(--color-border-3);
}
.tod-user-profile-popup {
  width: 320px;
  position: relative;
  height: 480px;
  overflow: hidden;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: flex-start;
  background-color: var(--color-bg-8);
  scrollbar-width: 11px;
}
.tod-user-profile-popup ::-webkit-scrollbar-thumb {
  border-radius: 10px;
  transition: all 1s;
  border: 2px solid transparent;
  background-clip: padding-box;
}
.tod-user-profile-popup ::-webkit-scrollbar {
  width: 11px;
  height: 11px;
}
.tod-user-profile-popup ::-webkit-scrollbar:hover {
  width: 11px;
  height: 11px;
}
.tod-user-profile-popup-wheel-section {
  position: relative;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
  transition: all 5s ease-out;
  transition: all 0.4s cubic-bezier(0.39, 0.58, 0.57, 1);
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-header {
  width: 100%;
  height: 136px;
  position: relative;
  flex-shrink: 0;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-header .tod-user-profile-popup-header-cover {
  position: absolute;
  width: 100%;
  height: 100%;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-header .tod-user-profile-popup-header-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  -webkit-user-select: none;
  user-select: none;
  background-color: var(--color-bg-8);
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body {
  position: relative;
  width: 100%;
  flex-grow: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: flex-start;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-info {
  opacity: 1;
  transition: all 0.4s cubic-bezier(0.39, 0.58, 0.57, 1);
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-info .tod-user-profile-popup-body-avatar {
  position: absolute;
  left: 12px;
  top: -48px;
  width: 96px;
  height: 96px;
  border-radius: 50%;
  background-color: var(--color-bg-8);
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-info .tod-user-profile-popup-body-avatar-wrapper {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid var(--color-bg-8);
  display: flex;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-info .tod-user-profile-popup-body-username {
  margin: 56px 16px 0;
  line-height: 28px;
  font-size: 20px;
  font-weight: 600;
  word-break: break-word;
  color: var(--color-text-1);
  display: -webkit-box;
  flex-shrink: 0;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-info .tod-user-profile-popup-body-username-text {
  margin-right: 8px;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-info-main-many-info {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-shrink: 0;
  opacity: 1;
  transition: all 0.4s cubic-bezier(0.39, 0.58, 0.57, 1);
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content {
  flex-grow: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  overflow: hidden;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-desc {
  overflow: hidden;
  margin: 8px 16px 0;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-desc span {
  font-size: 14px;
  line-height: 22px;
  color: var(--color-text-3);
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-actions {
  margin: 12px 16px 4px 16px;
  display: flex;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-actions .arco-btn {
  flex: 1;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-actions.tod-user-profile-popup-body-main-actions-multi {
  justify-content: space-between;
  align-items: center;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-actions.tod-user-profile-popup-body-main-actions-multi .arco-btn {
  height: 43px;
  margin-right: 6px;
  display: flex;
  align-items: center;
  flex-flow: column;
  justify-content: center;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-actions.tod-user-profile-popup-body-main-actions-multi .arco-btn span {
  margin: 0;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-actions.tod-user-profile-popup-body-main-actions-multi .arco-btn:last-child {
  margin-right: 0;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-container {
  flex-grow: 1;
  width: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-container::-webkit-scrollbar {
  display: none;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-container:hover::-webkit-scrollbar {
  display: block;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-container .tod-user-profile-popup-body-main-fields {
  --profile-fields-length: 3;
  box-sizing: border-box;
  font-size: 14px;
  line-height: 20px;
  padding: 16px 0 0 16px;
  width: 300px;
  display: grid;
  grid-template-columns: max-content auto;
  grid-column-gap: 12px;
  grid-template-rows: repeat(var(--profile-fields-length), max-content);
  grid-row-gap: 16px;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-container .tod-user-profile-popup-body-main-fields .arco-descriptions-item,
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-container .tod-user-profile-popup-body-main-fields .arco-descriptions-item-label,
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-container .tod-user-profile-popup-body-main-fields .arco-descriptions-item-value {
  padding-bottom: 16px;
}
.tod-user-profile-popup-wheel-section .tod-user-profile-popup-body-content .tod-user-profile-popup-body-main-container .tod-user-profile-popup-body-main-fields .arco-descriptions-item-value {
  width: 180px;
}
.tod-user-profile-popup-wheel-section .tod-user-profile__empty {
  height: 100%;
}
.tod-user-profile-popup-wheel-section .tod-user-profile__empty .tod-user-profile-popup-header {
  overflow: hidden;
}
.tod-user-profile-popup-wheel-section .tod-user-profile__empty .tod-user-profile-popup-header-fill {
  width: 100%;
  height: 180px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%);
}
.tod-user-profile-popup-wheel-section .tod-user-profile__empty .tod-user-profile-popup-body {
  height: 384px;
  width: 100%;
  display: flex;
  align-items: center;
}
.tod-user-profile-popup-wheel-section .tod-user-profile__empty .tod-user-profile-popup-body .tod-user-profile-popup-body-avatar-wrapper {
  background-color: var(--color-bg-4);
  box-shadow: 0px 8px 24px 8px rgba(31, 35, 41, 0.04), 0px 6px 12px 0px rgba(31, 35, 41, 0.04), 0px 4px 8px -8px rgba(31, 35, 41, 0.06);
}
.tod-user-profile-popup-wheel-section .tod-user-profile__empty .tod-user-profile-popup-body-main {
  display: flex;
  height: 100%;
  width: 100%;
  align-items: center;
  justify-content: center;
}
.tod-user-profile-popup-wheel-section .tod-user-profile__empty .tod-user-profile-popup-body-main p {
  color: var(--color-text-3);
}
.tod-user-profile-popup-wheel-section .tod-user-profile__empty .tod-user-profile-popup-body-main .force-icon {
  font-size: 80px;
}
.tod-user-profile-popup-footer {
  flex-shrink: 0;
  padding: 15px 0;
  height: 52px;
  display: flex;
  justify-content: center;
  border-top: 1px solid var(--color-border-2);
  background-color: var(--color-bg-8);
}
.tod-user-profile-popup-shrink-header {
  width: 100%;
  height: 48px;
  position: absolute;
  top: 0;
  left: 0;
  background: transparent;
  pointer-events: none;
  transition: all 5s ease-out;
  transition: all 0.4s cubic-bezier(0.39, 0.58, 0.57, 1);
}
.tod-user-profile-popup-shrink-header-cover {
  position: absolute;
  width: 100%;
  height: 100%;
  filter: blur(20px);
  opacity: 0.3;
}
.tod-user-profile-popup-shrink-header-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  user-select: none;
}
.tod-user-profile-popup-shrink-header-cover-mask {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(180deg, rgba(0, 0, 0, 0.16), rgba(0, 0, 0, 0.04));
}
.tod-user-profile-popup-top {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  margin: 12px 16px 0;
  align-items: center;
  width: calc(100% - 32px);
  height: 32px;
  pointer-events: none;
}
.tod-user-profile-popup-top-button {
  width: 24px;
  height: 24px;
  margin-right: 4px;
  font-size: 16px;
  border-radius: 50%;
  cursor: pointer;
  pointer-events: auto;
  text-align: center;
  color: var(--color-fill-5);
}
.tod-user-profile-popup-top-button:hover {
  background-color: var(--color-bg-6);
}
.tod-user-profile-popup-top-button svg {
  margin-right: 2px;
}
.tod-user-profile-popup-top-center {
  justify-content: center;
  align-items: center;
  display: flex;
  opacity: 0;
  transition: all 5s ease-out;
  transition: all 0.4s cubic-bezier(0.39, 0.58, 0.57, 1);
}
.tod-user-profile-popup-top-center .tod-user-profile-popup-top-avatar-container {
  position: relative;
  display: inline-block;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 6px;
}
.tod-user-profile-popup-top-username {
  line-height: 20px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-1);
  max-width: 124px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tod-user-profile-popup--shrink .tod-user-profile-popup-body-main-many-info {
  opacity: 0;
}
.tod-user-profile-popup--shrink .tod-user-profile-popup-shrink-header {
  background-color: var(--color-bg-8);
  pointer-events: auto;
}
.tod-user-profile-popup--shrink .tod-user-profile-popup-top-center {
  opacity: 1;
}
.tod-avatar-popover .arco-avatar-group-popover {
  display: flex;
  flex-direction: column;
}
.tod-avatar-popover .arco-avatar-group-popover .tod-avatar-wrap:last-child .user-profile-avatar {
  margin-bottom: 0;
}
.tod-avatar-popover .arco-avatar-group-popover .user-profile-avatar {
  margin-bottom: 4px;
  cursor: pointer;
}
.tod-user-profile-box {
  display: inline-block;
  cursor: pointer;
}
.tod-user-profile-box .tod-avatar-wrap {
  display: inline-block;
  cursor: pointer;
}
.tod-user-profile-box .rd-overflow-text {
  cursor: pointer;
}
.tod-user-profile-box .arco-avatar-group-max-count-avatar {
  cursor: pointer;
}
.tod-user-profile__profile-section {
  position: relative;
  --profile-width: 320px;
  --profile-length: 1;
  --profile-current-index: 0;
  width: var(--profile-width);
  overflow: hidden;
  border-radius: 8px;
  transform: translateZ(0);
}
.tod-user-profile__profiles-container--multi {
  display: flex;
  width: calc(var(--profile-width) * var(--profile-length));
  transform: translateX(calc(-1 * var(--profile-current-index) * var(--profile-width)));
  transition: transform 0.2s ease-out;
}
.tod-action-list {
  padding: 12px 16px 0;
  background: linear-gradient(160deg, rgb(var(--primary-2)), hsla(0, 0%, 100%, 0) 60px), #fff;
}
.tod-action-list .arco-list-header {
  padding: 0 !important;
  border: none;
}
.tod-action-list .tod-action-list-content .tod-action-list-item:last-child {
  border: none;
}
.tod-action-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 4px;
}
.tod-action-list-header .tod-action-list-header-title {
  display: flex;
  gap: 6px;
  align-items: center;
}
.tod-action-list-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid var(--color-border-2, #EAEDF1);
}
.tod-action-list-item .tod-action-list-item-content {
  flex: 1;
  display: inline-flex;
  flex-direction: column;
  gap: 2px;
}
.tod-action-list-item .tod-action-list-item-content .tod-action-list-item-content-title {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
}
.tod-action-list-item .tod-action-list-item-content .tod-action-list-item-content-description {
  color: var(--color-text-3, '#737A87');
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.tod-action-list-item .tod-action-list-item-action {
  display: inline-flex;
  align-items: center;
}
.tod-action-list-footer {
  cursor: pointer;
  padding: 10px 0;
}
.tod-action-list-footer-inner {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  color: var(--color-text-2, '#42464E');
}
.tod-action-list-footer-inner .tod-action-list-footer-inner-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  font-size: 12px;
}
.tod-scm-repo-select {
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  width: 100%;
}
.tod-principal-universal-search-option-item {
  display: flex;
  gap: 5px;
  align-items: center;
}
.tod-principal-universal-search-option-item .tod-pure-avatar {
  width: 20px !important;
  height: 20px !important;
  background-color: var(--color-fill-1);
}
.tod-principal-universal-search-render-tag-user .tod-pure-avatar {
  width: 18px !important;
  height: 0 !important;
  background-color: var(--color-bg-1);
}
.tod-principal-universal-search-render-tag-user .arco-avatar-image {
  width: 18px !important;
  height: 18px;
}
.tod-principal-universal-search-render-tag {
  line-height: 30px;
}
.tod-principal-universal-search-render-tag .tod-pure-avatar {
  width: 18px !important;
  height: 18px !important;
  background-color: inherit;
}
.tod-principal-universal-search-render-tag svg {
  background-color: inherit !important;
  vertical-align: middle;
}
.tod-x-cloud-site-tag-site-wrap-small {
  display: inline-flex;
  align-items: center;
  color: rgb(var(--color-text-1));
  gap: 5px;
  font-size: 13px;
  line-height: 22px;
}
.tod-x-cloud-site-tag-site-wrap-small .vod-tag-pro.vod-tag-pro-big .vod-tag {
  font-weight: 400;
}
.tod-x-cloud-site-tag-site-wrap-small .tod-x-cloud-site-tag-site-flag {
  width: 16px;
  height: 16px;
  font-size: 16px;
  line-height: 16px;
  border-radius: 50%;
}
.tod-x-cloud-site-tag-site-wrap {
  display: inline-flex;
  align-items: center;
  color: rgb(var(--color-text-1));
  gap: 8px;
  font-size: 13px;
  line-height: 22px;
}
.tod-x-cloud-site-tag-site-wrap .vod-tag-pro.vod-tag-pro-big .vod-tag {
  font-weight: 400;
}
.tod-x-cloud-site-tag-site-wrap .tod-x-cloud-site-tag-site-flag {
  width: 18px;
  height: 18px;
  font-size: 18px;
  line-height: 18px;
  border-radius: 50%;
}
.tod-x-cloud-site-tag-env-tag {
  display: inline-block;
  border-radius: 4px;
  color: rgb(var(--color-text-1));
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  padding: 0 4px;
}
.tod-x-cloud-site-tag-env-tag-small {
  display: inline-block;
  border-radius: 4px;
  color: rgb(var(--color-text-1));
  font-size: 10px;
  font-weight: 500;
  line-height: 16px;
  padding: 0 6px;
}
.tod-x-cloud-site-tag-vregion-tag {
  max-width: 100%;
  display: inline-flex;
  gap: 6px;
  align-items: center;
  font-size: 13px;
  line-height: 22px;
  color: rgb(var(--color-text-1));
}
.tod-x-cloud-site-tag-vregion-tag .tod-x-cloud-site-tag-vregion-flag {
  width: 18px;
  height: 18px;
  font-size: 16px;
  line-height: 18px;
  flex-shrink: 0;
}
.tod-x-cloud-site-tag-vregion-tag .tod-x-cloud-site-tag-vregion-tag-name {
  flex: 1;
  overflow: hidden;
}
.tod-x-cloud-site-tag-vregion-tag .vod-tag-pro.vod-tag-pro-big .vod-tag {
  font-weight: 400;
  flex-shrink: 0;
}
.tod-bilayer-tab * {
  margin: 0;
  padding: 0;
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-header-title {
  height: 56px;
  align-items: flex-start;
  border-radius: 4px 4px 0 0;
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-header-nav-card-gutter .arco-tabs-header-title:hover {
  background-color: var(--color-bg-4);
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-header-title-text {
  margin-right: 6px;
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-header {
  position: relative;
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-header-nav-card-gutter.arco-tabs-header-nav-horizontal::before {
  bottom: 0px;
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-header-nav-card-gutter .arco-tabs-header-title-active {
  border-top: none;
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-header-nav-card-gutter .arco-tabs-header-title-active.arco-tabs-header-title {
  background-color: #fff;
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-header-nav-card-gutter .arco-tabs-header-title-active.arco-tabs-header-title .tod-bilayer-tab-subtitle {
  font-weight: 400;
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-content.arco-tabs-content-horizontal {
  border: none;
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-header-nav-card-gutter .arco-tabs-header-title {
  border-bottom: none;
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-header-nav-card-gutter .arco-tabs-header-title:first-of-type {
  margin-left: 32px;
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-header-nav-card-gutter .arco-tabs-add-icon {
  height: 56px;
}
.tod-bilayer-tab.tod-bilayer-tab-wrapper .arco-tabs-close-icon {
  font-size: 10px;
  margin-top: 2px;
  font-weight: 600;
}
.tod-bilayer-tab-title:hover {
  color: #1664ff;
}
.tod-bilayer-tab-subtitle {
  color: #80838a;
  font-size: 12px;
}
.tod-bilayer-tab-plus-icon-container {
  width: 20px;
  height: 20px;
  margin-left: 16px;
  line-height: 20px;
  border-radius: 100%;
  background-color: #f6f8fa;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.tod-column-filter-table-popover-wrap {
  margin-bottom: 16px;
}
.tod-column-filter-table-popover .arco-popover-content {
  padding: 0;
}
.tod-column-filter-table-popover .arco-popover-content .arco-checkbox {
  white-space: nowrap;
}
.tod-column-filter-table-popover .arco-popover-content .arco-btn {
  width: 100%;
}
.tod-column-filter-table-popover .tod-check-group-wrap {
  padding: 4px 20px 0 20px;
}
.tod-column-filter-table-divider {
  margin: 10px 0 0 0;
}
.tod-dropdown-menu.arco-dropdown-menu {
  padding: 8px 6px;
}
.tod-dropdown-menu.arco-dropdown-menu .arco-input-group-wrapper {
  margin-bottom: 4px;
  padding-left: 3px;
  padding-right: 3px;
}
.tod-dropdown-menu.arco-dropdown-menu .arco-input-group-wrapper .arco-input {
  padding-left: 4px;
  font-size: 12px;
  height: 24px;
  line-height: 24px;
}
.tod-dropdown-menu.arco-dropdown-menu .arco-dropdown-menu-inner {
  padding: 0;
}
.tod-dropdown-menu.arco-dropdown-menu .arco-dropdown-menu-item {
  margin-top: 4px;
  margin-bottom: 4px;
}
.tod-dropdown-menu.arco-dropdown-menu .arco-dropdown-menu-item:last-child {
  margin-bottom: 0;
}
.tod-editableTable-row .arco-table-td {
  vertical-align: top;
}
.tod-editableTable-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 16px;
  color: var(--arco-volc3-color-text-2, var(--color-text-2));
  border-radius: 4px;
  position: relative;
  cursor: pointer;
}
.tod-editableTable-icon:hover::before {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  content: '';
  width: 100%;
  height: 100%;
  background: var(--arco-volc3-color-fill-4, var(--color-fill-4));
  opacity: 0.05;
  border-radius: 2px;
  pointer-events: none;
}
.tod-editableTable-icon-close {
  font-size: 16px;
  color: #db373f;
}
.tod-editableTable-icon-check {
  font-size: 16px;
  color: #1cb267;
}
.tod-editableText {
  display: inline-block;
  width: 100%;
}
.tod-editableText-title {
  display: flex;
  align-items: center;
  height: 28px;
}
.tod-editableText-title__text {
  font-family: 'PingFang SC';
  font-style: normal;
  font-size: 13px;
  line-height: 20px;
  color: var(--arco-volc3-color-text-1, var(--color-text-1));
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tod-editableText-title__btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  padding: 4px;
  width: 24px;
  height: 24px;
  line-height: 24px;
  font-size: 16px;
  color: var(--arco-volc3-color-fill-3, var(--color-fill-3));
  border-radius: 4px;
  text-align: center;
  box-sizing: border-box;
  cursor: pointer;
}
.tod-editableText-title__btn:hover {
  background: var(--arco-volc3-color-border-2, var(--color-border-2));
}
.tod-editableText-Popconfirm__title {
  font-weight: 600;
  font-size: 12px;
  line-height: 20px;
  color: var(--arco-volc3-color-text-1, var(--color-text-1));
  margin-bottom: 8px;
}
.tod-editableText-edit {
  display: flex;
  align-items: center;
}
.tod-editableText-edit-icon {
  font-size: 16px;
  margin-right: 12px;
  cursor: pointer;
}
.tod-editableText-edit-icon-check {
  color: rgba(var(--arco-volc3-success-6, var(--success-6)));
}
.tod-editableText-edit-icon-close {
  color: rgba(var(--arco-volc3-danger-6, var(--danger-6)));
}
.tod-editableText-icons {
  margin-left: 16px;
  white-space: nowrap;
}
.tod-empty {
  width: 100%;
  box-sizing: border-box;
  text-align: center;
}
.tod-empty-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  font-size: 80px;
}
.tod-empty-title {
  margin-top: 12px;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #1d2129;
}
.tod-empty-description {
  margin-top: 12px;
  font-size: 13px;
  line-height: 22px;
  color: #80838a;
}
.tod-empty-description .arco-volc3-link,
.tod-empty-description .arco-link {
  padding: 1px 8px;
}
.tod-empty-description--hasTitle {
  margin-top: 4px;
}
.tod-empty-action {
  margin-top: 24px;
}
.tod-empty-space {
  display: inline-flex;
  margin-right: 12px;
}
.tod-empty-space:last-child {
  margin-right: 0;
}
.tod-empty--small .arco-icon {
  height: 60px;
  font-size: 60px;
}
.tod-empty--small .force-icon {
  height: 60px;
  font-size: 60px;
}
.tod-empty--mini .arco-icon {
  height: 48px;
  font-size: 48px;
}
.tod-empty--mini .force-icon {
  height: 48px;
  font-size: 48px;
}
.tod-guide-mask {
  position: absolute;
  left: 0;
  top: 0;
  border-color: rgba(0, 0, 0, 0.6);
  border-style: solid;
  z-index: 1001;
  box-sizing: border-box;
}
.tod-guide-mask--hidden {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  border-color: transparent;
}
.tod-guide-mask--hidden::after {
  opacity: 0;
}
.tod-guide-mask::after {
  content: '';
  display: block;
  position: absolute;
  top: -8px;
  left: -8px;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  border: 8px solid #fff;
  box-shadow: 0 0 0 1px #fff;
  background: transparent;
  box-sizing: content-box;
}
.tod-guide-richcard {
  position: absolute;
  width: 320px;
  padding: 16px;
  z-index: 1100;
  background: #fff;
  border-radius: 8px;
  box-sizing: border-box;
  box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.05), 0px 15px 35px -2px rgba(0, 0, 0, 0.05);
}
.tod-guide-richcard * {
  text-align: left;
}
.tod-guide-richcard-image-title {
  margin-top: -16px;
  margin-left: -16px;
  width: calc(100% + 32px);
  margin-bottom: 6px;
}
.tod-guide-richcard-image-title image,
.tod-guide-richcard-image-title img {
  width: 100%;
}
.tod-guide-richcard-title {
  display: block;
  width: calc(100% - 12px);
  font-weight: 500;
  font-size: 16px;
  height: 24px;
  color: #020814;
  line-height: 24px;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tod-guide-richcard-content {
  font-size: 12px;
  line-height: 22px;
  font-weight: 400;
  color: #41464f;
  margin-bottom: 16px;
}
.tod-guide-richcard-close-icon {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  font-size: 24px;
  text-align: center;
  line-height: 24px;
  cursor: pointer;
}
.tod-guide-richcard-close-icon:hover {
  background: rgba(2, 8, 20, 0.05);
  border-radius: 4px;
}
.tod-guide-richcard-arrow {
  position: absolute;
  width: 8px;
  height: 8px;
  background: linear-gradient(106.19deg, #f0f5ff -29.01%, #f8faff 68.62%);
  transform: rotate(45deg);
  border: 1px solid #dde2e9;
  border-radius: 1px;
}
.tod-guide-richcard-footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.tod-guide-richcard-footer * {
  font-size: 12px;
  font-weight: 400;
}
.tod-guide-richcard-footer-text {
  color: #80838a;
  font-weight: 500;
}
.tod-guide-richcard-footer-btn-group {
  display: flex;
  justify-self: flex-end;
}
.tod-guide-richcard-footer-prev-btn {
  margin-right: 8px;
}
.tod-guide-card {
  position: absolute;
  width: 320px;
  padding: 20px;
  z-index: 1100;
  background: linear-gradient(106.19deg, #f0f5ff -29.01%, #f8faff 68.62%);
  border: 1px solid #dde2e9;
  border-radius: 8px;
  box-sizing: border-box;
}
.tod-guide-card * {
  text-align: left;
}
.tod-guide-card-title {
  display: block;
  width: calc(100% - 12px);
  font-weight: 500;
  font-size: 16px;
  height: 24px;
  color: #020814;
  line-height: 24px;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tod-guide-card-content {
  font-size: 12px;
  line-height: 22px;
  font-weight: 400;
  color: #41464f;
  margin-bottom: 16px;
}
.tod-guide-card-close-icon {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  font-size: 24px;
  text-align: center;
  line-height: 24px;
  cursor: pointer;
}
.tod-guide-card-close-icon:hover {
  background: rgba(2, 8, 20, 0.05);
  border-radius: 4px;
}
.tod-guide-card-arrow {
  position: absolute;
  width: 8px;
  height: 8px;
  background: linear-gradient(106.19deg, #f0f5ff -29.01%, #f8faff 68.62%);
  transform: rotate(45deg);
  border: 1px solid #dde2e9;
  border-radius: 1px;
}
.tod-guide-card-footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.tod-guide-card-footer * {
  font-size: 12px;
  font-weight: 400;
}
.tod-guide-card-footer-text {
  color: #80838a;
  font-weight: 500;
}
.tod-guide-card-footer-btn-group {
  display: flex;
  justify-self: flex-end;
}
.tod-guide-card-footer-prev-btn {
  margin-right: 8px;
}
.tod-guide-tip {
  position: absolute;
  width: 272px;
  padding: 12px;
  z-index: 1100;
  background: #4281ff;
  border-radius: 8px;
  box-sizing: border-box;
}
.tod-guide-tip.bits-light {
  border: 1px solid #dde2e9;
  background: linear-gradient(219deg, #6332ff 0%, #00e5e5 45.87%, #1664ff 100%);
  box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.05);
  background-size: calc(100% + 14.14px) calc(100% + 14.14px);
  background-position: -7.06px -7.06px;
  border-radius: 12px;
  padding: 0;
}
.tod-guide-tip.bits-light .tod-guide-tip-content-wrap {
  margin: 1px;
  border-radius: 10px;
  padding: 11px 14px 14px 14px;
  background: #ffffff;
}
.tod-guide-tip.bits-light .tod-guide-tip-arrow {
  background: white;
  border: 1px solid #dde2e9;
  border-radius: 1px;
  box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.05);
}
.tod-guide-tip.bits-light .tod-guide-tip-arrow-gradient {
  width: 14.14px;
  height: 14.14px;
  position: absolute;
  background: linear-gradient(219deg, #6332ff 0%, #00e5e5 45.87%, #1664ff 100%);
  z-index: 9;
}
.tod-guide-tip.bits-light .tod-guide-title {
  color: #020814;
}
.tod-guide-tip.bits-light .tod-guide-content {
  color: #41464f;
}
.tod-guide-tip.bits-light .tod-guide-tip-close-icon {
  color: #000;
}
.tod-guide-tip * {
  text-align: left;
}
.tod-guide-tip-title {
  display: block;
  width: calc(100% - 14px);
  font-weight: 500;
  font-size: 14px;
  height: 22px;
  color: #fff;
  line-height: 22px;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tod-guide-tip-content {
  width: calc(100% - 12px);
}
.tod-guide-tip-title + .tod-guide-tip-content {
  width: 100%;
}
.tod-guide-tip-content {
  font-size: 12px;
  line-height: 22px;
  font-weight: 400;
  color: #fff;
}
.tod-guide-tip-close-icon {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  font-size: 24px;
  color: #fff;
  text-align: center;
  line-height: 24px;
  cursor: pointer;
}
.tod-guide-tip-close-icon:hover {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}
.tod-guide-tip-arrow {
  position: absolute;
  width: 10px;
  height: 10px;
  background: #4281ff;
  transform: rotate(45deg);
  border-radius: 1px;
}
.tod-guide-tip-footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12px;
}
.tod-guide-tip-footer * {
  font-size: 12px;
  font-weight: 400;
}
.tod-guide-tip-footer-text {
  color: #80838a;
  font-weight: 500;
}
.tod-guide-tip-footer-btn-group {
  display: flex;
  justify-self: flex-end;
}
.tod-guide-tip-footer-prev-btn {
  margin-right: 8px;
}
.tod-guide-hotspot {
  position: absolute;
  width: 20px;
  height: 20px;
}
.tod-guide-hotspot-outer {
  width: 20px;
  height: 20px;
  animation: scale 1.4s infinite linear;
  background: rgba(106, 166, 255, 0.6);
  border-radius: 50%;
}
.tod-guide-hotspot-inner {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  width: 12px;
  height: 12px;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  background-color: #4e83fd;
}
@keyframes scale {
  0% {
    transform: scale(1.3);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.3);
  }
}
.j-byted-guide__custom-anchor {
  position: absolute;
  pointer-events: none;
  opacity: 0;
}
.arco-rc-tooltip-button {
  padding: 20px;
}
.tod-insert-row-table-wrap .tod-insert-row-btn {
  margin-top: 12px;
}
.tod-side-menu {
  /** -------- 概览 -------- **/
}
.tod-side-menu ul,
.tod-side-menu li {
  list-style: none;
  text-decoration: none;
}
.tod-side-menu * {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}
.tod-side-menu span {
  color: var(--color-text-1);
}
.tod-side-menu.tod-side-menu-wrapper {
  height: 100vh;
  font-size: 13px;
  background-color: var(--color-bg-4);
  /** -------- arco 默认样式修改 -------- **/
  /** -------- arco 默认收起样式修改 -------- **/
  /** -------- menu 无 icon 时的 arco 默认收起样式修改 -------- **/
}
.tod-side-menu.tod-side-menu-wrapper .arco-menu-inner {
  background-color: var(--color-bg-4);
  padding: 0 0 62px 0;
  display: flex;
  overflow: hidden;
  flex-direction: column;
}
.tod-side-menu.tod-side-menu-wrapper .arco-menu-inline,
.tod-side-menu.tod-side-menu-wrapper .arco-menu-inline-header,
.tod-side-menu.tod-side-menu-wrapper .arco-menu-item,
.tod-side-menu.tod-side-menu-wrapper .arco-menu-pop-header {
  background-color: var(--color-bg-4);
}
.tod-side-menu.tod-side-menu-wrapper .arco-menu-collapse-button {
  z-index: 10;
  height: 40px;
  width: calc(100% - 24px);
  align-items: center;
  background-color: var(--color-bg-4);
  justify-content: flex-start;
  border-top: 1px solid var(--color-border-2);
  padding: 8px;
  position: absolute;
  bottom: 0;
  left: 12px;
  transform: translateX(0);
  color: var(--color-text-1);
  display: flex;
}
.tod-side-menu.tod-side-menu-wrapper .arco-menu-collapse-button:hover {
  background-color: var(--color-bg-4);
}
.tod-side-menu.tod-side-menu-wrapper .arco-menu-collapse-button .tod-side-menu-collapse-button-wrapper {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tod-side-menu.tod-side-menu-wrapper .arco-menu-collapse-button .tod-side-menu-collapse-button-wrapper:hover {
  background-color: rgba(var(--primary-6), 0.08);
}
.tod-side-menu.tod-side-menu-wrapper .arco-menu-collapse-button .tod-side-menu-collapse-button-wrapper:hover svg {
  color: rgb(var(--primary-6));
}
.tod-side-menu.tod-side-menu-wrapper .arco-menu-collapse-button .tod-side-menu-collapse-button-wrapper svg {
  font-size: 20px;
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse {
  width: 68px;
  /** -------- 从上到下 -------- **/
  /*** -------- title -------- ***/
  /*** -------- overview -------- ***/
  /*** -------- expand -------- ***/
  /*** -------- 菜单 -------- ***/
  /*** -------- 分组标题 -------- ***/
  /*** -------- 折叠按钮 -------- ***/
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse .tod-side-menu-title {
  height: 60px;
  padding-top: 18px;
  padding-bottom: 18px;
  margin-bottom: 4px;
  padding-left: 10px;
  font-size: 24px;
  line-height: 24px;
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse .tod-side-menu-title > svg:first-of-type {
  color: var(--color-fill-2);
  margin-right: 100vw;
  vertical-align: top;
  margin-top: -2px;
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse .tod-side-menu-overview-button > svg {
  margin-right: 100vw;
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse .tod-side-menu-expand-button {
  padding: 0;
  flex-shrink: 0;
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse .tod-side-menu-expand-button .tod-side-menu-expand-button-wrapper .tod-side-menu-expand-button-left {
  font-size: 20px;
  left: 12px;
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse .tod-side-menu-expand-button .tod-side-menu-expand-button-wrapper .tod-side-menu-expand-button-text {
  display: none;
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse .tod-side-menu-expand-button .tod-side-menu-expand-button-wrapper .tod-side-menu-expand-button-right {
  display: none;
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse .tod-side-menu-expand-button .tod-side-menu-expand-button-wrapper > svg {
  color: var(--color-fill-2);
  font-size: 20px;
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse .tod-side-menu-sub-menu {
  height: 36px;
  line-height: 36px;
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse .tod-side-menu-sub-menu > svg {
  margin-right: 100vw;
  font-size: 20px;
  vertical-align: top;
  margin-top: 8px;
  display: inline-block;
  color: var(--color-fill-2);
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse .tod-side-menu-group-title {
  display: none;
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse .arco-menu-collapse-button {
  width: 44px;
  left: 12px;
  bottom: 0;
  font-size: 13px;
  padding: 8px 0 8px 8px;
}
.tod-side-menu.tod-side-menu-wrapper.arco-menu-collapse.arco-menu-pop:not(.arco-menu-pop-button) .arco-menu-item .arco-icon {
  margin-left: 0;
}
.tod-side-menu.tod-side-menu-wrapper.tod-side-menu-no-icon.arco-menu-collapse {
  width: 12px;
  border-right: 1px solid var(--color-border-2);
}
.tod-side-menu.tod-side-menu-wrapper.tod-side-menu-no-icon.arco-menu-collapse .arco-menu-inner {
  padding: 0;
}
.tod-side-menu.tod-side-menu-wrapper.tod-side-menu-no-icon.arco-menu-collapse .arco-menu-collapse-button {
  width: 24px;
  height: 24px;
  padding: 0;
  bottom: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  justify-content: center;
  border: 1px solid var(--color-border-2);
  border-left: 0;
}
.tod-side-menu.tod-side-menu-wrapper.tod-side-menu-no-icon.arco-menu-collapse .arco-menu-collapse-button .tod-side-menu-collapse-button-wrapper {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.tod-side-menu-group-title {
  color: var(--color-text-3);
  font-size: 13px;
  padding: 0 12px;
  height: 28px;
  line-height: 28px;
  margin-bottom: 4px;
  margin-top: 0px;
}
.tod-side-menu-title {
  margin: 0 12px;
  flex-shrink: 0;
}
.tod-side-menu-title.arco-menu-inline {
  margin-bottom: 0;
  font-weight: 500;
  font-size: 16px;
}
.tod-side-menu-title.arco-menu-inline .arco-menu-inline-header {
  padding-top: 18px;
  padding-bottom: 18px;
  margin-bottom: 4px;
  padding-left: 10px;
  line-height: 24px;
}
.tod-side-menu-title.arco-menu-inline .arco-menu-inline-header > span:first-of-type > svg {
  margin-right: 6px;
  color: var(--color-fill-2);
  font-size: 24px;
  vertical-align: top;
  margin-top: -2px;
}
.tod-side-menu-title.arco-menu-inline .arco-menu-inline-header .arco-menu-icon-suffix {
  display: none;
}
.tod-side-menu-overview-button {
  margin: 0 12px;
}
.tod-side-menu-overview-button.arco-menu-pop.arco-menu-pop-header {
  line-height: 36px;
  flex-shrink: 0;
}
.tod-side-menu-overview-button > svg {
  margin-right: 8px;
  font-size: 20px;
  vertical-align: top;
  margin-top: 8px;
  display: inline-block;
}
.tod-side-menu-overview-button > span {
  color: var(--color-text-1);
}
.tod-side-menu-overview-button .arco-menu-icon-suffix {
  display: none;
}
.tod-side-menu-expand-button {
  margin: 0 12px;
  margin-top: 4px;
  outline: 1px solid var(--color-border-3);
  flex-shrink: 0;
}
.tod-side-menu-expand-button.arco-menu-pop.arco-menu-pop-header {
  margin-bottom: 12px;
}
.tod-side-menu-expand-button-wrapper {
  height: 32px;
  line-height: 32px;
  display: flex;
  align-items: center;
  position: relative;
}
.tod-side-menu-expand-button-left {
  position: absolute;
  left: 0px;
  font-size: 20px;
  margin-right: 8px;
}
.tod-side-menu-expand-button-text {
  padding: 0 28px;
}
.tod-side-menu-expand-button .arco-menu-icon-suffix {
  display: none;
}
.tod-side-menu-expand-button-right {
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
}
.tod-side-menu-expand-button svg {
  color: var(--color-fill-2);
}
.tod-side-menu-expand-button.arco-menu-pop-header.arco-menu-pop:hover {
  outline: 1px solid rgb(var(--primary-6));
  background-color: rgba(var(--primary-3), 0.2);
  font-weight: 500;
}
.tod-side-menu-expand-button.arco-menu-pop-header.arco-menu-pop:hover span {
  color: rgb(var(--primary-6));
}
.tod-side-menu-expand-button.arco-menu-pop-header.arco-menu-pop:hover svg {
  color: rgb(var(--primary-6));
}
.arco-menu-inline.tod-side-menu-sub-menu {
  margin-bottom: 4px;
}
.arco-menu-inline.tod-side-menu-sub-menu-pop.arco-menu-pop-trigger.arco-trigger[trigger-placement='rt'] {
  transform: translateX(6px);
}
.arco-menu-inline.tod-side-menu-sub-menu-pop .arco-trigger-arrow.arco-dropdown-arrow {
  display: none;
}
.arco-menu-inline.tod-side-menu-sub-menu .arco-menu-inline-header {
  margin-bottom: 0;
  line-height: 36px;
}
.arco-menu-inline.tod-side-menu-sub-menu .arco-menu-inline-header > span:first-of-type > svg {
  margin-right: 8px;
  font-size: 20px;
  vertical-align: top;
  margin-top: 8px;
  display: inline-block;
}
.arco-menu-inner .tod-side-menu-menus-wrapper {
  flex-grow: 1;
  padding: 0 12px;
}
.arco-menu-inner .tod-side-menu-menus-wrapper .arco-menu-inline-header.arco-menu-selected > span {
  font-weight: 400;
}
.arco-menu-inner .tod-side-menu-menus-wrapper .arco-menu-item.arco-menu-selected > span {
  color: rgb(var(--primary-6));
}
.arco-menu-inner .tod-side-menu-menus-wrapper .arco-menu-item.arco-menu-selected {
  background-color: rgba(var(--primary-3), 0.2);
}
.arco-menu-inner .tod-side-menu-menus-wrapper .arco-menu-inline-header:hover {
  background-color: rgba(var(--primary-3), 0.2);
}
.arco-menu-inner .tod-side-menu-menus-wrapper .arco-menu-item:hover {
  background-color: rgba(var(--primary-3), 0.2);
}
.arco-menu-inner .tod-side-menu-menus-wrapper .arco-menu-inline-header svg {
  color: var(--color-fill-2);
}
.tod-side-menu.arco-menu-collapse .tod-side-menu-menus-wrapper .tod-side-menu-menu-item {
  line-height: 36px;
}
.tod-side-menu.arco-menu-collapse .tod-side-menu-menus-wrapper .tod-side-menu-menu-item > svg {
  vertical-align: top;
  margin-top: 8px;
  display: inline-block;
  margin-right: 100vw;
}
.tod-side-menu-menus-wrapper .tod-side-menu-menu-item.arco-menu-item {
  line-height: 32px;
  color: var(--color-text-1);
}
.tod-side-menu-menus-wrapper .tod-side-menu-menu-item.arco-menu-item > svg {
  font-size: 20px;
  color: var(--color-fill-2);
  margin-right: 8px;
  vertical-align: top;
  margin-top: 8px;
  display: inline-block;
}
.tod-side-menu-menus-wrapper .tod-side-menu-menu-item.arco-menu-item .arco-menu-item-inner {
  margin-left: 28px;
}
.tod-side-menu-menus-wrapper .tod-side-menu-menu-item.arco-menu-item > span:first-of-type {
  display: none;
}
.tod-side-menu-pop-container .arco-trigger-arrow.arco-dropdown-arrow {
  display: none;
}
.tod-side-menu-pop {
  height: 100vh;
  background: var(--color-bg-1);
  box-shadow: 2px 12px 24px rgba(10, 31, 68, 0.08);
}
.tod-side-menu-pop-header {
  height: 60px;
  width: 100%;
  padding-top: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e3e8ee;
}
.tod-side-menu-pop-header-search {
  width: 60%;
  height: 20px;
  border: none;
  outline: none;
  padding-left: 40px;
}
.tod-side-menu-pop-header-search .arco-select-prefix {
  margin-right: 8px;
}
.tod-side-menu-pop-header-search .arco-select-suffix {
  display: none;
}
.tod-side-menu-pop-header .arco-select {
  display: flex;
  align-items: center;
}
.tod-side-menu-pop-header .arco-select .arco-select-view {
  box-shadow: none;
  border: none;
}
.tod-side-menu-pop-header .arco-select .arco-select-view:hover {
  border: none;
  outline: none;
}
.tod-side-menu-pop-header .arco-select.arco-select-focused:not(.arco-select-no-border) .arco-select-view {
  box-shadow: none;
}
.tod-side-menu-pop-main {
  display: flex;
  height: calc(100% - 60px);
}
.tod-side-menu-pop-content {
  width: calc(100% - 204px);
  padding: 32px;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  overflow-x: auto;
}
.tod-side-menu-pop-content-block {
  height: fit-content;
  min-width: 204px;
  padding: 24px;
  margin-right: 16px;
  margin-bottom: 16px;
  background-color: var(--color-bg-1);
}
.tod-side-menu-pop-content-block:hover {
  background-color: var(--color-bg-4);
}
.tod-side-menu-pop-content .tod-side-menu-pop-content-block .tod-side-menu-pop-content-block-title {
  margin: 0;
}
.tod-side-menu-pop-content-block-title {
  font-size: 13px;
  font-weight: 500;
  line-height: 22px;
  display: flex;
  justify-content: space-between;
}
.tod-side-menu-pop-content-block-title-pin {
  cursor: pointer;
}
.tod-side-menu-pop-content-block-menu {
  list-style: none;
  margin: 0;
  padding: 0px;
  margin-top: 16px;
}
.tod-side-menu-pop-content-block-menu .arco-menu-inline-header:hover {
  background-color: var(--color-bg-4);
}
.tod-side-menu-pop-content-block-menu-item {
  list-style: none;
  cursor: pointer;
  margin-bottom: 12px;
  line-height: 22px;
}
.tod-side-menu-pop-content-block-menu-item:hover {
  font-weight: 500;
  color: rgb(var(--primary-6));
}
.tod-side-menu-pop-side {
  border-left: 1px solid var(--color-border-2);
  width: 204px;
  padding: 32px;
}
.tod-side-menu-pop-side-title {
  font-size: 13px;
  display: flex;
  align-items: center;
  color: #666d75;
}
.tod-side-menu-pop-side-icon {
  margin-right: 5px;
}
.tod-side-menu-pop-side-list-item {
  cursor: pointer;
  height: 22px;
  margin-top: 16px;
}
.tod-side-menu-collapse-tooltip {
  width: 65px;
}
.tod-side-menu-collapse-tooltip .arco-tooltip-content {
  padding: 6px 8px;
}
.tod-top-menu {
  height: 48px;
  background-color: var(--color-fill-5);
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
  width: 100%;
  padding-right: 24px;
  box-sizing: border-box;
}
.tod-top-menu ul,
.tod-top-menu li {
  list-style: none;
  padding: 0;
  margin: 0;
}
.tod-top-menu * {
  box-sizing: border-box;
}
.tod-top-menu .arco-menu .arco-menu-item {
  font-size: 13px;
  color: var(--color-text-1);
}
.tod-top-menu,
.tod-top-menu-left,
.tod-top-menu,
.tod-top-menu-right {
  display: flex;
  flex-wrap: nowrap;
  font-size: 13px;
  justify-content: space-between;
}
.tod-top-menu .tod-top-menu-left {
  flex-shrink: 0;
  cursor: pointer;
}
.tod-top-menu .tod-top-menu-left .tod-left-corner {
  width: 48px;
  height: 100%;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tod-top-menu .tod-top-menu-left .tod-left-corner .tod-left-corner-menu {
  width: 16px;
  height: 16px;
}
.tod-top-menu .tod-top-menu-left .tod-left-corner .tod-left-corner-menu .tod-left-corner-menu-item {
  width: 16px;
  height: 2px;
  background-color: var(--color-text-2);
}
.tod-top-menu .tod-top-menu-left .tod-left-corner .tod-left-corner-menu .tod-left-corner-menu-item:nth-child(1) {
  transform: rotate(0);
  transform-origin: left center;
}
.tod-top-menu .tod-top-menu-left .tod-left-corner .tod-left-corner-menu .tod-left-corner-menu-item:nth-child(2) {
  margin: 4px 0;
}
.tod-top-menu .tod-top-menu-left .tod-left-corner .tod-left-corner-menu .tod-left-corner-menu-item:nth-child(3) {
  transform: rotate(0);
  transform-origin: left center;
}
.tod-top-menu .tod-top-menu-left .tod-left-corner.tod-left-corner-close {
  background-color: rgb(var(--primary-6));
}
.tod-top-menu .tod-top-menu-left .tod-left-corner.tod-left-corner-close .tod-left-corner-menu-item {
  width: 17px;
  margin-left: 2px;
  background-color: var(--color-bg-white);
  transition: all 0.3s ease-out 0s;
}
.tod-top-menu .tod-top-menu-left .tod-left-corner.tod-left-corner-close .tod-left-corner-menu-item:nth-child(1) {
  transform: rotate(45deg);
  transform-origin: left center;
}
.tod-top-menu .tod-top-menu-left .tod-left-corner.tod-left-corner-close .tod-left-corner-menu-item:nth-child(2) {
  opacity: 0;
}
.tod-top-menu .tod-top-menu-left .tod-left-corner.tod-left-corner-close .tod-left-corner-menu-item:nth-child(3) {
  transform: rotate(-45deg);
  transform-origin: left center;
}
.tod-top-menu .tod-top-menu-left .tod-top-menu-menu {
  min-width: 200px;
}
.tod-top-menu .tod-top-menu-left .tod-menu-logo {
  width: 103px;
  height: 100%;
  line-height: 48px;
  margin-left: 16px;
  cursor: pointer;
}
.tod-top-menu .tod-top-menu-left .tod-menu-logo .tod-menu-logo-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}
.tod-top-menu .tod-top-menu-left .tod-menu-logo .tod-menu-logo-wrapper .tod-menu-logo-text {
  margin-left: 8px;
  fill: var(--color-text-1);
}
.tod-top-menu .tod-top-menu-right {
  justify-content: flex-end;
  padding: 8px 0;
}
.tod-top-menu .tod-top-menu-right .tod-search-input {
  max-width: 240px;
  border-radius: 4px;
  color: var(--color-text-4);
  background-color: var(--color-fill-5);
}
.tod-top-menu .tod-top-menu-right .tod-search-input .arco-input-inner-wrapper {
  background-color: inherit;
}
.tod-top-menu .tod-top-menu-right .tod-search-input .arco-input-inner-wrapper-has-prefix > .arco-input {
  padding-left: 8px;
}
.tod-top-menu .tod-top-menu-right .tod-top-menu-right-search {
  color: var(--color-text-4);
}
.tod-top-menu .tod-top-menu-right .tod-top-menu-right-menu {
  min-width: 200px;
}
.tod-top-menu .tod-top-menu-right .arco-menu-horizontal .arco-menu-inner {
  padding: 0;
  margin-left: 16px;
  overflow: hidden;
  width: 100%;
}
.tod-top-menu .tod-top-menu-right .arco-menu-overflow-wrap {
  width: 100%;
}
.tod-top-menu .tod-top-menu-right .arco-menu .arco-menu-item {
  padding: 0 8px;
  margin-left: 0;
}
.tod-top-menu .tod-top-menu-right .arco-menu-horizontal .arco-menu-pop-header {
  padding: 0;
}
.tod-top-menu .tod-top-menu-right .tod-top-menu-right-corner,
.tod-top-menu .tod-top-menu-right .tod-top-menu-right-operations {
  display: flex;
  align-items: center;
  color: var(--color-text-1);
}
.tod-top-menu .tod-top-menu-right .tod-top-menu-right-operations {
  margin: 0 16px;
  cursor: pointer;
}
.tod-top-menu .tod-top-menu-right .tod-top-menu-right-operations .tod-top-menu-right-operation:first-of-type {
  margin-left: 0px;
}
.tod-top-menu .tod-top-menu-right .tod-top-menu-right-operations .tod-top-menu-right-operation {
  width: 32px;
  height: 32px;
  margin-left: 8px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tod-top-menu .tod-top-menu-right .tod-top-menu-right-avatar {
  cursor: pointer;
}
.tod-sub-top-menu {
  height: 40px;
  padding: 0 24px;
}
.tod-sub-top-menu .tod-top-menu-left {
  flex-shrink: initial;
}
.tod-sub-top-menu .tod-top-menu-left .tod-menu-logo {
  width: 176px;
  margin-left: 0;
  flex-shrink: 0;
}
.tod-sub-top-menu .tod-top-menu-left .tod-menu-logo .tod-menu-logo-wrapper {
  justify-content: flex-start;
}
.tod-sub-top-menu .tod-top-menu-left .tod-menu-logo .tod-menu-logo-wrapper .tod-top-menu-live-text {
  margin-left: 9px;
}
.tod-sub-top-menu .tod-top-menu-left .tod-top-menu-menu {
  min-width: 420px;
}
.tod-sub-top-menu .tod-top-menu-right .tod-top-menu-right-operations {
  margin: 0;
}
.tod-sub-top-menu .arco-menu-horizontal .arco-menu-inner {
  padding: 14px 8px;
}
.tod-sub-top-menu .arco-menu-horizontal .arco-menu-item:not(:first-child),
.tod-sub-top-menu .arco-menu-horizontal .arco-menu-pop:not(:first-child) {
  margin-left: 0;
}
.tod-sub-top-menu .arco-menu-inner {
  padding: 0;
  margin-left: 0;
  overflow: hidden;
}

.tod-input-tag-remote-employee-input-tag-wrap .tod-input-tag-remote-employee-input-tag {
    display: inline-flex;
    align-items: center;
}
