"use client";

import React, { useState, useMemo, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { 
  Table, 
  Button, 
  Input, 
  Form, 
  Grid, 
  Select, 
  Space,
  Tag,
  Typography,
  Message,
  Card
} from "@arco-design/web-react";
import { 
  IconEye, 
  IconSearch,
  IconFile
} from "@arco-design/web-react/icon";
import { Modal } from "@arco-design/web-react";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { PageHeader } from "@/app/components/PageHeader";
import { evaluateExpression } from "@/lib/expression-evaluator";
import { agentOptions, variantOptions, toolsOptions } from "@/lib/option-config";
import { apiClient } from "@/app/api/request";

const { Text } = Typography;

// 知识项接口定义
export interface KnowledgeItem {
  path: string;
  id: string;
  title: string;
  enableIf: string;
  usedWhen: string;
  content: string;
}

// 筛选参数接口
interface FilterParams {
  keyword?: string;
  agent?: string;
  variant?: string;
  tools?: string[];
}

// 从JSON文件加载知识数据
const loadKnowledgeData = async (params: FilterParams, customData?: KnowledgeItem[]): Promise<KnowledgeItem[]> => {
  try {
    // 如果有自定义数据，直接使用
    if (customData && customData.length > 0) {
      return applyFilters(customData, params);
    }
    
    // 否则加载知识库JSON文件
    const knowledgeUrl = `https://tosv-cn.byted.org/obj/ttclient-android/aime/knowledge_list_final.json?_t=${Date.now()}`;
    const response = await fetch(knowledgeUrl, {
      method: 'GET',
      cache: 'no-store'
    });
    if (!response.ok) {
      throw new Error('Failed to load knowledge data');
    }
    
    const knowledgeData: KnowledgeItem[] = await response.json();
    return applyFilters(knowledgeData, params);
  } catch (error) {
    console.error('Error loading knowledge data:', error);
    return [];
  }
};

// 应用筛选条件
const applyFilters = (data: KnowledgeItem[], params: FilterParams): KnowledgeItem[] => {
  let filteredData = [...data];
  
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase();
    filteredData = filteredData.filter(item => 
      item.title.toLowerCase().includes(keyword) || 
      item.content.toLowerCase().includes(keyword) ||
      item.usedWhen.toLowerCase().includes(keyword)
    );
  }
  
  // 使用表达式求值工具处理 enableIf 条件
  if (params.agent || params.variant || (params.tools && params.tools.length > 0)) {
    const context: any = {};
    if (params.agent) context.agent = params.agent;
    if (params.variant) context.variant = params.variant;
    if (params.tools && params.tools.length > 0) context.tools = params.tools;
    
    filteredData = filteredData.filter(item => {
      // 如果 enableIf 为空或为 "false"，则不匹配
      if (!item.enableIf || item.enableIf.trim() === 'false') {
        return false;
      }
      
      // 如果 enableIf 为 "true"，则匹配
      if (item.enableIf.trim() === 'true') {
        return true;
      }
      
      // 使用表达式求值工具
      try {
        return evaluateExpression(item.enableIf, context);
      } catch (error) {
        console.error('表达式求值错误:', error, '表达式:', item.enableIf);
        return false;
      }
    });
  }
  
  return filteredData;
};

export default function KnowledgeAnalysisPage() {
  const urlSearchParams = useSearchParams();
  const [form] = Form.useForm();
  const [filterParams, setFilterParams] = useState<FilterParams>({});
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState('');
  const [showAnalysisConfig, setShowAnalysisConfig] = useState(false);
  const [selectedKnowledge, setSelectedKnowledge] = useState<KnowledgeItem[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);
  const [systemPrompt, setSystemPrompt] = useState('');
  const [userMessage, setUserMessage] = useState('');
  const [isPromptExpanded, setIsPromptExpanded] = useState(false);
  const [isMessageExpanded, setIsMessageExpanded] = useState(false);
  const [viewKnowledgeModalVisible, setViewKnowledgeModalVisible] = useState(false);
  const [currentKnowledge, setCurrentKnowledge] = useState<KnowledgeItem | null>(null);
  const [analysisType, setAnalysisType] = useState<'full' | 'usedWhen' | 'enableIf'>('full');
  const [customData, setCustomData] = useState<KnowledgeItem[]>([]);
  const [showImportModal, setShowImportModal] = useState(false);
  const [importJson, setImportJson] = useState('');

  // 从URL参数初始化筛选条件
  useEffect(() => {
    const params: FilterParams = {};
    
    // 读取URL参数
    const keyword = urlSearchParams.get('keyword');
    const agent = urlSearchParams.get('agent');
    const variant = urlSearchParams.get('variant');
    const tools = urlSearchParams.get('tools');

    
    // 设置参数
    if (keyword) params.keyword = keyword;
    if (agent) params.agent = agent;
    if (variant) params.variant = variant;
    if (tools) params.tools = tools.split(',').filter(tool => tool.trim() !== '');
    
    // 更新状态和表单
    setFilterParams(params);
    form.setFieldsValue(params);
  }, [urlSearchParams, form]);

  // 加载 system prompt
  useEffect(() => {
    const fetchPrompt = async () => {
        setSystemPrompt(`你是一个知识分析专家，请对提供的知识内容进行分析和总结。

分析要求：
1. 识别知识中的关键信息和核心概念
2. 分析知识的应用场景和使用条件
3. 总结知识的价值和潜在用途
4. 识别知识之间的关联和互补性
5. 提出知识应用的改进建议

请以结构化的方式呈现分析结果，包括：
- 知识概要：简要总结每条知识的主要内容
- 应用场景：分析每条知识适用的场景和条件
- 价值评估：评估每条知识的重要性和实用价值
- 关联分析：分析知识之间的关联和互补性
- 改进建议：提出知识应用的改进建议`);
    };

    fetchPrompt();
  }, []);

  // 查询知识列表
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["knowledgeAnalysisList", filterParams, customData],
    queryFn: async () => {
      try {
        const response = await loadKnowledgeData(filterParams, customData.length > 0 ? customData : undefined);
        return response;
      } catch (error) {
        console.error("获取知识列表失败:", error);
        toast.error("获取知识列表失败");
        return [];
      }
    },
  });

  // 表格数据
  const tableData = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];
    return data.map((item: KnowledgeItem) => ({
      ...item,
      key: item.id || item.path,
    }));
  }, [data]);

  // 表格列定义
  const columns = [
    {
      title: "标题",
      dataIndex: "title",
      width: 300,
      render: (col: string) => (
        <div className="max-w-[300px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "ID",
      dataIndex: "id",
      width: 150,
      render: (col: string) => (
        <div className="max-w-[150px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "路径",
      dataIndex: "path",
      width: 200,
      render: (col: string) => (
        <div className="max-w-[200px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "启用条件",
      dataIndex: "enableIf",
      width: 200,
      render: (col: string) => (
        <div className="max-w-[200px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "使用场景",
      dataIndex: "usedWhen",
      width: 200,
      render: (col: string) => (
        <div className="max-w-[200px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "内容预览",
      dataIndex: "content",
      width: 300,
      render: (col: string) => (
        <div className="max-w-[300px] truncate" title={col}>
          {col.substring(0, 100)}{col.length > 100 ? '...' : ''}
        </div>
      ),
    },
    {
      title: "操作",
      dataIndex: "actions",
      width: 100,
      render: (_: any, record: KnowledgeItem) => (
        <Button 
          type="text" 
          size="small"
          onClick={() => {
            setCurrentKnowledge(record);
            setViewKnowledgeModalVisible(true);
          }}
        >
          查看
        </Button>
      ),
    },
  ];

  // 更新URL参数的函数
  const updateURLParams = (params: FilterParams) => {
    const urlParams = new URLSearchParams();
    
    // 添加筛选参数
    if (params.keyword) urlParams.set('keyword', params.keyword);
    if (params.agent) urlParams.set('agent', params.agent);
    if (params.variant) urlParams.set('variant', params.variant);
    if (params.tools && params.tools.length > 0) urlParams.set('tools', params.tools.join(','));
    
    // 更新URL
    const newURL = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
    window.history.replaceState({}, '', newURL);
  };

  // 搜索表单变更
  const handleSearch = () => {
    const values = form.getFieldsValue();
    setFilterParams(values);
    updateURLParams(values);
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setFilterParams({});
    updateURLParams({});
  };

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: (string | number)[], selectedRows: KnowledgeItem[]) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedKnowledge(selectedRows);
    }
  };

  // 处理JSON导入
  const handleImportJson = () => {
    setShowImportModal(true);
    setImportJson('');
  };

  // 确认导入JSON
  const confirmImportJson = () => {
    try {
      const parsedData = JSON.parse(importJson);
      if (Array.isArray(parsedData) && parsedData.length > 0) {
        // 验证数据格式
        const isValid = parsedData.every(item => 
          item.id && item.title && item.content && item.enableIf && item.usedWhen
        );
        
        if (isValid) {
          setCustomData(parsedData);
          setShowImportModal(false);
          toast.success('JSON导入成功');
        } else {
          toast.error('JSON格式不正确，请确保每个项目包含id、title、content、enableIf和usedWhen字段');
        }
      } else {
        toast.error('JSON格式不正确，请确保是一个非空数组');
      }
    } catch (error) {
      console.error('JSON解析错误:', error);
      toast.error('JSON格式不正确，请检查后重试');
    }
  };

  // 清除导入的数据
  const clearImportedData = () => {
    setCustomData([]);
    toast.success('已清除导入的数据');
  };

  // 拼接知识内容
  const concatenateKnowledge = (knowledge: KnowledgeItem[]): string => {
    return knowledge.map((item, index) => {
      let content = '';
      
      if (analysisType === 'full') {
        content = `知识 ${index + 1}:
标题: ${item.title}
ID: ${item.id}
路径: ${item.path}
启用条件: ${item.enableIf}
使用场景: ${item.usedWhen}
内容: ${item.content}`;
      } else if (analysisType === 'usedWhen') {
        content = `知识 ${index + 1}:
标题: ${item.title}
ID: ${item.id}
使用场景: ${item.usedWhen}`;
      } else if (analysisType === 'enableIf') {
        content = `知识 ${index + 1}:
标题: ${item.title}
ID: ${item.id}
启用条件: ${item.enableIf}`;
      }
      
      return content + '\n---';
    }).join('\n\n');
  };

  // 根据分析类型获取不同的system prompt
  const getSystemPromptByType = (type: 'full' | 'usedWhen' | 'enableIf'): string => {
    if (type === 'usedWhen') {
      return `你是一个知识分析专家，请对提供的知识内容的使用场景(usedWhen)进行分析和总结。

分析要求：
1. 识别知识中的使用场景和适用条件
2. 分析不同使用场景之间的关联和区别
3. 总结使用场景的应用价值和潜在用途
4. 提出使用场景的改进建议

请以结构化的方式呈现分析结果，包括：
- 使用场景概要：简要总结每条知识的使用场景
- 场景分类：将使用场景进行分类整理
- 应用价值：评估每个使用场景的价值和实用性
- 改进建议：提出使用场景的改进建议`;
    } else if (type === 'enableIf') {
      return `你是一个知识分析专家，请对提供的知识内容的启用条件(enableIf)进行分析和总结。

分析要求：
1. 识别知识中的启用条件和限制条件
2. 分析不同启用条件之间的逻辑关系
3. 总结启用条件的应用场景和影响
4. 提出启用条件的优化建议

请以结构化的方式呈现分析结果，包括：
- 启用条件概要：简要总结每条知识的启用条件
- 条件分类：将启用条件进行分类整理
- 逻辑分析：分析启用条件之间的逻辑关系
- 优化建议：提出启用条件的优化建议`;
    } else {
      return `你是一个知识分析专家，请对提供的知识内容进行全面分析和总结。

分析要求：
1. 识别知识中的关键信息和核心概念
2. 分析知识的应用场景和使用条件
3. 总结知识的价值和潜在用途
4. 识别知识之间的关联和互补性
5. 提出知识应用的改进建议

请以结构化的方式呈现分析结果，包括：
- 知识概要：简要总结每条知识的主要内容
- 应用场景：分析每条知识适用的场景和条件
- 价值评估：评估每条知识的重要性和实用价值
- 关联分析：分析知识之间的关联和互补性
- 改进建议：提出知识应用的改进建议`;
    }
  };

  // 显示分析配置区域
  const handleAnalyze = () => {
    if (selectedKnowledge.length === 0) {
      Message.warning('请至少选择一条知识进行分析');
      return;
    }

    // 初始化system prompt和user message
    const systemPromptContent = getSystemPromptByType(analysisType);
    const concatenatedContent = concatenateKnowledge(selectedKnowledge);
    const userMessageContent = `请分析以下知识：\n\n${concatenatedContent}`;
    
    setSystemPrompt(systemPromptContent);
    setUserMessage(userMessageContent);
    setAnalysisResult('');
    setShowAnalysisConfig(true);
  };

  // 执行实际的分析
  const executeAnalysis = async () => {
    if (!systemPrompt.trim() || !userMessage.trim()) {
      Message.warning('System Prompt 和 User Message 都不能为空');
      return;
    }

    setIsAnalyzing(true);
    setAnalysisResult('');

    try {
      // 调用模型进行分析
      const res = await apiClient.ChatStream({
        model: 'gemini-2.5-pro',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userMessage },
        ],
        temperature: 0.7,
        max_tokens: 64000
      });
      
      const content = (res as any)?.choices?.[0]?.message?.content ?? '';
      setAnalysisResult(content);
      
    } catch (error) {
      console.error('知识分析失败:', error);
      setAnalysisResult('分析过程中出现错误，请稍后重试');
      toast.error('知识分析失败');
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <PageHeader 
        title="知识分析" 
        description="分析现有知识内容"
      />
      
      <Form layout="horizontal" className="mb-4 flex-shrink-0" form={form}>
        <Grid.Row gutter={16}>
          <Grid.Col span={6}>
            <Form.Item label="关键词" field="keyword">
              <Input placeholder="请输入关键词" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="Agent" field="agent">
              <Select placeholder="请选择Agent" allowClear>
                {agentOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="Variant" field="variant">
              <Select placeholder="请选择Variant" allowClear>
                {variantOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="Tools" field="tools">
              <Select 
                placeholder="请选择Tools" 
                allowClear
                mode="multiple"
                style={{ width: '100%' }}
              >
                {toolsOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
        </Grid.Row>
        <Grid.Row gutter={16} className="mt-4">
          <Grid.Col span={20}></Grid.Col>
          <Grid.Col span={4} className="flex justify-end items-end">
            <Space>
              <Button type="primary" icon={<IconSearch />} onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Grid.Col>
        </Grid.Row>
      </Form>

      <div className="flex justify-between mb-4 flex-shrink-0">
        <div>
          <Space>
            <Select 
              value={analysisType} 
              onChange={(value) => setAnalysisType(value as 'full' | 'usedWhen' | 'enableIf')}
              style={{ width: 150 }}
            >
              <Select.Option value="full">全面分析</Select.Option>
              <Select.Option value="usedWhen">仅分析UsedWhen</Select.Option>
              <Select.Option value="enableIf">仅分析EnableIf</Select.Option>
            </Select>
            <Button 
              type="primary" 
              icon={<IconEye />}
              onClick={handleAnalyze}
              disabled={selectedRowKeys.length === 0}
              loading={isAnalyzing}
            >
              分析选中知识
            </Button>
            <Button 
              icon={<IconFile />}
              onClick={handleImportJson}
            >
              导入JSON
            </Button>
            {customData.length > 0 && (
              <Button 
                onClick={clearImportedData}
              >
                清除导入数据
              </Button>
            )}
            <Button 
              href="/lab/expression-test"
              target="_blank"
            >
              表达式测试
            </Button>
            <Button 
              href="https://bytedance.larkoffice.com/wiki/E5gXwcnENiJgy4kLCrTc59aen0W"
              target="_blank"
            >
              导入JSON说明
            </Button>
          </Space>
        </div>
        <div>
          <Text>已选择 {selectedRowKeys.length} 条知识</Text>
          {customData.length > 0 && (
            <Text className="ml-4" style={{ color: '#165DFF' }}>已导入 {customData.length} 条数据</Text>
          )}
          <Text className="ml-4">总计 {data?.length || 0} 条数据</Text>
        </div>
      </div>

      <div className="flex-shrink-0" style={{ height: '400px', overflow: 'hidden' }}>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          data={tableData}
            pagination={{
            current: 1,
            pageSize: 500,
            total: data?.length || 0,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          loading={isLoading}
          scroll={{ x: 1500, y: 300 }}
          border
        />
      </div>

      {/* 分析配置和结果区域 */}
      {showAnalysisConfig && (
        <Card className="mt-4 flex-shrink-0">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">
              {analysisResult ? "知识分析结果" : "知识分析配置"}
            </h2>
            <Space>
              <Button onClick={() => setShowAnalysisConfig(false)}>
                关闭
              </Button>
              {!analysisResult && (
                <Button 
                  type="primary" 
                  onClick={executeAnalysis}
                  loading={isAnalyzing}
                >
                  开始分析
                </Button>
              )}
            </Space>
          </div>
          
          {!analysisResult ? (
            <div className="analysis-config">
              <div className="mb-4">
                <h3 className="text-md font-medium mb-2">System Prompt</h3>
                <textarea
                  className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
                  value={systemPrompt}
                  onChange={(e) => setSystemPrompt(e.target.value)}
                  rows={isPromptExpanded ? 20 : 6}
                  style={{ maxHeight: '300px', overflowY: 'auto' }}
                />
                <Button 
                  type="text" 
                  size="small" 
                  onClick={() => setIsPromptExpanded(!isPromptExpanded)}
                  className="mt-2"
                >
                  {isPromptExpanded ? '收起' : '展开'}
                </Button>
              </div>
              
              <div className="mb-4">
                <h3 className="text-md font-medium mb-2">User Message</h3>
                <textarea
                  className="bg-gray-100 p-4 rounded-md w-full whitespace-pre-wrap font-mono text-sm"
                  value={userMessage}
                  onChange={(e) => setUserMessage(e.target.value)}
                  rows={isMessageExpanded ? 20 : 6}
                  style={{ maxHeight: '300px', overflowY: 'auto' }}
                />
                <Button 
                  type="text" 
                  size="small" 
                  onClick={() => setIsMessageExpanded(!isMessageExpanded)}
                  className="mt-2"
                >
                  {isMessageExpanded ? '收起' : '展开'}
                </Button>
              </div>
            </div>
          ) : (
            <div className="analysis-result">
              <div className="mb-4">
                <h3 className="text-md font-medium mb-2">分析结果</h3>
                <pre className="whitespace-pre-wrap bg-gray-100 p-2 rounded mt-2">
                 {analysisResult}
                </pre>
              </div>
            </div>
          )}
        </Card>
      )}

      {/* 查看知识内容模态框 */}
      <Modal
        title="知识内容详情"
        visible={viewKnowledgeModalVisible}
        onCancel={() => setViewKnowledgeModalVisible(false)}
        footer={null}
        // width={800}
        style={{ maxHeight: '80vh', overflow: 'auto',width:'80%' }}
      >
        {currentKnowledge && (
          <div>
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">{currentKnowledge.title}</h3>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-500">ID</p>
                  <p>{currentKnowledge.id}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">路径</p>
                  <p>{currentKnowledge.path}</p>
                </div>
              </div>
              <div className="mb-4">
                <p className="text-sm text-gray-500">启用条件</p>
                <div className="bg-gray-100 p-2 rounded">
                  <pre>{currentKnowledge.enableIf}</pre>
                </div>
              </div>
              <div className="mb-4">
                <p className="text-sm text-gray-500">使用场景</p>
                <div className="bg-gray-100 p-2 rounded">
                  <pre>{currentKnowledge.usedWhen}</pre>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500">内容</p>
                <div className="bg-gray-100 p-4 rounded max-h-96 overflow-auto">
                  <pre className="whitespace-pre-wrap">{currentKnowledge.content}</pre>
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* 导入JSON模态框 */}
      <Modal
        title="导入JSON数据"
        visible={showImportModal}
        onOk={confirmImportJson}
        onCancel={() => setShowImportModal(false)}
        okText="确认导入"
        cancelText="取消"
        style={{ width: '80%', maxWidth: '800px' }}
      >
        <div className="mb-4">
          <p className="text-sm text-gray-500 mb-2">
            请粘贴JSON格式的知识数据，格式应为包含id、title、content、enableIf、usedWhen和path字段的数组。
          </p>
          <textarea
            className="bg-gray-100 p-4 rounded-md w-full font-mono text-sm"
            value={importJson}
            onChange={(e) => setImportJson(e.target.value)}
            rows={15}
            placeholder={`[
  {
    "id": "knowledge_1",
    "title": "知识标题",
    "path": "/path/to/knowledge",
    "enableIf": "true",
    "usedWhen": "使用场景描述",
    "content": "知识内容"
  }
]`}
          />
        </div>
      </Modal>
    </div>
  );
}