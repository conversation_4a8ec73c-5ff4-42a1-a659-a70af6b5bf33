"use client";

import React, { useState, useMemo, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { 
  Table, 
  Button, 
  Input, 
  Form, 
  Grid, 
  Select, 
  Space,
  Tag,
  Typography,
  Message,
  Card,
  Input as TextArea
} from "@arco-design/web-react";
import { 
  IconEye, 
  IconSearch,
  IconFile,
  IconPlayArrow,
  IconLoading
} from "@arco-design/web-react/icon";
import { Modal } from "@arco-design/web-react";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { PageHeader } from "@/app/components/PageHeader";
import { evaluateExpression } from "@/lib/expression-evaluator";
import { agentOptions, variantOptions, toolsOptions } from "@/lib/option-config";
import { apiClient } from "@/app/api/request";
import { ListModelsResponse, Model } from "@/app/bam/aime/namespaces/trace";
import { uniq } from "lodash-es";

const { Text } = Typography;

// 知识项接口定义
export interface KnowledgeItem {
  path: string;
  id: string;
  title: string;
  enableIf: string;
  usedWhen: string;
  content: string;
}

// 筛选参数接口
interface FilterParams {
  keyword?: string;
  agent?: string;
  variant?: string;
  tools?: string[];
}

// 召回结果接口
interface RecallResult {
  model: string;
  query?: string; // 添加查询内容字段，用于多query场景
  result: string;
  selectedIds: string[];
  isLoading: boolean;
  elapsedTime?: number; // 耗时（秒）
  avgRecallCount?: number; // 平均召回数量
  roundResults?: Array<{
    result: string;
    selectedIds: string[];
    elapsedTime: number; // 毫秒
  }>; // 保存每轮结果
}

// 从JSON文件加载知识数据
const loadKnowledgeData = async (params: FilterParams, customData?: KnowledgeItem[]): Promise<KnowledgeItem[]> => {
  try {
    // 如果有自定义数据，直接使用
    if (customData && customData.length > 0) {
      return applyFilters(customData, params);
    }
    
    // 否则加载知识库JSON文件
    const knowledgeUrl = `https://tosv-cn.byted.org/obj/ttclient-android/aime/knowledge_list_final.json?_t=${Date.now()}`;
    const response = await fetch(knowledgeUrl, {
      method: 'GET',
      cache: 'no-store'
    });
    if (!response.ok) {
      throw new Error('Failed to load knowledge data');
    }
    
    const knowledgeData: KnowledgeItem[] = await response.json();
    return applyFilters(knowledgeData, params);
  } catch (error) {
    console.error('Error loading knowledge data:', error);
    return [];
  }
};

// 应用筛选条件
const applyFilters = (data: KnowledgeItem[], params: FilterParams): KnowledgeItem[] => {
  let filteredData = [...data];
  
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase();
    filteredData = filteredData.filter(item => 
      item.title.toLowerCase().includes(keyword) || 
      item.content.toLowerCase().includes(keyword) ||
      item.usedWhen.toLowerCase().includes(keyword)
    );
  }
  
  // 使用表达式求值工具处理 enableIf 条件
  if (params.agent || params.variant || (params.tools && params.tools.length > 0)) {
    const context: any = {};
    if (params.agent) context.agent = params.agent;
    if (params.variant) context.variant = params.variant;
    if (params.tools && params.tools.length > 0) context.tools = params.tools;
    
    filteredData = filteredData.filter(item => {
      // 如果 enableIf 为空或为 "false"，则不匹配
      if (!item.enableIf || item.enableIf.trim() === 'false') {
        return false;
      }
      
      // 如果 enableIf 为 "true"，则匹配
      if (item.enableIf.trim() === 'true') {
        return true;
      }
      
      // 使用表达式求值工具
      try {
        return evaluateExpression(item.enableIf, context);
      } catch (error) {
        console.error('表达式求值错误:', error, '表达式:', item.enableIf);
        return false;
      }
    });
  }
  
  return filteredData;
};

export default function KnowledgeQueryPage() {
  const urlSearchParams = useSearchParams();
  const [form] = Form.useForm();
  const [filterParams, setFilterParams] = useState<FilterParams>({});
  const [selectedKnowledge, setSelectedKnowledge] = useState<KnowledgeItem[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);
  const [viewKnowledgeModalVisible, setViewKnowledgeModalVisible] = useState(false);
  const [currentKnowledge, setCurrentKnowledge] = useState<KnowledgeItem | null>(null);
  const [customData, setCustomData] = useState<KnowledgeItem[]>([]);
  const [showImportModal, setShowImportModal] = useState(false);
  const [importJson, setImportJson] = useState('');
  
  // 召回测试相关状态
  const [query, setQuery] = useState('');
  const [queries, setQueries] = useState<string[]>([]); // 多个查询内容
  const [selectedModels, setSelectedModels] = useState<string[]>(['gemini-2.5-flash']);
  const [recallResults, setRecallResults] = useState<RecallResult[]>([]);
  const [isRecalling, setIsRecalling] = useState(false);
  const [temperature, setTemperature] = useState<number>(0.5);
  const [showRecallConfig, setShowRecallConfig] = useState(false);
  const [expandedResults, setExpandedResults] = useState<Record<string, boolean>>({}); // 控制结果展开/收起
  const [recallCount, setRecallCount] = useState<number>(1); // 召回次数
  const [currentRecallRound, setCurrentRecallRound] = useState<number>(0); // 当前召回轮次
  const [showImportQueriesModal, setShowImportQueriesModal] = useState(false); // 导入多个查询的模态框
  const [importQueriesText, setImportQueriesText] = useState(''); // 导入的查询文本
  const [useMultiQuery, setUseMultiQuery] = useState(false); // 是否使用多查询模式
  const [reasoningEffort, setReasoningEffort] = useState<string>('medium'); // reasoning_effort 参数

  // 判断当前选择的模型是否包含 gpt-5 相关内容
  const hasGPT5Model = useMemo(() => {
    return selectedModels.some(model => model.toLowerCase().includes('gpt-5'));
  }, [selectedModels]);

  // 从URL参数初始化筛选条件
  useEffect(() => {
    const params: FilterParams = {};
    
    // 读取URL参数
    const keyword = urlSearchParams.get('keyword');
    const agent = urlSearchParams.get('agent');
    const variant = urlSearchParams.get('variant');
    const tools = urlSearchParams.get('tools');

    
    // 设置参数
    if (keyword) params.keyword = keyword;
    if (agent) params.agent = agent;
    if (variant) params.variant = variant;
    if (tools) params.tools = tools.split(',').filter(tool => tool.trim() !== '');
    
    // 更新状态和表单
    setFilterParams(params);
    form.setFieldsValue(params);
  }, [urlSearchParams, form]);

  // 查询知识列表
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["knowledgeQueryList", filterParams, customData],
    queryFn: async () => {
      try {
        const response = await loadKnowledgeData(filterParams, customData.length > 0 ? customData : undefined);
        return response;
      } catch (error) {
        console.error("获取知识列表失败:", error);
        toast.error("获取知识列表失败");
        return [];
      }
    },
  });

  // 获取模型列表
  const { data: modelsData } = useQuery({
    queryKey: ["list-models"],
    queryFn: () => apiClient.ListModels({}),
    staleTime: Infinity,
  });

  const models = useMemo(() => {
    const models: Model[] = [];
    for (const model of modelsData?.models ?? []) {
      const exists = models?.find((item) => item.type === model?.type);
      if (!exists) {
        models.push(model);
      } else {
        exists?.models?.push(...(model?.models ?? []));
        exists.models = uniq(exists?.models);
      }
    }
    return models;
  }, [modelsData]);

  // 表格数据
  const tableData = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];
    return data.map((item: KnowledgeItem) => ({
      ...item,
      key: item.id || item.path,
    }));
  }, [data]);

  // 表格列定义
  const columns = [
    {
      title: "标题",
      dataIndex: "title",
      width: 300,
      render: (col: string) => (
        <div className="max-w-[300px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "ID",
      dataIndex: "id",
      width: 150,
      render: (col: string) => (
        <div className="max-w-[150px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "路径",
      dataIndex: "path",
      width: 200,
      render: (col: string) => (
        <div className="max-w-[200px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "启用条件",
      dataIndex: "enableIf",
      width: 200,
      render: (col: string) => (
        <div className="max-w-[200px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "使用场景",
      dataIndex: "usedWhen",
      width: 200,
      render: (col: string) => (
        <div className="max-w-[200px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "内容预览",
      dataIndex: "content",
      width: 300,
      render: (col: string) => (
        <div className="max-w-[300px] truncate" title={col}>
          {col.substring(0, 100)}{col.length > 100 ? '...' : ''}
        </div>
      ),
    },
    {
      title: "操作",
      dataIndex: "actions",
      width: 100,
      render: (_: any, record: KnowledgeItem) => (
        <Button 
          type="text" 
          size="small"
          onClick={() => {
            setCurrentKnowledge(record);
            setViewKnowledgeModalVisible(true);
          }}
        >
          查看
        </Button>
      ),
    },
  ];

  // 更新URL参数的函数
  const updateURLParams = (params: FilterParams) => {
    const urlParams = new URLSearchParams();
    
    // 添加筛选参数
    if (params.keyword) urlParams.set('keyword', params.keyword);
    if (params.agent) urlParams.set('agent', params.agent);
    if (params.variant) urlParams.set('variant', params.variant);
    if (params.tools && params.tools.length > 0) urlParams.set('tools', params.tools.join(','));
    
    // 更新URL
    const newURL = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
    window.history.replaceState({}, '', newURL);
  };

  // 搜索表单变更
  const handleSearch = () => {
    const values = form.getFieldsValue();
    setFilterParams(values);
    updateURLParams(values);
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setFilterParams({});
    updateURLParams({});
  };

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: (string | number)[], selectedRows: KnowledgeItem[]) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedKnowledge(selectedRows);
    }
  };

  // 处理JSON导入
  const handleImportJson = () => {
    setShowImportModal(true);
    setImportJson('');
  };

  // 确认导入JSON
  const confirmImportJson = () => {
    try {
      const parsedData = JSON.parse(importJson);
      if (Array.isArray(parsedData) && parsedData.length > 0) {
        // 验证数据格式
        const isValid = parsedData.every(item => 
          item.id && item.title && item.content && item.enableIf && item.usedWhen
        );
        
        if (isValid) {
          setCustomData(parsedData);
          setShowImportModal(false);
          toast.success('JSON导入成功');
        } else {
          toast.error('JSON格式不正确，请确保每个项目包含id、title、content、enableIf和usedWhen字段');
        }
      } else {
        toast.error('JSON格式不正确，请确保是一个非空数组');
      }
    } catch (error) {
      console.error('JSON解析错误:', error);
      toast.error('JSON格式不正确，请检查后重试');
    }
  };

  // 清除导入的数据
  const clearImportedData = () => {
    setCustomData([]);
    toast.success('已清除导入的数据');
  };

  // 处理知识ID点击，查看对应知识
  const handleKnowledgeIdClick = (id: string) => {
    // 在所有知识数据中查找对应ID的知识
    const knowledge = data?.find(item => item.id === id);
    if (knowledge) {
      setCurrentKnowledge(knowledge);
      setViewKnowledgeModalVisible(true);
    } else {
      toast.error(`未找到ID为 ${id} 的知识`);
    }
  };

  // 切换结果展开/收起状态
  const toggleResultExpansion = (model: string) => {
    setExpandedResults(prev => ({
      ...prev,
      [model]: !prev[model]
    }));
  };

  // 处理导入多个查询
  const handleImportQueries = () => {
    setShowImportQueriesModal(true);
    setImportQueriesText('');
  };

  // 确认导入多个查询
  const confirmImportQueries = () => {
    try {
      // 尝试解析为JSON数组
      const parsedQueries = JSON.parse(importQueriesText);
      if (Array.isArray(parsedQueries) && parsedQueries.length > 0) {
        // 验证每个元素都是字符串
        const isValid = parsedQueries.every(item => typeof item === 'string' && item.trim() !== '');
        
        if (isValid) {
          setQueries(parsedQueries);
          setShowImportQueriesModal(false);
          toast.success(`成功导入 ${parsedQueries.length} 个查询`);
        } else {
          toast.error('JSON格式不正确，请确保是一个字符串数组');
        }
      } else {
        // 如果不是JSON数组，尝试按行分割
        const lines = importQueriesText.split('\n').filter(line => line.trim() !== '');
        if (lines.length > 0) {
          setQueries(lines);
          setShowImportQueriesModal(false);
          toast.success(`成功导入 ${lines.length} 个查询`);
        } else {
          toast.error('未找到有效的查询内容');
        }
      }
    } catch (error) {
      // 如果JSON解析失败，尝试按行分割
      const lines = importQueriesText.split('\n').filter(line => line.trim() !== '');
      if (lines.length > 0) {
        setQueries(lines);
        setShowImportQueriesModal(false);
        toast.success(`成功导入 ${lines.length} 个查询`);
      } else {
        console.error('查询导入错误:', error);
        toast.error('导入格式不正确，请检查后重试');
      }
    }
  };

  // 清除导入的查询
  const clearImportedQueries = () => {
    setQueries([]);
    toast.success('已清除导入的查询');
  };

  // 切换单查询/多查询模式
  const toggleQueryMode = () => {
    setUseMultiQuery(!useMultiQuery);
    // 切换模式时清空之前的结果
    setRecallResults([]);
  };

  // 复制结果到剪贴板
  const copyResultToClipboard = (result: RecallResult) => {
    const textToCopy = `模型: ${result.model}\n查询: ${result.query || query}\n召回数量: ${result.selectedIds.length}\n召回的知识ID: ${result.selectedIds.join(', ')}\n召回结果: ${result.result}`;
    
    navigator.clipboard.writeText(textToCopy).then(() => {
      toast.success('结果已复制到剪贴板');
    }).catch(err => {
      console.error('复制失败:', err);
      toast.error('复制失败，请手动复制');
    });
  };

  // 复制所有结果到剪贴板
  const copyAllResultsToClipboard = () => {
    let allResultsText = '召回测试结果汇总:\n\n';
    
    recallResults.forEach((result, index) => {
      allResultsText += `=== 结果 ${index + 1} ===\n`;
      allResultsText += `模型: ${result.model}\n`;
      allResultsText += `查询: ${result.query || query}\n`;
      allResultsText += `召回数量: ${result.selectedIds.length}\n`;
      if (result.elapsedTime !== undefined) {
        allResultsText += `耗时: ${result.elapsedTime.toFixed(2)}s\n`;
      }
      allResultsText += `召回的知识ID: ${result.selectedIds.join(', ')}\n`;
      allResultsText += `召回结果: ${result.result}\n\n`;
    });
    
    navigator.clipboard.writeText(allResultsText).then(() => {
      toast.success('所有结果已复制到剪贴板');
    }).catch(err => {
      console.error('复制失败:', err);
      toast.error('复制失败，请手动复制');
    });
  };

  // 显示召回测试配置区域
  const handleRecallTest = () => {
    if (selectedKnowledge.length === 0) {
      Message.warning('请至少选择一条知识进行召回测试');
      return;
    }

    // 初始化查询内容
    setQuery('');
    setRecallResults([]);
    setShowRecallConfig(true);
  };

  // 构建召回测试的prompt
  const buildRecallPrompt = (knowledgeItems: KnowledgeItem[]) => {
    // 构建知识项列表
    let knowledgeList = '';
    knowledgeItems.forEach(item => {
      knowledgeList += `<item id="${item.id}">
<title>${item.title}</title>
<use_this_knowledge_when>
${item.usedWhen}
</use_this_knowledge_when>
</item>
`;
    });

    // 完整的system prompt
    const systemPrompt = `Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return "no useful knowledge items".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:
${knowledgeList}

Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in \`<id>xxx</id>\` format.

DO NOT skip reviewing any single knowledge item, review one by one.`;

    return systemPrompt;
  };

  // 执行召回测试
  const executeRecallTest = async () => {
    // 检查查询内容
    if (useMultiQuery) {
      if (queries.length === 0) {
        Message.warning('请导入至少一个查询');
        return;
      }
    } else {
      if (!query.trim()) {
        Message.warning('请输入查询内容');
        return;
      }
    }

    if (selectedModels.length === 0) {
      Message.warning('请至少选择一个模型');
      return;
    }

    if (selectedKnowledge.length === 0) {
      Message.warning('请至少选择一条知识进行召回测试');
      return;
    }

    setIsRecalling(true);
    setCurrentRecallRound(0); // 重置当前召回轮次
    
    // 确定要测试的查询列表
    const queriesToTest = useMultiQuery ? queries : [query];
    
    // 初始化召回结果 - 为每个模型和每个查询创建一个结果
    const initialResults: RecallResult[] = [];
    selectedModels.forEach(model => {
      queriesToTest.forEach(q => {
        initialResults.push({
          model,
          query: q,
          result: '',
          selectedIds: [],
          isLoading: true
        });
      });
    });
    
    setRecallResults(initialResults);
    // 重置展开状态
    setExpandedResults({});

    // 执行多次召回测试
    for (let round = 0; round < recallCount; round++) {
      // 更新当前召回轮次
      setCurrentRecallRound(round + 1);
      
      // 为每个查询和每个模型创建并发请求
      const promises = [];
      
      for (let queryIndex = 0; queryIndex < queriesToTest.length; queryIndex++) {
        const currentQuery = queriesToTest[queryIndex];
        
        for (let modelIndex = 0; modelIndex < selectedModels.length; modelIndex++) {
          const model = selectedModels[modelIndex];
          const resultIndex = queryIndex * selectedModels.length + modelIndex;
          
          promises.push((async () => {
            const startTime = Date.now(); // 记录开始时间
            
            try {
              const systemPrompt = buildRecallPrompt(selectedKnowledge);
              
              // 构建请求参数
              const requestParams: any = {
                model,
                messages: [
                  { role: 'system', content: systemPrompt },
                  { role: 'user', content: currentQuery },
                ],
                temperature,
                max_tokens: 64000
              };
              
              // 如果是 GPT-5 模型，添加 reasoning_effort 参数
              if (model.toLowerCase().includes('gpt-5')) {
                requestParams.reasoning_effort = reasoningEffort;
                requestParams.temperature = 1.0;
              }
              
              const res = await apiClient.ChatStream(requestParams);
              
              const content = (res as any)?.choices?.[0]?.message?.content ?? '';
              
              // 解析结果，提取ID
              const idRegex = /<id>(.*?)<\/id>/g;
              const ids: string[] = [];
              let match;
              
              while ((match = idRegex.exec(content)) !== null) {
                ids.push(match[1]);
              }
              
              const endTime = Date.now(); // 记录结束时间
              const elapsedTime = endTime - startTime; // 计算耗时（毫秒）
              
              // 如果是第一次召回，直接设置结果
              if (round === 0) {
                setRecallResults(prev => {
                  const newResults = [...prev];
                  newResults[resultIndex] = {
                    model,
                    query: currentQuery,
                    result: content,
                    selectedIds: ids,
                    isLoading: false,
                    elapsedTime: elapsedTime / 1000, // 转换为秒
                    roundResults: [{ result: content, selectedIds: ids, elapsedTime }] // 保存每轮结果
                  };
                  return newResults;
                });
              } else {
                // 如果不是第一次，累加结果用于计算平均值
                setRecallResults(prev => {
                  const newResults = [...prev];
                  const existingResult = newResults[resultIndex];
                  const roundResults = [...(existingResult.roundResults || []), { result: content, selectedIds: ids, elapsedTime }];
                  
                  // 计算平均召回数量
                  const totalRecallCount = roundResults.reduce((sum, r) => sum + r.selectedIds.length, 0);
                  const avgRecallCount = totalRecallCount / roundResults.length;
                  
                  // 计算平均耗时（转换为秒）
                  const totalElapsedTime = roundResults.reduce((sum, r) => sum + r.elapsedTime, 0);
                  const avgElapsedTime = totalElapsedTime / roundResults.length / 1000; // 转换为秒
                  
                  newResults[resultIndex] = {
                    model,
                    query: currentQuery,
                    result: content, // 显示最后一次的结果
                    selectedIds: ids, // 显示最后一次的ID
                    isLoading: false,
                    elapsedTime: avgElapsedTime, // 平均耗时（秒）
                    avgRecallCount, // 平均召回数量
                    roundResults // 保存所有轮次的结果
                  };
                  return newResults;
                });
              }
              
            } catch (error) {
              console.error(`模型 ${model} 查询 "${currentQuery}" 第${round + 1}次召回测试失败:`, error);
              
              const endTime = Date.now(); // 记录结束时间
              const elapsedTime = endTime - startTime; // 计算耗时（毫秒）
              
              // 如果是第一次召回，直接设置错误结果
              if (round === 0) {
                setRecallResults(prev => {
                  const newResults = [...prev];
                  newResults[resultIndex] = {
                    model,
                    query: currentQuery,
                    result: '召回过程中出现错误，请稍后重试',
                    selectedIds: [],
                    isLoading: false,
                    elapsedTime: elapsedTime / 1000, // 转换为秒
                    roundResults: [{ result: '召回过程中出现错误，请稍后重试', selectedIds: [], elapsedTime: elapsedTime / 1000 }]
                  };
                  return newResults;
                });
              } else {
                // 如果不是第一次，累加结果用于计算平均值
                setRecallResults(prev => {
                  const newResults = [...prev];
                  const existingResult = newResults[resultIndex];
                  const roundResults = [...(existingResult.roundResults || []), { result: '召回过程中出现错误，请稍后重试', selectedIds: [], elapsedTime }];
                  
                  // 计算平均召回数量
                  const totalRecallCount = roundResults.reduce((sum, r) => sum + r.selectedIds.length, 0);
                  const avgRecallCount = totalRecallCount / roundResults.length;
                  
                  // 计算平均耗时（转换为秒）
                  const totalElapsedTime = roundResults.reduce((sum, r) => sum + r.elapsedTime, 0);
                  const avgElapsedTime = totalElapsedTime / roundResults.length / 1000; // 转换为秒
                  
                  newResults[resultIndex] = {
                    model,
                    query: currentQuery,
                    result: '召回过程中出现错误，请稍后重试',
                    selectedIds: [],
                    isLoading: false,
                    elapsedTime: avgElapsedTime, // 平均耗时（秒）
                    avgRecallCount, // 平均召回数量
                    roundResults // 保存所有轮次的结果
                  };
                  return newResults;
                });
              }
            }
          })());
        }
      }

      // 等待当前轮次所有模型和查询调用完成
      await Promise.all(promises);
    }
    
    setIsRecalling(false);
    setCurrentRecallRound(0); // 重置当前召回轮次
  };

  return (
    <div className="h-full flex flex-col">
      <PageHeader 
        title="知识召回测试" 
        description="测试不同模型的知识召回效果"
      />
      
      <Form layout="horizontal" className="mb-4 flex-shrink-0" form={form}>
        <Grid.Row gutter={16}>
          <Grid.Col span={6}>
            <Form.Item label="关键词" field="keyword">
              <Input placeholder="请输入关键词" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="Agent" field="agent">
              <Select placeholder="请选择Agent" allowClear>
                {agentOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="Variant" field="variant">
              <Select placeholder="请选择Variant" allowClear>
                {variantOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="Tools" field="tools">
              <Select 
                placeholder="请选择Tools" 
                allowClear
                mode="multiple"
                style={{ width: '100%' }}
              >
                {toolsOptions.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Grid.Col>
        </Grid.Row>
        <Grid.Row gutter={16} className="mt-4">
          <Grid.Col span={20}></Grid.Col>
          <Grid.Col span={4} className="flex justify-end items-end">
            <Space>
              <Button type="primary" icon={<IconSearch />} onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Grid.Col>
        </Grid.Row>
      </Form>

      <div className="flex justify-between mb-4 flex-shrink-0">
        <div>
          <Space>
            <Button 
              type="primary" 
              icon={<IconPlayArrow />}
              onClick={handleRecallTest}
              disabled={selectedRowKeys.length === 0}
            >
              召回测试选中知识
            </Button>
            <Button 
              icon={<IconFile />}
              onClick={handleImportJson}
            >
              导入JSON
            </Button>
            {customData.length > 0 && (
              <Button 
                onClick={clearImportedData}
              >
                清除导入数据
              </Button>
            )}
            <Button 
              href="https://bytedance.larkoffice.com/wiki/E5gXwcnENiJgy4kLCrTc59aen0W"
              target="_blank"
            >
              导入JSON说明
            </Button>
          </Space>
        </div>
        <div>
          <Text>已选择 {selectedRowKeys.length} 条知识</Text>
          {customData.length > 0 && (
            <Text className="ml-4" style={{ color: '#165DFF' }}>已导入 {customData.length} 条数据</Text>
          )}
          <Text className="ml-4">总计 {data?.length || 0} 条数据</Text>
        </div>
      </div>

      <div className="flex-shrink-0" style={{ height: '400px', overflow: 'hidden' }}>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          data={tableData}
          pagination={{
            current: 1,
            pageSize: 500,
            total: data?.length || 0,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          loading={isLoading}
          scroll={{ x: 1500, y: 300 }}
          border
        />
      </div>

      {/* 召回测试配置和结果区域 */}
      {showRecallConfig && (
        <Card className="mt-4 flex-shrink-0">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">
              {recallResults.length > 0 ? "召回测试结果" : "召回测试配置"}
            </h2>
            <Space>
              <Button onClick={() => setShowRecallConfig(false)}>
                关闭
              </Button>
              {recallResults.length === 0 ? (
                <Button 
                  type="primary" 
                  icon={isRecalling ? <IconLoading /> : <IconPlayArrow />}
                  onClick={executeRecallTest}
                  loading={isRecalling}
                  disabled={!query.trim() || selectedModels.length === 0 || selectedKnowledge.length === 0}
                >
                  {isRecalling ? 
                    (recallCount > 1 ? `召回测试中... (${currentRecallRound}/${recallCount})` : '召回测试中...') 
                    : '开始召回测试'
                  }
                </Button>
              ) : (
                <Button 
                  type="primary" 
                  icon={isRecalling ? <IconLoading /> : <IconPlayArrow />}
                  onClick={executeRecallTest}
                  loading={isRecalling}
                  disabled={!query.trim() || selectedModels.length === 0 || selectedKnowledge.length === 0}
                >
                  {isRecalling ? 
                    (recallCount > 1 ? `重新测试中... (${currentRecallRound}/${recallCount})` : '重新测试中...') 
                    : '重新测试'
                  }
                </Button>
              )}
            </Space>
          </div>
          
          {recallResults.length === 0 ? (
            <div className="recall-config">
              {/* 查询模式切换 */}
              <div className="mb-4">
                <div className="flex items-center mb-2">
                    <h3 className="text-md font-medium mr-8">查询模式</h3>
                    <div className="ml-4">
                      <Button 
                        type={useMultiQuery ? "default" : "primary"}
                        size="small"
                        onClick={() => setUseMultiQuery(false)}
                        className="mr-2"
                      >
                        单查询
                      </Button>
                      <Button 
                        type={useMultiQuery ? "primary" : "default"}
                        size="small"
                        onClick={() => setUseMultiQuery(true)}
                      >
                        多查询
                      </Button>
                    </div>
                  </div>
              </div>
              
              {/* 查询内容区域 */}
              {useMultiQuery ? (
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-md font-medium">查询列表</h3>
                    <Space>
                      <Button 
                        icon={<IconFile />}
                        onClick={handleImportQueries}
                        size="small"
                      >
                        导入查询
                      </Button>
                      {queries.length > 0 && (
                        <Button 
                          onClick={clearImportedQueries}
                          size="small"
                        >
                          清除查询
                        </Button>
                      )}
                    </Space>
                  </div>
                  
                  {queries.length > 0 ? (
                    <div className="bg-gray-50 p-3 rounded-md max-h-40 overflow-auto">
                      {queries.map((q, index) => (
                        <div key={index} className="mb-2 last:mb-0">
                          <Text className="text-sm">{index + 1}. {q}</Text>
                        </div>
                      ))}
                      <div className="mt-2 text-sm text-gray-500">
                        已导入 {queries.length} 个查询
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gray-50 p-3 rounded-md text-center text-gray-500">
                      请导入查询内容
                    </div>
                  )}
                </div>
              ) : (
                <div className="mb-4">
                  <h3 className="text-md font-medium mb-2">查询内容</h3>
                  <TextArea
                    placeholder="请输入要测试的查询内容"
                    value={query}
                    onChange={(value) => setQuery(value)}
                    style={{ minHeight: 50 }}
                  />
                </div>
              )}
              
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div>
                  <h3 className="text-md font-medium mb-2">选择模型</h3>
                  <Select
                    placeholder="选择模型"
                    mode="multiple"
                    value={selectedModels}
                    onChange={(value) => setSelectedModels(value)}
                    style={{ width: '100%' }}
                  >
                    {models.map((modelGroup, modelIndex) => (
                      <Select.OptGroup
                        label={modelGroup.type}
                        key={`${modelGroup?.type}_${modelIndex}`}
                      >
                        {modelGroup?.models?.map((item, index) => (
                          <Select.Option
                            key={`${modelGroup?.type}_${modelIndex}_${index}`}
                            value={item}
                          >
                            {item}
                          </Select.Option>
                        ))}
                      </Select.OptGroup>
                    ))}
                  </Select>
                </div>
                
                <div>
                  <h3 className="text-md font-medium mb-2">Temperature</h3>
                  <Select
                    value={temperature}
                    onChange={(value) => setTemperature(value)}
                    style={{ width: '100%' }}
                  >
                    <Select.Option value={0.1}>0.1</Select.Option>
                    <Select.Option value={0.3}>0.3</Select.Option>
                    <Select.Option value={0.5}>0.5</Select.Option>
                    <Select.Option value={0.7}>0.7</Select.Option>
                    <Select.Option value={0.9}>0.9</Select.Option>
                  </Select>
                </div>
                
                <div>
                  <h3 className="text-md font-medium mb-2">召回次数</h3>
                  <Select
                    value={recallCount}
                    onChange={(value) => setRecallCount(value)}
                    style={{ width: '100%' }}
                  >
                    <Select.Option value={1}>1次</Select.Option>
                    <Select.Option value={3}>3次</Select.Option>
                    <Select.Option value={5}>5次</Select.Option>
                    <Select.Option value={10}>10次</Select.Option>
                  </Select>
                </div>
              </div>
              
              {/* Reasoning Effort 选择器，仅在选择 GPT-5 模型时显示 */}
              {hasGPT5Model && (
                <div className="mb-4">
                  <h3 className="text-md font-medium mb-2">Reasoning Effort</h3>
                  <Select
                    value={reasoningEffort}
                    onChange={(value) => setReasoningEffort(value)}
                    style={{ width: '100%' }}
                  >
                    <Select.Option value="minimal">Minimal</Select.Option>
                    <Select.Option value="low">Low</Select.Option>
                    <Select.Option value="medium">Medium</Select.Option>
                    <Select.Option value="high">High</Select.Option>
                  </Select>
                </div>
              )}
            </div>
          ) : (
            <div className="recall-results">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-md font-medium">召回结果</h3>
                <Space>
                  <Button 
                    type="primary" 
                    size="small"
                    onClick={copyAllResultsToClipboard}
                  >
                    复制所有结果
                  </Button>
                </Space>
              </div>
              
              {/* 显示查询内容 */}
              {useMultiQuery ? (
                <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">查询列表:</p>
                  <div className="max-h-32 overflow-auto">
                    {queries.map((q, index) => (
                      <p key={index} className="font-medium mb-1">{index + 1}. {q}</p>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">查询内容:</p>
                  <p className="font-medium">{query}</p>
                </div>
              )}
              
              {/* 显示召回进度 */}
              {isRecalling && recallCount > 1 && (
                <div className="mb-4 p-3 bg-yellow-50 rounded-lg">
                  <p className="text-sm text-gray-500">
                    召回进度: <span className="font-medium">{currentRecallRound}/{recallCount}</span>
                  </p>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${(currentRecallRound / recallCount) * 100}%` }}
                    ></div>
                  </div>
                </div>
              )}
              
              <div className="grid grid-cols-1 gap-4">
                {recallResults.map((result, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <div>
                        <h4 className="font-medium">{result.model}</h4>
                        {result.query && (
                          <p className="text-sm text-gray-500">查询: {result.query}</p>
                        )}
                      </div>
                      <div className="flex items-center">
                        {result.isLoading ? (
                          <Tag color="orange" icon={<IconLoading />}>处理中</Tag>
                        ) : (
                          <Tag color="green">完成</Tag>
                        )}
                        {!result.isLoading && (
                          <Button 
                            type="text" 
                            size="mini"
                            onClick={() => copyResultToClipboard(result)}
                            className="ml-2"
                          >
                            复制
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    {/* 统计信息 */}
                    {!result.isLoading && (
                      <div className="flex flex-wrap gap-4 mb-2 text-sm">
                        <div>
                          <span className="text-gray-500">召回数量:</span>
                          <span className="ml-1 font-medium">
                            {recallCount > 1 && result.avgRecallCount !== undefined 
                              ? `${result.avgRecallCount.toFixed(1)} (平均)` 
                              : result.selectedIds.length}
                          </span>
                        </div>
                        {result.elapsedTime !== undefined && (
                          <div>
                            <span className="text-gray-500">耗时:</span>
                            <span className="ml-1 font-medium">
                              {recallCount > 1 ? `${result.elapsedTime.toFixed(2)}s (平均)` : `${result.elapsedTime.toFixed(2)}s`}
                            </span>
                          </div>
                        )}
                        {recallCount > 1 && (
                          <div>
                            <span className="text-gray-500">召回次数:</span>
                            <span className="ml-1 font-medium">{recallCount}次</span>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {result.selectedIds.length > 0 && (
                      <div className="mb-2">
                        <p className="text-sm text-gray-500 mb-1">召回的知识ID:</p>
                        <div className="flex flex-wrap gap-2">
                          {result.selectedIds.map((id, idIndex) => (
                            <Tag 
                              key={idIndex} 
                              color="blue" 
                              className="cursor-pointer hover:bg-blue-100"
                              onClick={() => handleKnowledgeIdClick(id)}
                            >
                              {id}
                            </Tag>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <p className="text-sm text-gray-500">召回结果:</p>
                        {!result.isLoading && (
                          <Button 
                            type="text" 
                            size="mini"
                            onClick={() => toggleResultExpansion(`${result.model}_${result.query || 'single'}`)}
                          >
                            {expandedResults[`${result.model}_${result.query || 'single'}`] ? '收起' : '展开'}
                          </Button>
                        )}
                      </div>
                      {expandedResults[`${result.model}_${result.query || 'single'}`] ? (
                        <pre className="whitespace-pre-wrap bg-gray-100 p-2 rounded text-sm">
                          {result.result}
                        </pre>
                      ) : (
                        <div className="bg-gray-100 p-2 rounded text-sm text-gray-500 italic">
                          {result.result.substring(0, 100)}{result.result.length > 100 ? '...' : ''}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Card>
      )}

      {/* 导入多个查询模态框 */}
      <Modal
        title="导入多个查询"
        visible={showImportQueriesModal}
        onOk={confirmImportQueries}
        onCancel={() => setShowImportQueriesModal(false)}
        okText="确认导入"
        cancelText="取消"
        style={{ width: '80%', maxWidth: '800px' }}
      >
        <div className="mb-4">
          <p className="text-sm text-gray-500 mb-2">
            请粘贴要测试的查询内容，支持以下格式：
          </p>
          <ul className="text-sm text-gray-500 mb-2 list-disc pl-5">
            <li>JSON格式：["查询1", "查询2", "查询3"]</li>
            <li>每行一个查询：查询1<br/>查询2<br/>查询3</li>
          </ul>
          <textarea
            className="bg-gray-100 p-4 rounded-md w-full font-mono text-sm"
            value={importQueriesText}
            onChange={(e) => setImportQueriesText(e.target.value)}
            rows={15}
            placeholder={`[
  "如何使用Python进行数据分析？",
  "什么是机器学习？",
  "深度学习和传统机器学习有什么区别？"
]

或者：

如何使用Python进行数据分析？
什么是机器学习？
深度学习和传统机器学习有什么区别？`}
          />
        </div>
      </Modal>

      {/* 查看知识内容模态框 */}
      <Modal
        title="知识内容详情"
        visible={viewKnowledgeModalVisible}
        onCancel={() => setViewKnowledgeModalVisible(false)}
        footer={null}
        style={{ maxHeight: '80vh', overflow: 'auto', width:'80%' }}
      >
        {currentKnowledge && (
          <div>
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">{currentKnowledge.title}</h3>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-500">ID</p>
                  <p>{currentKnowledge.id}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">路径</p>
                  <p>{currentKnowledge.path}</p>
                </div>
              </div>
              <div className="mb-4">
                <p className="text-sm text-gray-500">启用条件</p>
                <div className="bg-gray-100 p-2 rounded">
                  <pre>{currentKnowledge.enableIf}</pre>
                </div>
              </div>
              <div className="mb-4">
                <p className="text-sm text-gray-500">使用场景</p>
                <div className="bg-gray-100 p-2 rounded">
                  <pre>{currentKnowledge.usedWhen}</pre>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500">内容</p>
                <div className="bg-gray-100 p-4 rounded max-h-96 overflow-auto">
                  <pre className="whitespace-pre-wrap">{currentKnowledge.content}</pre>
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* 导入JSON模态框 */}
      <Modal
        title="导入JSON数据"
        visible={showImportModal}
        onOk={confirmImportJson}
        onCancel={() => setShowImportModal(false)}
        okText="确认导入"
        cancelText="取消"
        style={{ width: '80%', maxWidth: '800px' }}
      >
        <div className="mb-4">
          <p className="text-sm text-gray-500 mb-2">
            请粘贴JSON格式的知识数据，格式应为包含id、title、content、enableIf、usedWhen和path字段的数组。
          </p>
          <textarea
            className="bg-gray-100 p-4 rounded-md w-full font-mono text-sm"
            value={importJson}
            onChange={(e) => setImportJson(e.target.value)}
            rows={15}
            placeholder={`[
  {
    "id": "knowledge_1",
    "title": "知识标题",
    "path": "/path/to/knowledge",
    "enableIf": "true",
    "usedWhen": "使用场景描述",
    "content": "知识内容"
  }
]`}
          />
        </div>
      </Modal>
    </div>
  );
}