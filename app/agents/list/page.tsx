"use client";

import { apiClient } from "../../api/request";
import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Edit, Trash2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { PaginationInfo } from "@/components/common/PaginationInfo";
import { useAgents } from "@/app/hooks/useAgents";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";

// 常量定义
const pageSize = 10;

// 表单结构定义
const formSchema = z.object({
  name: z.string().min(1, "名称不能为空"),
  description: z.string().optional(),
});

// Agent类型定义
interface Agent {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  creator?:
    | {
        id: string;
        username: string;
        avatar_url?: string;
      }
    | string;
}

// 日期时间格式化工具函数
function formatDateTime(dateString: string) {
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    }).format(date);
  } catch {
    return dateString;
  }
}

// 获取创建者名称的工具函数
function getCreatorName(creator: Agent["creator"]) {
  if (!creator) return "未知";
  if (typeof creator === "string") return creator;
  return creator.username;
}

// Agent表单组件
function AgentFormDialog({
  open,
  onOpenChange,
  editingAgent,
  onSubmit,
  isSubmitting,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editingAgent: Agent | null;
  onSubmit: (values: z.infer<typeof formSchema>) => void;
  isSubmitting: boolean;
}) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: editingAgent?.name || "",
      description: editingAgent?.description || "",
    },
  });

  useEffect(() => {
    if (editingAgent) {
      form.reset({
        name: editingAgent.name,
        description: editingAgent.description,
      });
    } else {
      form.reset({
        name: "",
        description: "",
      });
    }
  }, [form, editingAgent, open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {editingAgent ? "编辑 Agent" : "创建 Agent"}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>名称</FormLabel>
                  <FormControl>
                    <Input {...field} disabled={isSubmitting} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>描述</FormLabel>
                  <FormControl>
                    <Textarea {...field} disabled={isSubmitting} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  {editingAgent ? "保存中..." : "创建中..."}
                </div>
              ) : editingAgent ? (
                "保存"
              ) : (
                "创建"
              )}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

// 主页面组件
export default function AgentsListPage() {
  const router = useRouter();
  const { data: agents = [], isLoading, refetch } = useAgents();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // 获取Agent列表数据
  const fetchAgents = useCallback(async () => {
    try {
      // 调用react-query提供的refetch方法刷新数据
      const result = await refetch();
      const agentsData = result.data || [];
      const total = agentsData.length;
      setTotalCount(total);
      setTotalPages(Math.max(1, Math.ceil(total / pageSize)));
    } catch {
      toast.error("获取 Agent 列表失败");
    }
  }, [refetch]);

  // 页面首次加载时获取数据
  useEffect(() => {
    fetchAgents();
  }, [fetchAgents]); // 依赖fetchAgents

  // 页码变化处理
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 创建Agent
  const handleCreate = async (values: z.infer<typeof formSchema>) => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    try {
      await apiClient.CreateAgent({
        name: values.name,
        description: values.description || "",
      });
      toast.success("创建成功");
      setIsDialogOpen(false);
      await fetchAgents();
    } catch {
      toast.error("创建失败");
    } finally {
      setIsSubmitting(false);
    }
  };

  // 编辑Agent
  const handleEdit = async (values: z.infer<typeof formSchema>) => {
    if (!editingAgent || isSubmitting) return;
    setIsSubmitting(true);
    try {
      await apiClient.UpdateAgent({
        agent_id: editingAgent.id,
        name: values.name,
        description: values.description || "",
      });
      toast.success("更新成功");
      setIsDialogOpen(false);
      await fetchAgents();
    } catch {
      toast.error("更新失败");
    } finally {
      setIsSubmitting(false);
    }
  };

  // 删除Agent
  const handleDelete = async (id: string) => {
    try {
      await apiClient.DeleteAgent({ agent_id: id });
      toast.success("删除成功");
      await fetchAgents();
    } catch {
      toast.error("删除失败");
    }
  };

  // 表单提交处理
  const handleFormSubmit = (values: z.infer<typeof formSchema>) => {
    if (editingAgent) {
      handleEdit(values);
    } else {
      handleCreate(values);
    }
  };

  // 编辑按钮点击处理
  const handleEditClick = (agent: Agent) => {
    setEditingAgent(agent);
    setIsDialogOpen(true);
  };

  // 创建按钮点击处理
  const handleCreateClick = () => {
    setEditingAgent(null);
    setIsDialogOpen(true);
  };

  // 对话框开关状态变化处理
  const handleDialogOpenChange = (open: boolean) => {
    if (!open) {
      setIsDialogOpen(false);
      setEditingAgent(null);
    } else {
      setIsDialogOpen(true);
    }
  };

  const handleCardClick = (agentId: string) => {
    router.push(`/agents/env?agent_id=${agentId}`);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{/* Agent List */}</h1>
        <Button onClick={handleCreateClick}>创建 Agent</Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
        </div>
      ) : agents.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <p className="text-gray-500">
            暂无数据，请点击右上角&ldquo;创建 Agent&rdquo;按钮添加
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {agents.map(agent => (
            <Card
              key={agent.id}
              className="cursor-pointer hover:shadow-lg transition-shadow border border-gray-100"
              onClick={() => handleCardClick(agent.id)}
            >
              <CardHeader className="pb-2">
                <CardTitle className="flex justify-between items-center">
                  <span className="truncate text-xl font-bold">
                    {agent.name}
                  </span>
                  <div className="flex space-x-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={e => {
                        e.stopPropagation();
                        handleEditClick(agent);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-red-500"
                      onClick={e => {
                        e.stopPropagation();
                        handleDelete(agent.id);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-2">
                <div className="space-y-4">
                  <p className="text-gray-600 line-clamp-2 min-h-[3rem]">
                    {agent.description || "暂无描述"}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <Badge variant="outline" className="bg-gray-50 font-normal">
                      {getCreatorName(agent.creator)}
                    </Badge>
                    <span className="text-xs">
                      {formatDateTime(agent.created_at)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {agents.length > 0 && (
        <PaginationInfo
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          totalCount={totalCount}
          onPageChange={handlePageChange}
        />
      )}

      <AgentFormDialog
        open={isDialogOpen}
        onOpenChange={handleDialogOpenChange}
        editingAgent={editingAgent}
        onSubmit={handleFormSubmit}
        isSubmitting={isSubmitting}
      />
    </div>
  );
}
