import { AgentConfigType } from "@/app/bam/aime/namespaces/agent";

// 常量定义
export const pageSize = 10;

// 日期时间格式化工具函数
export function formatDateTime(dateString: string) {
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    }).format(date);
  } catch {
    return dateString;
  }
}

interface Creator {
  id: string;
  username: string;
  avatar_url?: string;
}

// 获取创建者名称的工具函数
export function getCreatorName(creator: string | Creator | undefined) {
  if (!creator) return "未知";
  if (typeof creator === "string") return creator;
  return creator.username;
}

// 类型显示格式化
export function formatType(type: AgentConfigType) {
  return type;
}

// 分页状态接口
export interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalCount: number;
}

// 分页状态更新函数
export function updatePaginationState(
  total: number,
  setTotalPages: (pages: number) => void,
  setTotalCount: (count: number) => void
) {
  const totalCount = Number(total) || 0;
  setTotalCount(totalCount);
  setTotalPages(Math.ceil(totalCount / pageSize));
}

// 删除后的分页处理
export function handleDeletePagination<T>(
  currentItems: T[],
  totalPages: number,
  currentPage: number,
  setCurrentPage: (page: number) => void
) {
  if (currentItems.length === 1 && totalPages > 1 && currentPage > 1) {
    setCurrentPage(currentPage - 1);
  }
} 