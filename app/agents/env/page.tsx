"use client";

import { apiClient } from "../../api/request";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AgentConfig,
  AgentConfigType,
  Agent,
} from "@/app/bam/aime/namespaces/agent";
import { PaginationInfo } from "@/components/common/PaginationInfo";
import { DataTable } from "@/components/common/DataTable";
import { pageSize, formatDateTime, formatType } from "../utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useAgents } from "@/app/hooks/useAgents";
import {
  useAbConfigs,
  useBaseConfigs,
  useOtherConfigs,
} from "@/app/hooks/useAgentConfigs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useRouter } from "next/navigation";

interface AgentConfigFormProps {
  formData: {
    agent_id: string;
    type: AgentConfigType;
    name: string;
    description: string;
  };
  onSubmit: (e: React.FormEvent) => void;
  onFormDataChange: (data: {
    agent_id: string;
    type: AgentConfigType;
    name: string;
    description: string;
  }) => void;
  editingConfig: AgentConfig | null;
  isSubmitting: boolean;
  agents: Agent[];
}

const formSchema = z.object({
  agent_id: z.string().min(1, "请选择一个 Agent"),
  type: z.nativeEnum(AgentConfigType),
  name: z.string().min(1, "请输入名称"),
  description: z.string(),
});

function AgentConfigForm({
  formData,
  onSubmit,
  onFormDataChange,
  editingConfig,
  isSubmitting,
  agents,
}: AgentConfigFormProps) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: formData,
  });

  const handleFormSubmit = (values: z.infer<typeof formSchema>) => {
    onFormDataChange(values);
    const event = new Event(
      "submit"
    ) as unknown as React.FormEvent<HTMLFormElement>;
    onSubmit(event);
  };

  // 当外部 formData 变化时重置表单
  useEffect(() => {
    form.reset(formData);
  }, [formData]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleFormSubmit)}
        className="space-y-4"
      >
        <FormField
          control={form.control}
          name="agent_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Agent</FormLabel>
              <FormControl>
                <Select
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value);
                    onFormDataChange({
                      ...formData,
                      agent_id: value,
                    });
                  }}
                  disabled={isSubmitting || !!editingConfig}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an agent" />
                  </SelectTrigger>
                  <SelectContent>
                    {agents.map((agent) => (
                      <SelectItem key={agent.id} value={agent.id}>
                        {agent.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Type</FormLabel>
              <FormControl>
                <Select
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value);
                    onFormDataChange({
                      ...formData,
                      type: value as AgentConfigType,
                    });
                  }}
                  disabled={isSubmitting}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={AgentConfigType.AgentConfigTypeBase}>
                      {formatType(AgentConfigType.AgentConfigTypeBase)}
                    </SelectItem>
                    <SelectItem value={AgentConfigType.AgentConfigTypeFeature}>
                      {formatType(AgentConfigType.AgentConfigTypeFeature)}
                    </SelectItem>
                    <SelectItem value={AgentConfigType.AgentConfigTypeUser}>
                      {formatType(AgentConfigType.AgentConfigTypeUser)}
                    </SelectItem>
                    <SelectItem value={AgentConfigType.AgentConfigTypeAbTest}>
                      {formatType(AgentConfigType.AgentConfigTypeAbTest)}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  onChange={(e) => {
                    field.onChange(e);
                    onFormDataChange({
                      ...formData,
                      name: e.target.value,
                    });
                  }}
                  disabled={isSubmitting}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  onChange={(e) => {
                    field.onChange(e);
                    onFormDataChange({
                      ...formData,
                      description: e.target.value,
                    });
                  }}
                  disabled={isSubmitting}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              {editingConfig ? "Saving..." : "Creating..."}
            </div>
          ) : editingConfig ? (
            "Save"
          ) : (
            "Create"
          )}
        </Button>
      </form>
    </Form>
  );
}

export default function AgentsConfigPage() {
  const [editingConfig, setEditingConfig] = useState<AgentConfig | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageAb, setCurrentPageAb] = useState(1);
  const [filterAgentId, setFilterAgentId] = useState<string>("all");
  const [formData, setFormData] = useState<{
    agent_id: string;
    type: AgentConfigType;
    name: string;
    description: string;
  }>({
    agent_id: "",
    type: AgentConfigType.AgentConfigTypeBase,
    name: "",
    description: "",
  });

  const { data: agents = [] } = useAgents();
  const filterForm = useForm({
    defaultValues: {
      filterAgentId: "",
    },
  });

  const router = useRouter();

  // 从 URL 参数中获取 agent_id
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const agentId = searchParams.get("agent_id");
    if (agentId) {
      setFilterAgentId(agentId);
      setFormData((prev) => ({ ...prev, agent_id: agentId }));
    }
  }, []);

  // 使用新的 hook 获取配置数据
  const {
    data: baseConfigData,
    isLoading: isLoadingBase,
    refetch: refetchBase,
  } = useBaseConfigs(filterAgentId);
  const {
    data: otherConfigData,
    isLoading: isLoadingOther,
    refetch: refetchOther,
  } = useOtherConfigs(filterAgentId, currentPage, pageSize);

  const {
    data: abConfigData,
    isLoading: isLoadingAb,
    refetch: refetchAb,
  } = useAbConfigs(filterAgentId, currentPageAb, pageSize);

  const baseConfigs = baseConfigData?.agent_configs || [];
  const otherConfigs = otherConfigData?.agent_configs || [];
  const abConfigs = abConfigData?.agent_configs || [];

  // 计算总页数和总数
  const totalOtherCount = Number(otherConfigData?.total || 0);
  const totalOtherPages = Math.ceil(totalOtherCount / pageSize);

  const totalAbCount = Number(abConfigData?.total || 0);
  const totalAbPages = Math.ceil(totalAbCount / pageSize);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleAbPageChange = (page: number) => {
    setCurrentPageAb(page);
  };

  const handleCreate = async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    try {
      await apiClient.CreateAgentConfig({
        agent_id: formData.agent_id,
        type: formData.type,
        name: formData.name,
        description: formData.description,
      });
      toast.success("创建成功");
      setIsDialogOpen(false);
      setFormData({
        agent_id: "",
        type: AgentConfigType.AgentConfigTypeBase,
        name: "",
        description: "",
      });
      // 重新获取数据
      await Promise.all([refetchBase(), refetchOther(), refetchAb()]);
    } catch {
      toast.error("创建失败");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = async () => {
    if (!editingConfig || isSubmitting) return;
    setIsSubmitting(true);
    try {
      await apiClient.UpdateAgentConfig({
        agent_config_id: editingConfig.id,
        name: formData.name,
        description: formData.description,
      });
      toast.success("更新成功");
      setIsDialogOpen(false);
      setFormData({
        agent_id: "",
        type: AgentConfigType.AgentConfigTypeBase,
        name: "",
        description: "",
      });
      setEditingConfig(null);
    } catch {
      toast.error("更新失败");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await apiClient.DeleteAgentConfig({ agent_config_id: id });
      toast.success("删除成功");
      // 重新获取数据
      await Promise.all([refetchBase(), refetchOther(), refetchAb()]);
      if (currentPage > 1 && otherConfigs.length === 1) {
        setCurrentPage(currentPage - 1);
      }
    } catch {
      toast.error("删除失败");
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingConfig) {
      handleEdit();
    } else {
      handleCreate();
    }
  };

  const columns = [
    {
      header: "名称",
      accessor: "name" as keyof AgentConfig,
      className: "w-[180px] font-medium",
    },
    {
      header: "Agent",
      accessor: (config: AgentConfig) => {
        const agent = agents.find((a) => a.id === config.agent_id);
        return agent?.name || config.agent_id;
      },
      className: "w-[180px]",
    },
    {
      header: "类型",
      accessor: (config: AgentConfig) => (
        <Badge variant="outline" className="bg-gray-50 font-normal">
          {formatType(config.type)}
        </Badge>
      ),
      className: "w-[100px] text-center",
    },
    {
      header: "描述",
      accessor: (config: AgentConfig) => (
        <div className="truncate max-w-[300px]" title={config.description}>
          {config.description || "-"}
        </div>
      ),
      className: "min-w-[300px] max-w-[400px]",
    },
    {
      header: "创建者",
      accessor: (config: AgentConfig) => config.creator,
      className: "w-[120px]",
    },
    {
      header: "创建时间",
      accessor: (config: AgentConfig) => formatDateTime(config.created_at),
      className: "w-[160px] whitespace-nowrap",
    },
    {
      header: "更新时间",
      accessor: (config: AgentConfig) => formatDateTime(config.updated_at),
      className: "w-[160px] whitespace-nowrap",
    },
    {
      header: "操作",
      accessor: (config: AgentConfig) => (
        <div className="flex justify-center items-center space-x-2 action-column">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 hover:bg-gray-100"
                  onClick={() => {
                    setEditingConfig(config);
                    setFormData({
                      agent_id: config.agent_id,
                      type: config.type,
                      name: config.name,
                      description: config.description,
                    });
                    setIsDialogOpen(true);
                  }}
                >
                  <Edit className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>编辑配置</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 hover:bg-red-100 text-red-500"
                  onClick={() => handleDelete(config.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>删除配置</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      ),
      sticky: true,
      className:
        "w-[140px] text-center sticky right-0 bg-white shadow-[-8px_0_16px_-6px_rgba(0,0,0,0.1)]",
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Form {...filterForm}>
          <div className="flex items-center gap-3">
            <FormLabel className="text-sm font-medium whitespace-nowrap">
              Filter by Agent
            </FormLabel>
            <FormItem className="w-[240px] mb-0">
              <FormControl>
                <Select
                  value={filterAgentId}
                  onValueChange={(value) => {
                    setFilterAgentId(value);
                    setCurrentPage(1);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an agent" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Agents</SelectItem>
                    {agents.map((agent) => (
                      <SelectItem key={agent.id} value={agent.id}>
                        {agent.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
            </FormItem>
          </div>
        </Form>
      </div>

      {/* Base 配置部分 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Base Configurations</h2>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button
                disabled={baseConfigs.length > 0}
                onClick={() => {
                  setEditingConfig(null);
                  setFormData({
                    agent_id: filterAgentId !== "all" ? filterAgentId : "",
                    type: AgentConfigType.AgentConfigTypeBase,
                    name: "",
                    description: "",
                  });
                }}
              >
                New Version
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editingConfig ? "Edit Config" : "Create Base Config"}
                </DialogTitle>
              </DialogHeader>
              <AgentConfigForm
                formData={formData}
                onSubmit={handleSubmit}
                onFormDataChange={setFormData}
                editingConfig={editingConfig}
                isSubmitting={isSubmitting}
                agents={agents}
              />
            </DialogContent>
          </Dialog>
        </div>
        <div className="relative">
          <DataTable
            data={baseConfigs}
            columns={columns}
            loading={isLoadingBase}
            keyField="id"
            onRowClick={(row: AgentConfig) => {
              // 检查点击事件是否来自操作列
              const event = window.event as MouseEvent;
              const target = event.target as HTMLElement;
              if (target.closest(".action-column")) {
                return;
              }
              router.push(`/agents/version?config_id=${row.id}`);
            }}
            rowClassName="cursor-pointer hover:bg-gray-50"
          />
          {isLoadingBase && (
            <div className="absolute inset-0 bg-white/50 flex items-center justify-center">
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            </div>
          )}
        </div>
      </div>

      {/* AB 配置部分 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">ABTest Configurations</h2>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button
                onClick={() => {
                  setEditingConfig(null);
                  setFormData({
                    agent_id: filterAgentId !== "all" ? filterAgentId : "",
                    type: AgentConfigType.AgentConfigTypeAbTest,
                    name: "",
                    description: "",
                  });
                }}
              >
                New Version
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editingConfig ? "Edit Config" : "Create Base Config"}
                </DialogTitle>
              </DialogHeader>
              <AgentConfigForm
                formData={formData}
                onSubmit={handleSubmit}
                onFormDataChange={setFormData}
                editingConfig={editingConfig}
                isSubmitting={isSubmitting}
                agents={agents}
              />
            </DialogContent>
          </Dialog>
        </div>
        <div className="relative">
          <DataTable
            data={abConfigs}
            columns={columns}
            loading={isLoadingAb}
            keyField="id"
            onRowClick={(row: AgentConfig) => {
              // 检查点击事件是否来自操作列
              const event = window.event as MouseEvent;
              const target = event.target as HTMLElement;
              if (target.closest(".action-column")) {
                return;
              }
              router.push(`/agents/version?config_id=${row.id}`);
            }}
            rowClassName="cursor-pointer hover:bg-gray-50"
          />
          {isLoadingAb && (
            <div className="absolute inset-0 bg-white/50 flex items-center justify-center">
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            </div>
          )}
        </div>
      </div>

      <PaginationInfo
        currentPage={currentPage}
        totalPages={totalAbPages}
        pageSize={pageSize}
        totalCount={totalAbCount}
        onPageChange={handleAbPageChange}
      />

      {/* 其他配置部分 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Other Configurations</h2>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button
                onClick={() => {
                  setEditingConfig(null);
                  setFormData({
                    agent_id: filterAgentId !== "all" ? filterAgentId : "",
                    type: AgentConfigType.AgentConfigTypeFeature,
                    name: "",
                    description: "",
                  });
                }}
              >
                New Version
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editingConfig ? "Edit Config" : "Create Other Config"}
                </DialogTitle>
              </DialogHeader>
              <AgentConfigForm
                formData={formData}
                onSubmit={handleSubmit}
                onFormDataChange={setFormData}
                editingConfig={editingConfig}
                isSubmitting={isSubmitting}
                agents={agents}
              />
            </DialogContent>
          </Dialog>
        </div>
        <div className="relative">
          <DataTable
            data={otherConfigs}
            columns={columns}
            loading={isLoadingOther}
            keyField="id"
            onRowClick={(row: AgentConfig) => {
              // 检查点击事件是否来自操作列
              const event = window.event as MouseEvent;
              const target = event.target as HTMLElement;
              if (target.closest(".action-column")) {
                return;
              }
              router.push(`/agents/version?config_id=${row.id}`);
            }}
            rowClassName="cursor-pointer hover:bg-gray-50"
          />
          {isLoadingOther && (
            <div className="absolute inset-0 bg-white/50 flex items-center justify-center">
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            </div>
          )}
        </div>
      </div>

      <PaginationInfo
        currentPage={currentPage}
        totalPages={totalOtherPages}
        pageSize={pageSize}
        totalCount={totalOtherCount}
        onPageChange={handlePageChange}
      />
    </div>
  );
}
