"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { apiClient } from "@/app/api/request";
import {
  AgentConfigVersion,
  AgentConfig,
} from "@/app/bam/aime/namespaces/agent";
import { toast } from "sonner";
import { VersionDiff } from "../components/VersionDiff";

export default function VersionDiffPage() {
  const searchParams = useSearchParams();
  const configId = searchParams.get("config_id");
  const leftVersionId = searchParams.get("left_id");
  const rightVersionId = searchParams.get("right_id");
  
  const [leftVersion, setLeftVersion] = useState<AgentConfigVersion | undefined>();
  const [rightVersion, setRightVersion] = useState<AgentConfigVersion | undefined>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (leftVersionId && rightVersionId && configId) {
      fetchVersions();
    }
  }, [leftVersionId, rightVersionId, configId]);

  const fetchVersions = async () => {
    try {
      setLoading(true);

      // 获取左侧版本信息
      const leftResponse = await apiClient.GetAgentConfigVersion({
        agent_config_version_id: leftVersionId!,
      });
      setLeftVersion(leftResponse.agent_config_version);

      // 获取右侧版本信息
      const rightResponse = await apiClient.GetAgentConfigVersion({
        agent_config_version_id: rightVersionId!,
      });
      setRightVersion(rightResponse.agent_config_version);
    } catch {
      toast.error("获取版本信息失败");
    } finally {
      setLoading(false);
    }
  };

  if (!configId || !leftVersionId || !rightVersionId) {
    return <div>缺少必要的参数</div>;
  }

  return (
    <div className="container mx-auto py-6">
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        </div>
      ) : (
        <VersionDiff
          leftVersion={leftVersion}
          rightVersion={rightVersion}
        />
      )}
    </div>
  );
}