"use client";
import { Select, Form } from "@arco-design/web-react";
import { apiClient } from "@/app/api/request";
import { KnowledgesetVersionStatus } from "@/app/bam/aime/namespaces/knowledgeset";
import { useQuery } from "@tanstack/react-query";

interface KnowledgeSetVersionProps {
  knowledgeSetId: string;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  agentConfigType?: string;
}

export function KnowledgeSetVersion({
  knowledgeSetId,
  value,
  onChange,
  disabled = false,
}: KnowledgeSetVersionProps) {
  const { data: versions, isLoading: loading } = useQuery({
    queryKey: ["ListKnowledgesetVersionsOnline", knowledgeSetId],
    queryFn: async () => {
      const response = await apiClient.ListKnowledgesetVersions({
        knowledge_set_id: knowledgeSetId,
        page_num: 1,
        page_size: 100,
        status: KnowledgesetVersionStatus.KnowledgeVersionStatusOnline,
      });

      return response?.knowledge_set_versions || [];
    },
    staleTime: Infinity,
    enabled: Boolean(knowledgeSetId),
  });

  return (
    <Form.Item
      label="知识集版本"
      layout="inline"
      className="!w-auto"
      rules={[{ required: true, message: "请选择知识集版本" }]}
    >
      <Select
        placeholder="请选择知识集版本"
        value={value}
        onChange={onChange}
        loading={loading}
        disabled={disabled || !knowledgeSetId}
        className="flex !w-[300px]"
      >
        {versions?.map((version) => {
          return (
            <Select.Option key={version.id} value={version.id}>
              {version.version}-{version?.description}
            </Select.Option>
          );
        })}
      </Select>
    </Form.Item>
  );
}

export default KnowledgeSetVersion;
