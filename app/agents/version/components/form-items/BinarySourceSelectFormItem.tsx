"use client";

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Control } from "react-hook-form";
import {
  AgentConfigType,
  RuntimeConfigType,
} from "@/app/bam/aime/namespaces/agent";
import { useSearchParams } from "next/navigation";
import { XSCMSelect } from "@tod-m/materials";
import { getEnv } from "@/app/api/env";
import { Input } from "@arco-design/web-react";

interface BinarySourceSelectFormItemProps {
  control: Control<any>;
  name: string;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  runtimeType?: RuntimeConfigType;
}

/**
 * 二进制源选择表单项组件
 *
 * 使用Arco Design的Select组件实现可搜索的二进制源选择
 * 通过GetScmVersion API获取二进制源选项
 * 使用react-query实现API缓存，避免重复请求
 * 对Docker运行时类型有特殊的验证逻辑
 */
export function BinarySourceSelectFormItem({
  control,
  name,
  label = "二进制源",
  disabled,
  runtimeType,
}: BinarySourceSelectFormItemProps) {
  const searchParams = useSearchParams();
  const type = searchParams.get("type");

  // 格式化二进制源地址
  const formatBinarySource = (version: string): string => {
    // 这里可以根据实际需求调整格式化逻辑
    return `scm://devgpt/agentsphere/runtime?version=${version}`;
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            {runtimeType === RuntimeConfigType.RuntimeConfigTypeDocker ? (
              <Input {...field} />
            ) : (
              <XSCMSelect
                {...field}
                scmID={401256}
                showRefresh
                style={{ width: "100%" }}
                selectProps={{
                  showSearch: true,
                  allowClear: false,
                  disabled,
                }}
                domainID="online"
                onChange={(v) => {
                  field.onChange(formatBinarySource(v));
                }}
                disabledFunc={(v) => {
                  if (getEnv() !== "online") {
                    return false;
                  }
                  if (type === AgentConfigType.AgentConfigTypeBase) {
                    return !(v.type === "online");
                  } else if (type === AgentConfigType.AgentConfigTypeAbTest) {
                    return !["online", "test"].includes(v.type);
                  }
                  return false;
                }}
              />
            )}
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
