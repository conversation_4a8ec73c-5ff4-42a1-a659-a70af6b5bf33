"use client";

import { useState, useEffect, useCallback } from "react";
import { Select, Descriptions, Typography, Space } from "@arco-design/web-react";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Control } from "react-hook-form";
import { IcmVersion } from "@/app/bam/aime/namespaces/deploy";
import { debounce } from "lodash-es";
import { useSearchParams } from "next/navigation";
import { AgentConfigType } from "@/app/bam/aime/namespaces/agent";
import { useIcmVersionQuery } from "@/app/hooks/useIcmVersionQuery";
import dayjs from "dayjs";

const { Option } = Select;
const { Text } = Typography;

interface ImageSelectFormItemProps {
  control: Control<any>;
  name: string;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  imageType?: string;
}

/**
 * 镜像选择表单项组件
 * 
 * 使用Arco Design的Select组件实现可搜索的镜像选择
 * 通过useIcmVersionQuery hook获取镜像选项并缓存
 */
export function ImageSelectFormItem({
  control,
  name,
  label = "agent镜像",
  placeholder = "请选择或输入镜像版本搜索",
  disabled = false,
  imageType = "",
}: ImageSelectFormItemProps) {
  const searchParams = useSearchParams();
  const type = searchParams.get("type");
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState("");

  // 使用自定义hook获取数据
  const { data, isFetching, error } = useIcmVersionQuery({
    searchValue,
    type,
    imageType: imageType,
  });

  // 从查询结果中获取选项列表
  const options = data?.icm_versions || [];

  // 创建防抖的搜索函数
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchValue(value);
      setLoading(false); // 搜索开始后，由isFetching控制加载状态
    }, 500),
    []
  );

  // 同步isFetching状态到loading状态
  useEffect(() => {
    if (isFetching) {
      setLoading(true);
    } else {
      // 当请求完成时，将loading状态设置为false
      setLoading(false);
    }
  }, [isFetching]);

  // 处理错误状态
  useEffect(() => {
    if (error) {
      console.error("获取镜像列表失败:", error);
      // 错误发生时，重置加载状态
      setLoading(false);
    }
  }, [error]);

  // 在组件卸载时取消防抖函数的执行
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setLoading(true); // 立即设置加载状态，提升用户体验
    debouncedSearch(value);
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Select
              {...field}
              placeholder={placeholder}
              disabled={disabled}
              loading={loading}
              showSearch
              allowClear
              filterOption={false}
              onSearch={handleSearch}
              onChange={(value) => field.onChange(value)}
              style={{ width: "100%" }}
              renderFormat={((_option: string, value: string) => value) as any}
            >
              {options.map((option) => (
                <Option key={option.image_name} value={option.image_name}>
                  <div style={{ padding: '8px 0' }}>
                    {(() => {
                      // 创建数据数组
                      const data = [
                         {
                          label: '名称',
                          value: option.image_name || '-'
                        },
                        {
                          label: '创建人',
                          value: option.builder || '-'
                        },
                        {
                          label: '创建时间',
                          value: option.create_time ? dayjs(option.create_time).format('YYYY-MM-DD HH:mm:ss') : '-'
                        },
                        {
                          label: '版本',
                          value: option.image_version || '-'
                        },
                        {
                          label: '描述',
                          value: (
                            <div style={{ 
                              maxHeight: '60px', 
                              overflow: 'auto', 
                              wordBreak: 'break-word' 
                            }}>
                              {option.describe || '-'}
                            </div>
                          )
                        }
                      ];
                    
                      
                      return (
                        <Descriptions
                          // title={
                          //   <Text bold style={{ fontSize: '14px' }}>
                          //     {option.image_name}
                          //   </Text>
                          // }
                          colon=' :'
                          column={1}
                          labelStyle={{ textAlign: 'right' }}
                          style={{ width: '100%' }}
                          data={data}
                          // border
                        />
                      );
                    })()}
                  </div>
                </Option>
              ))}
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
