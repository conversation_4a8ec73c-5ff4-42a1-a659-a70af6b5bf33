"use client";

import { useState, useEffect } from "react";
import { Select, Form } from "@arco-design/web-react";
import { apiClient } from "@/app/api/request";
import { toast } from "sonner";
import { useQuery } from "@tanstack/react-query";

interface KnowledgeSetProps {
  type: string;
  value: string;
  onChange: (value: string) => void;
  onRemove?: () => void;
  disabled?: boolean;
}

export function KnowledgeSet({
  type,
  value,
  onChange,
  disabled = false,
}: KnowledgeSetProps) {
  const { data: knowledgeSets, isLoading: loading } = useQuery({
    queryKey: ["ListKnowledgesets", type],
    queryFn: async () => {
      const response = await apiClient.ListKnowledgesets({
        type: type,
        page_num: 1,
        page_size: 1000,
      });
      return response.knowledge_sets || [];
    },
    staleTime: Infinity,
     enabled: Boolean(type),
  });

  return (
    <Form.Item
      label="知识集"
      layout="inline"
      className="!w-auto"
      rules={[{ required: true, message: "请选择知识集" }]}
    >
      <Select
        placeholder="请选择知识集"
        value={value}
        onChange={onChange}
        loading={loading}
        disabled={disabled}
        style={{ width: 300 }}
      >
        {knowledgeSets?.map((set) => (
          <Select.Option key={set.id} value={set.id}>
            {set.name}
          </Select.Option>
        ))}
      </Select>
    </Form.Item>
  );
}

export default KnowledgeSet;
