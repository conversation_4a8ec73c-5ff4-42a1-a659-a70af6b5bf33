"use client";

import { useEffect, useState } from "react";
import { <PERSON>ton, Select, Card, Spin, Empty } from "@arco-design/web-react";
import { IconPlus, IconDelete } from "@arco-design/web-react/icon";
import { KnowledgesetMetadata } from "@/app/bam/aime/namespaces/agent";
import { apiClient } from "@/app/api/request";
import { toast } from "sonner";
import { useQuery } from "@tanstack/react-query";
import KnowledgeSet from "./KnowledgeSet";
import KnowledgeSetVersion from "./KnowledgeSetVersion";
import { cloneDeep } from "lodash-es";

interface KnowledgesetConfigProps {
  value?: KnowledgeSetTypeGroup[];
  onChange?: (value: KnowledgeSetTypeGroup[]) => void;
  disabled?: boolean;
  agentConfigType?: string;
}

// 知识集项定义
export interface KnowledgeSetItem {
  knowledge_set_id: string; // 知识集id
  knowledge_set_version_id: string; // 知识集版本id
}

// 内部使用的分组数据结构
export interface KnowledgeSetTypeGroup {
  type?: string; // 知识集类型
  knowledgeSets: KnowledgeSetItem[];
}

// 将扁平结构转换为分组结构
export const flatToGrouped = (
  flatData: KnowledgesetMetadata[] = []
): KnowledgeSetTypeGroup[] => {
  if (!flatData || !Array.isArray(flatData) || flatData.length === 0) {
    return [];
  }

  const groupMap: Record<string, KnowledgeSetTypeGroup> = {};
  flatData.forEach((item) => {
    if (!item) return;
    const type = item.knowledge_set_type;

    if (!groupMap[type]) {
      groupMap[type] = {
        type,
        knowledgeSets: [],
      };
    }
    // 只有当知识集ID存在时才添加
    if (item.knowledge_set_id) {
      groupMap[type].knowledgeSets.push({
        knowledge_set_id: item.knowledge_set_id,
        knowledge_set_version_id: item.knowledge_set_version_id || "",
      });
    } else if (groupMap[type].knowledgeSets.length === 0) {
      // 如果没有知识集ID但分组是空的，添加一个空项
      groupMap[type].knowledgeSets.push({
        knowledge_set_id: "",
        knowledge_set_version_id: "",
      });
    }
  });

  return Object.values(groupMap);
};

// 将分组结构转换为扁平结构
export const groupedToFlat = (groupedData: KnowledgeSetTypeGroup[] = []) => {
  if (!groupedData || !Array.isArray(groupedData) || groupedData.length === 0) {
    return [];
  }

  const flatData: any[] = [];

  groupedData.forEach((group) => {
    // 过滤掉无效的知识集项
    const validKnowledgeSets = group.knowledgeSets;

    if (validKnowledgeSets.length === 0) {
      // 如果没有有效的知识集项，不添加到扁平结构中
      return;
    }

    validKnowledgeSets.forEach((item) => {
      flatData.push({
        knowledge_set_type: group.type,
        knowledge_set_id: item.knowledge_set_id,
        knowledge_set_version_id: item.knowledge_set_version_id || "",
      });
    });
  });

  return flatData;
};

export function KnowledgesetConfig({
  value,
  onChange,
  disabled = false,
  agentConfigType,
}: KnowledgesetConfigProps) {
  // 分组数据状态
  const [typeGroups, setTypeGroups] = useState<KnowledgeSetTypeGroup[]>([]);

  // 获取知识集元数据配置
  const {
    data: metaData,
    isLoading: isLoadingMetadata,
    error: metadataError,
  } = useQuery({
    queryKey: ["knowledgeSets"],
    queryFn: async () => {
      try {
        return await apiClient.GetKnowledgesetMetadataConf();
      } catch (error) {
        throw error;
      }
    },
    retry: 2,
  });

  // 初始化时，将扁平结构转换为分组结构
  useEffect(() => {
    if (value) {
      console.log(value, "valuevaluevaluevalue");
      setTypeGroups(value);
    }
  }, [value]);

  // 类型变动
  const handleTypeChange = (type: string, groupIndex: number) => {
    if (!type) return;
    const newGroups = cloneDeep(typeGroups) || [];
    if (newGroups[groupIndex].type === type) {
      return;
    }
    newGroups[groupIndex] = {
      type,
      knowledgeSets: [
        {
          knowledge_set_id: "",
          knowledge_set_version_id: "",
        },
      ],
    };
    setTypeGroups(newGroups);
    onChange?.(newGroups);
  };

  // 删除知识集类型分组
  const handleRemoveTypeGroup = (index: number) => {
    const newGroups = [...typeGroups];
    newGroups.splice(index, 1);
    setTypeGroups(newGroups);
    onChange?.(newGroups);
  };

  // 在特定分组内添加知识集
  const handleAddKnowledgeSet = (groupIndex: number) => {
    const newGroups = [...typeGroups];
    newGroups[groupIndex].knowledgeSets.push({
      knowledge_set_id: "",
      knowledge_set_version_id: "",
    });
    setTypeGroups(newGroups);
    onChange?.(newGroups);
  };

  // 在特定分组内删除知识集
  const handleRemoveKnowledgeSet = (groupIndex: number, setIndex: number) => {
    const newGroups = [...typeGroups];
    newGroups[groupIndex].knowledgeSets.splice(setIndex, 1);

    // 如果分组内没有知识集了，则删除整个分组
    // if (newGroups[groupIndex].knowledgeSets.length === 0) {
    //   newGroups.splice(groupIndex, 1);
    // }

    setTypeGroups(newGroups);
    onChange?.(newGroups);
  };

  const handleAddGroup = () => {
    const targetTypeInfo = metaData?.knowledge_set_types?.find(
      (item) => !typeGroups.some((group) => group.type === item.value)
    );
    if (!targetTypeInfo) {
      return;
    }
    const newValue = cloneDeep(typeGroups) || [];
    newValue.push({
      type: targetTypeInfo.value,
      knowledgeSets: [
        {
          knowledge_set_id: "",
          knowledge_set_version_id: "",
        },
      ],
    });
    setTypeGroups(newValue);
    onChange?.(newValue);
  };

  // 处理知识集变更
  const handleKnowledgeSetChange = (
    value: string,
    groupIndex: number,
    setIndex: number
  ) => {
    const newGroups = [...typeGroups];
    newGroups[groupIndex].knowledgeSets[setIndex].knowledge_set_id = value;
    newGroups[groupIndex].knowledgeSets[setIndex].knowledge_set_version_id = ""; // 重置版本ID
    setTypeGroups(newGroups);
    onChange?.(newGroups);
  };

  // // 处理知识集版本变更
  const handleVersionChange = (
    value: string,
    groupIndex: number,
    setIndex: number
  ) => {
    const newGroups = [...typeGroups];
    newGroups[groupIndex].knowledgeSets[setIndex].knowledge_set_version_id =
      value;
    setTypeGroups(newGroups);
    onChange?.(newGroups);
  };

  // 渲染知识集类型选择器
  const renderTypeSelector = (groupIndex: number, type: string) => {
    return (
      <Select
        placeholder="请选择知识集类型"
        style={{ width: 200 }}
        defaultValue={type}
        onChange={(value) => handleTypeChange(value, groupIndex)}
        disabled={disabled || isLoadingMetadata}
        loading={isLoadingMetadata}
      >
        {metaData?.knowledge_set_types?.map((type) => (
          <Select.Option key={type.value} value={type.value}>
            {type.name}
          </Select.Option>
        ))}
      </Select>
    );
  };

  // 渲染知识集类型分组
  const renderTypeGroup = (
    group: KnowledgeSetTypeGroup,
    groupIndex: number
  ) => {
    const { type, knowledgeSets } = group;

    return (
      <Card
        key={`${type}-${groupIndex}`}
        title={renderTypeSelector(groupIndex, type as string)}
        extra={
          !disabled && (
            <>
              <Button
                size="small"
                className="!mr-[10px]"
                icon={<IconPlus />}
                onClick={() => handleAddKnowledgeSet(groupIndex)}
              ></Button>
              <Button
                size="small"
                type="text"
                icon={<IconDelete />}
                onClick={() => handleRemoveTypeGroup(groupIndex)}
              />
            </>
          )
        }
        style={{ marginBottom: 16 }}
      >
        <div className="space-y-4">
          {knowledgeSets?.map((item, setIndex) => (
            <div key={`${type}-${setIndex}`} className="mb-4 flex">
              {/* 使用独立的知识集组件 */}
              <KnowledgeSet
                type={type as string}
                value={item.knowledge_set_id}
                onChange={(value) =>
                  handleKnowledgeSetChange(value, groupIndex, setIndex)
                }
                disabled={disabled}
              />

              {/* 使用独立的知识集版本组件 */}
              <KnowledgeSetVersion
                knowledgeSetId={item.knowledge_set_id}
                value={item.knowledge_set_version_id}
                onChange={(value) =>
                  handleVersionChange(value, groupIndex, setIndex)
                }
                disabled={disabled}
                agentConfigType={agentConfigType}
              />

              {!disabled && (
                <Button
                  type="text"
                  icon={<IconDelete />}
                  onClick={() => handleRemoveKnowledgeSet(groupIndex, setIndex)}
                  className="mt-1"
                />
              )}
            </div>
          ))}
        </div>
      </Card>
    );
  };

  // 如果元数据加载中，显示加载状态
  if (isLoadingMetadata && !metaData) {
    return (
      <div className="flex justify-center items-center py-8">
        <Spin tip="加载知识集类型中..." />
      </div>
    );
  }

  // 如果元数据加载失败，显示错误状态
  if (metadataError && !metaData) {
    return (
      <div className="text-center py-8">
        <Empty
          description={
            <span className="text-red-500">
              加载知识集类型失败，请刷新页面重试
            </span>
          }
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="!mb-2 !mt-[-48px] flex flex-row-reverse">
        <Button
          type="primary"
          size="small"
          onClick={handleAddGroup}
          disabled={disabled}
        >
          添加知识集类型
        </Button>
      </div>

      {typeGroups.map((group, index) => renderTypeGroup(group, index))}

      {typeGroups.length === 0 && <Empty description="暂无知识集配置" />}
    </div>
  );
}

export default KnowledgesetConfig;
