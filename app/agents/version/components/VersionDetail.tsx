"use client";

import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Edit, Plus, Trash } from "lucide-react";
import {
  flatToGrouped,
  KnowledgesetConfig as KnowledgesetConfigComponent,
} from "./KnowledgeSetConfig";
import {
  AgentConfigVersion,
  AgentConfig,
  AgentConfigType,
  RuntimeConfig,
  RuntimeConfigType,
  PromptConfig,
  KnowledgesetConfig,
  AgentConfigStatus,
} from "@/app/bam/aime/namespaces/agent";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { JSONEditor } from "@/components/ui/json-editor";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useState, useEffect } from "react";
import { ImageSelectFormItem, BinarySourceSelectFormItem } from "./form-items";
import { apiClient } from "@/app/api/request";
import { toast } from "sonner";

interface VersionDetailProps {
  configId: string;
  version?: AgentConfigVersion;
  agentConfig?: AgentConfig;
}

interface Prompt {
  id: string;
  name: string;
  description: string;
}

interface PromptVersion {
  id: string;
  version: number;
  enabled: boolean;
}

const defaultRuntimeConfig: RuntimeConfig = {
  id: "",
  type: RuntimeConfigType.RuntimeConfigTypeStratoCube,
  inject_openai_token: true,
  psm: "",
  image: "hub.byted.org/base/iris_runtime_general:latest",
  bash_image: "",
  binary_source: "scm://devgpt/agentsphere/runtime?version=1.0.0.682",
  envs: {},
  port: 0,
  orchestration_config: {
    max_concurrency: 0,
    max_poolsize: 0,
    runtime_stop_timeout: "24h",
    runtime_delete_timeout: "72h",
  },
  runtime_resource_quota: {
    cpu: {
      requests: "1000m",
      limits: "4000m",
    },
    mem: {
      requests: "8192Mi",
      limits: "16384Mi",
    },
    persist_workspace: {
      requests: "64Gi",
      limits: "64Gi",
    },
    storage_class: "strato-csi-ceph-lq",
  },
};

const defaultPromptConfig: PromptConfig = {
  prompts: [],
};

const defaultKnowledgesetConfig = {
  knowledge_sets: [],
};

const formSchema = z.object({
  description: z.string(),
  enabled: z.boolean(),
  runtime_config: z
    .object({
      id: z.string().min(1, "Agent ID 不能为空"),
      type: z.nativeEnum(RuntimeConfigType),
      inject_openai_token: z.boolean(),
      psm: z.string(),
      image: z.string(),
      bash_image: z.string().optional(),
      binary_source: z.string().optional(),
      port: z.number(),
      orchestration_config: z.object({
        max_concurrency: z.number(),
        max_poolsize: z.number(),
        runtime_stop_timeout: z.string(),
        runtime_delete_timeout: z.string(),
      }),
      runtime_resource_quota: z.object({
        cpu: z.object({
          requests: z.string(),
          limits: z.string(),
        }),
        mem: z.object({
          requests: z.string(),
          limits: z.string(),
        }),
        persist_workspace: z.object({
          requests: z.string(),
          limits: z.string(),
        }),
        storage_class: z.string(),
      }),
    })
    .superRefine((data, ctx) => {
      if (
        data.type === RuntimeConfigType.RuntimeConfigTypeDocker &&
        data.binary_source
      ) {
        if (!data.binary_source.startsWith("file://")) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "docker provider 仅支持 file:// 协议的二进制源",
            path: ["binary_source"],
          });
        }
      }
    }),
  custom_config: z.string(),
  prompt_config: z.object({
    prompts: z.array(
      z.object({
        key: z.string(),
        id: z.string(),
        version: z.number(),
      })
    ),
  }),
  knowledge_set_config: z.object({
    knowledge_sets: z.array(
      z.object({
        type: z.string(),
        knowledgeSets: z.array(
          z.object({
            knowledge_set_id: z.string(), // 知识集id
            knowledge_set_version_id: z.string(), // 知识集版本id
          })
        ),
      })
    ),
  }),
});

export function VersionDetail({
  configId,
  version,
  agentConfig,
}: VersionDetailProps) {
  const router = useRouter();
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [promptVersionsMap, setPromptVersionsMap] = useState<
    Record<string, PromptVersion[]>
  >({});
  const [agentId, setAgentId] = useState<string>("");

  // 判断是否可编辑
  const isBaseAndEnabled =
    agentConfig?.type === AgentConfigType.AgentConfigTypeBase &&
    version?.enabled === true;
  const isReadOnly =
    isBaseAndEnabled ||
    (agentConfig?.type === AgentConfigType.AgentConfigTypeAbTest &&
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (version as any).status === AgentConfigStatus.AgentConfigStatusOnline);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      description: version?.description || "",
      enabled: version?.enabled ?? false,
      runtime_config: version?.runtime_config || defaultRuntimeConfig,
      custom_config: version?.custom_config || "{}",
      prompt_config: version?.prompt_config || defaultPromptConfig,
      knowledge_set_config: version?.knowledgeset_config
        ? {
            ...version.knowledgeset_config,
            knowledge_sets: flatToGrouped(
              version.knowledgeset_config.knowledge_sets || []
            ),
          }
        : defaultKnowledgesetConfig,
    },
  });

  // 获取 agent_id
  useEffect(() => {
    const fetchAgentId = async () => {
      try {
        if (agentConfig) {
          setAgentId(agentConfig.agent_id);
        } else {
          const response = await apiClient.GetAgentConfig({
            agent_config_id: configId,
          });
          setAgentId(response.agent_config.agent_id);
        }
      } catch {
        toast.error("获取 Agent ID 失败");
      }
    };

    if (configId) {
      fetchAgentId();
    }
  }, [configId, agentConfig]);

  // 获取 prompt 列表
  const fetchPrompts = async () => {
    if (!agentId) return;

    try {
      const response = await apiClient.ListPrompt({
        page_num: 1,
        page_size: 100,
        agent_id: agentId,
      });
      setPrompts(response.prompts);
    } catch {
      toast.error("获取提示词列表失败");
    }
  };

  // 获取 prompt 版本列表
  const fetchPromptVersions = async (promptId: string) => {
    try {
      const response = await apiClient.ListPromptVersion({
        prompt_id: promptId,
        page_num: 1,
        page_size: 100,
      });
      setPromptVersionsMap((prev) => ({
        ...prev,
        [promptId]: response.prompt_versions,
      }));
    } catch {
      toast.error("获取提示词版本列表失败");
    }
  };

  useEffect(() => {
    if (agentId) {
      fetchPrompts();
    }
  }, [agentId]);

  // 初始化时获取已有提示词的版本列表
  useEffect(() => {
    const existingPrompts = form.getValues("prompt_config.prompts");
    const fetchExistingVersions = async () => {
      for (const prompt of existingPrompts) {
        if (prompt.id) {
          await fetchPromptVersions(prompt.id);
        }
      }
    };

    if (version) {
      fetchExistingVersions();
    }
  }, [version]);

  const handleGoToEdit = () => {
    if (version) {
      router.push(
        `/agents/version/edit?id=${version.id}&config_id=${configId}`
      );
    }
  };

  return (
    <Form {...form}>
      <form className="space-y-6">
        <div className="flex items-center justify-between">
          <Button
            type="button"
            variant="ghost"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回
          </Button>
          {!isReadOnly && (
            <Button type="button" onClick={handleGoToEdit}>
              <Edit className="h-4 w-4 mr-2" />
              编辑
            </Button>
          )}
        </div>

        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>描述</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="请输入版本描述"
                      disabled={true}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="enabled"
              render={({ field }) => (
                <FormItem className="flex items-center gap-2">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={true}
                    />
                  </FormControl>
                  <FormLabel className="!mt-0">
                    {field.value ? "已启用" : "已禁用"}
                  </FormLabel>
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>运行时配置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="runtime_config.id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Agent ID</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="请输入 Agent ID"
                      disabled={true}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>类型</FormLabel>
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                    disabled={true}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem
                        value={RuntimeConfigType.RuntimeConfigTypeDocker}
                      >
                        Docker
                      </SelectItem>
                      <SelectItem
                        value={RuntimeConfigType.RuntimeConfigTypeStratoCube}
                      >
                        StratoCube
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.inject_openai_token"
              render={({ field }) => (
                <FormItem className="flex items-center gap-2">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={true}
                    />
                  </FormControl>
                  <FormLabel className="!mt-0">注入 OpenAI Token</FormLabel>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.psm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>PSM</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入PSM" disabled={true} />
                  </FormControl>
                </FormItem>
              )}
            />

            <ImageSelectFormItem
              control={form.control}
              name="runtime_config.image"
              disabled={true}
            />

            <ImageSelectFormItem
              control={form.control}
              name="runtime_config.bash_image"
              label="bash通用镜像"
              imageType="bash"
              disabled={true}
            />

            <BinarySourceSelectFormItem
              control={form.control}
              name="runtime_config.binary_source"
              runtimeType={form.watch("runtime_config.type")}
              disabled={true}
            />

            <FormField
              control={form.control}
              name="runtime_config.port"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>端口</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                      placeholder="请输入端口号"
                      disabled={true}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormItem>
              <FormLabel>运行时资源配额</FormLabel>
              <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
                <div>
                  <Label>CPU 请求</Label>
                  <Input
                    value={form.getValues(
                      "runtime_config.runtime_resource_quota.cpu.requests"
                    )}
                    disabled={true}
                  />
                </div>
                <div>
                  <Label>CPU 限制</Label>
                  <Input
                    value={form.getValues(
                      "runtime_config.runtime_resource_quota.cpu.limits"
                    )}
                    disabled={true}
                  />
                </div>
                <div>
                  <Label>内存请求</Label>
                  <Input
                    value={form.getValues(
                      "runtime_config.runtime_resource_quota.mem.requests"
                    )}
                    disabled={true}
                  />
                </div>
                <div>
                  <Label>内存限制</Label>
                  <Input
                    value={form.getValues(
                      "runtime_config.runtime_resource_quota.mem.limits"
                    )}
                    disabled={true}
                  />
                </div>
                <div>
                  <Label>工作空间请求</Label>
                  <Input
                    value={form.getValues(
                      "runtime_config.runtime_resource_quota.persist_workspace.requests"
                    )}
                    disabled={true}
                  />
                </div>
                <div>
                  <Label>工作空间限制</Label>
                  <Input
                    value={form.getValues(
                      "runtime_config.runtime_resource_quota.persist_workspace.limits"
                    )}
                    disabled={true}
                  />
                </div>
                <div>
                  <Label>存储类</Label>
                  <Input
                    value={form.getValues(
                      "runtime_config.runtime_resource_quota.storage_class"
                    )}
                    disabled={true}
                  />
                </div>
              </div>
            </FormItem>

            <FormItem>
              <FormLabel>编排配置</FormLabel>
              <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
                <div>
                  <Label>最大并发数</Label>
                  <Input
                    type="number"
                    value={form.getValues(
                      "runtime_config.orchestration_config.max_concurrency"
                    )}
                    disabled={true}
                  />
                </div>
                <div>
                  <Label>最大池大小</Label>
                  <Input
                    type="number"
                    value={form.getValues(
                      "runtime_config.orchestration_config.max_poolsize"
                    )}
                    disabled={true}
                  />
                </div>
                <div>
                  <Label>运行时停止超时</Label>
                  <Input
                    value={form.getValues(
                      "runtime_config.orchestration_config.runtime_stop_timeout"
                    )}
                    disabled={true}
                  />
                </div>
                <div>
                  <Label>运行时删除超时</Label>
                  <Input
                    value={form.getValues(
                      "runtime_config.orchestration_config.runtime_delete_timeout"
                    )}
                    disabled={true}
                  />
                </div>
              </div>
            </FormItem>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>自定义配置</CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="custom_config"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <JSONEditor
                      value={field.value}
                      onChange={() => {}}
                      height="300px"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>提示词配置</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {form.watch("prompt_config.prompts").map((prompt, index) => (
                <div
                  key={index}
                  className="grid gap-4 grid-cols-1 md:grid-cols-2 items-end"
                >
                  <FormItem>
                    <FormLabel>提示词 Key</FormLabel>
                    <FormControl>
                      <Input value={prompt.key || ""} disabled={true} />
                    </FormControl>
                  </FormItem>

                  <FormItem>
                    <FormLabel>提示词</FormLabel>
                    <FormControl>
                      <Select value={prompt.id || ""} disabled={true}>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择提示词" />
                        </SelectTrigger>
                        <SelectContent>
                          {prompts.map((p) => (
                            <SelectItem key={p.id} value={p.id}>
                              {p.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                  </FormItem>

                  {prompt.id && (
                    <FormItem>
                      <FormLabel>版本</FormLabel>
                      <FormControl>
                        <Select
                          value={String(prompt.version) || ""}
                          disabled={true}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="请选择版本" />
                          </SelectTrigger>
                          <SelectContent>
                            {promptVersionsMap[prompt.id]?.map((v) => (
                              <SelectItem key={v.id} value={String(v.version)}>
                                v{v.version} {v.enabled ? "(已启用)" : ""}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                    </FormItem>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>知识集配置</CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="knowledge_set_config.knowledge_sets"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <KnowledgesetConfigComponent
                      value={field.value}
                      onChange={field.onChange}
                      disabled={true}
                      agentConfigType={agentConfig?.type}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>
      </form>
    </Form>
  );
}
