"use client";

import { useState, useEffect } from "react";
import { ImageSelectFormItem, BinarySourceSelectFormItem } from "./form-items";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { ArrowLeft } from "lucide-react";
import { apiClient } from "@/app/api/request";
import { toast } from "sonner";
import {
  AgentConfigVersion,
  RuntimeConfig,
  RuntimeConfigType,
  PromptConfig,
} from "@/app/bam/aime/namespaces/agent";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { JSONEditor } from "@/components/ui/json-editor";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  KnowledgesetConfig as KnowledgesetConfigComponent,
  flatToGrouped,
  groupedToFlat,
} from "./KnowledgeSetConfig";

interface VersionFormProps {
  configId: string;
  version?: AgentConfigVersion;
}

interface Prompt {
  id: string;
  name: string;
  description: string;
}

interface PromptVersion {
  id: string;
  version: number;
  enabled: boolean;
}

const defaultRuntimeConfig: RuntimeConfig = {
  id: "",
  type: RuntimeConfigType.RuntimeConfigTypeStratoCube,
  inject_openai_token: true,
  psm: "",
  image: "hub.byted.org/base/iris_runtime_general:latest",
  bash_image:"",
  binary_source: "scm://devgpt/agentsphere/runtime?version=1.0.0.682",
  envs: {},
  port: 0,
  orchestration_config: {
    max_concurrency: 0,
    max_poolsize: 0,
    runtime_stop_timeout: "24h",
    runtime_delete_timeout: "72h",
  },
  runtime_resource_quota: {
    cpu: {
      requests: "1000m",
      limits: "4000m",
    },
    mem: {
      requests: "8192Mi",
      limits: "16384Mi",
    },
    persist_workspace: {
      requests: "64Gi",
      limits: "64Gi",
    },
    storage_class: "strato-csi-ceph-lq",
  },
};

const defaultPromptConfig: PromptConfig = {
  prompts: [],
};
const defaultKnowledgesetConfig = {
  knowledge_sets: [],
};

const formSchema = z.object({
  description: z.string(),
  enabled: z.boolean(),
  runtime_config: z
    .object({
      id: z.string().min(1, "Agent ID 不能为空"),
      type: z.nativeEnum(RuntimeConfigType),
      inject_openai_token: z.boolean(),
      psm: z.string(),
      image: z.string(),
      bash_image: z.string().optional(),
      binary_source: z.string().optional(),
      port: z.number(),
      orchestration_config: z.object({
        max_concurrency: z.number(),
        max_poolsize: z.number(),
        runtime_stop_timeout: z.string(),
        runtime_delete_timeout: z.string(),
      }),
      runtime_resource_quota: z.object({
        cpu: z.object({
          requests: z.string(),
          limits: z.string(),
        }),
        mem: z.object({
          requests: z.string(),
          limits: z.string(),
        }),
        persist_workspace: z.object({
          requests: z.string(),
          limits: z.string(),
        }),
        storage_class: z.string(),
      }),
    })
    .superRefine((data, ctx) => {
      if (
        data.type === RuntimeConfigType.RuntimeConfigTypeDocker &&
        data.binary_source
      ) {
        if (!data.binary_source.startsWith("file://")) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "docker provider 仅支持 file:// 协议的二进制源",
            path: ["binary_source"],
          });
        }
      }
    }),
  custom_config: z.string(),
  prompt_config: z.object({
    prompts: z.array(
      z.object({
        key: z.string(),
        id: z.string(),
        version: z.number(),
      })
    ),
  }),
  knowledge_set_config: z.object({
    knowledge_sets: z.array(
      z.object({
        type: z.string(),
        knowledgeSets: z.array(
          z.object({
            knowledge_set_id: z.string(), // 知识集id
            knowledge_set_version_id: z.string(), // 知识集版本id
          })
        ),
      })
    ),
  }),
});

export function VersionForm({ configId, version }: VersionFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [promptVersionsMap, setPromptVersionsMap] = useState<
    Record<string, PromptVersion[]>
  >({});
  const [agentId, setAgentId] = useState<string>("");

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      description: version?.description || "",
      enabled: version?.enabled ?? false,
      runtime_config: version?.runtime_config || defaultRuntimeConfig,
      custom_config: version?.custom_config || "{}",
      prompt_config: version?.prompt_config || defaultPromptConfig,
      knowledge_set_config: version?.knowledgeset_config
        ? {
            ...version.knowledgeset_config,
            knowledge_sets: flatToGrouped(
              version.knowledgeset_config.knowledge_sets || []
            ),
          }
        : defaultKnowledgesetConfig,
    },
  });

  // 获取 agent_id
  useEffect(() => {
    const fetchAgentId = async () => {
      try {
        const response = await apiClient.GetAgentConfig({
          agent_config_id: configId,
        });
        setAgentId(response.agent_config.agent_id);
      } catch {
        toast.error("获取 Agent ID 失败");
      }
    };

    if (configId) {
      fetchAgentId();
    }
  }, [configId]);

  // 获取 prompt 列表
  const fetchPrompts = async () => {
    if (!agentId) return;

    try {
      const response = await apiClient.ListPrompt({
        page_num: 1,
        page_size: 100,
        agent_id: agentId,
      });
      setPrompts(response.prompts);
    } catch {
      toast.error("获取提示词列表失败");
    }
  };

  // 获取 prompt 版本列表
  const fetchPromptVersions = async (promptId: string) => {
    try {
      const response = await apiClient.ListPromptVersion({
        prompt_id: promptId,
        page_num: 1,
        page_size: 100,
      });
      setPromptVersionsMap((prev) => ({
        ...prev,
        [promptId]: response.prompt_versions,
      }));
    } catch {
      toast.error("获取提示词版本列表失败");
    }
  };

  useEffect(() => {
    if (agentId) {
      fetchPrompts();
    }
  }, [agentId]);

  // 初始化时获取已有提示词的版本列表
  useEffect(() => {
    const existingPrompts = form.getValues("prompt_config.prompts");
    const fetchExistingVersions = async () => {
      for (const prompt of existingPrompts) {
        if (prompt.id) {
          await fetchPromptVersions(prompt.id);
        }
      }
    };

    if (version) {
      fetchExistingVersions();
    }
  }, [version]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (loading) return;
    setLoading(true);

    const knowledge_sets =
      groupedToFlat(values?.knowledge_set_config?.knowledge_sets) || [];

    let isValid = true;
    knowledge_sets?.forEach((item) => {
      if (
        !item?.knowledge_set_id ||
        !item?.knowledge_set_type ||
        !item?.knowledge_set_version_id
      ) {
        isValid = false;
      }
    });

    if (!isValid) {
      toast.error("知识集配置错误，有未选择完成的配置集！");
      setLoading(false);
      return;
    }

    try {
      JSON.parse(values.custom_config);
    } catch {
      toast.error("自定义配置校验失败");
      setLoading(false);
      return;
    }

    const uniqueArr = [];
    const seen = new Set();
    for (const item of knowledge_sets) {
      if (!seen.has(item.knowledge_set_version_id)) {
        seen.add(item.knowledge_set_version_id);
        uniqueArr.push(item);
      }
    }

    if (uniqueArr.length !== knowledge_sets.length) {
      toast.error("知识集配置错误，有重复的配置集！");
      setLoading(false);
      return;
    }

    try {
      if (version) {
        await apiClient.UpdateAgentConfigVersion({
          agent_config_version_id: version.id,
          description: values.description,
          enabled: values.enabled,
          runtime_config: values.runtime_config,
          custom_config: values.custom_config,
          prompt_config: values.prompt_config,
          knowledge_set_config: {
            ...values.knowledge_set_config,
            knowledge_sets,
          },
          updated_at: version?.updated_at,
        });
        toast.success("版本更新成功");
      } else {
        await apiClient.CreateAgentConfigVersion({
          agent_config_id: configId,
          description: values.description,
          enabled: values.enabled,
          runtime_config: values.runtime_config,
          custom_config: values.custom_config,
          prompt_config: values.prompt_config,
          knowledge_set_config: {
            ...values.knowledge_set_config,
            knowledge_sets,
          },
        });
        toast.success("版本创建成功");
      }
      router.back();
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : "未知错误";
      toast.error(
        version
          ? `版本更新失败: ${errorMessage}`
          : `版本创建失败: ${errorMessage}`
      );
    } finally {
      setLoading(false);
    }
  };

  const addPrompt = () => {
    const currentPrompts = form.getValues("prompt_config.prompts");
    form.setValue("prompt_config.prompts", [
      ...currentPrompts,
      { key: "", id: "", version: 1 },
    ]);
  };

  const removePrompt = (index: number) => {
    const currentPrompts = form.getValues("prompt_config.prompts");
    currentPrompts.splice(index, 1);
    form.setValue("prompt_config.prompts", [...currentPrompts]);
  };

  const handlePromptSelect = async (promptId: string, index: number) => {
    const currentPrompts = form.getValues("prompt_config.prompts");
    currentPrompts[index].id = promptId;
    form.setValue("prompt_config.prompts", [...currentPrompts]);

    if (!promptVersionsMap[promptId]) {
      await fetchPromptVersions(promptId);
    }
  };

  const handleVersionSelect = (version: number, index: number) => {
    const currentPrompts = form.getValues("prompt_config.prompts");
    currentPrompts[index].version = version;
    form.setValue("prompt_config.prompts", [...currentPrompts]);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="flex items-center justify-between">
          <Button
            type="button"
            variant="ghost"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? "保存中..." : version ? "更新" : "创建"}
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>描述</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入版本描述" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="enabled"
              render={({ field }) => (
                <FormItem className="flex items-center gap-2">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel className="!mt-0">
                    {field.value ? "已启用" : "已禁用"}
                  </FormLabel>
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>运行时配置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="runtime_config.id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Agent ID</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入 Agent ID" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>类型</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem
                        value={RuntimeConfigType.RuntimeConfigTypeDocker}
                      >
                        Docker
                      </SelectItem>
                      <SelectItem
                        value={RuntimeConfigType.RuntimeConfigTypeStratoCube}
                      >
                        StratoCube
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.inject_openai_token"
              render={({ field }) => (
                <FormItem className="flex items-center gap-2">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel className="!mt-0">注入 OpenAI Token</FormLabel>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.psm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>PSM</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入PSM" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <ImageSelectFormItem
              control={form.control}
              name="runtime_config.image"
            />

            <ImageSelectFormItem
              control={form.control}
              name="runtime_config.bash_image"
              label="bash通用镜像"
              imageType="bash"
            />

            <BinarySourceSelectFormItem
              control={form.control}
              name="runtime_config.binary_source"
              runtimeType={form.watch("runtime_config.type")}
            />

            <FormField
              control={form.control}
              name="runtime_config.port"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>端口</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                      placeholder="请输入端口号"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.runtime_resource_quota.cpu.requests"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>CPU 请求</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入CPU请求量" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.runtime_resource_quota.cpu.limits"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>CPU 限制</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入CPU限制量" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.runtime_resource_quota.mem.requests"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>内存 请求</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入内存请求量" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.runtime_resource_quota.mem.limits"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>内存 限制</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入内存限制量" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.runtime_resource_quota.persist_workspace.requests"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>存储 请求</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入存储请求量" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.runtime_resource_quota.persist_workspace.limits"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>存储 限制</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入存储限制量" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.runtime_resource_quota.storage_class"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>存储类型</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入存储类型" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.orchestration_config.max_concurrency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>最大并发数</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                      placeholder="请输入最大并发数"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.orchestration_config.max_poolsize"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>最大池大小</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                      placeholder="请输入最大池大小"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.orchestration_config.runtime_stop_timeout"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>运行时停止超时</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入运行时停止超时" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="runtime_config.orchestration_config.runtime_delete_timeout"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>运行时删除超时</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="请输入运行时删除超时" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>自定义配置</CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="custom_config"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>配置内容</FormLabel>
                  <FormControl>
                    <JSONEditor
                      value={field.value}
                      onChange={field.onChange}
                      height="200px"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>提示词配置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              {form.watch("prompt_config.prompts").map((prompt, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>提示词 {index + 1}</Label>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removePrompt(index)}
                    >
                      删除
                    </Button>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name={`prompt_config.prompts.${index}.key`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Key</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="请输入 key" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormItem>
                      <FormLabel>提示词</FormLabel>
                      <Select
                        value={prompt.id}
                        onValueChange={(value) =>
                          handlePromptSelect(value, index)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择提示词" />
                        </SelectTrigger>
                        <SelectContent>
                          {prompts.map((p) => (
                            <SelectItem key={p.id} value={p.id}>
                              {p.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                    <FormItem>
                      <FormLabel>版本</FormLabel>
                      <Select
                        value={String(prompt.version)}
                        onValueChange={(value) =>
                          handleVersionSelect(Number(value), index)
                        }
                        disabled={!prompt.id}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择版本" />
                        </SelectTrigger>
                        <SelectContent>
                          {promptVersionsMap[prompt.id]?.map((v) => (
                            <SelectItem key={v.id} value={String(v.version)}>
                              版本 {v.version}
                              {v.enabled ? " (已启用)" : ""}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  </div>
                </div>
              ))}
              <Button type="button" variant="outline" onClick={addPrompt}>
                添加提示词
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>知识集配置</CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="knowledge_set_config.knowledge_sets"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <KnowledgesetConfigComponent
                      value={field.value}
                      onChange={field.onChange}
                      // disabled={true}
                      // agentConfigType={agentConfig?.type}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>
      </form>
    </Form>
  );
}
