"use client";

import React, { useMemo } from "react";
import { useRouter } from "next/navigation";
import ReactDiffViewer from "react-diff-viewer";
import { AgentConfigVersion } from "@/app/bam/aime/namespaces/agent";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface VersionDiffProps {
  leftVersion?: AgentConfigVersion;
  rightVersion?: AgentConfigVersion;
  showBack?: boolean;
}

// 自定义diff显示组件
const DiffViewer = ({
  oldValue,
  newValue,
}: {
  oldValue: string;
  newValue: string;
}) => {
  // 将JSON字符串转换为对象进行比较
  const oldObj = useMemo(() => {
    try {
      return JSON.parse(oldValue);
    } catch (e) {
      return oldValue;
    }
  }, [oldValue]);

  const newObj = useMemo(() => {
    try {
      return JSON.parse(newValue);
    } catch (e) {
      return newValue;
    }
  }, [newValue]);

  // 比较两个对象，找出差异
  const isDifferent = JSON.stringify(oldObj) !== JSON.stringify(newObj);

  return (
    <div className="w-full overflow-auto">
      <ReactDiffViewer
        oldValue={oldValue}
        newValue={newValue}
      ></ReactDiffViewer>
    </div>
  );
};

export function VersionDiff({
  leftVersion,
  rightVersion,
  showBack = true,
}: VersionDiffProps) {
  const router = useRouter();

  // 处理基本信息的差异
  const basicInfoDiff = useMemo(() => {
    if (!leftVersion || !rightVersion) return null;

    const leftInfo = {
      description: leftVersion.description,
      enabled: leftVersion.enabled,
      status: leftVersion.status,
    };

    const rightInfo = {
      description: rightVersion.description,
      enabled: rightVersion.enabled,
      status: rightVersion.status,
    };

    return {
      left: JSON.stringify(leftInfo, null, 2),
      right: JSON.stringify(rightInfo, null, 2),
    };
  }, [leftVersion, rightVersion]);

  // 处理运行时配置的差异
  const runtimeConfigDiff = useMemo(() => {
    if (!leftVersion || !rightVersion) return null;

    return {
      left: JSON.stringify(leftVersion.runtime_config, null, 2),
      right: JSON.stringify(rightVersion.runtime_config, null, 2),
    };
  }, [leftVersion, rightVersion]);

  // 处理自定义配置的差异
  const customConfigDiff = useMemo(() => {
    if (!leftVersion || !rightVersion) return null;

    // 尝试解析JSON字符串，以便格式化
    let leftConfig = leftVersion.custom_config;
    let rightConfig = rightVersion.custom_config;

    try {
      const leftObj = JSON.parse(leftVersion.custom_config);
      leftConfig = JSON.stringify(leftObj, null, 2);
    } catch (e) {
      // 如果解析失败，使用原始字符串
    }

    try {
      const rightObj = JSON.parse(rightVersion.custom_config);
      rightConfig = JSON.stringify(rightObj, null, 2);
    } catch (e) {
      // 如果解析失败，使用原始字符串
    }

    return {
      left: leftConfig,
      right: rightConfig,
    };
  }, [leftVersion, rightVersion]);

  // 处理提示词配置的差异
  const promptConfigDiff = useMemo(() => {
    if (!leftVersion || !rightVersion) return null;

    return {
      left: JSON.stringify(leftVersion.prompt_config, null, 2),
      right: JSON.stringify(rightVersion.prompt_config, null, 2),
    };
  }, [leftVersion, rightVersion]);

    const knowledgeset_configDiff = useMemo(() => {
    if (!leftVersion || !rightVersion) return null;

    return {
      left: JSON.stringify(leftVersion.knowledgeset_config, null, 2),
      right: JSON.stringify(rightVersion.knowledgeset_config, null, 2),
    };
  }, [leftVersion, rightVersion]);

  if (!leftVersion || !rightVersion) {
    return <div>加载中...</div>;
  }

  return (
    <div className="space-y-6">
      {showBack ? (
        <div className="flex items-center justify-between">
          <Button
            type="button"
            variant="ghost"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回
          </Button>
        </div>
      ) : null}

      <div className="grid grid-cols-2 gap-4">
        <div className="text-center">
          <h3 className="text-lg font-medium">
            v{leftVersion.version} ({leftVersion.status})
          </h3>
          <p className="text-sm text-gray-500">创建者: {leftVersion.creator}</p>
          <Badge
            variant="outline"
            className={`${
              leftVersion.enabled ? "bg-green-50 text-green-700" : "bg-gray-50"
            } font-normal mt-1`}
          >
            {leftVersion.enabled ? "已启用" : "已禁用"}
          </Badge>
        </div>
        <div className="text-center">
          <h3 className="text-lg font-medium">
            v{rightVersion.version} ({rightVersion.status})
          </h3>
          <p className="text-sm text-gray-500">
            创建者: {rightVersion.creator}
          </p>
          <Badge
            variant="outline"
            className={`${
              rightVersion.enabled ? "bg-green-50 text-green-700" : "bg-gray-50"
            } font-normal mt-1`}
          >
            {rightVersion.enabled ? "已启用" : "已禁用"}
          </Badge>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>基本信息</CardTitle>
        </CardHeader>
        <CardContent>
          {basicInfoDiff && (
            <DiffViewer
              oldValue={basicInfoDiff.left}
              newValue={basicInfoDiff.right}
            />
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>运行时配置</CardTitle>
        </CardHeader>
        <CardContent>
          {runtimeConfigDiff && (
            <DiffViewer
              oldValue={runtimeConfigDiff.left}
              newValue={runtimeConfigDiff.right}
            />
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>自定义配置</CardTitle>
        </CardHeader>
        <CardContent>
          {customConfigDiff && (
            <DiffViewer
              oldValue={customConfigDiff.left}
              newValue={customConfigDiff.right}
            />
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>提示词配置</CardTitle>
        </CardHeader>
        <CardContent>
          {promptConfigDiff && (
            <DiffViewer
              oldValue={promptConfigDiff.left}
              newValue={promptConfigDiff.right}
            />
          )}
        </CardContent>
      </Card>

       <Card>
        <CardHeader>
          <CardTitle>知识集</CardTitle>
        </CardHeader>
        <CardContent>
          {knowledgeset_configDiff && (
            <DiffViewer
              oldValue={knowledgeset_configDiff.left}
              newValue={knowledgeset_configDiff.right}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
