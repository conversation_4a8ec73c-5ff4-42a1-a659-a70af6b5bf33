"use client";
import { Form, Input, Radio, Button } from "@arco-design/web-react";
import { XConfigProvider, XInputTagRemoteEmployee } from "@tod-m/materials";
import { apiClient } from "@/app/api/request";
import { toast } from "sonner";
import { useEffect } from "react";
import { useQuery } from "@tanstack/react-query";

const RadioGroup = Radio.Group;
const FormItem = Form.Item;

interface PublishFormProps {
  agentConfigVersionId: string;
  formData?: {
    reviewer: string;
    enable_ab: boolean;
    ab_comment: string;
  };
  onPrev: () => void;
  onNext: ({
    workflow_id,
    deploy_id,
  }: {
    workflow_id: string | number;
    deploy_id: string;
  }) => void;
  disabled: boolean;
}

export function PublishForm({
  agentConfigVersionId,
  onPrev,
  onNext,
  disabled,
  formData,
}: PublishFormProps) {
  const [form] = Form.useForm();

  useEffect(() => {
    if (formData) {
      form.setFieldsValue({
        reviewer: formData.reviewer,
        is_enable_ab: formData.enable_ab,
        ab_comment: formData.ab_comment,
      });
    } else {
      form.setFieldsValue({
        is_enable_ab: true,
      });
    }
  }, [form, formData]);

  const { data: reviewerList } = useQuery({
    queryKey: ["GetDeployReviewUser"],
    queryFn: () => apiClient.GetDeployReviewUser(),
    staleTime: 1000 * 60 * 5,
  });

  const handleSubmit = async () => {
    const values = await form.validate();
    try {
      const res = await apiClient.CreateDeploy({
        agent_config_version_id: agentConfigVersionId,
        reviewer: values.reviewer,
        is_enable_ab: values.is_enable_ab,
        ab_comment: values.ab_comment,
      });
      onNext(res);
    } catch(error) {
      toast.error((error as Error)?.message);
    }
  };

  return (
    <>
      <Form
        form={form}
        layout="vertical"
        autoComplete="off"
        disabled={disabled}
      >
        <XConfigProvider env="online">
          <FormItem
            label="审核人"
            field="reviewer"
            rules={[{ required: true, message: "请输入审核人" }]}
          >
           {
            reviewerList?.users && <XInputTagRemoteEmployee
              filter={(user) => {
                return !!reviewerList?.users?.includes(user.username);
              }}
              usernames={reviewerList?.users}
            />
           } 
          </FormItem>
        </XConfigProvider>

        {/* <FormItem
          label="是否开启 A/B"
          field="is_enable_ab"
          rules={[{ required: true, message: "请选择是否开启 A/B" }]}
        >
          <RadioGroup>
            <Radio value={true}>是</Radio>
            <Radio value={false}>否</Radio>
          </RadioGroup>
        </FormItem>

        <Form.Item shouldUpdate noStyle>
          {(values) => {
            return values.is_enable_ab ? (
              <FormItem
                label="Libra 链接"
                field="ab_comment"
                rules={[
                  {
                    required: true,
                    message: "开启 A/B 时，请输入 Libra 链接",
                  },
                ]}
              >
                <Input placeholder="请输入 Libra 链接" />
              </FormItem>
            ) : (
              <FormItem
                label="未开启 A/B 实验的原因"
                field="ab_comment"
                rules={[
                  {
                    required: true,
                    message: "未开启 A/B 时，请输入原因",
                  },
                ]}
              >
                <Input.TextArea placeholder="请输入未开启 A/B 实验的原因" />
              </FormItem>
            );
          }}
        </Form.Item> */}
      </Form>

      <div className="mt-6 flex justify-between">
        <Button onClick={onPrev} disabled={disabled}>
          上一步
        </Button>
        <Button type="primary" onClick={handleSubmit} disabled={disabled}>
          提交
        </Button>
      </div>
    </>
  );
}
