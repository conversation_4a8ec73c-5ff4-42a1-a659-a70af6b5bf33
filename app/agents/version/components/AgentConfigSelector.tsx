import { useForm } from "react-hook-form";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { Select } from "@arco-design/web-react";
import { useInfiniteAgents } from "@/app/hooks/useInfiniteAgents";
import {
  useInfiniteAgentConfigs,
  useAllAgentConfigs,
} from "@/app/hooks/useInfiniteAgentConfigs";
import { useDebounce } from "@/app/hooks/useDebounce";
import { useEffect, useState, useMemo, useCallback } from "react";
import { Agent, AgentConfig } from "@/app/bam/aime/namespaces/agent";
import { Badge } from "@/components/ui/badge";
import { formatType } from "../../utils";

const { Option } = Select;

interface AgentConfigSelectorProps {
  selectedAgent?: Agent;
  selectedAgentConfig?: AgentConfig;
  onAgentChange: (agentId: string) => void;
  onConfigChange: (configId: string) => void;
}

export function AgentConfigSelector({
  selectedAgent,
  selectedAgentConfig,
  onAgentChange,
  onConfigChange,
}: AgentConfigSelectorProps) {
  const filterAgentConfigId = selectedAgentConfig?.id;
  const selectedAgentId = selectedAgent?.id;
  // 搜索状态
  const [agentSearchTerm, setAgentSearchTerm] = useState("");
  const [configSearchTerm, setConfigSearchTerm] = useState("");

  // 防抖搜索词
  const debouncedAgentSearch = useDebounce(agentSearchTerm, 300);
  const debouncedConfigSearch = useDebounce(configSearchTerm, 300);

  const filterForm = useForm({
    defaultValues: {
      agent_id: "",
      config_id: "",
    },
  });

  // 使用无限加载的Agents Hook
  const {
    agents: infiniteAgents,
    isLoading: agentsLoading,
    fetchNextPage: fetchNextAgentsPage,
    hasNextPage: hasNextAgentsPage,
    isFetchingNextPage: isFetchingNextAgentsPage,
  } = useInfiniteAgents({
    searchTerm: debouncedAgentSearch,
    pageSize: 20,
  });

  // 获取所有 Agent Configs（用于自动关联）
  const { data: allAgentConfigs = [] } = useAllAgentConfigs();

  // 使用无限加载的特定Agent配置Hook
  const {
    agentConfigs: infiniteAgentConfigs,
    isLoading: configsLoading,
    fetchNextPage: fetchNextConfigsPage,
    hasNextPage: hasNextConfigsPage,
    isFetchingNextPage: isFetchingNextConfigsPage,
  } = useInfiniteAgentConfigs({
    agentId: selectedAgentId,
    searchTerm: debouncedConfigSearch,
    pageSize: 20,
    enabled: !!selectedAgentId,
  });

  // 当有 config_id 时，自动设置对应的 agent_id
  useEffect(() => {
    if (filterAgentConfigId && allAgentConfigs.length > 0) {
      const config = allAgentConfigs.find(
        (c: AgentConfig) => c.id === filterAgentConfigId
      );
      if (config && config.agent_id !== selectedAgentId) {
        onAgentChange(config.agent_id);
      }
    }
  }, [filterAgentConfigId, allAgentConfigs, selectedAgentId, onAgentChange]);

  // Agent选择处理
  const handleAgentChange = useCallback(
    (value: string) => {
      onAgentChange(value);
      onConfigChange("");
      setConfigSearchTerm(""); // 清空配置搜索
    },
    [onAgentChange, onConfigChange]
  );

  // 配置选择处理
  const handleConfigChange = useCallback(
    (value: string) => {
      onConfigChange(value);
    },
    [onConfigChange]
  );

  // Agent搜索处理
  const handleAgentSearch = useCallback((value: string) => {
    setAgentSearchTerm(value);
  }, []);

  // 配置搜索处理
  const handleConfigSearch = useCallback((value: string) => {
    setConfigSearchTerm(value);
  }, []);

  // Agent下拉滚动处理
  const handleAgentPopupScroll = useCallback(
    (element: any) => {
      const { scrollTop, scrollHeight, clientHeight } = element;
      const scrollBottom = scrollHeight - (scrollTop + clientHeight);

      if (scrollBottom < 10) {
        fetchNextAgentsPage();
      }
    },
    [fetchNextAgentsPage]
  );

  // 配置下拉滚动处理
  const handleConfigPopupScroll = useCallback(
    (element: any) => {
      const { scrollTop, scrollHeight, clientHeight } = element;
      const scrollBottom = scrollHeight - (scrollTop + clientHeight);

      if (scrollBottom < 10) {
        fetchNextConfigsPage();
      }
    },
    [fetchNextConfigsPage]
  );

  // 渲染Agent选项
  const renderAgentOptions = useMemo(() => {
    const arr = [...infiniteAgents];
    const has = infiniteAgents?.some((item) => item?.id === selectedAgent?.id);
    if (!has && selectedAgent) {
      arr.unshift(selectedAgent);
    }
    return arr.map((agent) => (
      <Option key={agent.id} value={agent.id}>
        {agent.name}
      </Option>
    ));
  }, [infiniteAgents, selectedAgent]);

  // 渲染配置选项
  const renderConfigOptions = useMemo(() => {
    const arr = [...infiniteAgentConfigs];

    const has = infiniteAgentConfigs?.some(
      (item) => item?.id === selectedAgentConfig?.id
    );
    if (!has && selectedAgentConfig) {
      arr.unshift(selectedAgentConfig);
    }
    return arr.map((config) => (
      <Option key={config.id} value={config.id}>
        <div className="flex items-center gap-2">
          <span>{config.name}</span>
          <Badge variant="outline" className="bg-gray-50 font-normal">
            {formatType(config.type)}
          </Badge>
        </div>
      </Option>
    ));
  }, [infiniteAgentConfigs, selectedAgentConfig]);

  return (
    <Form {...filterForm}>
      <div className="flex items-center">
        <FormField
          control={filterForm.control}
          name="agent_id"
          render={({ field }) => (
            <FormItem className="mb-0">
              <FormControl>
                <Select
                  style={{ width: 160 }}
                  placeholder="选择 Agent"
                  value={selectedAgentId || undefined}
                  onChange={handleAgentChange}
                  onSearch={handleAgentSearch}
                  onPopupScroll={handleAgentPopupScroll}
                  showSearch
                  filterOption={false}
                  loading={agentsLoading}
                  allowClear
                  notFoundContent={
                    agentsLoading ? (
                      <div className="text-center text-gray-500">加载中...</div>
                    ) : (
                      <div className="text-center text-gray-500">
                        暂无 Agent
                      </div>
                    )
                  }
                  dropdownRender={(menu) => (
                    <div>
                      {menu}
                      {isFetchingNextAgentsPage && (
                        <div className="text-center text-gray-500 text-sm">
                          加载更多...
                        </div>
                      )}
                    </div>
                  )}
                >
                  {renderAgentOptions}
                </Select>
              </FormControl>
            </FormItem>
          )}
        />

        <div className="mx-2 text-gray-400">/</div>

        <FormField
          control={filterForm.control}
          name="config_id"
          render={({ field }) => (
            <FormItem className="mb-0">
              <FormControl>
                <Select
                  style={{ width: 200 }}
                  placeholder="选择配置"
                  value={filterAgentConfigId || undefined}
                  onChange={handleConfigChange}
                  onSearch={handleConfigSearch}
                  onPopupScroll={handleConfigPopupScroll}
                  showSearch
                  filterOption={false}
                  loading={configsLoading}
                  disabled={!selectedAgentId}
                  allowClear
                  notFoundContent={
                    !selectedAgentId ? (
                      <div className="p-2 text-center text-gray-500">
                        请先选择 Agent
                      </div>
                    ) : configsLoading ? (
                      <div className="p-2 text-center text-gray-500">
                        加载中...
                      </div>
                    ) : (
                      <div className="p-2 text-center text-gray-500">
                        暂无配置
                      </div>
                    )
                  }
                  dropdownRender={(menu) => (
                    <div>
                      {menu}
                      {isFetchingNextConfigsPage && (
                        <div className="text-center text-gray-500 text-sm">
                          加载更多...
                        </div>
                      )}
                    </div>
                  )}
                >
                  {renderConfigOptions}
                </Select>
              </FormControl>
            </FormItem>
          )}
        />
      </div>
    </Form>
  );
}
