"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Steps, Button, Space, Descriptions } from "@arco-design/web-react";
import { apiClient } from "@/app/api/request";
import {
  AgentConfigVersion,
  AgentConfigStatus,
  AgentConfig,
  AgentConfigType,
  Agent,
} from "@/app/bam/aime/namespaces/agent";
import { toast } from "sonner";
import { VersionDiff } from "../components/VersionDiff";
import { PublishForm } from "../components/PublishForm";
import { PageHeader } from "@/app/components/PageHeader";
import { useQuery } from "@tanstack/react-query";
import { getEnv, BpmDomainMap } from "@/app/api/env";
import { Envs } from "@/app/api/const";
import { List, BarChart } from "lucide-react";
import { Button as CustomButton } from "@/components/ui/button";
const Step = Steps.Step;

// 获取配置类型显示文本的工具函数
function getConfigTypeText(type: AgentConfigType): string {
  switch (type) {
    case AgentConfigType.AgentConfigTypeBase:
      return "基础配置";
    case AgentConfigType.AgentConfigTypeAbTest:
      return "AB测试";
    case AgentConfigType.AgentConfigTypeFeature:
      return "功能配置";
    case AgentConfigType.AgentConfigTypeUser:
      return "用户配置";
    default:
      return "未知类型";
  }
}

export default function PublishPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const versionId = searchParams.get("id");
  const configId = searchParams.get("config_id");

  const [currentStep, setCurrentStep] = useState(1);
  const [userStep, setUserStep] = useState<number | undefined>();

  const [version, setVersion] = useState<AgentConfigVersion | undefined>();
  const [latestVersion, setLatestVersion] = useState<
    AgentConfigVersion | undefined
  >();
  const [agentConfig, setAgentConfig] = useState<AgentConfig | undefined>();
  const [agent, setAgent] = useState<Agent | undefined>();
  const [loading, setLoading] = useState(false);
  const urlDeployId = searchParams.get("deploy_id") || "";
  const [deployId, setDeployId] = useState<string | undefined>(urlDeployId);
  const [workflowId, setWorkflowId] = useState<string | undefined>();
  const [refreshLoading, setRefreshLoading] = useState(false);
  const isAbTest = searchParams.get("isAbTest") === "true";
  // ab 测试版本不需要填信息
  const totalStep = useMemo(() => (isAbTest ? 3 : 4), [isAbTest]);

  const finalStep = useMemo(() => {
    if (!userStep || userStep > currentStep) {
      return currentStep;
    }
    return userStep;
  }, [userStep, currentStep]);

  const { data: deployProcessInfo, refetch: refetchDeployProcessInfo } =
    useQuery({
      queryKey: ["GetDeployProcessInfo", deployId],
      queryFn: () =>
        apiClient.GetDeployProcessInfo({
          deploy_id: deployId!,
        }),
      enabled: Boolean(deployId),
    });

  // 处理刷新按钮点击
  const handleRefresh = async () => {
    if (deployId && currentStep === totalStep - 1) {
      setRefreshLoading(true);
      try {
        await refetchDeployProcessInfo();
      } finally {
        setRefreshLoading(false);
      }
    }
  };

  const { data: workflowInfo, isFetching: workflowInfoFetching } = useQuery({
    queryKey: ["GetWorkflowInfo", deployId],
    queryFn: () =>
      apiClient.GetDeploy({
        deploy_id: deployId!,
      }),
    enabled: Boolean(deployId),
  });

  useEffect(() => {
    if ((deployProcessInfo as any)?.deploy_status === "finish") {
      setCurrentStep(totalStep);
    }
  }, [deployProcessInfo, totalStep]);

  useEffect(() => {
    if (
      workflowInfoFetching ||
      !deployId ||
      (deployProcessInfo as any)?.deploy_status === "finish"
    ) {
      return;
    }
    // 进入发布中了
    if (workflowInfo?.deploy.workflow_id) {
      setCurrentStep(totalStep - 1);
      setWorkflowId(String(workflowInfo.deploy.workflow_id));
    } else if (workflowInfo?.deploy) {
      setCurrentStep(1);
    }
  }, [
    workflowInfo,
    totalStep,
    workflowInfoFetching,
    deployId,
    deployProcessInfo,
  ]);

  useEffect(() => {
    if (versionId && configId) {
      fetchVersions();
    }
  }, [versionId, configId]);

  const fetchVersions = async () => {
    try {
      setLoading(true);

      // 获取当前版本信息
      const versionResponse = await apiClient.GetAgentConfigVersion({
        agent_config_version_id: versionId!,
      });
      setVersion(versionResponse.agent_config_version);

      // 获取Agent配置信息
      const agentConfigResponse = await apiClient.GetAgentConfig({
        agent_config_id: configId!,
      });
      setAgentConfig(agentConfigResponse.agent_config);

      // 获取Agent基本信息
      const agentResponse = await apiClient.GetAgent({
        agent_id: agentConfigResponse.agent_config.agent_id,
      });
      setAgent(agentResponse.agent);

      // 获取最新的线上版本信息
      const versionsResponse = await apiClient.ListAgentConfigVersions({
        agent_config_id: configId!,
        page_num: 1,
        page_size: 100,
        status: AgentConfigStatus.AgentConfigStatusOnline,
      });

      const onlineVersions = versionsResponse.agent_config_versions?.filter(
        (v) => v.status === AgentConfigStatus.AgentConfigStatusOnline
      );

      if (onlineVersions && onlineVersions.length > 0) {
        setLatestVersion(onlineVersions[0]);
      }
    } catch {
      toast.error("获取版本信息失败");
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    if (currentStep === totalStep) {
      return;
    }
    setCurrentStep(currentStep + 1);
    setUserStep(currentStep + 1);
  };

  const handleFinishDiff = async () => {
    if (isAbTest && versionId) {
      try {
        const res = await apiClient.CreateDeploy({
          agent_config_version_id: versionId,
        } as any);
        handleFormSuccess(res);
      } catch (error) {
        toast.error((error as Error)?.message);
      }
    }
    handleNext();
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleFormSuccess = (res: any) => {
    setDeployId(res.deploy_id);
    const params = new URLSearchParams(searchParams.toString());
    // 2. 设置/更新参数
    params.set("deploy_id", res.deploy_id);
    router.replace(`?${params.toString()}`);
    setWorkflowId(res.workflow_id);
    handleNext();
  };

  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };
  const handleFinish = async () => {
    toast.success("版本部署成功");
    router.push(`/agents/version?config_id=${configId}`);
  };

  if (!versionId || !configId) {
    return <div>缺少必要的参数</div>;
  }

  // 获取日期字符串（相对今天的偏移天数）
  const getDateString = (daysOffset: number): string => {
    const date = new Date();
    date.setDate(date.getDate() + daysOffset);
    return date.toISOString().split("T")[0];
  };

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <PageHeader title="发布管控" />
        {currentStep > totalStep - 2 ? (
          <div className="flex gap-2">
            <CustomButton
              variant="outline"
              onClick={() => {
                router.push(
                  `/agents/version/online-tasks?agent_config_version_id=${versionId}&config_id=${configId}`
                );
              }}
              className="flex items-center gap-2"
            >
              <List className="h-4 w-4" />
              线上任务列表
            </CustomButton>
            <CustomButton
              variant="outline"
              onClick={() => {
                const env = getEnv() === Envs.ONLINE ? "" : "-boe";
                const url = `https://aime-auto-eval-fe.gf${env}.bytedance.net/online-tasks?startDate=${getDateString(
                  -7
                )}&endDate=${getDateString(
                  0
                )}&tab=onlineData&xDebugVersion=${versionId}`;
                window.open(url, "_blank");
              }}
              className="flex items-center gap-2"
            >
              <BarChart className="h-4 w-4" />
              评测任务列表
            </CustomButton>
          </div>
        ) : null}
      </div>

      <Steps className="mb-8" type="arrow">
        <Step
          title="确认变更"
          description="查看版本差异"
          onClick={() => setUserStep(1)}
          status={
            finalStep === 1 ? "process" : currentStep >= 1 ? "finish" : "wait"
          }
        />
        {!isAbTest && (
          <Step
            title="填写信息"
            description="提供必要的发布信息"
            onClick={() => setUserStep(2)}
            status={
              finalStep === 2 ? "process" : currentStep >= 2 ? "finish" : "wait"
            }
          />
        )}
        <Step
          title="发布"
          description="查看发布详情"
          onClick={() => setUserStep(totalStep - 1)}
          status={
            finalStep === totalStep - 1
              ? "process"
              : currentStep >= totalStep - 1
              ? "finish"
              : "wait"
          }
        />
        <Step
          title="完成"
          description="发布成功"
          onClick={() => setUserStep(totalStep)}
          status={
            finalStep === totalStep
              ? "process"
              : currentStep >= totalStep
              ? "finish"
              : "wait"
          }
        />
      </Steps>

      <div className="mt-8">
        {finalStep === 1 && (
          <div>
            <div className="mb-4">
              <p className="text-gray-500">
                请确认以下版本差异，确认无误后点击[下一步]按钮继续。
              </p>
            </div>
            {loading ? (
              <div className="flex items-center justify-center h-[600px]">
                <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
              </div>
            ) : (
              <VersionDiff
                leftVersion={latestVersion}
                rightVersion={version}
                showBack={false}
              />
            )}
            <div className="mt-6 flex justify-end">
              <Button
                type="primary"
                onClick={handleFinishDiff}
                disabled={loading || currentStep !== 1}
              >
                确认变更，下一步
              </Button>
            </div>
          </div>
        )}

        {finalStep === totalStep - 2 && !isAbTest && (
          <div>
            <div className="mb-4">
              <p className="text-gray-500">请填写以下发布信息。</p>
            </div>
            <PublishForm
              agentConfigVersionId={versionId}
              onPrev={handlePrev}
              onNext={handleFormSuccess}
              formData={workflowInfo?.deploy?.extra_info as any}
              disabled={currentStep !== totalStep - 2}
            />
          </div>
        )}

        {finalStep === totalStep - 1 && (
          <>
            <div className="flex justify-between items-center mb-2">
              <div>发布详情</div>
              <Button
                type="primary"
                onClick={handleRefresh}
                loading={refreshLoading}
              >
                刷新数据
              </Button>
            </div>
            <div className="mb-4">
              <Descriptions
                border
                data={[
                  {
                    label: "Agent名称",
                    value: agent?.name || "-",
                  },
                  {
                    label: "配置类型",
                    value: agentConfig?.type ? getConfigTypeText(agentConfig.type) : "-",
                  },
                  {
                    label: "版本号",
                    value: version?.version ? `v${version.version}` : "-",
                  },
                  {
                    label: "版本描述",
                    value: version?.description || "-",
                  },
                  {
                    label: "运行中任务数",
                    value: deployProcessInfo?.running_count || "-",
                  },
                  {
                    label: "成功数",
                    value: deployProcessInfo?.success_count || "-",
                  },
                  {
                    label: "失败数",
                    value: deployProcessInfo?.fail_count || "-",
                  },
                  {
                    label: "灰度比例",
                    value: deployProcessInfo?.canary_ratio || "-",
                  },
                ]}
              ></Descriptions>
            </div>
            <div className="w-full h-[700px]">
              {workflowId ? (
                <iframe
                  src={`https://${BpmDomainMap[getEnv() || Envs.ONLINE]}/record/${workflowId}`}
                  className="w-full h-full"
                />
              ) : null}
            </div>  
          </>
        )}

        {finalStep === totalStep && (
          <div className="text-center py-12">
            <div className="mb-6">
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-2xl font-bold mb-2">发布流程已完成</h2>
              <p className="text-gray-500">您的版本已成功部署。</p>
            </div>
            <Space>
              <Button
                onClick={() =>
                  router.push(`/agents/version?config_id=${configId}`)
                }
              >
                返回版本列表
              </Button>
              <Button type="primary" onClick={handleFinish}>
                完成
              </Button>
            </Space>
          </div>
        )}
      </div>
    </div>
  );
}
