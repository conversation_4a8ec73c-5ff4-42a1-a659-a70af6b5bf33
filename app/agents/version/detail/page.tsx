"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { apiClient } from "@/app/api/request";
import {
  AgentConfigVersion,
  AgentConfig,
} from "@/app/bam/aime/namespaces/agent";
import { toast } from "sonner";
import { VersionDetail } from "../components/VersionDetail";

export default function VersionDetailPage() {
  const searchParams = useSearchParams();
  const configId = searchParams.get("config_id");
  const versionId = searchParams.get("id");
  const [version, setVersion] = useState<AgentConfigVersion | undefined>();
  const [agentConfig, setAgentConfig] = useState<AgentConfig | undefined>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (versionId && configId) {
      fetchVersionAndConfig();
    }
  }, [versionId, configId]);

  const fetchVersionAndConfig = async () => {
    try {
      setLoading(true);

      // 获取版本信息
      const versionResponse = await apiClient.GetAgentConfigVersion({
        agent_config_version_id: versionId!,
      });
      setVersion(versionResponse.agent_config_version);

      // 获取配置信息
      const configResponse = await apiClient.GetAgentConfig({
        agent_config_id: configId!,
      });
      setAgentConfig(configResponse.agent_config);
    } catch {
      toast.error("获取版本信息失败");
    } finally {
      setLoading(false);
    }
  };

  if (!configId || !versionId) {
    return <div>缺少必要的参数</div>;
  }

  return (
    <div className="container mx-auto py-6">
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        </div>
      ) : (
        <VersionDetail
          configId={configId}
          version={version}
          agentConfig={agentConfig}
        />
      )}
    </div>
  );
}
