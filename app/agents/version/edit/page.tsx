"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { VersionForm } from "../components/VersionForm";
import { apiClient } from "@/app/api/request";
import { AgentConfigVersion } from "@/app/bam/aime/namespaces/agent";
import { toast } from "sonner";

export default function EditVersionPage() {
  const searchParams = useSearchParams();
  const configId = searchParams.get("config_id");
  const versionId = searchParams.get("id");
  const [version, setVersion] = useState<AgentConfigVersion | undefined>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (versionId) {
      fetchVersion();
    }
  }, [versionId]);

  const fetchVersion = async () => {
    try {
      setLoading(true);
      const response = await apiClient.GetAgentConfigVersion({
        agent_config_version_id: versionId!,
      });
      setVersion(response.agent_config_version);
    } catch {
      toast.error("获取版本信息失败");
    } finally {
      setLoading(false);
    }
  };

  if (!configId) {
    return <div>缺少必要的参数</div>;
  }

  return (
    <div className="container mx-auto py-6">
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        </div>
      ) : (
        <VersionForm configId={configId} version={version} />
      )}
    </div>
  );
}
