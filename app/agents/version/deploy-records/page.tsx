"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { apiClient } from "@/app/api/request";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PageHeader } from "@/app/components/PageHeader";
import { PaginationInfo } from "@/components/common/PaginationInfo";
import { DataTable } from "@/components/common/DataTable";
import { pageSize, formatDateTime, updatePaginationState } from "../../utils";
import { toast } from "sonner";
import { Clock } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { AgentDeploy } from "@/app/bam/aime/namespaces/deploy";
import { Link, Typography } from "@arco-design/web-react";
import { XCopy } from "@tod-m/materials";
import { IconCopy } from "@arco-design/web-react/icon";

export default function DeployRecordsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [deploys, setDeploys] = useState<AgentDeploy[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const configId = searchParams.get("config_id") || "";

  const fetchDeploys = async (page: number = currentPage) => {
    if (!configId) return;

    try {
      setLoading(true);
      const response = await apiClient.GetAgentDeployList({
        agent_config_id: configId,
        page_num: page,
        page_size: pageSize,
      });

      setDeploys(response.agent_deploys || []);
      updatePaginationState(
        Number(response.total),
        setTotalPages,
        setTotalCount
      );
    } catch {
      toast.error("获取发布记录失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (configId) {
      fetchDeploys();
    }
  }, [currentPage, configId]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRowClick = (deploy: AgentDeploy) => {
    router.push(
      `/agents/version/publish?id=${deploy.agent_config_version_info.id}&config_id=${configId}&deploy_id=${deploy.id}`
    );
  };

  const handleVersionClick = (
    e: React.MouseEvent,
    version: any,
    agent_config_id: any
  ) => {
    router.push(
      `/agents/version/detail?id=${version.id}&config_id=${agent_config_id}`
    );
    e.stopPropagation();
  };

  const columns = [
    {
      header: "本次发布版本",
      accessor: (deploy: AgentDeploy) => (
        <div
          className="flex flex-col gap-0.5"
          onClick={(e) =>
            handleVersionClick(
              e,
              deploy.agent_config_version_info,
              deploy?.agent_config_id
            )
          }
        >
          <div className="flex items-center">
            版本：
            <Link className="overflow-hidden w-ful">
              {deploy.agent_config_version_info?.version || "-"}
            </Link>
          </div>
          <div className="flex items-center">
            ID：
            <Link className="overflow-hidden">
              {deploy.agent_config_version_info?.id || "-"}
            </Link>
            <span onClick={(e) => e.stopPropagation()}>
              <XCopy
                value={deploy.agent_config_version_info?.id || "-"}
                onCopy={() => toast.success("复制ID成功")}
              >
                <IconCopy />
              </XCopy>
            </span>
          </div>
        </div>
      ),
      className: "min-w-[200px]",
    },
    {
      header: "上一发布版本",
      accessor: (deploy: AgentDeploy) => (
        <div
          className="flex flex-col gap-0.5"
          onClick={(e) =>
            handleVersionClick(
              e,
              deploy.agent_config_version_online_info,
              deploy?.agent_config_id
            )
          }
        >
          <div className="flex items-center">
            版本：
            <Link className="overflow-hidden w-ful">
              {deploy.agent_config_version_online_info?.version || "-"}
            </Link>
          </div>
          <div className="flex items-center">
            ID：
            <Link className="overflow-hidden">
              {deploy.agent_config_version_online_info?.id || "-"}
            </Link>
            {deploy.agent_config_version_online_info?.id ? (
              <span onClick={(e) => e.stopPropagation()}>
                <XCopy
                  value={deploy.agent_config_version_online_info?.id || "-"}
                  onCopy={() => toast.success("复制ID成功")}
                >
                  <IconCopy />
                </XCopy>
              </span>
            ) : null}
          </div>
        </div>
      ),
      className: "min-w-[200px]",
    },
    {
      header: "创建者",
      accessor: (deploy: AgentDeploy) => deploy?.actor || "-",
      className: "w-[100px] text-center",
    },
    {
      header: "状态",
      accessor: (deploy: AgentDeploy) => (
        <Badge
          variant="outline"
          className={`${
            deploy.status === "finish"
              ? "bg-green-50 text-green-700"
              : deploy.status === "failed"
              ? "bg-red-50 text-red-700"
              : "bg-gray-50"
          } font-normal`}
        >
          {deploy.status || "-"}
        </Badge>
      ),
      className: "w-[100px] text-center",
    },
    {
      header: "创建时间",
      accessor: (deploy: AgentDeploy) => formatDateTime(deploy.created_at),
      className: "w-[160px] whitespace-nowrap",
    },
    {
      header: "操作",
      accessor: (deploy: AgentDeploy) => (
        <div className="flex justify-center items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 hover:bg-green-100 text-green-500"
                  onClick={(e) => {
                    e.stopPropagation();
                    router.push(
                      `/agents/version/publish?id=${deploy.agent_config_version_info.id}&config_id=${configId}&deploy_id=${deploy.id}`
                    );
                  }}
                >
                  <Clock className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>查看发布详情</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      ),
      sticky: true,
      className:
        "w-[100px] text-center sticky right-0 bg-white shadow-[-8px_0_16px_-6px_rgba(0,0,0,0.1)]",
    },
  ];

  if (!configId) {
    return <div>缺少必要的参数</div>;
  }

  return (
    <div>
      <div className="flex justify-between mb-4">
        <PageHeader title="发布记录" />
        <Button
          variant="outline"
          onClick={() => router.push(`/agents/version?config_id=${configId}`)}
        >
          返回版本列表
        </Button>
      </div>

      <div className="relative">
        <DataTable
          data={deploys}
          columns={columns}
          loading={loading}
          keyField="id"
          onRowClick={handleRowClick}
          rowClassName="cursor-pointer hover:bg-gray-50"
        />
        {loading && (
          <div className="absolute inset-0 bg-white/50 flex items-center justify-center">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          </div>
        )}
      </div>

      <PaginationInfo
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalCount={totalCount}
        onPageChange={handlePageChange}
      />
    </div>
  );
}
