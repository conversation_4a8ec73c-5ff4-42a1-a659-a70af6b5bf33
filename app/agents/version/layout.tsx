"use client";

import { useState, useEffect } from "react";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { AgentConfigSelector } from "./components/AgentConfigSelector";
import { apiClient } from "@/app/api/request";
import { useQuery } from "@tanstack/react-query";
import { Agent, AgentConfig } from "@/app/bam/aime/namespaces/agent";

export default function VersionLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedAgentId, setSelectedAgentId] = useState("");
  const [selectedAgent, setSelectedAgent] = useState<Agent>();
  const [selectedAgentConfig, setSelectedAgentConfig] = useState<AgentConfig>();
  const [filterAgentConfigId, setFilterAgentConfigId] = useState(
    searchParams.get("config_id") || ""
  );
  const pathname = usePathname();

  const { data: agentVersion } = useQuery({
    queryKey: ["versions", filterAgentConfigId],
    queryFn: async () => {
      const response = await apiClient.ListAgentConfigVersions({
        agent_config_id: filterAgentConfigId,
        page_num: 1,
        page_size: 1000,
      });
      return response;
    },
    enabled: Boolean(filterAgentConfigId),
  });


  // 获取所有 Agent Configs
  const { data: allAgentConfigs = [] } = useQuery({
    queryKey: ["allAgentConfigs", agentVersion?.agent_config?.agent_id, selectedAgentId],
    queryFn: async () => {
      const response = await apiClient.ListAgentConfigs({
        page_num: 1,
        page_size: 100,
        agent_id: selectedAgentId || agentVersion?.agent_config?.agent_id,
      });
      setSelectedAgent(response.agent);
      return response.agent_configs || [];
    },
    staleTime: 2000,
    enabled: Boolean(agentVersion?.agent_config?.agent_id || selectedAgentId),
  });



  // 当有 config_id 时，自动设置对应的 agent_id
  useEffect(() => {
    if (filterAgentConfigId && allAgentConfigs.length > 0) {
      const config = allAgentConfigs.find((c) => c.id === filterAgentConfigId);
      setSelectedAgentConfig(config);
      if (config && config.agent_id !== selectedAgentId) {
        setSelectedAgentId(config.agent_id);
      }
    }
  }, [filterAgentConfigId, allAgentConfigs, selectedAgentId]);

  // 监听 URL 变化，更新 filterAgentConfigId
  useEffect(() => {
    const configId = searchParams.get("config_id") || "";
    if (configId !== filterAgentConfigId) {
      setFilterAgentConfigId(configId);
    }
  }, [searchParams]);

  const handleConfigChange = (configId: string) => {
    setFilterAgentConfigId(configId);
    // 更新 URL 参数
    const params = new URLSearchParams(searchParams.toString());
    if (configId) {
      params.set("config_id", configId);
    } else {
      params.delete("config_id");
    }
    router.push(`/agents/version?${params.toString()}`);
  };

  console.log(selectedAgent, 'selectedAgentselectedAgentselectedAgent')

  return (
    <div className="p-6 space-y-6">
      {pathname?.includes("version/publish") ? null : (
        <div className="flex items-center justify-between">
          <AgentConfigSelector
            selectedAgent={selectedAgent}
            selectedAgentConfig={selectedAgentConfig}
            onAgentChange={setSelectedAgentId}
            onConfigChange={handleConfigChange}
          />
        </div>
      )}
      {children}
    </div>
  );
}
