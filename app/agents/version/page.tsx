"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { apiClient } from "@/app/api/request";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Edit,
  Play,
  Copy,
  Bug,
  GitCompare,
  CopyPlus,
  Clock,
  List,
  BarChart,
} from "lucide-react";
import { Modal, Select, Space, Input } from "@arco-design/web-react";
import {
  AgentConfigVersion,
  AgentConfig,
  AgentConfigType,
} from "@/app/bam/aime/namespaces/agent";
import { AgentConfigStatus } from "@/app/bam/aime/namespaces/agent";
import { PaginationInfo } from "@/components/common/PaginationInfo";
import { DataTable } from "@/components/common/DataTable";
import { pageSize, formatDateTime, updatePaginationState } from "../utils";
import { toast } from "sonner";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  Too<PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useQuery } from "@tanstack/react-query";
import { debounce } from "lodash-es";
import { getEnv } from "@/app/api/env";
import { Envs } from "@/app/api/const";

export default function AgentsVersionPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [versions, setVersions] = useState<AgentConfigVersion[]>([]);
  const [agentConfig, setAgentConfig] = useState<AgentConfig | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const filterAgentConfigId = searchParams.get("config_id") || "";

  // 添加选择版本的状态
  const [selectedVersions, setSelectedVersions] = useState<string[]>([]);
  // 添加控制是否显示选择框的状态
  const [showCheckbox, setShowCheckbox] = useState(false);
  const [versionCopyModalVisible, setVersionCopyModalVisible] = useState(false);
  const [selectedTargetConfigId, setSelectedTargetConfigId] = useState("");
  const [selectedSourceVersionId, setSelectedSourceVersionId] = useState("");
  const [targetVersionDescription, setTargetVersionDescription] = useState("");
  const [allAgentConfigs, setAllAgentConfigs] = useState<AgentConfig[]>([]);
  const [copyLoading, setCopyLoading] = useState(false);

  const fetchVersions = async (page: number = currentPage) => {
    try {
      setLoading(true);
      const response = await apiClient.ListAgentConfigVersions({
        agent_config_id: filterAgentConfigId,
        page_num: page,
        page_size: pageSize,
      });

      setVersions(response.agent_config_versions || []);
      setAgentConfig(response.agent_config || null);
      fetchAllAgentConfigs("", response.agent_config?.agent_id);
      updatePaginationState(
        Number(response.total),
        setTotalPages,
        setTotalCount
      );
    } catch {
      toast.error("获取版本列表失败");
    } finally {
      setLoading(false);
    }
  };

  const { data: copyVersions = [] } = useQuery({
    queryKey: ["versions", selectedTargetConfigId],
    queryFn: async () => {
      const response = await apiClient.ListAgentConfigVersions({
        agent_config_id: selectedTargetConfigId,
        page_num: 1,
        page_size: 1000,
      });
      return response.agent_config_versions || [];
    },
    enabled: Boolean(selectedTargetConfigId),
  });

  // 获取所有 Agent Configs
  const fetchAllAgentConfigs = async (name?: string, id?: string) => {
    try {
      if (!agentConfig?.agent_id && !id) {
        return;
      }
      const response = await apiClient.ListAgentConfigs({
        name,
        page_num: 1,
        page_size: 100,
        agent_id: agentConfig?.agent_id || id,
      });
      setAllAgentConfigs(response.agent_configs || []);
    } catch {
      toast.error("获取配置列表失败");
    }
  };

  const debouncedFetchAgentConfigs = useCallback(
    debounce(fetchAllAgentConfigs, 300),
    [setAllAgentConfigs, agentConfig]
  );

  useEffect(() => {
    if (filterAgentConfigId) {
      fetchVersions();
    }
  }, [currentPage, filterAgentConfigId]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // const handleDeploy = async (version: AgentConfigVersion) => {
  //   try {
  //     await apiClient.DeployAgentConfigVersion({
  //       agent_config_version_id: version.id,
  //       status: AgentConfigStatus.AgentConfigStatusOnline,
  //     });
  //     toast.success("部署成功");
  //     fetchVersions();
  //   } catch {
  //     toast.error("部署失败");
  //   }
  // };

  const handleCopy = async (version: AgentConfigVersion) => {
    try {
      await apiClient.CopyAgentConfigVersion({
        agent_config_id: filterAgentConfigId,
        source_id: version.id,
      });
      toast.success("复制成功");
      fetchVersions();
    } catch {
      toast.error("复制失败");
    }
  };

  const handleVersionCopy = async () => {
    if (!selectedSourceVersionId || !selectedTargetConfigId) {
      toast.error("请选择源版本和目标配置");
      return;
    }

    try {
      setCopyLoading(true);
      await apiClient.CopyAgentConfigVersion({
        source_id: selectedSourceVersionId,
        agent_config_id: filterAgentConfigId,
        description: targetVersionDescription,
      });
      toast.success("版本复制成功");
      setVersionCopyModalVisible(false);
      fetchVersions();
      // 重置选择状态
      setSelectedSourceVersionId("");
      setSelectedTargetConfigId("");
      setTargetVersionDescription("");
    } catch (error) {
      toast.error("版本复制失败");
      console.error("版本复制失败:", error);
    } finally {
      setCopyLoading(false);
    }
  };

  const handleCopyVersionId = async (version: AgentConfigVersion) => {
    try {
      await navigator.clipboard.writeText(version.id);
      toast.success(
        `版本 ID 已复制，可通过 x-debug-version: ${version.id} 测试`
      );
    } catch {
      toast.error("复制失败");
    }
  };

  const handleRowClick = (version: AgentConfigVersion) => {
    if (showCheckbox) {
      handleSelectVersion(version);
      return;
    }
    router.push(
      `/agents/version/detail?id=${version.id}&config_id=${filterAgentConfigId}`
    );
  };

  // 添加选择版本的处理函数
  const handleSelectVersion = (
    version: AgentConfigVersion,
    e?: React.MouseEvent
  ) => {
    e?.stopPropagation();
    setSelectedVersions((prev) => {
      // 如果已经选中，则取消选中
      if (prev.includes(version.id)) {
        return prev.filter((id) => id !== version.id);
      }

      // 如果已经选择了两个版本，则替换最早选择的那个
      if (prev.length >= 2) {
        return [prev[1], version.id];
      }

      // 否则添加到选中列表
      return [...prev, version.id];
    });
  };

  // 添加比较版本的处理函数
  const handleCompareVersions = () => {
    if (selectedVersions.length !== 2) {
      toast.error("请选择两个版本进行比较");
      return;
    }

    router.push(
      `/agents/version/diff?left_id=${selectedVersions[0]}&right_id=${selectedVersions[1]}&config_id=${filterAgentConfigId}`
    );
  };

  // 切换显示选择框的状态
  const toggleCheckbox = () => {
    setShowCheckbox(!showCheckbox);
    // 如果隐藏选择框，则清空已选择的版本
    if (showCheckbox) {
      setSelectedVersions([]);
    }
  };

  // 根据showCheckbox状态动态生成列
  const getColumns = () => {
    const baseColumns = [
      {
        header: "版本",
        accessor: (version: AgentConfigVersion) => `v${version.version}`,
        className: "w-[80px] font-medium",
      },
      {
        header: "类型",
        accessor: (version: AgentConfigVersion) => (
          <Badge variant="outline" className="bg-gray-50 font-normal">
            {version.runtime_config.type}
          </Badge>
        ),
        className: "w-[100px] text-center",
      },
      {
        header: "描述",
        accessor: (version: AgentConfigVersion) => (
          <div className="truncate max-w-[300px]" title={version.description}>
            {version.description || "-"}
          </div>
        ),
        className: "min-w-[300px] max-w-[400px]",
      },
      {
        header: "状态",
        accessor: (version: AgentConfigVersion) => (
          <Badge
            variant="outline"
            className={`${
              version.status === AgentConfigStatus.AgentConfigStatusOnline
                ? "bg-green-50 text-green-700"
                : "bg-gray-50"
            } font-normal`}
          >
            {version.status}
          </Badge>
        ),
        className: "w-[100px] text-center",
      },
      {
        header: "启用状态",
        accessor: (version: AgentConfigVersion) => (
          <Badge
            variant="outline"
            className={`${
              version.enabled ? "bg-green-50 text-green-700" : "bg-gray-50"
            } font-normal`}
          >
            {version.enabled ? "Enabled" : "Disabled"}
          </Badge>
        ),
        className: "w-[100px] text-center",
      },
      {
        header: "创建者",
        accessor: (version: AgentConfigVersion) => version.creator,
        className: "w-[120px]",
      },
      {
        header: "创建时间",
        accessor: (version: AgentConfigVersion) =>
          formatDateTime(version.created_at),
        className: "w-[160px] whitespace-nowrap",
      },
      {
        header: "更新时间",
        accessor: (version: AgentConfigVersion) =>
          formatDateTime(version.updated_at),
        className: "w-[160px] whitespace-nowrap",
      },
      {
        header: "操作",
        accessor: (version: AgentConfigVersion) => (
          <div className="flex justify-center items-center space-x-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 hover:bg-gray-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(
                        `/agents/version/edit?id=${version.id}&config_id=${filterAgentConfigId}&type=${agentConfig?.type}`
                      );
                    }}
                    disabled={
                      (agentConfig?.type ===
                        AgentConfigType.AgentConfigTypeBase &&
                        version.enabled) ||
                      (agentConfig?.type ===
                        AgentConfigType.AgentConfigTypeAbTest &&
                        version.status ===
                          AgentConfigStatus.AgentConfigStatusOnline)
                    }
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>编辑版本</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {[
              AgentConfigType.AgentConfigTypeBase,
              AgentConfigType.AgentConfigTypeAbTest,
            ].includes(agentConfig?.type as AgentConfigType) && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 hover:bg-green-100 text-green-500"
                      onClick={async (e) => {
                        e.stopPropagation();

                        if (!version.deploy_id) {
                          // 检查是否有正在发布中的版本
                          try {
                            const response = await apiClient.GetAgentDeployList(
                              {
                                agent_config_id: filterAgentConfigId,
                                page_num: 1,
                                page_size: 10,
                                status: "created",
                              }
                            );

                            if (response.agent_deploys?.length > 0) {
                              // 如果有正在发布中的版本，弹出提示
                              toast.warning(
                                "当前有正在发布中的版本，请等待发布完成后再进行新的发布。"
                              );
                              return;
                            }
                          } catch (error) {
                            console.error("检查发布状态失败:", error);
                          }
                        }

                        // 如果没有正在发布中的版本，正常跳转到发布页面
                        router.push(
                          `/agents/version/publish?id=${
                            version.id
                          }&config_id=${filterAgentConfigId}&isAbTest=${
                            agentConfig?.type ===
                            AgentConfigType.AgentConfigTypeAbTest
                          }&deploy_id=${version.deploy_id || ""}`
                        );
                      }}
                      disabled={
                        version.status ===
                        AgentConfigStatus.AgentConfigStatusOnline
                      }
                    >
                      {version.deploy_id ? (
                        <Clock className="h-4 w-4" />
                      ) : (
                        <Play className="h-4 w-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{version.deploy_id ? "部署中，查看详情" : "部署版本"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 hover:bg-blue-100 text-blue-500"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCopy(version);
                    }}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>复制版本</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 hover:bg-purple-100 text-purple-500"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCopyVersionId(version);
                    }}
                  >
                    <Bug className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>复制版本 ID</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 hover:bg-orange-100 text-orange-500"
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(
                        `/agents/version/online-tasks?agent_config_version_id=${version.id}&config_id=${filterAgentConfigId}`
                      );
                    }}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>线上任务列表</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 hover:bg-cyan-100 text-cyan-500"
                    onClick={(e) => {
                      e.stopPropagation();
                      // 根据环境生成评测任务列表URL
                      const getDateString = (daysOffset: number): string => {
                        const date = new Date();
                        date.setDate(date.getDate() + daysOffset);
                        return date.toISOString().split('T')[0];
                      };
                       const env = getEnv() === Envs.ONLINE ? "" : "-boe";
                      const url = `https://aime-auto-eval-fe.gf${env}.bytedance.net/online-tasks?startDate=${getDateString(-7)}&endDate=${getDateString(0)}&tab=onlineData&xDebugVersion=${version.id}`;
                      window.open(url, '_blank');
                    }}
                  >
                    <BarChart className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>评测任务列表</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        ),
        sticky: true,
        className:
          "w-[240px] text-center sticky right-0 bg-white shadow-[-8px_0_16px_-6px_rgba(0,0,0,0.1)]",
      },
    ];

    // 如果需要显示选择框，则在基础列前添加选择列
    if (showCheckbox) {
      return [
        {
          header: "选择",
          accessor: (version: AgentConfigVersion) => (
            <input
              type="checkbox"
              checked={selectedVersions.includes(version.id)}
              onClick={(e) => handleSelectVersion(version, e)}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
          ),
          className: "w-[60px] text-center",
        },
        ...baseColumns,
      ];
    }

    return baseColumns;
  };

  const columns = getColumns();

  return (
    <>
      <div className="flex justify-between mb-4">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={toggleCheckbox}
            className="flex items-center gap-2"
          >
            <GitCompare className="h-4 w-4" />
            版本 diff
          </Button>

          {showCheckbox && selectedVersions.length === 2 && (
            <Button
              onClick={handleCompareVersions}
              variant="default"
              className="flex items-center gap-2 ml-2"
            >
              <GitCompare className="h-4 w-4" />
              比较所选版本
            </Button>
          )}

          <Button
            variant="outline"
            onClick={() => setVersionCopyModalVisible(true)}
            className="flex items-center gap-2"
            disabled={!filterAgentConfigId}
          >
            <CopyPlus className="h-4 w-4" />
            版本复制
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={(e) => {
              e.stopPropagation();
              router.push(
                `/agents/version/deploy-records?config_id=${filterAgentConfigId}`
              );
            }}
            variant="outline"
          >
            发布记录
          </Button>
          <Button
            onClick={() =>
              router.push(
                `/agents/version/edit?config_id=${filterAgentConfigId}`
              )
            }
            disabled={!filterAgentConfigId}
          >
            New Version
          </Button>
        </div>
      </div>

      {/* 版本复制弹窗 */}
      <Modal
        title="版本复制"
        visible={versionCopyModalVisible}
        onOk={handleVersionCopy}
        onCancel={() => {
          setVersionCopyModalVisible(false);
          setSelectedSourceVersionId("");
          setSelectedTargetConfigId("");
          setTargetVersionDescription("");
        }}
        confirmLoading={copyLoading}
        okText="确认复制"
        cancelText="取消"
        autoFocus={false}
        maskClosable={false}
      >
        <Space direction="vertical" style={{ width: "100%" }} size="large">
          <div>
            <div className="mb-2">选择Agent Config</div>
            <Select
              placeholder="请选择要复制到的Agent Config"
              style={{ width: "100%" }}
              value={selectedTargetConfigId}
              onSearch={debouncedFetchAgentConfigs}
              onChange={(value) => {
                setSelectedTargetConfigId(value);
                setSelectedSourceVersionId("");
              }}
              showSearch
              filterOption={false}
            >
              {allAgentConfigs?.map((config) => (
                <Select.Option key={config.id} value={config.id}>
                  {config.name} - {config.type}
                </Select.Option>
              ))}
            </Select>
          </div>

          <div>
            <div className="mb-2">选择版本</div>
            <Select
              placeholder="请选择要复制的版本"
              style={{ width: "100%" }}
              value={selectedSourceVersionId}
              onChange={(value) => setSelectedSourceVersionId(value)}
              showSearch
            >
              {copyVersions.map((version) => (
                <Select.Option key={version.id} value={version.id}>
                  {version?.status ===
                  AgentConfigStatus.AgentConfigStatusOnline ? (
                    <div className="text-[rgb(var(--primary-6))]">
                      {version.version} - {version.description || "无描述"} -{version?.status}
                    </div>
                  ) : (
                    <>
                      {version.version} - {version.description || "无描述"} -{version?.status}
                    </>
                  )}
                </Select.Option>
              ))}
            </Select>
          </div>

          <div>
            <div className="mb-2">目标版本描述</div>
            <Input
              placeholder="请输入目标版本描述"
              value={targetVersionDescription}
              onChange={(value) => setTargetVersionDescription(value)}
              style={{ width: "100%" }}
            />
          </div>
        </Space>
      </Modal>

      <div className="relative">
        <DataTable
          data={versions}
          columns={columns}
          loading={loading}
          keyField="id"
          onRowClick={handleRowClick}
          rowClassName="cursor-pointer hover:bg-gray-50"
        />
        {loading && (
          <div className="absolute inset-0 bg-white/50 flex items-center justify-center">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          </div>
        )}
      </div>

      <PaginationInfo
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalCount={totalCount}
        onPageChange={handlePageChange}
      />
    </>
  );
}
