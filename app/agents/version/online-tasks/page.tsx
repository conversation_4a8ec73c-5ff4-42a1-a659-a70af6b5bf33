"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { apiClient } from "@/app/api/request";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, ExternalLink, Eye } from "lucide-react";
import { Select, DatePicker, Space } from "@arco-design/web-react";
import { UserSession, SessionStatus } from "@/app/bam/aime/namespaces/session";
import { PaginationInfo } from "@/components/common/PaginationInfo";
import { DataTable } from "@/components/common/DataTable";
import { pageSize, formatDateTime, updatePaginationState } from "../../utils";
import { toast } from "sonner";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Envs } from "@/app/api/const";
import { getEnv } from "@/app/api/env";
import { Dayjs } from "dayjs";

const { RangePicker } = DatePicker;

// 状态选项
const statusOptions = [
  { label: "全部状态", value: "" },
  { label: "运行中", value: SessionStatus.SessionStatusRunning },
  { label: "空闲", value: SessionStatus.SessionStatusIdle },
  { label: "错误", value: SessionStatus.SessionStatusError },
  { label: "已停止", value: SessionStatus.SessionStatusStopped },
  { label: "已取消", value: SessionStatus.SessionStatusCanceled },
  { label: "等待中", value: SessionStatus.SessionStatusWaiting },
  { label: "已创建", value: SessionStatus.SessionStatusCreated },
];

// 获取状态对应的Badge样式
function getStatusVariant(
  status: SessionStatus
): "default" | "secondary" | "destructive" | "outline" {
  switch (status) {
    case SessionStatus.SessionStatusRunning:
      return "default";
    case SessionStatus.SessionStatusIdle:
      return "secondary";
    case SessionStatus.SessionStatusError:
      return "destructive";
    default:
      return "outline";
  }
}

// 获取状态显示文本
function getStatusText(status: SessionStatus): string {
  switch (status) {
    case SessionStatus.SessionStatusRunning:
      return "运行中";
    case SessionStatus.SessionStatusIdle:
      return "空闲";
    case SessionStatus.SessionStatusError:
      return "错误";
    case SessionStatus.SessionStatusStopped:
      return "已停止";
    case SessionStatus.SessionStatusCanceled:
      return "已取消";
    case SessionStatus.SessionStatusWaiting:
      return "等待中";
    case SessionStatus.SessionStatusCreated:
      return "已创建";
    default:
      return status;
  }
}

// 获取日期字符串（相对今天的偏移天数）
function getDateString(daysOffset: number): string {
  const date = new Date();
  date.setDate(date.getDate() + daysOffset);
  return date.toISOString().split("T")[0];
}

export default function OnlineTasksPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // 路由参数
  const agentConfigVersionId =
    searchParams.get("agent_config_version_id") || "";

  // 状态管理
  const [sessions, setSessions] = useState<UserSession[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [dates, setDates] = useState<Dayjs[]>([]);
  const [totalCount, setTotalCount] = useState(0);

  // 筛选条件
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [dateRange, setDateRange] = useState<[string, string]>([
    getDateString(-2), // 默认3天前
    getDateString(0), // 今天
  ]);

  // 获取任务列表数据
  const fetchSessions = async (page: number = currentPage) => {
    if (!agentConfigVersionId) {
      toast.error("缺少必要参数：agent_config_version_id");
      return;
    }

    try {
      setLoading(true);
      const response = await apiClient.ListUserSessions({
        page_num: page,
        page_size: pageSize,
        agent_config_version_id: agentConfigVersionId,
        start_time: dateRange[0],
        end_time: dateRange[1],
        ...(selectedStatus && { status: selectedStatus as SessionStatus }),
      });

      setSessions(response.sessions || []);
      updatePaginationState(
        Number(response.total),
        setTotalPages,
        setTotalCount
      );
    } catch (error) {
      console.error("获取任务列表失败:", error);
      toast.error("获取任务列表失败");
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchSessions();
  }, [currentPage, agentConfigVersionId]);

  // 筛选条件变化时重新获取数据
  const handleFilterChange = () => {
    setCurrentPage(1);
    fetchSessions(1);
  };

  // 分页处理
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 返回上一页
  const handleGoBack = () => {
    router.back();
  };

  const domain = `https://aime${
    getEnv() === Envs.ONLINE ? "" : "-boe"
  }.bytedance.net`;

  // 表格列定义
  const columns = [
    {
      header: "任务ID",
      accessor: (session: UserSession) => (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="link"
                className="p-0 h-auto text-blue-600 hover:text-blue-800"
                // onClick={(e) => {
                //   e.stopPropagation();
                //   window.open(`${domain}/chat/${session.id}`, '_blank');
                // }}
              >
                {session.id}
                {/* <ExternalLink className="h-3 w-3 ml-1" /> */}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>完整ID: {session.id}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ),
      className: "w-[200px]",
    },
    {
      header: "状态",
      accessor: (session: UserSession) => (
        <Badge variant={getStatusVariant(session.status)}>
          {getStatusText(session.status)}
        </Badge>
      ),
      className: "w-[100px] text-center",
    },

    {
      header: "创建时间",
      accessor: (session: UserSession) => formatDateTime(session.created_at),
      className: "w-[160px] whitespace-nowrap",
    },
    {
      header: "更新时间",
      accessor: (session: UserSession) => formatDateTime(session.updated_at),
      className: "w-[160px] whitespace-nowrap",
    },
    {
      header: "最后消息时间",
      accessor: (session: UserSession) =>
        session.last_message_at ? formatDateTime(session.last_message_at) : "-",
      className: "w-[160px] whitespace-nowrap",
    },
    {
      header: "操作",
      accessor: (session: UserSession) => (
        <div className="flex justify-center items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 hover:bg-blue-100 text-blue-500"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(`${domain}/chat/${session.id}`, "_blank");
                  }}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>跳转到Aime任务页面</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 hover:bg-green-100 text-green-500"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(
                      `${domain}/lab/trace/session?session_id=${session.id}`,
                      "_blank"
                    );
                  }}
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>查看Session详情</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      ),
      sticky: true,
      className:
        "w-[120px] text-center sticky right-0 bg-white shadow-[-8px_0_16px_-6px_rgba(0,0,0,0.1)]",
    },
  ];

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleGoBack}
            className="h-8 w-8 cursor-pointer"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">线上任务列表</h1>
          </div>
        </div>
      </div>

      {/* 筛选条件 */}
      <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
        <Space size="large">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">状态筛选:</span>
            <Select
              placeholder="选择状态"
              style={{ width: 120 }}
              value={selectedStatus}
              onChange={(value) => setSelectedStatus(value)}
            >
              {statusOptions.map((option) => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">时间范围:</span>
            <RangePicker
              style={{ width: 340 }}
              value={dateRange}
              onChange={(dateString) => {
                if (dateString && dateString.length === 2) {
                  setDateRange([dateString[0], dateString[1]]);
                }
              }}
              onSelect={(valueString, value) => {
                setDates(value);
              }}
              onVisibleChange={(visible) => {
                if (!visible) {
                  setDates([]);
                }
              }}
              disabledDate={(current) => {
                if (dates && dates.length) {
                  const tooLate =
                    dates[0] && Math.abs(current.diff(dates[0], "day")) > 2;
                  const tooEarly =
                    dates[1] && Math.abs(dates[1].diff(current, "day")) > 2;
                  return tooEarly || tooLate;
                }

                return false;
              }}
              format="YYYY-MM-DD HH:mm:ss"
              clearRangeOnReselect
              showTime
            />
          </div>

          <Button onClick={handleFilterChange} disabled={loading}>
            查询
          </Button>
        </Space>

        <div className="text-sm text-gray-500">共 {totalCount} 条记录</div>
      </div>

      {/* 任务列表表格 */}
      <div className="relative">
        <DataTable
          data={sessions}
          columns={columns}
          loading={loading}
          keyField="id"
          noDataMessage="暂无任务数据"
          rowClassName="hover:bg-gray-50"
        />
        {loading && (
          <div className="absolute inset-0 bg-white/50 flex items-center justify-center">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          </div>
        )}
      </div>

      {/* 分页组件 */}
      <PaginationInfo
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalCount={totalCount}
        onPageChange={handlePageChange}
      />
    </div>
  );
}
