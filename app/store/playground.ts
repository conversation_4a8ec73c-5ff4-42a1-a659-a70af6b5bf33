import { atom } from "nanostores";

import { ChatCompletion } from "@/app/bam/aime/namespaces/trace";

interface Tool {
  role: string;
  content: string;
  data: any[];
}

export interface CustomChatData extends ChatCompletion {
  messages: { role: string; content: string }[];
  tools?: Tool[];
}

export const $chatData = atom<CustomChatData | null>(null);

export function genChatData(data: CustomChatData) {
 let tools = data?.messages?.find((item) => item.role === "tools");
  const messages = data?.messages?.filter((item) => item.role !== "tools");
  let resultArray = [];
  try {
    resultArray = JSON.parse(tools?.content || "{}");
  } catch {
    const objects = tools?.content.trim().split(/(?<=\})\s*(?=\{)/) || [];
    resultArray = objects.map((objStr) => JSON.parse(objStr));
  }
  try {
    tools = {
      ...tools,
      data: resultArray,
    } as any;
  } catch (error) {
    console.error(error);
  }
  return {
    ...data,
    messages,
    tools: (tools || []) as Tool[],
  };
}

export const setChatData = (data: CustomChatData) => {
  $chatData.set(genChatData(data));
};

export const CHAT_DATA_STORAGE_KEY = "LLM-ChatData";
