import { atom } from 'nanostores';
import { verifyAuth, getCurrentUser } from '../api/auth';

// ==================== 类型定义 ====================

/**
 * 用户信息类型
 */
export type User = {
  username: string;
  avatarUrl: string;
} | null;

/**
 * 认证结果类型
 */
export type AuthResult = {
  authenticated: boolean; // 是否已认证
  hasPermission: boolean; // 是否有权限
  message: string;        // 消息内容
};

// ==================== 状态原子 ====================

// 认证状态
export const isAuthenticatedAtom = atom<boolean>(false);       // 是否已认证
export const hasPermissionAtom = atom<boolean>(false);         // 是否有权限
export const userAtom = atom<User>(null);                      // 用户信息

// UI状态
export const isLoadingAtom = atom<boolean>(true);              // 是否加载中
export const redirectInProgressAtom = atom<boolean>(false);    // 是否正在重定向
export const authErrorMessageAtom = atom<string>("");          // 认证错误消息

// 缓存状态
export const lastAuthCheckAtom = atom<number>(0);              // 上次认证检查时间

// ==================== 认证方法 ====================

/**
 * 检查用户认证状态
 * @returns 认证结果
 */
export async function checkAuth(): Promise<AuthResult> {
  // 1. 检查是否正在重定向，避免重复检查
  if (redirectInProgressAtom.get()) {
    return getCurrentState();
  }
  
  // 2. 避免重复进入加载状态
  // 如果已经在加载中，且不是初始加载，则返回当前状态
  if (isLoadingAtom.get() && lastAuthCheckAtom.get() > 0) {
    return getCurrentState();
  }

  try {
    // 3. 标记为加载中
    isLoadingAtom.set(true);
    
    // 4. 调用API验证认证状态
    const result = await verifyAuth();
    
    // 5. 更新认证状态
    updateAuthState(result);
    
    // 6. 如果已认证，获取用户信息
    if (result.authenticated) {
      await updateUserInfo();
    } else {
      userAtom.set(null);
    }

    return result;
  } catch (error) {
    console.error("认证检查失败:", error);
    // 认证失败时重置状态
    resetAuthState("认证检查失败");
    return {
      authenticated: false,
      hasPermission: false,
      message: "认证检查失败"
    };
  } finally {
    // 完成加载
    isLoadingAtom.set(false);
  }
}

// ==================== 辅助方法 ====================

/**
 * 获取当前认证状态
 */
function getCurrentState(): AuthResult {
  return {
    authenticated: isAuthenticatedAtom.get(),
    hasPermission: hasPermissionAtom.get(),
    message: authErrorMessageAtom.get()
  };
}

/**
 * 更新认证状态
 */
function updateAuthState(result: AuthResult): void {
  isAuthenticatedAtom.set(result.authenticated);
  hasPermissionAtom.set(result.hasPermission);
  authErrorMessageAtom.set(result.message || "");
  lastAuthCheckAtom.set(Date.now());
}

/**
 * 重置认证状态
 */
function resetAuthState(errorMessage: string): void {
  isAuthenticatedAtom.set(false);
  hasPermissionAtom.set(false);
  authErrorMessageAtom.set(errorMessage);
  userAtom.set(null);
}

/**
 * 更新用户信息
 */
async function updateUserInfo(): Promise<void> {
  const userData = await getCurrentUser();
  userAtom.set(userData);
} 