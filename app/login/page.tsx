"use client";
import { useEffect } from "react";
import { redirectToLogin } from "../api/user";
import { redirectInProgressAtom, checkAuth } from "../store/auth";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";

export default function LoginPage() {
  const router = useRouter();

  useEffect(() => {
    const checkLoginStatus = async () => {
      // 检查认证状态
      const result = await checkAuth();

      // 如果已经认证且有权限，跳转到首页
      if (result.authenticated && result.hasPermission) {
        router.push("/");
        return;
      }

      // 如果已经认证但没有权限，跳转到无权限页面
      if (result.authenticated && !result.hasPermission) {
        router.push("/no-permission");
        return;
      }

      // 未认证，开始登录流程
      redirectInProgressAtom.set(false);
      const timer = setTimeout(() => {
        redirectToLogin();
      }, 5000);

      return () => clearTimeout(timer);
    };

    checkLoginStatus();
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 p-4">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="p-8 bg-white rounded-xl shadow-lg text-center max-w-md w-full border border-gray-100"
      >
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-6 flex justify-center"
        >
          <div className="w-20 h-20 rounded-full border-4 border-gray-200 border-t-green-500 animate-spin"></div>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="text-2xl font-bold mb-4 text-gray-800"
        >
          准备登录中...
        </motion.h2>
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="text-gray-600 mb-8 leading-relaxed"
        >
          正在连接到认证服务，请稍候
        </motion.p>
      </motion.div>
    </div>
  );
}
