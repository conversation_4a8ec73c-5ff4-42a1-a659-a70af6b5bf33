"use client";

import { useStore } from "@nanostores/react";
import { userAtom, isAuthenticated<PERSON>tom, hasPermission<PERSON>tom } from "./store/auth";
import { Box, Flex, Grid, Heading, Text, Card } from "@radix-ui/themes";
import Link from "next/link";

export default function Home() {
  const user = useStore(userAtom);
  const isAuthenticated = useStore(isAuthenticatedAtom);
  const hasPermission = useStore(hasPermissionAtom);

  return (
    <Box className="p-6 max-w-7xl mx-auto">
      {/* 顶部欢迎区域 */}
      <Card className="mb-8 bg-gradient-to-r from-green-50 to-blue-50 border-none">
        <Flex direction="column" gap="3" className="p-4">
          <Heading size="6" className="text-green-800">
            欢迎来到 AIMO Lab
          </Heading>
          <Text size="4" className="text-gray-700">
            AIME Agent 优化实验室 — 探索、优化、评估
          </Text>
          {user && (
            <Text className="text-green-700 font-medium">
              {user.username}，很高兴再次见到您
            </Text>
          )}
        </Flex>
      </Card>

      {/* 功能卡片区域 */}
      <Grid
        columns={{ initial: "1", md: "2", lg: "3" }}
        gap="4"
        className="mb-8"
      >
        <FeaturedCard
          title="提示词工程"
          description="设计和优化AI模型的提示词，提高模型响应质量"
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-8 h-8 text-indigo-500"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"
              />
            </svg>
          }
          link="/prompts"
        />

        <FeaturedCard
          title="智能体评估"
          description="测试和评估AI智能体的性能、准确性和效率"
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-8 h-8 text-emerald-500"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5M9 11.25v1.5M12 9v3.75m3-6v6"
              />
            </svg>
          }
          link="/evaluation"
        />

        <FeaturedCard
          title="跟踪分析"
          description="追踪和分析AI模型的行为和输出模式"
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-8 h-8 text-rose-500"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9.348 14.651a3.75 3.75 0 0 1 0-5.303m5.304 0a3.75 3.75 0 0 1 0 5.303m-7.425 2.122a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.808-3.808-9.98 0-13.789m13.788 0c3.808 3.808 3.808 9.981 0 13.79M12 12h.008v.007H12V12Z"
              />
            </svg>
          }
          link="/trace"
        />
      </Grid>

      {/* 底部状态卡片 */}
      <Card className="mt-6 bg-white">
        <Flex direction="column" gap="2" className="p-4">
          <Heading size="3" className="text-gray-800">
            系统状态
          </Heading>
          <Grid columns="2" gap="3" className="mt-2">
            <StatusItem
              label="登录状态"
              value={isAuthenticated ? "已登录" : "未登录"}
              isPositive={isAuthenticated}
            />
            <StatusItem
              label="权限状态"
              value={hasPermission ? "有权限" : "无权限"}
              isPositive={hasPermission}
            />
          </Grid>
        </Flex>
      </Card>
    </Box>
  );
}

// 功能卡片组件
function FeaturedCard({
  title,
  description,
  icon,
  link,
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  link: string;
}) {
  return (
    <Card className="hover:shadow-lg transition-shadow duration-300 border border-gray-100">
      <Flex direction="column" gap="2" className="p-4">
        <Flex gap="3" align="center">
          <Box>{icon}</Box>
          <Heading size="4">{title}</Heading>
        </Flex>
        <Text className="text-gray-600">{description}</Text>
        <Link
          href={link}
          className="text-blue-600 hover:text-blue-800 font-medium text-sm inline-flex items-center group"
        >
          探索功能
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={2}
            stroke="currentColor"
            className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
            />
          </svg>
        </Link>
      </Flex>
    </Card>
  );
}

// 状态项组件
function StatusItem({
  label,
  value,
  isPositive,
}: {
  label: string;
  value: string;
  isPositive: boolean;
}) {
  return (
    <Flex direction="column" gap="1">
      <Text size="2" className="text-gray-500">
        {label}
      </Text>
      <Flex gap="2" align="center">
        <Box
          className={`w-2 h-2 rounded-full ${
            isPositive ? "bg-green-500" : "bg-red-500"
          }`}
        />
        <Text
          size="3"
          className={isPositive ? "text-green-700" : "text-red-700"}
        >
          {value}
        </Text>
      </Flex>
    </Flex>
  );
}
