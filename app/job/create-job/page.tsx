"use client";

import React, { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { Button, Form, Input, Card, Grid, Typography, Space, Select, Modal, Message, Tag, InputNumber } from "@arco-design/web-react";
import { IconPlus, IconDelete, IconSave, IconEye } from "@arco-design/web-react/icon";
import { createEvaluationJob, getEvaluationCases, EvaluationCase } from "../api/evaluation";
import { apiClient } from "@/app/api/request";
import { useQuery } from "@tanstack/react-query";
import { Model } from "@/app/bam/aime/namespaces/trace";
import { uniq } from "lodash-es";
import { useStore } from "@nanostores/react";
import { userAtom } from "@/app/store/auth";
import EvaluationPreview from "../components/EvaluationPreview";

const { Text } = Typography;
const { Item: FormItem } = Form;
const TextArea = Input.TextArea;

interface ModelConfig {
  key: string;
  value: string;
}

export default function CreateEvaluationJobPage() {
  const router = useRouter();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [cases, setCases] = useState<EvaluationCase[]>([]);
  const [selectedCase, setSelectedCase] = useState<EvaluationCase | null>(null);
  const [modelConfig, setModelConfig] = useState<ModelConfig[]>([]);
  const [casePreviewVisible, setCasePreviewVisible] = useState(false);
  const user = useStore(userAtom);

  // 初始化 modelConfig
  useEffect(() => {
    if (modelConfig.length === 0) {
      setModelConfig([
        { key: 'temperature', value: '0.6' },
        { key: 'max_tokens', value: '32000' },
        { key: 'verbosity', value: 'medium' },
        { key: 'reasoning_effort', value: 'medium' },
      ]);
    }
  }, [modelConfig]);

  // 获取评测用例列表
  useEffect(() => {
    const fetchCases = async () => {
      try {
        const response = await getEvaluationCases(1, 100);
        if (response.code === 200 && response.data) {
          setCases(response.data.data);
        }
      } catch (error) {
        console.error('获取评测用例失败:', error);
        Message.error('获取评测用例失败');
      }
    };

    fetchCases();
  }, []);

  // 获取模型列表
  const { data } = useQuery({
    queryKey: ["list-models"],
    queryFn: () => apiClient.ListModels({}),
    staleTime: Infinity,
  });

  const models = useMemo(() => {
    const models: Model[] = [];
    for (const model of data?.models ?? []) {
      const exists = models?.find((item) => item.type === model?.type);
      if (!exists) {
        models.push(model);
      } else {
        exists?.models?.push(...(model?.models ?? []));
        exists.models = uniq(exists?.models);
      }
    }
    return models;
  }, [data]);

  // 添加配置项
  const addConfigItem = () => {
    setModelConfig(prevConfig => [...prevConfig, { key: '', value: '' }]);
  };

  // 删除配置项
  const removeConfigItem = (index: number) => {
    setModelConfig(prevConfig => {
      if (prevConfig.length > 1) {
        const newConfig = [...prevConfig];
        newConfig.splice(index, 1);
        return newConfig;
      }
      return prevConfig;
    });
  };

  // 更新配置项
  const updateConfigItem = (index: number, field: keyof ModelConfig, value: string) => {
    setModelConfig(prevConfig => {
      const newConfig = [...prevConfig];
      newConfig[index] = { ...newConfig[index], [field]: value };
      return newConfig;
    });
  };

  // 选择评测用例
  const handleCaseChange = (caseId: string) => {
    const selected = cases.find(c => c.id === caseId);
    setSelectedCase(selected || null);
    if (selected) {
      form.setFieldValue('title', `${selected.title} - 评测任务`);
    }
  };

  // 预览评测任务
  const handlePreview = () => {
    setPreviewVisible(true);
  };

  // 预览评测用例
  const handlePreviewCase = () => {
    setCasePreviewVisible(true);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validate();
      setLoading(true);

      // 构建模型配置JSON
      const configObj: Record<string, string> = {};
      modelConfig.forEach(item => {
        if (item.key && item.value) {
          configObj[item.key] = item.value;
        }
      });

      // 构建提交数据，符合API文档要求
      const data = {
        caseId: values.caseId,
        title: values.title,
        model: values.model,
        runCount: values.runCount || 1,
        modelConfig: JSON.stringify(configObj),
        budgetTokens: parseInt(configObj.budget_tokens || '-1'),
        includeThoughts: configObj.include_thoughts === 'true',
        user: user?.username || '' // 添加user字段
      };

      const response = await createEvaluationJob(data);
      if (response.code === 200) {
        Message.success('评测任务创建成功');
        router.push('/job');
      } else {
        Message.error(response.msg || '创建失败');
      }
    } catch (error) {
      console.error('创建评测任务失败:', error);
      Message.error('创建评测任务失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消
  const handleCancel = () => {
    router.back();
  };

  // 初始化表单默认值
  useEffect(() => {
    // 设置表单默认值
    form.setFieldsValue({
      runCount: 1,
      model: 'gemini-2.5-pro' // 设置一个默认模型
    });
  }, [form]);

  return (
    <div className="container mx-auto p-6">
      <Card title="创建评测任务" className="mb-6">
        <Form form={form} layout="vertical" initialValues={{ runCount: 1 }}>
          <Grid.Row gutter={16}>
            <Grid.Col span={12}>
              <FormItem
                label="评测用例"
                field="caseId"
                rules={[{ required: true, message: '请选择评测用例' }]}
              >
                <Select
                  placeholder="请选择评测用例"
                  onChange={handleCaseChange}
                >
                  {cases.map(caseItem => (
                    <Select.Option key={caseItem.id} value={caseItem.id || ''}>
                      {caseItem.title}
                    </Select.Option>
                  ))}
                </Select>
              </FormItem>
            </Grid.Col>
            <Grid.Col span={12}>
              <FormItem
                label="任务标题"
                field="title"
                rules={[{ required: true, message: '请输入任务标题' }]}
              >
                <Input placeholder="请输入任务标题" />
              </FormItem>
            </Grid.Col>
          </Grid.Row>

          <Grid.Row gutter={16}>
            <Grid.Col span={12}>
              <FormItem
                label="模型名称"
                field="model"
                rules={[{ required: true, message: '请选择模型名称' }]}
              >
                <Select
                  placeholder="请选择模型名称"
                  showSearch
                >
                  {models.map((modelGroup, modelIndex) => (
                    <Select.OptGroup
                      label={modelGroup.type}
                      key={`${modelGroup?.type}_${modelIndex}`}
                    >
                      {modelGroup?.models?.map((item, index) => (
                        <Select.Option
                          key={`${modelGroup?.type}_${modelIndex}_${index}`}
                          value={item}
                        >
                          {item}
                        </Select.Option>
                      ))}
                    </Select.OptGroup>
                  ))}
                </Select>
              </FormItem>
            </Grid.Col>
            <Grid.Col span={12}>
              <FormItem
                label="运行次数"
                field="runCount"
                rules={[{ required: true, message: '请输入运行次数' }]}
                initialValue={1}
              >
                <Input 
                  type="number" 
                  placeholder="请输入运行次数，默认为1" 
                  min={1}
                  max={10}
                />
              </FormItem>
            </Grid.Col>
          </Grid.Row>

          <Grid.Row gutter={16}>
            <Grid.Col span={12}>
              <FormItem label="操作">
                <Space>
                  <Button
                    type="outline"
                    size="small"
                    icon={<IconEye />}
                    onClick={handlePreviewCase}
                    disabled={!selectedCase}
                  >
                    预览用例
                  </Button>
                </Space>
              </FormItem>
            </Grid.Col>
          </Grid.Row>

          <div className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <Text className="font-medium">模型配置</Text>
              <Button
                type="outline"
                size="small"
                icon={<IconPlus />}
                onClick={addConfigItem}
              >
                添加配置
              </Button>
            </div>
            {modelConfig.map((item, index) => (
              <Card key={index} className="mb-4">
                <Grid.Row gutter={16}>
                  <Grid.Col span={10}>
                    <FormItem label="配置项" field={`configKey${index}`} initialValue={item.key}>
                      <Select
                        value={item.key}
                        onChange={(value) => updateConfigItem(index, 'key', value)}
                        placeholder="选择或输入配置项"
                        showSearch
                        allowCreate
                      >
                        <Select.Option value="temperature">temperature</Select.Option>
                        <Select.Option value="max_tokens">max_tokens</Select.Option>
                        <Select.Option value="verbosity">verbosity</Select.Option>
                        <Select.Option value="reasoning_effort">reasoning_effort</Select.Option>
                        <Select.Option value="budget_tokens">budget_tokens</Select.Option>
                        <Select.Option value="include_thoughts">include_thoughts</Select.Option>
                      </Select>
                    </FormItem>
                  </Grid.Col>
                  <Grid.Col span={10}>
                    <FormItem label="配置值" field={`configValue${index}`} initialValue={
                      item.key === 'temperature' ? '0.6' : 
                      item.key === 'max_tokens' ? '32000' : 
                      item.key === 'verbosity' ? 'medium' : 
                      item.key === 'reasoning_effort' ? 'medium' :
                      item.key === 'budget_tokens' ? '10000' :
                      item.key === 'include_thoughts' ? 'false' : item.value
                    }>
                      {item.key === 'temperature' ? (
                        <InputNumber
                          value={parseFloat(item.value) || 0.6}
                          onChange={(value) => updateConfigItem(index, 'value', (value || 0.6).toString())}
                          placeholder="如 0.6"
                          step={0.1}
                          min={0}
                          max={1}
                          defaultValue={0.6}
                        />
                      ) : item.key === 'max_tokens' ? (
                        <InputNumber
                          value={parseInt(item.value) || 4096}
                          onChange={(value) => updateConfigItem(index, 'value', (value || 32000).toString())}
                          placeholder="如 4096"
                          step={1}
                          min={1}
                          max={1000000}
                          defaultValue={32000}
                        />
                      ) : item.key === 'budget_tokens' ? (
                        <InputNumber
                          value={parseInt(item.value) || 10000}
                          onChange={(value) => updateConfigItem(index, 'value', (value || 10000).toString())}
                          placeholder="如 10000"
                          step={1}
                          min={1}
                          max={1000000}
                          defaultValue={10000}
                        />
                      ) : item.key === 'include_thoughts' ? (
                        <Select
                          value={item.value}
                          onChange={(value) => updateConfigItem(index, 'value', value)}
                          placeholder="是否包含思考过程"
                          defaultValue="false"
                        >
                          <Select.Option value="true">是</Select.Option>
                          <Select.Option value="false">否</Select.Option>
                        </Select>
                      ) : item.key === 'verbosity' ? (
                        <div>
                          <Select
                            value={item.value}
                            onChange={(value) => updateConfigItem(index, 'value', value)}
                            placeholder="选择 verbosity"
                            defaultValue="medium"
                          >
                            <Select.Option value="low">low</Select.Option>
                            <Select.Option value="medium">medium</Select.Option>
                            <Select.Option value="high">high</Select.Option>
                          </Select>
                          <div className="text-xs text-gray-500 mt-1">
                            此配置仅在 gpt-5 模型下生效
                          </div>
                        </div>
                      ) : item.key === 'reasoning_effort' ? (
                        <div>
                          <Select
                            value={item.value}
                            onChange={(value) => updateConfigItem(index, 'value', value)}
                            placeholder="选择 reasoning effort"
                            defaultValue="medium"
                          >
                            <Select.Option value="minimal">minimal</Select.Option>
                            <Select.Option value="low">low</Select.Option>
                            <Select.Option value="medium">medium</Select.Option>
                            <Select.Option value="high">high</Select.Option>
                          </Select>
                          <div className="text-xs text-gray-500 mt-1">
                            此配置仅在 gpt-5 模型下生效
                          </div>
                        </div>
                      ) : (
                        <Input
                          value={item.value}
                          onChange={(value) => updateConfigItem(index, 'value', value)}
                          placeholder="输入配置值"
                        />
                      )}
                    </FormItem>
                  </Grid.Col>
                  <Grid.Col span={4} className="flex items-end">
                    <Button
                      type="outline"
                      status="danger"
                      size="small"
                      icon={<IconDelete />}
                      onClick={() => removeConfigItem(index)}
                      disabled={modelConfig.length <= 1}
                    >
                      删除
                    </Button>
                  </Grid.Col>
                </Grid.Row>
              </Card>
            ))}
          </div>

          <div className="flex justify-end space-x-4">
            <Button onClick={handleCancel}>取消</Button>
            <Button onClick={handlePreview}>预览</Button>
            <Button
              type="primary"
              icon={<IconSave />}
              onClick={handleSubmit}
              loading={loading}
            >
              保存
            </Button>
          </div>
        </Form>
      </Card>

      {/* 预览模态框 */}
      <Modal
        title="评测任务预览"
        visible={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        style={{ width: '80%' }}
      >
        <Card>
          <div className="mb-4">
            <Text className="font-medium">任务标题：</Text>
            <Text>{form.getFieldValue('title')}</Text>
          </div>
          
          <div className="mb-4">
            <Text className="font-medium">评测用例：</Text>
            <Text>{selectedCase?.title}</Text>
          </div>
          
          <div className="mb-4">
            <Text className="font-medium">模型：</Text>
            <Text>{form.getFieldValue('model')}</Text>
          </div>
          
          <div className="mb-4">
            <Text className="font-medium">运行次数：</Text>
            <Text>{form.getFieldValue('runCount') || 1}</Text>
          </div>
          
          <div>
            <Text className="font-medium">模型配置：</Text>
            <div className="mt-2 p-3 bg-gray-50 rounded">
              <pre className="whitespace-pre-wrap text-sm">
                {JSON.stringify(
                  modelConfig.reduce((acc, item) => {
                    if (item.key && item.value) {
                      acc[item.key] = item.value;
                    }
                    return acc;
                  }, {} as Record<string, string>),
                  null,
                  2
                )}
              </pre>
            </div>
          </div>
        </Card>
      </Modal>

      {/* 评测用例预览模态框 */}
      <Modal
        title="评测用例预览"
        visible={casePreviewVisible}
        onCancel={() => setCasePreviewVisible(false)}
        footer={null}
        style={{ width: '80%' }}
      >
        {selectedCase && (
          <EvaluationPreview 
            evaluationCase={selectedCase}
            showId={true}
            showUser={true}
            showCreateTime={true}
          />
        )}
      </Modal>
    </div>
  );
}