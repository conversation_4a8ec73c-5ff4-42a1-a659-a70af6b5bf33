"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button, Form, Input, Card, Typography, Modal, Select, Tag, Message, Upload, Space, Collapse } from "@arco-design/web-react";
import { IconPlus, IconDelete, IconSave, IconArrowLeft, IconUpload, IconDownload } from "@arco-design/web-react/icon";
import { getEvaluationCaseById, updateEvaluationCase, createEvaluationCase, QueryItem, GtRule, EvaluationTask, Tool, FunctionCall, MultiContent } from "../api/evaluation";
import { useStore } from "@nanostores/react";
import { userAtom } from "@/app/store/auth";
import EvaluationPreview from "@/app/job/components/EvaluationPreview";
import { FunctionManager } from "@/app/playground/chat2/components/FunctionManager";
import { CHAT_DATA_STORAGE_KEY } from "@/app/store/playground";

const { Text } = Typography;
const { Item: FormItem } = Form;
const TextArea = Input.TextArea;
const { Item: CollapsePanel } = Collapse;

interface EvaluationTaskItem extends EvaluationTask {
  title: string;
}

export default function EvaluationCaseFormPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const caseId = searchParams.get('id');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [evaluationTasks, setEvaluationTasks] = useState<EvaluationTaskItem[]>([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [jsonUploadVisible, setJsonUploadVisible] = useState(false);
  const user = useStore(userAtom);

  // 获取评测用例数据（编辑模式）
  useEffect(() => {
    // 检查URL参数中是否有isFromCache=true
    const isFromCache = searchParams.get('isFromCache') === 'true';
    
    // // 如果存在isFromCache参数，从URL中移除它
    // if (searchParams.has('isFromCache')) {
    //   const newSearchParams = new URLSearchParams(searchParams);
    //   newSearchParams.delete('isFromCache');
    //   const newUrl = `${window.location.pathname}?${newSearchParams.toString()}`;
    //   window.history.replaceState(null, '', newUrl);
    // }
    
    if (caseId) {
      setIsEditMode(true);
      const fetchCase = async () => {
        try {
          const response = await getEvaluationCaseById(caseId);
          if (response.code === 200 && response.data) {
            const data = response.data;
            const formValues: Record<string, string> = {
              title: data.title,
            };
            
            // 设置表单字段值
            data.evaluationTasks?.forEach((task: EvaluationTaskItem, taskIndex: number) => {
              task.query.forEach((query: QueryItem, queryIndex: number) => {
                formValues[`queryRole${taskIndex}_${queryIndex}`] = query.role;
                formValues[`queryContent${taskIndex}_${queryIndex}`] = query.content;
              });
              
              task.gtRules.forEach((rule: GtRule, ruleIndex: number) => {
                formValues[`rule${taskIndex}_${ruleIndex}`] = rule.rule;
                formValues[`result${taskIndex}_${ruleIndex}`] = rule.result;
              });
            });
            
            form.setFieldsValue(formValues);
            // 为每个任务设置标题
            const tasksWithTitles = (data.evaluationTasks || []).map((task: EvaluationTaskItem, index: number) => ({
              ...task,
              title: `用例 ${index + 1}`
            }));
            // 直接使用 tasksWithTitles，因为它已经包含了从聊天数据解析出来的 query
            setEvaluationTasks(tasksWithTitles);
          } else {
            Message.error(response.msg ?? '获取评测用例失败');
            router.push('/job/cases');
          }
        } catch (error) {
          console.error('获取评测用例失败:', error);
          Message.error('获取评测用例失败');
          router.push('/job/cases');
        }
      };

      fetchCase();
    } else if (isFromCache) {
      setIsEditMode(false);
      // 从缓存中读取聊天数据
      const chatEvaluationData = localStorage.getItem(CHAT_DATA_STORAGE_KEY);
      if (chatEvaluationData) {
        try {
          // 解析聊天数据
          const chatDataArray = JSON.parse(chatEvaluationData);
          // 获取第一个聊天组的数据
          const firstChatGroup = Array.isArray(chatDataArray) && chatDataArray.length > 0 ? chatDataArray[0] : null;
          
          if (firstChatGroup && firstChatGroup.data && firstChatGroup.data.messages) {

            // 生成日期字符串 (年月日)
            const today = new Date();
            const dateStr = `${today.getFullYear()}${(today.getMonth() + 1).toString().padStart(2, '0')}${today.getDate().toString().padStart(2, '0')}`;
            
            // 生成评测集标题
            const title = `${dateStr}-单case评测`;
            
            // 解析消息内容的函数
            const extractContentAndToolCalls = (input: string) => {
              const contentRegex = /<content>([\s\S]*?)<\/content>/;
              const toolCallsRegex = /<tool_calls>([\s\S]*?)<\/tool_calls>/;
              
              let content = input;
              let toolCalls = null;
              
              // 提取content标签内容
              const contentMatch = input.match(contentRegex);
              if (contentMatch) {
                content = contentMatch[1].trim();
              }
              
              // 提取tool_calls标签内容
              const toolCallsMatch = input.match(toolCallsRegex);
              if (toolCallsMatch) {
                try {
                  toolCalls = JSON.parse(toolCallsMatch[1].trim());
                } catch (error) {
                  console.error('解析tool_calls失败:', error);
                }
              }
              
              // 如果没有content标签但有tool_calls标签，清理掉tool_calls标签
              if (toolCallsMatch && !contentMatch) {
                content = input.replace(toolCallsRegex, '').trim();
              }
              
              return { content, toolCalls };
            };
            
            // 转换聊天数据为评测任务格式
            const evaluationTasks = [{
              title: '用例 1',
              query: firstChatGroup.data.messages.map((msg: any) => {
                let messageContent = msg.content;
                let messageToolCalls = msg.tool_calls;
                
                // 如果content是字符串，尝试解析其中的标签
                if (typeof messageContent === 'string') {
                  const { content, toolCalls } = extractContentAndToolCalls(messageContent);
                  messageContent = content;
                  if (toolCalls && !messageToolCalls) {
                    messageToolCalls = toolCalls;
                  }
                }
                
                return {
                  role: msg.role,
                  content: messageContent || ( (msg.multi_content && msg.multi_content.length > 0 ? JSON.stringify(msg.multi_content, null, 2) : '')) || null,
                  tool_calls: messageToolCalls || null,
                  tool_call_id: msg.tool_call_id || null
                };
              }),
              gtRules: [],
              tools: Array.isArray((firstChatGroup.data.tools as any)?.data) 
                ? ((firstChatGroup.data.tools as any)?.data || []).map((tool: any) => ({
                    type: tool.type,
                    function: {
                      name: tool.function?.name || '',
                      description: tool.function?.description || '',
                      arguments: typeof tool.function?.parameters === 'string' ? tool.function?.parameters : JSON.stringify(tool.function?.parameters || {})
                    }
                  }))
                : [],
            }];
            
            // 设置表单字段值
            const formValues: Record<string, string> = {
              title: title,
            };
            
            // 设置每个任务的查询项和规则项到表单
            evaluationTasks.forEach((task: EvaluationTaskItem, taskIndex: number) => {
              task.query.forEach((query: QueryItem, queryIndex: number) => {
                formValues[`queryRole${taskIndex}_${queryIndex}`] = query.role;
                formValues[`queryContent${taskIndex}_${queryIndex}`] = query.content;
                if (query.tool_call_id) {
                  formValues[`toolCallId${taskIndex}_${queryIndex}`] = query.tool_call_id;
                }
              });
              
              task.gtRules.forEach((rule: GtRule, ruleIndex: number) => {
                formValues[`rule${taskIndex}_${ruleIndex}`] = rule.rule;
                formValues[`result${taskIndex}_${ruleIndex}`] = rule.result;
              });
            });
            
            form.setFieldsValue(formValues);
            
            // 为每个任务设置标题
            const tasksWithTitles = (evaluationTasks || []).map((task: EvaluationTaskItem, index: number) => ({
              ...task,
              title: `用例 ${index + 1}`
            }));
            // 直接使用 tasksWithTitles，因为它已经包含了从聊天数据解析出来的 query
            setEvaluationTasks(tasksWithTitles);
          } 
        } catch (error) {
          console.error('解析聊天评测数据失败:', error);
          Message.error('解析聊天数据失败');
        }
      } 
    } else {
      setIsEditMode(false);
      // 创建模式下，检查是否有从聊天页面传递过来的数据
      const chatEvaluationData = localStorage.getItem(CHAT_DATA_STORAGE_KEY);
      if (chatEvaluationData) {
        try {
          const evaluationData = JSON.parse(chatEvaluationData);
          
          // 设置表单字段值
          const formValues: Record<string, string> = {
            title: evaluationData.title || '',
          };
          
          // 设置每个任务的查询项和规则项到表单
          evaluationData.evaluationTasks?.forEach((task: EvaluationTaskItem, taskIndex: number) => {
            task.query.forEach((query: QueryItem, queryIndex: number) => {
              formValues[`queryRole${taskIndex}_${queryIndex}`] = query.role;
              formValues[`queryContent${taskIndex}_${queryIndex}`] = query.content;
              if (query.tool_call_id) {
                formValues[`toolCallId${taskIndex}_${queryIndex}`] = query.tool_call_id;
              }
            });
            
            task.gtRules.forEach((rule: GtRule, ruleIndex: number) => {
              formValues[`rule${taskIndex}_${ruleIndex}`] = rule.rule;
              formValues[`result${taskIndex}_${ruleIndex}`] = rule.result;
            });
          });
          
          form.setFieldsValue(formValues);
          
          // 为每个任务设置标题
          const tasksWithTitles = (evaluationData.evaluationTasks || []).map((task: EvaluationTaskItem, index: number) => ({
            ...task,
            title: `用例 ${index + 1}`
          }));
          // 直接使用 tasksWithTitles，因为它已经包含了从聊天数据解析出来的 query
          setEvaluationTasks(tasksWithTitles);
          
        } catch (error) {
          console.error('解析聊天评测数据失败:', error);
          Message.error('解析聊天数据失败');
          // 设置默认空表单
          form.setFieldsValue({});
          setEvaluationTasks([{ 
            title: '用例 1',
            query: [{ role: 'user', content: '' }], 
            gtRules: [{ rule: 'gt_model', result: '' }] 
          }]);
        }
      } else {
        // 没有从聊天页面传递过来的数据，设置默认空表单
        form.setFieldsValue({});
        setEvaluationTasks([{ 
          title: '用例 1',
          query: [{ role: 'user', content: '' }], 
          gtRules: [{ rule: 'gt_model', result: '' }] 
        }]);
      }
    }
  }, [caseId, form, router, searchParams]);

  // 添加评测用例
  const addEvaluationTask = () => {
    const newIndex = evaluationTasks.length;
    const newTask = { 
      title: `用例 ${newIndex + 1}`,
      query: [{ role: 'user', content: '' }], 
      gtRules: [] 
    };
    
    setEvaluationTasks(prevTasks => [...prevTasks, newTask]);
    
    // 获取当前表单值
    const currentFormValues = form.getFieldsValue();
    
    // 设置表单字段值，保留已有字段值
    const formValues: Record<string, string> = { ...currentFormValues };
    formValues[`queryRole${newIndex}_0`] = 'user';
    formValues[`queryContent${newIndex}_0`] = '';
    // formValues[`rule${newIndex}_0`] = '';
    // formValues[`result${newIndex}_0`] = '';
    
    form.setFieldsValue(formValues);
  };

  // 删除评测用例
  const removeEvaluationTask = (taskIndex: number) => {
    setEvaluationTasks(prevTasks => {
        const newTasks = [...prevTasks];
        newTasks.splice(taskIndex, 1);
        // 重新分配标题
        return newTasks.map((task, index) => ({
          ...task,
          title: `用例 ${index + 1}`
        }));
    });
  };

  // 添加查询项
  const addQueryItem = (taskIndex: number) => {
    setEvaluationTasks(prevTasks => {
      const newTasks = [...prevTasks];
      newTasks[taskIndex] = {
        ...newTasks[taskIndex],
        query: [...newTasks[taskIndex].query, { role: 'user', content: '' }]
      };
      return newTasks;
    });
  };

  // 删除查询项
  const removeQueryItem = (taskIndex: number, queryIndex: number) => {
    setEvaluationTasks(prevTasks => {
      const newTasks = [...prevTasks];
      if (newTasks[taskIndex].query.length > 1) {
        newTasks[taskIndex] = {
          ...newTasks[taskIndex],
          query: newTasks[taskIndex].query.filter((_, idx) => idx !== queryIndex)
        };
      }
      return newTasks;
    });
  };

  // 更新查询项
  const updateQueryItem = (taskIndex: number, queryIndex: number, field: keyof QueryItem, value: string) => {
    setEvaluationTasks(prevTasks => {
      const newTasks = [...prevTasks];
      newTasks[taskIndex] = {
        ...newTasks[taskIndex],
        query: newTasks[taskIndex].query.map((item, idx) => 
          idx === queryIndex ? { ...item, [field]: value } : item
        )
      };
      return newTasks;
    });
  };

  // 添加规则
  const addGtRule = (taskIndex: number) => {
    setEvaluationTasks(prevTasks => {
      const newTasks = [...prevTasks];
      newTasks[taskIndex] = {
        ...newTasks[taskIndex],
        gtRules: [...newTasks[taskIndex].gtRules, { rule: 'gt_model', result: '' }]
      };
      return newTasks;
    });
  };

  // 删除规则
  const removeGtRule = (taskIndex: number, ruleIndex: number) => {
    setEvaluationTasks(prevTasks => {
      const newTasks = [...prevTasks];
       newTasks[taskIndex] = {
          ...newTasks[taskIndex],
          gtRules: newTasks[taskIndex].gtRules.filter((_, idx) => idx !== ruleIndex)
        };
      return newTasks;
    });
  };

  // 更新规则
  const updateGtRule = (taskIndex: number, ruleIndex: number, field: keyof GtRule, value: string) => {
    setEvaluationTasks(prevTasks => {
      const newTasks = [...prevTasks];
      newTasks[taskIndex] = {
        ...newTasks[taskIndex],
        gtRules: newTasks[taskIndex].gtRules.map((rule, idx) => 
          idx === ruleIndex ? { ...rule, [field]: value } : rule
        )
      };
      return newTasks;
    });
  };

  

  // 添加工具调用
  const addToolCall = (taskIndex: number, queryIndex: number) => {
    setEvaluationTasks(prevTasks => {
      const newTasks = [...prevTasks];
      const newToolCall: Tool = {
        type: 'function',
        function: {
          name: '',
          description: '',
          arguments: '{}'
        }
      };
      
      newTasks[taskIndex] = {
        ...newTasks[taskIndex],
        query: newTasks[taskIndex].query.map((item, idx) => 
          idx === queryIndex ? { 
            ...item, 
            tool_calls: [...(item.tool_calls || []), newToolCall] 
          } : item
        )
      };
      return newTasks;
    });
  };

  // 删除工具调用
  const removeToolCall = (taskIndex: number, queryIndex: number, toolCallIndex: number) => {
    setEvaluationTasks(prevTasks => {
      const newTasks = [...prevTasks];
      const queryItem = newTasks[taskIndex].query[queryIndex];
      
      if (queryItem.tool_calls && queryItem.tool_calls.length > 1) {
        newTasks[taskIndex] = {
          ...newTasks[taskIndex],
          query: newTasks[taskIndex].query.map((item, idx) => 
            idx === queryIndex ? { 
              ...item, 
              tool_calls: item.tool_calls?.filter((_, tcIdx) => tcIdx !== toolCallIndex) 
            } : item
          )
        };
      }
      return newTasks;
    });
  };

  // 更新工具调用
  const updateToolCall = (taskIndex: number, queryIndex: number, toolCallIndex: number, field: keyof Tool, value: any) => {
    setEvaluationTasks(prevTasks => {
      const newTasks = [...prevTasks];
      const queryItem = newTasks[taskIndex].query[queryIndex];
      
      if (!queryItem.tool_calls) return newTasks;
      
      const updatedToolCalls = [...queryItem.tool_calls];
      if (field === 'function') {
        // 处理函数字段的更新
        // value 应该是一个对象，包含要更新的函数字段
        const functionUpdate = typeof value === 'string' ? JSON.parse(value) : value;
        updatedToolCalls[toolCallIndex] = {
          ...updatedToolCalls[toolCallIndex],
          function: {
            ...updatedToolCalls[toolCallIndex].function,
            ...functionUpdate
          }
        };
      } else {
        // 处理其他字段的更新
        updatedToolCalls[toolCallIndex] = {
          ...updatedToolCalls[toolCallIndex],
          [field]: value
        };
      }
      
      newTasks[taskIndex] = {
        ...newTasks[taskIndex],
        query: newTasks[taskIndex].query.map((item, idx) => 
          idx === queryIndex ? { ...item, tool_calls: updatedToolCalls } : item
        )
      };
      return newTasks;
    });
  };

  // 添加工具响应
  const addToolResponse = (taskIndex: number, queryIndex: number) => {
    setEvaluationTasks(prevTasks => {
      const newTasks = [...prevTasks];
      
      // 在当前查询项后添加一个工具响应
      const newQuery: QueryItem = {
        role: 'tool',
        content: '',
        tool_call_id: ''
      };
      
      const newQueryList = [...newTasks[taskIndex].query];
      newQueryList.splice(queryIndex + 1, 0, newQuery);
      
      newTasks[taskIndex] = {
        ...newTasks[taskIndex],
        query: newQueryList
      };
      return newTasks;
    });
  };

  // 处理JSON导入
  const handleJsonImport = (jsonString: string) => {
    try {
      const jsonData = JSON.parse(jsonString);
      
      // 验证JSON格式
      if (!Array.isArray(jsonData)) {
        Message.error('JSON格式错误：需要是评测任务数组');
        return;
      }
      
      // 转换数据格式
      const newTasks = jsonData.map((task, index) => {
        // 确保每个任务都有必要的字段
        const newTask: EvaluationTaskItem = {
          title: task.title || `用例 ${index + 1}`,
          query: task.query || [],
          gtRules: task.gtRules || task.rules || [],
          tools: task.tools || []
        };
        
        // 处理查询项
        newTask.query = newTask.query.map(q => ({
          role: q.role ?? 'user',
          content: q.content || (q.multi_content && q.multi_content.length > 0 ? JSON.stringify(q.multi_content, null, 2) : '') || "",
          tool_calls: q.tool_calls || [],
          tool_call_id: q.tool_call_id || ''
        }));
        
        // 处理规则项
        newTask.gtRules = newTask.gtRules.map(r => ({
          rule: r.rule === '' ? 'gt_model' : r.rule,
          result: r.result ?? ''
        }));
        
        // 处理工具项
        if (newTask.tools) {
          newTask.tools = newTask.tools.map(t => ({
            id: t.id || `tool_${Date.now()}_${Math.random()}`,
            type: t.type || 'function',
            function: {
              name: t.function?.name || '',
              description: t.function?.description || '',
              arguments: typeof t.function?.arguments === 'string' ? t.function?.arguments : JSON.stringify(t.function?.arguments || {})
            }
          }));
        }
        
        return newTask;
      });
      
      // 创建表单字段值
      const formValues: Record<string, string> = {};
      
      // 设置标题字段值
      formValues['title'] = form.getFieldValue('title') || '';
      
      // 设置每个任务的查询项和规则项到表单
      newTasks.forEach((task, taskIndex) => {
        task.query.forEach((query, queryIndex) => {
          formValues[`queryRole${taskIndex}_${queryIndex}`] = query.role;
          formValues[`queryContent${taskIndex}_${queryIndex}`] = query.content;
          if (query.tool_call_id) {
            formValues[`toolCallId${taskIndex}_${queryIndex}`] = query.tool_call_id;
          }
        });
        
        task.gtRules.forEach((rule, ruleIndex) => {
          formValues[`rule${taskIndex}_${ruleIndex}`] = rule.rule;
          formValues[`result${taskIndex}_${ruleIndex}`] = rule.result;
        });
      });
      
      // 更新表单字段值
      form.setFieldsValue(formValues);
      
      // 更新任务状态
      setEvaluationTasks(newTasks);
      setJsonUploadVisible(false);
      Message.success('JSON导入成功');
    } catch (error) {
      console.error('JSON解析错误:', error);
      Message.error('JSON解析失败，请检查JSON格式是否正确');
    }
  };

  // 导出为JSON
  const handleExportJson = () => {
    // 导出时包含标题字段
    const exportData = evaluationTasks.map(task => ({
      title: task.title,
      query: task.query,
      gtRules: task.gtRules,
      tools: task.tools
    }));
    
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = 'evaluation_tasks.json';
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  // 预览评测集
  const handlePreview = () => {
    setPreviewVisible(true);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validate();
      setLoading(true);

      // 构建提交数据
      const data = {
        title: values.title,
        evaluationTasks: evaluationTasks,
        user: user?.username || '' // 添加user字段
      };

      let response;
      if (isEditMode && caseId) {
        // 编辑模式
        response = await updateEvaluationCase(caseId, data);
        if (response.code === 200) {
          Message.success('评测集更新成功');
        } else {
          Message.error(response.msg ?? '更新失败');
          return;
        }
      } else {
        // 创建模式
        response = await createEvaluationCase(data);
        if (response.code === 200) {
          Message.success('评测集创建成功');
        } else {
          Message.error(response.msg ?? '创建失败');
          return;
        }
      }
      
      router.push('/job/cases');
    } catch (error) {
      console.error(isEditMode ? '更新评测集失败:' : '创建评测集失败:', error);
      Message.error(isEditMode ? '更新评测集失败' : '创建评测集失败');
    } finally {
      setLoading(false);
    }
  };

  // 返回列表
  const handleBack = () => {
    router.push('/job/cases');
  };

  return (
    <div className="w-full p-4">
      <Card 
        title={
          <div className="flex items-center">
            <Button 
              type="text" 
              icon={<IconArrowLeft />} 
              onClick={handleBack}
              className="mr-2"
            >
              返回
            </Button>
            <span>{isEditMode ? '编辑评测集' : '创建评测集'}</span>
          </div>
        } 
        className="mb-6"
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="评测集标题"
            field="title"
            rules={[{ required: true, message: '请输入评测集标题' }]}
          >
            <Input placeholder="请输入评测集标题" />
          </FormItem>

          <div className="mb-6">
            <div className="flex justify-end items-center mb-4">
             {evaluationTasks.length > 0 && <Text className="font-medium mr-auto">评测用例</Text>} 
              <Space>
                {evaluationTasks.length > 0 && (
                  <Button
                    type="outline"
                    size="small"
                    icon={<IconDownload />}
                    onClick={handleExportJson}
                  >
                    导出JSON用例
                  </Button>
                )}
                <Button
                  type="outline"
                  size="small"
                  icon={<IconUpload />}
                  onClick={() => setJsonUploadVisible(true)}
                >
                  JSON导入
                </Button>
                <Button
                  type="outline"
                  size="small"
                  icon={<IconPlus />}
                  onClick={addEvaluationTask}
                >
                  手动添加用例
                </Button>
              </Space>
            </div>
            
            {evaluationTasks.map((task, taskIndex) => (
              <Card key={taskIndex} className="mb-6">
                <Collapse defaultActiveKey={[]} className="mb-4">
                  <CollapsePanel header={task.title} key={`task-${taskIndex}`} name={`task-${taskIndex}`}>
                    <div className="flex justify-between items-center mb-4">
                      <Text className="font-medium">{task.title}</Text>
                      <Button
                        type="outline"
                        status="danger"
                        size="small"
                        icon={<IconDelete />}
                        onClick={() => removeEvaluationTask(taskIndex)}
                        // disabled={evaluationTasks.length <= 1}
                      >
                        删除用例
                      </Button>
                    </div>
                
                    <div className="mb-4">
                      <div className="flex justify-between items-center mb-4">
                        <Text className="font-medium">查询内容</Text>
                        <Button
                          type="outline"
                          size="small"
                          icon={<IconPlus />}
                          onClick={() => addQueryItem(taskIndex)}
                        >
                          添加查询
                        </Button>
                      </div>
                      {task.query.map((item, queryIndex) => (
                        <Card key={queryIndex} className="mb-4">
                          <div className="grid grid-cols-12 gap-4 mb-4">
                            <div className="col-span-11 flex items-center">
                              <div className="flex items-center space-x-2 w-full">
                                <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">角色</label>
                                <Select
                                  value={item.role}
                                  onChange={(value) => updateQueryItem(taskIndex, queryIndex, 'role', value)}
                                  className="flex-1"
                                >
                                  <Select.Option value="user">用户</Select.Option>
                                  <Select.Option value="assistant">助手</Select.Option>
                                  <Select.Option value="system">系统</Select.Option>
                                  <Select.Option value="tool">工具</Select.Option>
                                </Select>
                              </div>
                            </div>
                            <div className="col-span-1 flex items-center justify-center">
                              <Button
                                type="outline"
                                status="danger"
                                size="small"
                                icon={<IconDelete />}
                                onClick={() => removeQueryItem(taskIndex, queryIndex)}
                                disabled={task.query.length <= 1}
                              >
                                删除
                              </Button>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-12 gap-4">
                            <div className="col-span-12">
                              <FormItem label="内容" field={`queryContent${taskIndex}_${queryIndex}`}>
                                <TextArea
                                  value={item.content}
                                  onChange={(value: string) => updateQueryItem(taskIndex, queryIndex, 'content', value)}
                                  placeholder="请输入查询内容"
                                  autoSize={{ minRows: 3, maxRows: 3 }}
                                />
                              </FormItem>
                              
                              {/* 工具调用ID（仅当角色为tool时显示） */}
                              {item.role === 'tool' && (
                                <FormItem label="工具调用ID" field={`toolCallId${taskIndex}_${queryIndex}`}>
                                  <Input
                                    value={item.tool_call_id || ''}
                                    onChange={(value) => updateQueryItem(taskIndex, queryIndex, 'tool_call_id', value)}
                                    placeholder="请输入工具调用ID"
                                  />
                                </FormItem>
                              )}
                              
                              {/* 工具调用列表（仅当角色为assistant时显示） */}
                              {item.role === 'assistant' && (
                                <div className="mt-2">
                                  <div className="flex justify-between items-center mb-2">
                                    <Text className="text-sm font-medium">工具调用</Text>
                                    <Button
                                      type="outline"
                                      size="mini"
                                      icon={<IconPlus />}
                                      onClick={() => addToolCall(taskIndex, queryIndex)}
                                    >
                                      添加工具调用
                                    </Button>
                                  </div>
                                  
                                  {item.tool_calls?.map((toolCall, toolCallIndex) => (
                                    <Card key={toolCallIndex} className="mb-2 p-2">
                                      <div className="grid grid-cols-12 gap-2">
                                        <div className="col-span-5">
                                          <Input
                                            value={toolCall.function?.name || ''}
                                            onChange={(value) => updateToolCall(taskIndex, queryIndex, toolCallIndex, 'function', { name: value })}
                                            placeholder="函数名称"
                                          />
                                        </div>
                                        <div className="col-span-6">
                                          <TextArea
                                            value={toolCall.function?.arguments || '{}'}
                                            onChange={(value) => updateToolCall(taskIndex, queryIndex, toolCallIndex, 'function', { arguments: value })}
                                            placeholder="函数参数 (JSON格式)"
                                            autoSize={{ minRows: 1, maxRows: 2 }}
                                          />
                                        </div>
                                        <div className="col-span-1 flex items-center justify-center">
                                          <Button
                                            type="outline"
                                            status="danger"
                                            size="mini"
                                            icon={<IconDelete />}
                                            onClick={() => removeToolCall(taskIndex, queryIndex, toolCallIndex)}
                                            disabled={!item.tool_calls || item.tool_calls.length <= 1}
                                          />
                                        </div>
                                      </div>
                                    </Card>
                                  ))}
                                  
                                  {/* 添加工具响应按钮 */}
                                  <Button
                                    type="outline"
                                    size="mini"
                                    onClick={() => addToolResponse(taskIndex, queryIndex)}
                                    className="mt-2"
                                  >
                                    添加工具响应
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>

                    <div>
                      <Collapse defaultActiveKey={[]} className="mb-4">
                        <CollapsePanel header="评测规则" key="rules" name="rules">
                          <div className="flex justify-between items-center mb-4">
                            <Text className="font-medium">规则列表</Text>
                            <Button
                              type="outline"
                              size="small"
                              icon={<IconPlus />}
                              onClick={() => addGtRule(taskIndex)}
                            >
                              添加规则
                            </Button>
                          </div>
                          {task.gtRules.map((rule, ruleIndex) => (
                            <Card key={ruleIndex} className="mb-4">
                              <div className="grid grid-cols-12 gap-4">
                                <div className="col-span-5">
                                  <FormItem label="规则" field={`rule${taskIndex}_${ruleIndex}`} initialValue={rule.rule ?? 'gt_model'}>
                                    <Select
                                      value={rule.rule}
                                      onChange={(value) => updateGtRule(taskIndex, ruleIndex, 'rule', value)}
                                    >
                                      <Select.Option value="gt_model">gt_model</Select.Option>
                                    </Select>
                                  </FormItem>
                                </div>
                                <div className="col-span-6">
                                  <FormItem label="结果" field={`result${taskIndex}_${ruleIndex}`}>
                                    <TextArea
                                      value={rule.result}
                                      onChange={(value: string) => updateGtRule(taskIndex, ruleIndex, 'result', value)}
                                      placeholder="请输入预期结果"
                                      autoSize={{ minRows: 1, maxRows: 1 }}
                                    />
                                  </FormItem>
                                </div>
                                <div className="col-span-1 flex items-center justify-center">
                                  <Button
                                    type="outline"
                                    status="danger"
                                    size="small"
                                    icon={<IconDelete />}
                                    onClick={() => removeGtRule(taskIndex, ruleIndex)}
                                  >
                                    删除
                                  </Button>
                                </div>
                              </div>
                            </Card>
                          ))}
                        </CollapsePanel>
                      </Collapse>
                    </div>

                    {/* 工具配置部分 */}
                    <div>
                      <Collapse defaultActiveKey={[]} className="mb-4">
                        <CollapsePanel header="工具配置" key="tools" name="tools">
                          <FunctionManager 
                            tools={task.tools?.map(tool => ({
                              type: tool.type,
                              function: {
                                name: tool.function?.name || '',
                                description: tool.function?.description || '',
                                strict: false,
                                parameters: tool.function?.arguments || '{}'
                              }
                            })) || []}
                            onChange={(updatedTools) => {
                              const convertedTools = updatedTools.map(tool => ({
                                // id: `tool_${Date.now()}_${Math.random()}`,
                                type: tool.type,
                                function: {
                                  name: tool.function?.name || '',
                                  description: tool.function?.description || '',
                                  arguments: typeof tool.function?.parameters === 'string' ? tool.function?.parameters : JSON.stringify(tool.function?.parameters || {})
                                }
                              }));
                              
                              setEvaluationTasks(prevTasks => {
                                const newTasks = [...prevTasks];
                                newTasks[taskIndex] = {
                                  ...newTasks[taskIndex],
                                  tools: convertedTools
                                };
                                return newTasks;
                              });
                            }}
                          />
                        </CollapsePanel>
                      </Collapse>
                    </div>
                  </CollapsePanel>
                </Collapse>
              </Card>
            ))}
          </div>

          {evaluationTasks.length > 0 &&
            <div className="flex justify-end space-x-4">
              <Button onClick={handleBack}>取消</Button>
              <Button onClick={handlePreview}>预览</Button>
              <Button
                type="primary"
                icon={<IconSave />}
                onClick={handleSubmit}
                loading={loading}
              >
                {isEditMode ? '更新' : '创建'}
              </Button>
            </div>
          } 
        </Form>
      </Card>

      {/* 预览模态框 */}
      <Modal
        title="评测集预览"
        visible={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        style={{ width: '80%' }}
      >
        <EvaluationPreview 
          evaluationCase={{
            title: form.getFieldValue('title'),
            evaluationTasks: evaluationTasks
          }}
        />
      </Modal>

      {/* JSON导入模态框 */}
      <Modal
        title="JSON导入评测任务"
        visible={jsonUploadVisible}
        onCancel={() => setJsonUploadVisible(false)}
        footer={null}
        style={{ width: '60%' }}
      >
        <Card>
          <div className="mb-4">
            <Text className="font-medium">导入说明：</Text>
            <div className="mt-2 p-3 bg-gray-50 rounded">
              <Text>请粘贴JSON格式的评测任务内容。内容应包含评测任务数组，每个任务可包含title（标题）、query和gtRules字段。如果不提供title字段，系统将自动生成"用例 X"格式的标题。</Text>
            </div>
          </div>
          
          <div className="mb-4">
              <TextArea
                placeholder={`[
  {
    "title": "用例标题1",
    "query": [
      {
        "role": "user",
        "content": "查询内容1"
      },
      {
        "role": "assistant",
        "content": "助手回答1",
        "tool_calls": [
          {
            "type": "function",
            "function": {
              "name": "get_weather",
              "description": "获取天气信息",
              "arguments": "{\\"location\\": \\"北京\\"}"
            }
          }
        ]
      },
      {
        "role": "tool",
        "content": "工具返回结果1",
        "tool_call_id": "call_123"
      }
    ],
    "gtRules": [
      {
        "rule": "gt_model",
        "result": "预期结果1"
      }
    ],
    "tools": [
      {
        "id": "weather_tool",
        "type": "function",
        "function": {
          "name": "get_weather",
          "description": "获取指定位置的天气信息",
          "arguments": "{\\"location\\": \\"{location}\\"}"
        }
      }
    ]
  },
  {
    "title": "用例标题2",
    "query": [
      {
        "role": "user",
        "content": "查询内容2"
      }
    ],
    "gtRules": [
      {
        "rule": "gt_model",
        "result": "预期结果2"
      }
    ]
  }
]`}
                autoSize={{ minRows: 8, maxRows: 15 }}
                id="jsonContent"
                style={{ width: '100%' }}
              />
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button onClick={() => setJsonUploadVisible(false)}>
              取消
            </Button>
            <Button
              type="primary"
              onClick={() => {
                const jsonContent = (document.getElementById('jsonContent') as HTMLTextAreaElement)?.value;
                if (jsonContent && jsonContent.trim()) {
                  handleJsonImport(jsonContent);
                } else {
                  Message.error('请输入JSON内容');
                }
              }}
            >
              导入
            </Button>
          </div>
        </Card>
      </Modal>
    </div>
  );
}