import { BASE_URL } from '@/app/experience/api/experience';
import { fetchRequest } from '../../api/request';

// 评测用例相关类型定义
export interface EvaluationCase {
  id?: string;                   // 用例ID
  title: string;                 // 评测标题
  evaluationTasks: EvaluationTask[]; // 评测任务列表，每个任务包含query和gtRules
  createTime?: string;          // 创建时间
  updateTime?: string;          // 更新时间
}

export interface EvaluationTask {
  title: string;                // 任务标题
  query: QueryItem[];           // 评测的query内容
  gtRules: GtRule[];            // 评测的规则
  tools?: Tool[];               // 可选的工具列表
}

export interface QueryItem {
  role: string;                 // 角色
  content: string;              // 内容
  multi_content?: MultiContent[]; // 多模态内容列表
  tool_calls?: Tool[];          // 工具调用列表
  tool_call_id?: string;        // 工具调用ID
}

export interface MultiContent {
  type: string;                 // 内容类型，暂时只支持 "text"
  text: string;                 // 文本内容
}

export interface Tool {
  id?: string;                  // 工具ID
  type: string;                 // 工具类型
  function: FunctionCall;       // 函数调用信息
}

export interface FunctionCall {
  name: string;                 // 函数名称
  description?: string;          // 函数描述
  arguments: string;            // 函数参数，JSON格式字符串
}

export interface GtRule {
  rule: string;                 // 规则
  result: string;               // 结果
}

// 评测任务相关类型定义
export interface EvaluationJob {
  id?: string;                  // 评测任务ID
  caseId: string;               // 用例ID
  title: string;                // 评测任务标题
  model: string;                // 模型名称
  modelConfig: string;          // 模型的其他配置，JSON格式字符串
  runCount?: number;            // 运行次数，默认为1
  result?: string;              // 模型返回的result（多次运行时为汇总结果）
  gtResult?: string[];          // 评测的结果列表（多次运行时为汇总结果）
  gtStatus?: boolean[];         // 评测状态列表，true表示通过，false表示不通过
  createTime?: string;          // 评测任务创建时间
  updateTime?: string;          // 评测任务更新时间
  endTime?: string;             // 评测任务结束时间
  status?: string;              // 任务状态 (pending/running/completed/failed)
  subTasks?: SubTask[];         // 子任务列表，每个子任务对应评测用例中的一个EvaluationTask
}

export interface SubTask {
  taskIndex: number;            // 对应评测用例中的任务索引
  runIndex?: number;            // 运行次数索引，从0开始
  status?: string;              // 子任务状态 (pending/running/completed/failed)
  result?: string;              // 模型返回的结果
  gtResult?: string[];          // 评测结果列表，每个结果对应一条gt_model规则，包含pass、reason和rule信息
  gtStatus?: boolean[];        // 评测状态列表，每个状态对应一条gt_model规则的通过状态
  gtRules?: GtRule[];          // 执行的评测规则内容列表
  modelApiCallTime?: number;   // 模型API调用时间，单位毫秒
  startTime?: string;          // 子任务开始时间
  endTime?: string;            // 子任务结束时间
  errorMsg?: string;           // 错误信息
}

// 分页响应接口
export interface PaginationResponse<T> {
  total: number;
  page: number;
  size: number;
  data: T[];
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  msg?: string;
  data?: T;
}

// 筛选评测任务的参数接口
export interface FilterEvaluationJobParams {
  status?: string;              // 任务状态 (pending/running/completed/failed)
  caseId?: string;              // 用例ID
  user?: string;
  model?: string;               // 模型名称
  title?: string;               // 任务标题
  page?: number;                // 页码
  size?: number;                // 每页大小
}

// 任务状态选项
export const statusOptions = [
  { label: "待处理", value: "pending" },
  { label: "运行中", value: "running" },
  { label: "已完成", value: "completed" },
  { label: "失败", value: "failed" }
];

// 获取状态颜色
export const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'orange';
    case 'running':
      return 'blue';
    case 'completed':
      return 'green';
    case 'failed':
      return 'red';
    default:
      return 'gray';
  }
};

// 获取状态文本
export const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '待处理';
    case 'running':
      return '运行中';
    case 'completed':
      return '已完成';
    case 'failed':
      return '失败';
    default:
      return status;
  }
};

// 获取评测状态文本
export const getGtStatusText = (gtStatus?: boolean) => {
  if (gtStatus === undefined) return '-';
  return gtStatus ? '通过' : '不通过';
};

// 获取评测状态颜色
export const getGtStatusColor = (gtStatus?: boolean) => {
  if (gtStatus === undefined) return 'gray';
  return gtStatus ? 'green' : 'red';
};

// 创建自定义request对象，用于发送请求
const request = {
  get: async (url: string, params?: Record<string, any>) => {
    const queryParams = params ? new URLSearchParams(params).toString() : '';
    const fullUrl = queryParams ? `${url}?${queryParams}` : url;
    return fetchRequest(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  },
  post: async (url: string, data: any) => {
    return fetchRequest(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
  put: async (url: string, data: any) => {
    return fetchRequest(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
  delete: async (url: string, options?: { data?: any }) => {
    return fetchRequest(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: options?.data ? JSON.stringify(options.data) : undefined,
    });
  },
};


// ===== 评测用例管理 =====

// 创建评测用例
export const createEvaluationCase = async (data: Omit<EvaluationCase, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<EvaluationCase>> => {
  return request.post(`${BASE_URL}/api/evaluation-cases`, data);
};

// 获取评测用例详情
export const getEvaluationCaseById = async (id: string): Promise<ApiResponse<EvaluationCase>> => {
  return request.get(`${BASE_URL}/api/evaluation-cases/${id}`);
};

// 获取评测用例列表（分页）
export const getEvaluationCases = async (page: number = 1, size: number = 10): Promise<ApiResponse<PaginationResponse<EvaluationCase>>> => {
  return request.get(`${BASE_URL}/api/evaluation-cases`, { page, size });
};

// 按标题搜索评测用例
export const searchEvaluationCases = async (title: string, page: number = 1, size: number = 10, id?: string,user?:string): Promise<ApiResponse<PaginationResponse<EvaluationCase>>> => {
  const params: any = { page, size };
  if (title) {
    params.title = title;
  }
  if (id) {
    params.id = id;
  }
  if(user){
    params.user = user;
  }
  return request.get(`${BASE_URL}/api/evaluation-cases/search`, params);
};

// 更新评测用例
export const updateEvaluationCase = async (id: string, data: Partial<EvaluationCase>): Promise<ApiResponse<boolean>> => {
  return request.put(`${BASE_URL}/api/evaluation-cases/${id}`, data);
};

// 删除评测用例
export const deleteEvaluationCase = async (id: string): Promise<ApiResponse<boolean>> => {
  if (!id) {
    throw new Error("评测用例ID不能为空");
  }
  return request.delete(`${BASE_URL}/api/evaluation-cases/${id}`);
};

// ===== 评测任务管理 =====

// 创建评测任务
export const createEvaluationJob = async (data: Omit<EvaluationJob, 'id' | 'createTime' | 'updateTime' | 'endTime' | 'status'>): Promise<ApiResponse<EvaluationJob>> => {
  return request.post(`${BASE_URL}/api/evaluation-jobs`, data);
};

// 获取评测任务详情
export const getEvaluationJobById = async (id: string): Promise<ApiResponse<EvaluationJob>> => {
  return request.get(`${BASE_URL}/api/evaluation-jobs/${id}`);
};

// 获取评测任务列表（分页，支持筛选）
export const filterEvaluationJobs = async (params: FilterEvaluationJobParams): Promise<ApiResponse<PaginationResponse<EvaluationJob>>> => {
  // 构建查询参数
  const queryParams: Record<string, any> = {};
  
  if (params.status) queryParams.status = params.status;
  if (params.caseId) queryParams.caseId = params.caseId;
  if (params.model) queryParams.model = params.model;
  if (params.title) queryParams.title = params.title;
  if (params.page) queryParams.page = params.page;
  if (params.size) queryParams.size = params.size;
  
  const url = `${BASE_URL}/api/evaluation-jobs/filter${Object.keys(queryParams).length > 0 ? '?' + new URLSearchParams(queryParams).toString() : ''}`;
  return request.get(url);
};

// 更新评测任务
export const updateEvaluationJob = async (id: string, data: Partial<EvaluationJob>): Promise<ApiResponse<boolean>> => {
  return request.put(`${BASE_URL}/api/evaluation-jobs/${id}`, data);
};

// 删除评测任务
export const deleteEvaluationJob = async (id: string): Promise<ApiResponse<boolean>> => {
  return request.delete(`${BASE_URL}/api/evaluation-jobs/${id}`);
};

// 启动评测任务（异步执行）
export const startEvaluationJob = async (id: string): Promise<ApiResponse<boolean>> => {
  return request.post(`${BASE_URL}/api/evaluation-jobs/${id}/start`, {});
};

// 重启评测任务
export const restartEvaluationJob = async (id: string): Promise<ApiResponse<boolean>> => {
  return request.post(`${BASE_URL}/api/evaluation-jobs/${id}/restart`,{});
};