// URL参数更新工具函数
export const updateURLParams = (params: Record<string, string | number>) => {
  const url = new URL(window.location.href);
  
  // 清除所有现有参数
  url.search = '';
  
  // 添加新的参数
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(item => {
          url.searchParams.append(key, String(item));
        });
      } else {
        url.searchParams.set(key, String(value));
      }
    }
  });
  
  // 更新URL但不刷新页面
  window.history.pushState({}, '', url.toString());
};