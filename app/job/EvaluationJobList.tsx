"use client";

import React, { useState, useMemo, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button, Space, Form, Modal } from "@arco-design/web-react";
import { 
  IconPlus, 
  IconRefresh
} from "@arco-design/web-react/icon";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import ConfirmDeleteDialog from "../experience/components/ConfirmDeleteDialog";
import { 
  deleteEvaluationJob, 
  filterEvaluationJobs,
  FilterEvaluationJobParams,
  EvaluationJob,
  startEvaluationJob,
  restartEvaluationJob
} from "./api/evaluation";
import { SearchForm, EvaluationJobTable, ActionConfirmModal, PaginationControls } from "./components";
import { updateURLParams } from "./utils";

// 扩展搜索参数接口
interface SearchParams {
  caseId?: string;
  model?: string;
  status?: string;
  user?: string;
  title?: string;
}

export function EvaluationJobList() {
  const router = useRouter();
  const urlSearchParams = useSearchParams();
  const [form] = Form.useForm();
  const [searchParams, setSearchParams] = useState<SearchParams>({});

  // 从URL参数初始化搜索条件
  useEffect(() => {
    const params: SearchParams = {};
    
    // 读取URL参数
    const caseId = urlSearchParams.get('caseId');
    const model = urlSearchParams.get('model');
    const status = urlSearchParams.get('status');
    const user = urlSearchParams.get('user');
    const title = urlSearchParams.get('title');
    const page = urlSearchParams.get('page');
    const size = urlSearchParams.get('size');
    
    // 设置参数
    if (caseId) params.caseId = caseId;
    if (model) params.model = model;
    if (status) params.status = status;
    if (user) params.user = user;
    if (title) params.title = title;
    if (page) setCurrentPage(parseInt(page));
    if (size) setPageSize(parseInt(size));
    
    // 更新状态和表单
    setSearchParams(params);
    form.setFieldsValue(params);
  }, [urlSearchParams, form]);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null);
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [actionConfirmVisible, setActionConfirmVisible] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState<'start' | 'restart' | null>(null);
  const [jobToConfirm, setJobToConfirm] = useState<EvaluationJob | null>(null);
  const [batchActionToConfirm, setBatchActionToConfirm] = useState<'batch_start' | 'batch_delete' | null>(null);
  const [batchActionConfirmVisible, setBatchActionConfirmVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);

  // 查询评测任务列表
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["evaluationJobList", searchParams, currentPage, pageSize],
    queryFn: async () => {
      try {
        // 构建查询参数
        const filterParams: FilterEvaluationJobParams = {
          page: currentPage,
          size: pageSize,
        };

        // 添加各种筛选条件
        if (searchParams.caseId) {
          filterParams.caseId = searchParams.caseId;
        }
        if (searchParams.model) {
          filterParams.model = searchParams.model;
        }
        if (searchParams.status) {
          filterParams.status = searchParams.status;
        }
        if (searchParams.user) {
          filterParams.user = searchParams.user;
        }
        if (searchParams.title) {
          filterParams.title = searchParams.title;
        }

        const response = await filterEvaluationJobs(filterParams);
        return response.data;
      } catch (error) {
        console.error("获取评测任务列表失败:", error);
        toast.error("获取评测任务列表失败");
        return { total: 0, page: 1, size: pageSize, data: [] };
      }
    },
  });

  // 表格数据
  const tableData = useMemo(() => {
    if (!data || !data.data) return [];
    return data.data.map((item: EvaluationJob) => ({
      ...item,
      key: item.id || '',
    }));
  }, [data]);

  // 搜索表单变更
  const handleSearch = () => {
    const values = form.getFieldsValue();
    setSearchParams(values);
    setCurrentPage(1);
    updateURLParams({ ...values, page: 1, size: pageSize });
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setSearchParams({});
    setCurrentPage(1);
    updateURLParams({ page: 1, size: pageSize });
  };

  // 处理添加按钮点击
  const handleAdd = () => {
    router.push("/job/create-job");
  };


  // 分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateURLParams({ ...searchParams, page, size: pageSize });
  };

  // 每页条数变化
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
    updateURLParams({ ...searchParams, page: 1, size });
  };

  // 处理开始任务
  const handleStartJob = (job: EvaluationJob) => {
    // 检查任务状态，只有pending状态的任务可以开始执行
    let canStart = job.status === 'pending';
    
    // 如果有子任务，检查子任务状态
    if (job.subTasks && job.subTasks.length > 0) {
      const allPending = job.subTasks.every(task => task.status === 'pending' || !task.status);
      canStart = canStart && allPending;
    }
    
    if (!canStart) {
      toast.error('只有待处理状态的任务可以开始执行');
      return;
    }
    
    setActionToConfirm('start');
    setJobToConfirm(job);
    setActionConfirmVisible(true);
  };

  // 处理查看详情
  const handleViewDetail = (job: EvaluationJob) => {
    if (job.id) {
      router.push(`/job/detail?id=${job.id}`);
    }
  };

  // 处理重启任务
  const handleRestartJob = (job: EvaluationJob) => {
    if (!job.id) return;
    setActionToConfirm('restart');
    setJobToConfirm(job);
    setActionConfirmVisible(true);
  };

  // 处理删除任务
  const handleDeleteJob = (job: EvaluationJob) => {
    if (job.id) {
      setDeleteItemId(job.id);
      setDeleteConfirmVisible(true);
    }
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) return;
    setBatchActionToConfirm('batch_delete');
    setBatchActionConfirmVisible(true);
  };

  // 处理批量开始
  const handleBatchStart = () => {
    if (selectedRowKeys.length === 0) return;
    setBatchActionToConfirm('batch_start');
    setBatchActionConfirmVisible(true);
  };

  // 确认删除
  const confirmDelete = async () => {
    if (!deleteItemId) return;
    
    setDeleteLoading(true);
    try {
      const response = await deleteEvaluationJob(deleteItemId);
      if (response.code === 200) {
        toast.success("删除成功");
        refetch();
      } else {
        toast.error(response.msg || "删除失败");
      }
    } catch (error) {
      console.error("删除评测任务失败:", error);
      toast.error("删除评测任务失败");
    } finally {
      setDeleteLoading(false);
      setDeleteConfirmVisible(false);
      setDeleteItemId(null);
    }
  };

  // 确认操作
  const confirmAction = async () => {
    if (!jobToConfirm || !actionToConfirm || !jobToConfirm.id) return;

    setActionLoading(true);
    try {
      let response;
      switch (actionToConfirm) {
        case 'start':
          response = await startEvaluationJob(jobToConfirm.id);
          break;
        case 'restart':
          response = await restartEvaluationJob(jobToConfirm.id);
          break;
      }

      if (response && response.code === 200) {
        const actionText = {
          'start': '开始执行',
          'restart': '重启任务'
        }[actionToConfirm];
        toast.success(`${actionText}成功`);
        refetch();
      } else {
        toast.error(response?.msg || '操作失败');
      }
    } catch (error) {
      console.error(`${actionToConfirm}失败:`, error);
      toast.error(`${actionToConfirm}失败`);
    } finally {
      setActionLoading(false);
      setActionConfirmVisible(false);
      setJobToConfirm(null);
      setActionToConfirm(null);
    }
  };

  // 确认批量操作
  const confirmBatchAction = async () => {
    if (!batchActionToConfirm || selectedRowKeys.length === 0) return;

    // 将selectedRowKeys转换为string[]类型
    const stringKeys = selectedRowKeys.map(key => key.toString());
    
    setActionLoading(true);
    try {
      let successCount = 0;
      let failedCount = 0;
      
      if (batchActionToConfirm === 'batch_start') {
        // 批量调用单个开始接口
        const promises = stringKeys.map(id => startEvaluationJob(id));
        const results = await Promise.allSettled(promises);
        
        successCount = results.filter(result => result.status === 'fulfilled' && result.value.code === 200).length;
        failedCount = results.length - successCount;
        
        if (successCount > 0) {
          toast.success(`成功开始${successCount}个任务`);
          setSelectedRowKeys([]);
          refetch();
        }
        
        if (failedCount > 0) {
          toast.error(`${failedCount}个任务开始失败`);
        }
      } else if (batchActionToConfirm === 'batch_delete') {
        // 批量调用单个删除接口
        const promises = stringKeys.map(id => deleteEvaluationJob(id));
        const results = await Promise.allSettled(promises);
        
        successCount = results.filter(result => result.status === 'fulfilled' && result.value.code === 200).length;
        failedCount = results.length - successCount;
        
        if (successCount > 0) {
          toast.success(`成功删除${successCount}个任务`);
          setSelectedRowKeys([]);
          refetch();
        } 
        
        if (failedCount > 0) {
          toast.error(`${failedCount}个任务删除失败`);
        }
      }
    } catch (error) {
      console.error(`${batchActionToConfirm}失败:`, error);
      toast.error(`${batchActionToConfirm}失败`);
    } finally {
      setActionLoading(false);
      setBatchActionConfirmVisible(false);
      setBatchActionToConfirm(null);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <SearchForm 
        form={form}
        onSearch={handleSearch}
        onReset={handleReset}
      />

      <div className="flex-1 overflow-auto">
        <div className="mb-4 flex justify-between items-center">
          <Space>
            <Button 
              type="primary" 
              icon={<IconPlus />} 
              onClick={handleAdd}
            >
              创建评测任务
            </Button>
            <Button 
              type="outline" 
              icon={<IconRefresh />} 
              onClick={() => refetch()}
              status="success"
            >
              刷新
            </Button>
          </Space>
          
          <Space>
            <Button 
              type="outline" 
              onClick={handleBatchStart}
              status="warning"
              disabled={selectedRowKeys.length === 0}
            >
              批量开始 {selectedRowKeys.length > 0 && `(${selectedRowKeys.length})`}
            </Button>
            <Button 
              type="outline" 
              onClick={handleBatchDelete}
              status="danger"
              disabled={selectedRowKeys.length === 0}
            >
              批量删除 {selectedRowKeys.length > 0 && `(${selectedRowKeys.length})`}
            </Button>
          </Space>
        </div>
        
        <EvaluationJobTable
          data={tableData}
          loading={isLoading}
          onStartJob={handleStartJob}
          onViewDetail={handleViewDetail}
          onDeleteJob={handleDeleteJob}
          onRestartJob={handleRestartJob}
          selectedRowKeys={selectedRowKeys}
          onSelectionChange={setSelectedRowKeys}
        />
        
        <PaginationControls
          total={data?.total || 0}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      </div>
  

      {/* 删除确认对话框 */}
      <ConfirmDeleteDialog
        open={deleteConfirmVisible}
        onOpenChange={(open) => {
          if (!open) {
            setDeleteConfirmVisible(false);
            setDeleteItemId(null);
          }
        }}
        title="确认删除"
        description="确定要删除这个评测任务吗？此操作不可撤销。"
        onConfirm={confirmDelete}
        loading={deleteLoading}
      />

      {/* 操作确认对话框 */}
      <ActionConfirmModal
        visible={actionConfirmVisible}
        jobId={jobToConfirm?.id}
        actionType={actionToConfirm}
        onConfirm={confirmAction}
        onCancel={() => {
          setActionConfirmVisible(false);
          setJobToConfirm(null);
          setActionToConfirm(null);
        }}
        loading={actionLoading}
      />

      {/* 批量操作确认对话框 */}
      <Modal
        title="确认批量操作"
        visible={batchActionConfirmVisible}
        onConfirm={confirmBatchAction}
        onCancel={() => {
          setBatchActionConfirmVisible(false);
          setBatchActionToConfirm(null);
        }}
        confirmLoading={actionLoading}
      >
        <div>
          {batchActionToConfirm === 'batch_start' && (
            <span>确定要批量开始 {selectedRowKeys.length} 个评测任务吗？</span>
          )}
          {batchActionToConfirm === 'batch_delete' && (
            <span>确定要批量删除 {selectedRowKeys.length} 个评测任务吗？此操作不可撤销。</span>
          )}
        </div>
      </Modal>
    </div>
  );
}