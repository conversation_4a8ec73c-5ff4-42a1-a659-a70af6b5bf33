"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { Card, Grid, Tag, Spin, Typography, Button, Space, Collapse, Modal } from "@arco-design/web-react";
import { IconArrowLeft, IconPlayCircle, IconRefresh, IconEye, IconExport} from "@arco-design/web-react/icon";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { 
  getEvaluationJobById, 
  getEvaluationCaseById,
  EvaluationJob,
  startEvaluationJob,
  restartEvaluationJob,
  getStatusColor, 
  getStatusText, 
  getGtStatusColor, 
  getGtStatusText 
} from "../api/evaluation";
import { JSONEditor } from "@/components/ui/json-editor";
import { ActionConfirmModal } from "../components";

const { Text } = Typography;
const CollapseItem = Collapse.Item;

export default function EvaluationJobDetailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const jobId = searchParams.get('id');
  
  const [actionLoading, setActionLoading] = useState(false);
  const [actionConfirmVisible, setActionConfirmVisible] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState<'start' | 'restart' | null>(null);
  const [jobToConfirm, setJobToConfirm] = useState<EvaluationJob | null>(null);
  const [queryDialogVisible, setQueryDialogVisible] = useState(false);
  const [currentQueryItems, setCurrentQueryItems] = useState<any[]>([]);
  const [subTasksExpanded, setSubTasksExpanded] = useState(true);
  const [subTaskActiveKeys, setSubTaskActiveKeys] = useState<string[]>([]);

  // 查询评测任务详情
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["evaluationJobDetail", jobId],
    queryFn: async () => {
      try {
        const response = await getEvaluationJobById(jobId ?? "");
        return response.data;
      } catch (error) {
        console.error("获取评测任务详情失败:", error);
        toast.error("获取评测任务详情失败");
        return null;
      }
    },
    enabled: !!jobId,
  });

  // 查询评测用例详情
  const { data: caseData } = useQuery({
    queryKey: ["evaluationCaseDetail", data?.caseId],
    queryFn: async () => {
      if (!data?.caseId) return null;
      try {
        const response = await getEvaluationCaseById(data.caseId);
        return response.data;
      } catch (error) {
        console.error("获取评测用例详情失败:", error);
        toast.error("获取评测用例详情失败");
        return null;
      }
    },
    enabled: !!data?.caseId,
  });

  // 处理返回列表
  const handleBack = () => {
    router.push("/job");
  };

  // 初始化子任务展开状态
  useEffect(() => {
    if (data?.subTasks && data.subTasks.length > 0) {
      const allKeys = data.subTasks.map((_, index) => index.toString());
      setSubTaskActiveKeys(allKeys);
      setSubTasksExpanded(true);
    }
  }, [data?.subTasks]);

  // 处理子任务一键展开/收起
  const handleToggleAllSubTasks = () => {
    if (subTasksExpanded) {
      // 收起所有子任务
      setSubTaskActiveKeys([]);
    } else {
      // 展开所有子任务
      if (data?.subTasks) {
        const allKeys = data.subTasks.map((_, index) => index.toString());
        setSubTaskActiveKeys(allKeys);
      }
    }
    setSubTasksExpanded(!subTasksExpanded);
  };

  // 处理单个子任务的展开/收起
  const handleSubTaskCollapseChange = (key: string, keys: string[]) => {
    setSubTaskActiveKeys(keys);
    if (data?.subTasks) {
      setSubTasksExpanded(keys.length === data.subTasks.length);
    }
  };

  // 处理开始任务
  const handleStartJob = (job: EvaluationJob) => {
    // 检查任务状态，只有pending状态的任务可以开始执行
    let canStart = job.status === 'pending';
    
    // 如果有子任务，检查子任务状态
    if (job.subTasks && job.subTasks.length > 0) {
      const allPending = job.subTasks.every(task => task.status === 'pending' || !task.status);
      canStart = canStart && allPending;
    }
    
    if (!canStart) {
      toast.error('只有待处理状态的任务可以开始执行');
      return;
    }
    
    setActionToConfirm('start');
    setJobToConfirm(job);
    setActionConfirmVisible(true);
  };

  // 处理重启任务
  const handleRestartJob = (job: EvaluationJob) => {
    if (!job.id) return;
    setActionToConfirm('restart');
    setJobToConfirm(job);
    setActionConfirmVisible(true);
  };

  // 处理导出结果
  const handleExportResults = () => {
    if (!data || !data.subTasks || data.subTasks.length === 0) {
      toast.error('没有可导出的结果');
      return;
    }

    try {
      // 构建导出数据
      const exportData = data.subTasks.map(subTask => {
        // 获取对应的评测任务信息
        const evaluationTask = caseData?.evaluationTasks?.[subTask.taskIndex];
        
        // 构建导出项
        const exportItem: any = {
          // SubTask 内容
          ...subTask,
        };
        
        // 添加用例标题
        if (evaluationTask) {
          exportItem.caseTitle = evaluationTask.title;
          
          // 添加用例 query 的最后一条消息
          if (evaluationTask.query && evaluationTask.query.length > 0) {
            const lastQuery = evaluationTask.query[evaluationTask.query.length - 1];
            exportItem.lastQuery = {
              role: lastQuery.role,
              content: lastQuery.content
            };
          }
        }
        
        // 如果没有 gt，则删除 gtResult 和 gtStatus
        if (!data.gtResult) {
          delete exportItem.gtResult;
          delete exportItem.gtStatus;
        }
        
        return exportItem;
      });

      // 转换为 JSON 字符串
      const jsonString = JSON.stringify(exportData, null, 2);
      
      // 创建 Blob 对象
      const blob = new Blob([jsonString], { type: 'application/json' });
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `evaluation-results-${data.id || 'export'}.json`;
      document.body.appendChild(a);
      a.click();
      
      // 清理
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);
      
      toast.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      toast.error('导出失败');
    }
  };

  // 确认操作
  const confirmAction = async () => {
    if (!jobToConfirm || !actionToConfirm || !jobToConfirm.id) return;

    setActionLoading(true);
    try {
      let response;
      switch (actionToConfirm) {
        case 'start':
          response = await startEvaluationJob(jobToConfirm.id);
          break;
        case 'restart':
          response = await restartEvaluationJob(jobToConfirm.id);
          break;
      }

      if (response && response.code === 200) {
        const actionText = {
          'start': '开始执行',
          'restart': '重启任务'
        }[actionToConfirm];
        toast.success(`${actionText}成功`);
        refetch();
      } else {
        toast.error(response?.msg || '操作失败');
      }
    } catch (error) {
      console.error(`${actionToConfirm}失败:`, error);
      toast.error(`${actionToConfirm}失败`);
    } finally {
      setActionLoading(false);
      setActionConfirmVisible(false);
      setJobToConfirm(null);
      setActionToConfirm(null);
    }
  };

  if (!data && !isLoading) {
    return (
      <div className="p-6">
        <Text>任务不存在或加载失败</Text>
      </div>
    );
  }

  // 解析模型配置
  let modelConfig: any = {};
  try {
    if (data?.modelConfig) {
      modelConfig = JSON.parse(data.modelConfig);
    }
  } catch (error) {
    console.error('解析模型配置失败:', error);
  }

  // 处理可能为undefined的值
  const safeModelConfig = typeof modelConfig === 'object' ? modelConfig : {};
  const safeResult = data?.result || '{}';
  const safeGtResult = data?.gtResult ? 
    (Array.isArray(data.gtResult) ? JSON.stringify(data.gtResult, null, 2) : data.gtResult) : 
    '{}';

  return (
    <div className="p-6">
      {/* 页面头部 */}
      <div className="mb-6 flex justify-between items-center">
        <Space>
          <Button 
            icon={<IconArrowLeft />}
            onClick={handleBack}
            type="outline"
          >
            返回列表
          </Button>
          <Text className="text-xl font-semibold">评测任务详情</Text>
        </Space>
        
        <Space>
          <Button 
            icon={<IconRefresh />} 
            onClick={() => refetch()}
            type="outline"
            status="success"
          >
            刷新
          </Button>
          {data && (
            <Space>
              <Button 
                icon={<IconPlayCircle />}
                onClick={() => handleStartJob(data)}
                type="primary"
                disabled={(() => {
                  // 检查任务状态，只有pending状态的任务可以开始执行
                  let canStart = data.status === 'pending';
                  
                  // 如果有子任务，检查子任务状态
                  if (data.subTasks && data.subTasks.length > 0) {
                    const allPending = data.subTasks.every(task => task.status === 'pending' || !task.status);
                    canStart = canStart && allPending;
                  }
                  
                  return !canStart;
                })()}
              >
                开始执行
              </Button>
              <Button 
                icon={<IconRefresh />}
                onClick={() => handleRestartJob(data)}
                type="outline"
                status="warning"
              >
                重启任务
              </Button>
            </Space>
          )}
        </Space>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Spin  />
        </div>
      ) : data ? (
        <div className="space-y-6">
          {/* 基本信息 */}
          <Card 
            title="基本信息"
            extra={
              <Button 
                icon={<IconExport />}
                onClick={handleExportResults}
                type="outline"
                size="small"
                disabled={!data?.subTasks || data.subTasks.length === 0}
              >
                导出结果
              </Button>
            }
          >
            <Grid.Row gutter={16}>
              <Grid.Col span={12}>
                <Text>任务：</Text>
                <Text>{data.title}</Text>
              </Grid.Col>
              <Grid.Col span={12}>
                <Text>评测用例：</Text>
                {caseData?.title ? (
                  <Link href={`/job/cases?id=${data.caseId}`} className="text-blue-600 hover:text-blue-800 hover:underline">
                    {caseData.title}
                  </Link>
                ) : (
                  <Text>-</Text>
                )}
              </Grid.Col>
            </Grid.Row>
            
            <Grid.Row gutter={16} className="mt-4">
              <Grid.Col span={8}>
                <Text>任务ID：</Text>
                <Text>{data.id}</Text>
              </Grid.Col>
              <Grid.Col span={8}>
                <Text>用例ID：</Text>
                <Text>{data.caseId}</Text>
              </Grid.Col>
              <Grid.Col span={8}>
                <Text>状态：</Text>
                  {(() => {
                    // 检查任务状态
                    let displayStatus = data.status || 'pending';
                    let shouldShowLoading = displayStatus === 'running';
                    
                    // 如果有子任务，检查子任务状态
                    if (data.subTasks && data.subTasks.length > 0) {
                      const runningSubTasks = data.subTasks.filter(task => task.status === 'running');
                      const failedSubTasks = data.subTasks.filter(task => task.status === 'failed');
                      const completedSubTasks = data.subTasks.filter(task => task.status === 'completed');
                      
                      if (runningSubTasks.length > 0) {
                        displayStatus = 'running';
                        shouldShowLoading = true;
                      } else if (failedSubTasks.length === data.subTasks.length) {
                        displayStatus = 'failed';
                      } else if (completedSubTasks.length === data.subTasks.length) {
                        displayStatus = 'completed';
                      }
                    }
                    
                    return (
                      <>
                        {shouldShowLoading && <Spin />}
                        <Tag color={getStatusColor(displayStatus)}>
                          {getStatusText(displayStatus)}
                        </Tag>
                      </>
                    );
                  })()}
              </Grid.Col>
            </Grid.Row>
            
            <Grid.Row gutter={16} className="mt-4">
              <Grid.Col span={8}>
                <Text>评测状态：</Text>
                  {(() => {
                    // 如果有子任务，计算子任务的评测状态
                    if (data.subTasks && data.subTasks.length > 0) {
                      const completedSubTasks = data.subTasks.filter(task => task.status === 'completed');
                      
                      if (completedSubTasks.length === 0) {
                        return <Tag color="gray">-</Tag>;
                      }
                      
                      // 统计所有子任务的评测状态
                      let totalPassed = 0;
                      let totalFailed = 0;
                      
                      completedSubTasks.forEach(subTask => {
                        if (Array.isArray(subTask.gtStatus)) {
                          // 处理 gtStatus 为数组的情况
                          subTask.gtStatus.forEach(status => {
                            if (status === true) totalPassed++;
                            else if (status === false) totalFailed++;
                          });
                        } else if (subTask.gtStatus !== undefined && subTask.gtStatus !== null) {
                          // 处理 gtStatus 为布尔值的情况（向后兼容）
                          if (subTask.gtStatus === true) totalPassed++;
                          else if (subTask.gtStatus === false) totalFailed++;
                        }
                      });
                      
                      const total = totalPassed + totalFailed;
                      
                      if (total === 0) {
                        return <Tag color="gray">-</Tag>;
                      } else if (totalPassed === total) {
                        return <Tag color="green">全部通过</Tag>;
                      } else if (totalFailed === total) {
                        return <Tag color="red">全部不通过</Tag>;
                      } else {
                        return <Tag color="orange">部分通过 ({totalPassed}/{total})</Tag>;
                      }
                    }
                    
                    // 如果没有子任务，显示任务的 gtStatus
                    if (Array.isArray(data.gtStatus)) {
                      // 处理 gtStatus 为数组的情况
                      if (data.gtStatus.length === 0) {
                        return <Tag color="gray">-</Tag>;
                      }
                      
                      const allPassed = data.gtStatus.every(status => status === true);
                      const allFailed = data.gtStatus.every(status => status === false);
                      
                      if (allPassed) {
                        return <Tag color="green">全部通过</Tag>;
                      } else if (allFailed) {
                        return <Tag color="red">全部不通过</Tag>;
                      } else {
                        const passedCount = data.gtStatus.filter(status => status === true).length;
                        return <Tag color="orange">部分通过 ({passedCount}/{data.gtStatus.length})</Tag>;
                      }
                    }
                    
                    // 处理 gtStatus 为布尔值的情况（向后兼容）
                    return <Tag color={getGtStatusColor(data.gtStatus !== undefined && data.gtStatus !== null ? data.gtStatus as boolean : undefined)}>
                      {getGtStatusText(data.gtStatus !== undefined && data.gtStatus !== null ? data.gtStatus as boolean : undefined)}
                    </Tag>;
                  })()}
              </Grid.Col>
              <Grid.Col span={8}>
                <Text>平均耗时：</Text>
                <Text>
                  {(() => {
                    // 计算平均耗时
                    if (data.subTasks && data.subTasks.length > 0) {
                      const completedSubTasks = data.subTasks.filter(task => 
                        task.status === 'completed' && task.modelApiCallTime
                      );
                      
                      if (completedSubTasks.length === 0) {
                        return "-";
                      }
                      
                      const totalTime = completedSubTasks.reduce((sum, task) => 
                        sum + (task.modelApiCallTime || 0), 0
                      );
                      
                      const avgTimeMs = totalTime / completedSubTasks.length;
                      return `${(avgTimeMs / 1000).toFixed(2)}秒`;
                    }
                    
                    // 如果没有子任务，检查任务本身的耗时
                    if (data.createTime && data.endTime) {
                      const startTime = new Date(data.createTime).getTime();
                      const endTime = new Date(data.endTime).getTime();
                      const totalTimeMs = endTime - startTime;
                      return `${(totalTimeMs / 1000).toFixed(2)}秒`;
                    }
                    
                    return "-";
                  })()}
                </Text>
              </Grid.Col>
              <Grid.Col span={8}>
                <Text>50分位耗时：</Text>
                <Text>
                  {(() => {
                    // 计算50分位耗时
                    if (data.subTasks && data.subTasks.length > 0) {
                      const completedSubTasks = data.subTasks.filter(task => 
                        task.status === 'completed' && task.modelApiCallTime
                      );
                      
                      if (completedSubTasks.length === 0) {
                        return "-";
                      }
                      
                      // 获取所有耗时并排序
                      const times = completedSubTasks.map(task => task.modelApiCallTime || 0).sort((a, b) => a - b);
                      
                      // 计算中位数
                      const mid = Math.floor(times.length / 2);
                      const medianTimeMs = times.length % 2 !== 0 
                        ? times[mid] 
                        : (times[mid - 1] + times[mid]) / 2;
                        
                      return `${(medianTimeMs / 1000).toFixed(2)}秒`;
                    }
                    
                    // 如果没有子任务，使用任务本身的耗时作为50分位
                    if (data.createTime && data.endTime) {
                      const startTime = new Date(data.createTime).getTime();
                      const endTime = new Date(data.endTime).getTime();
                      const totalTimeMs = endTime - startTime;
                      return `${(totalTimeMs / 1000).toFixed(2)}秒`;
                    }
                    
                    return "-";
                  })()}
                </Text>
              </Grid.Col>
            </Grid.Row>
            
            <Grid.Row gutter={16} className="mt-4">
              <Grid.Col span={8}>
                <Text>模型：</Text>
                <Text>{data.model}</Text>
              </Grid.Col>
              <Grid.Col span={8}>
                <Text>运行次数：</Text>
                <Text>{data.runCount || 1}</Text>
              </Grid.Col>
              <Grid.Col span={8}>
                {/* 空列，保持布局平衡 */}
              </Grid.Col>
            </Grid.Row>
            
            <Grid.Row gutter={16} className="mt-4">
              <Grid.Col span={8}>
                <Text>创建时间：</Text>
                <Text>{data.createTime ? new Date(data.createTime).toLocaleString() : '-'}</Text>
              </Grid.Col>
              <Grid.Col span={8}>
                <Text>更新时间：</Text>
                <Text>{data.updateTime ? new Date(data.updateTime).toLocaleString() : '-'}</Text>
              </Grid.Col>
              <Grid.Col span={8}>
                <Text>结束时间：</Text>
                <Text>{data.endTime ? new Date(data.endTime).toLocaleString() : '-'}</Text>
              </Grid.Col>
            </Grid.Row>
          </Card>
          
          {/* 模型配置 */}
          <Collapse
            defaultActiveKey={[]}
            style={{
              borderRadius: 'var(--radius-lg)',
              border: '1px solid var(--border)',
              overflow: 'hidden'
            }}
            accordion={false}
          >
            <CollapseItem
              name="modelConfig"
              header="模型配置"
              key="modelConfig"
              style={{
                backgroundColor: 'var(--color-bg-2)',
                borderBottom: '1px solid var(--color-border-2)'
              }}
            >
              <div style={{ padding: '16px' }}>
                <JSONEditor 
                  value={JSON.stringify(safeModelConfig, null, 2)} 
                  onChange={() => {}}
                  showValidation={false}
                />
              </div>
            </CollapseItem>
          </Collapse>
          
          {/* 模型结果 */}
          {data.result && (
            <Collapse
              defaultActiveKey={[]}
              style={{
                borderRadius: 'var(--radius-lg)',
                border: '1px solid var(--border)',
                overflow: 'hidden'
              }}
              accordion={false}
            >
              <CollapseItem
                name="modelResult"
                header="模型结果"
                key="modelResult"
                style={{
                  backgroundColor: 'var(--color-bg-2)',
                  borderBottom: '1px solid var(--color-border-2)'
                }}
              >
                <div style={{ padding: '16px' }}>
                  <JSONEditor 
                    value={safeResult} 
                    onChange={() => {}}
                    showValidation={false}
                  />
                </div>
              </CollapseItem>
            </Collapse>
          )}
          
          {/* 评测结果 */}
          {data.gtResult && (
            <Collapse
              defaultActiveKey={[]}
              style={{
                borderRadius: 'var(--radius-lg)',
                border: '1px solid var(--border)',
                overflow: 'hidden'
              }}
              accordion={false}
            >
              <CollapseItem
                name="evaluationResult"
                header="评测结果"
                key="evaluationResult"
                style={{
                  backgroundColor: 'var(--color-bg-2)',
                  borderBottom: '1px solid var(--color-border-2)'
                }}
              >
                <div style={{ padding: '16px' }}>
                  <JSONEditor 
                    value={(() => {
                      // 处理 gtResult 为数组的情况
                      if (Array.isArray(data.gtResult)) {
                        return JSON.stringify(data.gtResult, null, 2);
                      }
                      // 处理 gtResult 为字符串的情况（向后兼容）
                      return data.gtResult;
                    })()} 
                    onChange={() => {}}
                    showValidation={false}
                  />
                </div>
              </CollapseItem>
            </Collapse>
          )}

          {/* 子任务信息 */}
          {data.subTasks && data.subTasks.length > 0 && (
            <Card title="子任务详情">
              <Collapse
                activeKey={subTaskActiveKeys}
                onChange={handleSubTaskCollapseChange}
                style={{
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--border)',
                  overflow: 'hidden'
                }}
                accordion={false}
              >
                {data.subTasks.map((subTask, index) => {
                  // 获取对应的评测任务信息
                  const evaluationTask = caseData?.evaluationTasks?.[subTask.taskIndex];
                  
                  return (
                    <CollapseItem
                      name={index.toString()}
                      header={
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          width: '100%'
                        }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '16px'
                          }}>
                            <div style={{
                              width: '32px',
                              height: '32px',
                              borderRadius: '8px',
                              backgroundColor: 'var(--color-primary-light-1)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'var(--color-primary)',
                              fontSize: '14px',
                              fontWeight: 'bold',
                              flexShrink: 0
                            }}>
                              {index + 1}
                            </div>
                            <Text style={{ fontWeight: 600, margin: 0 }}>
                              {evaluationTask?.title}
                            </Text>
                            {subTask.status === 'running' && (
                              <Spin />
                            )}
                            <Tag color={getStatusColor(subTask.status || 'pending')} size="small">
                              {getStatusText(subTask.status || 'pending')}
                            </Tag>
                          </div>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '16px'
                          }}>
                            {subTask.modelApiCallTime && (
                              <Text style={{ fontSize: '12px', color: 'var(--color-text-3)', marginLeft: '8px' }}>
                                耗时: {Math.floor(subTask.modelApiCallTime / 1000)}秒
                              </Text>
                            )}
                            <Text style={{ fontSize: '12px', color: 'var(--color-text-3)' }}>
                              评测状态:
                            </Text>
                            {(() => {
                              // 处理 gtStatus 为数组的情况
                              if (Array.isArray(subTask.gtStatus)) {
                                if (subTask.gtStatus.length === 0) {
                                  return <Tag color="gray" size="small">-</Tag>;
                                }
                                
                                const allPassed = subTask.gtStatus.every(status => status === true);
                                const allFailed = subTask.gtStatus.every(status => status === false);
                                
                                if (allPassed) {
                                  return <Tag color="green" size="small">全部通过:{subTask.gtStatus.length}</Tag>;
                                } else if (allFailed) {
                                  return <Tag color="red" size="small">全部不通过:{subTask.gtStatus.length}</Tag>;
                                } else {
                                  const passedCount = subTask.gtStatus.filter(status => status === true).length;
                                  return <Tag color="orange" size="small">部分通过 ({passedCount}/{subTask.gtStatus.length})</Tag>;
                                }
                              }
                              
                              // 处理 gtStatus 为布尔值的情况（向后兼容）
                              return <Tag color={getGtStatusColor(subTask.gtStatus !== undefined && subTask.gtStatus !== null ? subTask.gtStatus as boolean : undefined)} size="small">
                                {getGtStatusText(subTask.gtStatus !== undefined && subTask.gtStatus !== null ? subTask.gtStatus as boolean : undefined)}
                              </Tag>;
                            })()}
                          </div>

                           <Text style={{  marginLeft: '16px' }}>
                              { "第" + ((subTask.runIndex ?? 0) + 1) + "次运行"}
                            </Text>
                        </div>
                      }
                      key={index.toString()}
                      style={{
                        backgroundColor: 'var(--color-bg-2)',
                        borderBottom: '1px solid var(--color-border-2)'
                      }}
                    >
                      <div style={{ padding: '16px' }}>
                        <Grid.Row gutter={16}>
                         
                          <Grid.Col span={8}>
                            <Text>开始时间：</Text>
                            <Text>{subTask.startTime ? new Date(subTask.startTime).toLocaleString() : '-'}</Text>
                          </Grid.Col>
                          <Grid.Col span={8}>
                            <Text>结束时间：</Text>
                            <Text>{subTask.endTime ? new Date(subTask.endTime).toLocaleString() : '-'}</Text>
                          </Grid.Col>
                          <Grid.Col span={8}>
                            <Text>状态：</Text> 
                            {subTask.status === 'running' && (
                              <Spin  />
                            )}
                            <Tag color={getStatusColor(subTask.status || 'pending')}>
                              {getStatusText(subTask.status || 'pending')}
                            </Tag>
                          </Grid.Col>
                        </Grid.Row>
                        
                        {/* 显示耗时信息 */}
                        <Grid.Row gutter={16} className="mt-2">
                          <Grid.Col span={8}>
                            <Text>API调用耗时：</Text>
                            <Text>{subTask.modelApiCallTime ? `${Math.floor(subTask.modelApiCallTime / 1000)}秒` : '-'}</Text>
                          </Grid.Col>
                          <Grid.Col span={8}>
                            <Text>错误信息：</Text>
                            <Text>{subTask.errorMsg || '-'}</Text>
                          </Grid.Col>
                        </Grid.Row>
                        
  
                        
                        {/* 显示查询内容 */}
                        {evaluationTask?.query && (
                          <Grid.Row gutter={16} className="mt-2">
                            <Grid.Col span={24}>
                              <Text>查询内容：</Text>
                              <div className="mt-1 p-2 bg-white rounded">
                                {evaluationTask.query.length > 1 ? (
                                  <>
                                    {/* 只显示最后一条查询内容 */}
                                    <div className="mb-2">
                                      <Text className="font-medium">
                                        {evaluationTask.query[evaluationTask.query.length - 1].role === 'user' ? '用户' : 
                                         evaluationTask.query[evaluationTask.query.length - 1].role === 'assistant' ? '助手' : 
                                         evaluationTask.query[evaluationTask.query.length - 1].role === 'system' ? '系统' : '工具'}:
                                      </Text>
                                      {evaluationTask.query[evaluationTask.query.length - 1].tool_call_id && (
                                        <Tag color="purple" size="small" className="ml-2">ID: {evaluationTask.query[evaluationTask.query.length - 1].tool_call_id}</Tag>
                                      )}
                                      <div className="mt-1 text-sm">{evaluationTask.query[evaluationTask.query.length - 1].content}</div>
                                      
                                      {/* 显示工具调用信息 */}
                                      {evaluationTask.query[evaluationTask.query.length - 1].tool_calls && evaluationTask.query[evaluationTask.query.length - 1].tool_calls!.length > 0 && (
                                        <div className="mt-2 p-2 bg-blue-50 rounded">
                                          <Text className="font-medium text-sm">工具调用:</Text>
                                          {evaluationTask.query[evaluationTask.query.length - 1].tool_calls!.map((toolCall: any, toolCallIndex: number) => (
                                            <div key={toolCallIndex} className="mt-1 p-1 bg-white rounded text-xs">
                                              <div>函数: {toolCall.function?.name || '未设置'}</div>
                                              <div className="mt-1">
                                                <div>参数:</div>
                                                <pre className="mt-1 text-xs bg-gray-100 p-1 rounded whitespace-pre-wrap">
                                                  {toolCall.function?.arguments || '{}'}
                                                </pre>
                                              </div>
                                            </div>
                                          ))}
                                        </div>
                                      )}
                                    </div>
                                    {/* 显示查看更多按钮 */}
                                    <Button 
                                      type="outline" 
                                      size="mini" 
                                      icon={<IconEye />}
                                      onClick={() => {
                                        setCurrentQueryItems(evaluationTask.query);
                                        setQueryDialogVisible(true);
                                      }}
                                    >
                                      查看全部查询内容 ({evaluationTask.query.length}条)
                                    </Button>
                                  </>
                                ) : (
                                  // 如果只有一条查询内容，直接显示
                                  evaluationTask.query.map((queryItem, queryIndex) => (
                                    <div key={queryIndex} className="mb-2 last:mb-0">
                                      <Text className="font-medium">
                                        {queryItem.role === 'user' ? '用户' : 
                                         queryItem.role === 'assistant' ? '助手' : 
                                         queryItem.role === 'system' ? '系统' : '工具'}:
                                      </Text>
                                      {queryItem.tool_call_id && (
                                        <Tag color="purple" size="small" className="ml-2">ID: {queryItem.tool_call_id}</Tag>
                                      )}
                                      <div className="mt-1 text-sm">{queryItem.content}</div>
                                      
                                      {/* 显示工具调用信息 */}
                                      {queryItem.tool_calls && queryItem.tool_calls.length > 0 && (
                                        <div className="mt-2 p-2 bg-blue-50 rounded">
                                          <Text className="font-medium text-sm">工具调用:</Text>
                                          {queryItem.tool_calls.map((toolCall: any, toolCallIndex: number) => (
                                            <div key={toolCallIndex} className="mt-1 p-1 bg-white rounded text-xs">
                                              <div>函数: {toolCall.function?.name || '未设置'}</div>
                                              <div className="mt-1">
                                                <div>参数:</div>
                                                <pre className="mt-1 text-xs bg-gray-100 p-1 rounded whitespace-pre-wrap">
                                                  {toolCall.function?.arguments || '{}'}
                                                </pre>
                                              </div>
                                            </div>
                                          ))}
                                        </div>
                                      )}
                                    </div>
                                  ))
                                )}
                              </div>
                            </Grid.Col>
                          </Grid.Row>
                        )}
                        
                        {/* 显示评测规则 */}
                        {evaluationTask?.gtRules && evaluationTask.gtRules.length > 0 && (
                          <Grid.Row gutter={16} className="mt-2">
                            <Grid.Col span={24}>
                              <Text>评测规则：</Text>
                              <div className="mt-1 p-2 bg-blue-50 rounded">
                                {evaluationTask.gtRules.map((rule, ruleIndex) => (
                                  <div key={ruleIndex} className="mb-2 last:mb-0">
                                    <Text className="font-medium">规则 {ruleIndex + 1}:</Text>
                                    <div className="mt-1 text-sm">
                                      <div><span className="font-medium">规则类型:</span> {rule.rule}</div>
                                      <div><span className="font-medium">期望结果:</span> {rule.result}</div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </Grid.Col>
                          </Grid.Row>
                        )}
                        
                        {/* 显示工具配置 */}
                        {evaluationTask?.tools && evaluationTask.tools.length > 0 && (
                          <Grid.Row gutter={16} className="mt-2">
                            <Grid.Col span={24}>
                              <Collapse
                                defaultActiveKey={[]}
                                style={{
                                  borderRadius: 'var(--radius-lg)',
                                  border: '1px solid var(--border)',
                                  overflow: 'hidden',
                                  marginTop: '8px'
                                }}
                                accordion={false}
                              >
                                <CollapseItem
                                  name="toolsConfig"
                                  header="工具配置"
                                  key="toolsConfig"
                                  style={{
                                    backgroundColor: 'var(--color-bg-2)',
                                    borderBottom: '1px solid var(--color-border-2)'
                                  }}
                                >
                                  <div style={{ padding: '16px' }}>
                                    {evaluationTask.tools.map((tool: any, toolIndex: number) => (
                                      <div key={toolIndex} className="mb-3 last:mb-0 p-2 bg-white rounded">
                                        <div className="font-medium">工具 {toolIndex + 1}:</div>
                                        <div className="mt-1 text-sm">
                                          <div><span className="font-medium">工具ID:</span> {tool.id || '未设置'}</div>
                                          <div><span className="font-medium">工具类型:</span> {tool.type || 'function'}</div>
                                          <div><span className="font-medium">函数名称:</span> {tool.function?.name || '未设置'}</div>
                                          {tool.function?.description && (
                                            <div><span className="font-medium">函数描述:</span> {tool.function.description}</div>
                                          )}
                                          <div className="mt-1">
                                            <div className="font-medium">函数参数:</div>
                                            <pre className="mt-1 text-xs bg-gray-100 p-2 rounded whitespace-pre-wrap">
                                              {tool.function?.arguments || '{}'}
                                            </pre>
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </CollapseItem>
                              </Collapse>
                            </Grid.Col>
                          </Grid.Row>
                        )}
                        
                        {subTask.result && (
                          <div className="mt-2">
                            <Text>结果：</Text>
                            <div className="mt-1 p-2 bg-white rounded">
                              <pre className="whitespace-pre-wrap text-sm">{subTask.result}</pre>
                            </div>
                          </div>
                        )}
                        
                        {subTask.gtResult && (
                          <div className="mt-2">
                            <Text>评测结果：</Text>
                            <div className="mt-1 p-2 bg-green-50 rounded">
                              {(() => {
                                // 处理 gtResult 为数组的情况
                                if (Array.isArray(subTask.gtResult)) {
                                  return subTask.gtResult.map((result, index) => (
                                    <div key={index} className="mb-2 last:mb-0">
                                      <Text className="font-medium">结果 {index + 1}:</Text>
                                      <div className="mt-1 text-sm">
                                        {(() => {
                                          try {
                                            const parsedResult = JSON.parse(result);
                                            return (
                                              <div>
                                                <div><span className="font-medium">通过状态:</span> {parsedResult.pass ? '通过' : '不通过'}</div>
                                                <div><span className="font-medium">原因:</span> {parsedResult.reason || '-'}</div>
                                                {parsedResult.rule && (
                                                  <div>
                                                    <div><span className="font-medium">规则类型:</span> {parsedResult.rule.rule}</div>
                                                    <div><span className="font-medium">期望结果:</span> {parsedResult.rule.result}</div>
                                                  </div>
                                                )}
                                              </div>
                                            );
                                          } catch (e) {
                                            return <pre className="whitespace-pre-wrap text-sm">{result}</pre>;
                                          }
                                        })()}
                                      </div>
                                    </div>
                                  ));
                                }
                                
                                // 处理 gtResult 为字符串的情况（向后兼容）
                                return <pre className="whitespace-pre-wrap text-sm">{subTask.gtResult}</pre>;
                              })()}
                            </div>
                          </div>
                        )}
                      </div>
                    </CollapseItem>
                  );
                })}
              </Collapse>
            </Card>
          )}
        </div>
      ) : null}

      {/* 操作确认对话框 */}
      <ActionConfirmModal
        visible={actionConfirmVisible}
        jobId={jobToConfirm?.id}
        actionType={actionToConfirm}
        onConfirm={confirmAction}
        onCancel={() => {
          setActionConfirmVisible(false);
          setJobToConfirm(null);
          setActionToConfirm(null);
        }}
        loading={actionLoading}
      />

      {/* 查询内容详情弹窗 */}
      <Modal
        title="完整查询内容"
        visible={queryDialogVisible}
        onCancel={() => setQueryDialogVisible(false)}
        footer={null}
        style={{ width: '80%', maxWidth: 1200 }}
      >
        <div className="space-y-4 mt-4">
          {currentQueryItems.map((queryItem, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg">
              <div 
                className="font-medium text-blue-600 mb-2 cursor-pointer flex items-center justify-between"
                onClick={() => {
                  const element = document.getElementById(`query-content-${index}`);
                  if (element) {
                    element.classList.toggle('hidden');
                  }
                }}
              >
                <span>
                  {queryItem.role === 'user' ? '用户' : queryItem.role === 'assistant' ? '助手' : queryItem.role === 'system' ? '系统' : '工具'}:
                </span>
                {queryItem.tool_call_id && (
                  <Tag color="purple" size="small">ID: {queryItem.tool_call_id}</Tag>
                )}
                <span className="text-xs">点击展开/收起</span>
              </div>
              <div id={`query-content-${index}`} className="text-sm whitespace-pre-wrap break-words hidden">
                {queryItem.content}
                
                {/* 显示工具调用信息 */}
                {queryItem.tool_calls && queryItem.tool_calls.length > 0 && (
                  <div className="mt-3 p-3 bg-blue-50 rounded">
                    <div className="font-medium mb-2">工具调用:</div>
                    {queryItem.tool_calls.map((toolCall: any, toolCallIndex: number) => (
                      <div key={toolCallIndex} className="mb-3 last:mb-0 p-2 bg-white rounded">
                        <div className="font-medium">函数: {toolCall.function?.name || '未设置'}</div>
                        {toolCall.function?.description && (
                          <div className="mt-1">描述: {toolCall.function.description}</div>
                        )}
                        <div className="mt-1">
                          <div className="font-medium">参数:</div>
                          <pre className="mt-1 text-xs bg-gray-100 p-2 rounded whitespace-pre-wrap">
                            {toolCall.function?.arguments || '{}'}
                          </pre>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </Modal>

      {/* 右下角常驻浮动按钮 - 子任务展开/收起 */}
      {data?.subTasks && data.subTasks.length > 0 && (
        <div style={{
          position: 'fixed',
          bottom: '24px',
          right: '8px',
          zIndex: 1000,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          borderRadius: '50%',
          overflow: 'hidden'
        }}>
          <Button
            onClick={handleToggleAllSubTasks}
            type="primary"
            shape="circle"
            size="large"
            style={{
              width: '50px',
              height: '50px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
            }}
          >
            {subTasksExpanded ? '收起' : '展开'}
            </Button>
        </div>
      )}
    </div>
  );
}