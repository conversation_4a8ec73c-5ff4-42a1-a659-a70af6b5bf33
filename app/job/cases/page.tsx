"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button, Card, Typography, Space, Table, Tag, Message, Modal, Input, Form, Select } from "@arco-design/web-react";
import { IconPlus, IconSearch, IconDelete, IconEdit, IconEye, IconRefresh } from "@arco-design/web-react/icon";
import { PageHeader } from "@/app/components/PageHeader";
import { getEvaluationCases, searchEvaluationCases, deleteEvaluationCase, EvaluationCase } from "../api/evaluation";
import { updateURLParams } from "../utils";
import EvaluationPreview from "@/app/job/components/EvaluationPreview";

const { Text } = Typography;
const { Item: FormItem } = Form;

export default function EvaluationCaseListPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [cases, setCases] = useState<EvaluationCase[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(Number(searchParams.get("page")) || 1);
  const [pageSize, setPageSize] = useState(Number(searchParams.get("size")) || 10);
  const [searchTitle, setSearchTitle] = useState(searchParams.get("title") || "");
  const [searchId, setSearchId] = useState(searchParams.get("id") || "");
  const [searchUser, setSearchUser] = useState(searchParams.get("user") || "");
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [currentCase, setCurrentCase] = useState<EvaluationCase | null>(null);
  const [caseToDelete, setCaseToDelete] = useState<EvaluationCase | null>(null);

  // 获取评测用例列表
  const fetchCases = async (page: number, size: number, title?: string, id?: string, user?: string) => {
    setLoading(true);
    try {
      let response;
      if (title || id || user) {
        // 修改 searchEvaluationCases 调用，支持 id 和 user 参数
        const params: any = { page, size };
        if (title) params.title = title;
        if (id) params.id = id;
        if (user) params.user = user;
        
        // 使用 searchEvaluationCases 函数，但需要修改它以支持 id 和 user 参数
        response = await searchEvaluationCases(title || "", page, size, id, user);
      } else {
        response = await getEvaluationCases(page, size);
      }
      
      if (response.code === 200 && response.data) {
        setCases(response.data.data);
        setTotal(response.data.total);
      } else {
        Message.error(response.msg || "获取评测用例列表失败");
      }
    } catch (error) {
      console.error("获取评测用例列表失败:", error);
      Message.error("获取评测用例列表失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchCases(currentPage, pageSize, searchTitle, searchId, searchUser);
  }, [currentPage, pageSize, searchTitle, searchId, searchUser]);

  // 处理搜索
  const handleSearch = (values: { title?: string; id?: string; user?: string }) => {
    setSearchTitle(values.title || "");
    setSearchId(values.id || "");
    setSearchUser(values.user || "");
    setCurrentPage(1);
    updateURLParams({
      title: values.title || "",
      id: values.id || "",
      user: values.user || "",
      page: "1",
      size: pageSize.toString(),
    });
  };

  // 处理重置
  const handleReset = () => {
    setSearchTitle("");
    setSearchId("");
    setSearchUser("");
    setCurrentPage(1);
    updateURLParams({
      title: "",
      id: "",
      user: "",
      page: "1",
      size: pageSize.toString(),
    });
  };

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateURLParams({
      title: searchTitle,
      id: searchId,
      user: searchUser,
      page: page.toString(),
      size: pageSize.toString(),
    });
  };

  // 处理每页条数变化
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
    updateURLParams({
      title: searchTitle,
      id: searchId,
      user: searchUser,
      page: "1",
      size: size.toString(),
    });
  };

  // 处理添加按钮点击
  const handleAdd = () => {
    router.push("/job/case-form");
  };

  // 处理查看按钮点击
  const handleView = (record: EvaluationCase) => {
    setCurrentCase(record);
    // 这里可以打开一个模态框显示详情，或者跳转到详情页面
    // 暂时使用模态框方式
  };

  // 处理编辑按钮点击
  const handleEdit = (record: EvaluationCase) => {
    // 跳转到编辑页面
    router.push(`/job/case-form?id=${record.id}`);
  };

  // 处理删除按钮点击
  const handleDelete = (record: EvaluationCase) => {
    setCaseToDelete(record);
    setDeleteModalVisible(true);
  };

  // 确认删除
  const confirmDelete = async () => {
    if (!caseToDelete || !caseToDelete.id) {
      Message.error("无效的评测用例ID");
      return;
    }
    
    setDeleteLoading(true);
    try {
      const response = await deleteEvaluationCase(caseToDelete.id);
      if (response.code === 200) {
        Message.success("评测用例删除成功");
        setDeleteModalVisible(false);
        setCaseToDelete(null);
        // 如果当前页只有一条数据且不是第一页，返回上一页
        if (cases.length === 1 && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        } else {
          // 重新获取当前页数据
          await fetchCases(currentPage, pageSize, searchTitle, searchId, searchUser);
        }
      } else {
        Message.error(response.msg  || "删除失败");
      }
    } catch (error) {
      console.error("删除评测用例失败:", error);
      const errorMessage = error instanceof Error ? error.message : "删除评测用例失败";
      Message.error(errorMessage);
    } finally {
      setDeleteLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      width: 200,
      ellipsis: true,
    },
    {
      title: "标题",
      dataIndex: "title",
      width: 200,
      ellipsis: true,
    },
    {
      title: "用户",
      dataIndex: "user",
      width: 100,
      render: (user: string) => user || "-",
    },
    {
      title: "用例数量",
      dataIndex: "evaluationTasks",
      width: 100,
      render: (evaluationTasks: unknown[]) => <Text>{evaluationTasks?.length || 0}</Text>,
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      width: 180,
      render: (time: string) => <Text>{new Date(time).toLocaleString()}</Text>,
    },
    {
      title: "操作",
      width: 200,
      render: (_: unknown, record: EvaluationCase) => (
        <Space>
          <Button
            type="text"
            size="small"
            icon={<IconEye />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="text"
            size="small"
            icon={<IconEdit />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            size="small"
            status="danger"
            icon={<IconDelete />}
            onClick={() => handleDelete(record)}
            loading={deleteLoading && caseToDelete?.id === record.id}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="h-full flex flex-col">
      <PageHeader 
        title="评测集管理" 
        description="管理和维护评测集，包括评测集的创建、编辑、删除和查看等功能。"
      />
      <div className="flex-1 overflow-auto">
        <div className="mb-4 flex justify-between items-center">
          <Space>
            <Button
              type="primary"
              icon={<IconPlus />}
              onClick={handleAdd}
            >
              创建评测集
            </Button>
            <Button
              type="outline"
              icon={<IconRefresh />}
              onClick={() => fetchCases(currentPage, pageSize, searchTitle, searchId)}
              status="success"
            >
              刷新
            </Button>
          </Space>
        </div>

        <div className="mb-4">
          <Form layout="inline" onSubmit={handleSearch}>
            <FormItem field="id" label="ID">
              <Input
                placeholder="请输入ID"
                value={searchId}
                onChange={(value) => setSearchId(value)}
                style={{ width: 200 }}
              />
            </FormItem>
            <FormItem field="title" label="标题">
              <Input
                placeholder="请输入标题"
                value={searchTitle}
                onChange={(value) => setSearchTitle(value)}
                style={{ width: 200 }}
              />
            </FormItem>
            <FormItem field="user" label="用户">
              <Input
                placeholder="请输入用户名"
                value={searchUser}
                onChange={(value) => setSearchUser(value)}
                style={{ width: 200 }}
              />
            </FormItem>
            <FormItem>
              <Space>
                <Button type="primary" htmlType="submit" icon={<IconSearch />}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </FormItem>
          </Form>
        </div>

        <Table
          rowKey="id"
          columns={columns}
          data={cases}
          loading={loading}
          pagination={false}
          className="w-full"
          border={false}
          scroll={{ x: true, y: 'calc(100vh - 330px)' }}
        />

        <div className="mt-4 flex justify-between items-center">
          <Text>共 {total} 条记录</Text>
          <div className="flex items-center space-x-2">
            <Button
              size="small"
              disabled={currentPage <= 1}
              onClick={() => handlePageChange(currentPage - 1)}
            >
              上一页
            </Button>
            <Text>{currentPage}</Text>
            <Button
              size="small"
              disabled={currentPage * pageSize >= total}
              onClick={() => handlePageChange(currentPage + 1)}
            >
              下一页
            </Button>
            <Select
              value={pageSize}
              onChange={(value) => handlePageSizeChange(Number(value))}
              style={{ width: 80 }}
            >
              <Select.Option value={10}>10条/页</Select.Option>
              <Select.Option value={20}>20条/页</Select.Option>
              <Select.Option value={50}>50条/页</Select.Option>
            </Select>
          </div>
        </div>
      </div>

      {/* 删除确认模态框 */}
      <Modal
        title="删除确认"
        visible={deleteModalVisible}
        onOk={confirmDelete}
        onCancel={() => {
          setDeleteModalVisible(false);
          setCaseToDelete(null);
        }}
        confirmLoading={deleteLoading}
        okText="确认"
        cancelText="取消"
        okButtonProps={{ status: 'danger' }}
      >
        <p>确定要删除评测集 &ldquo;{caseToDelete?.title}&rdquo; 吗？此操作不可撤销。</p>
        {caseToDelete?.evaluationTasks && caseToDelete.evaluationTasks.length > 0 && (
          <p className="text-orange-500 mt-2">注意：该评测集包含 {caseToDelete.evaluationTasks.length} 个评测用例，删除后将无法恢复。</p>
        )}
      </Modal>

      {/* 查看详情模态框 */}
      <Modal
        title="评测集详情"
        visible={!!currentCase}
        onCancel={() => setCurrentCase(null)}
        footer={null}
        style={{ width: '80%' }}
      >
        {currentCase && (
          <EvaluationPreview 
            evaluationCase={currentCase}
            showId={true}
            showUser={true}
            showCreateTime={true}
          />
        )}
      </Modal>
    </div>
  );
}