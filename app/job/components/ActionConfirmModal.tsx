"use client";

import React from "react";
import { Modal } from "@arco-design/web-react";

interface ActionConfirmModalProps {
  visible: boolean;
  jobId?: string;
  actionType: 'start' | 'restart' | null;
  onConfirm: () => void;
  onCancel: () => void;
  loading: boolean;
}

export function ActionConfirmModal({ 
  visible, 
  jobId, 
  actionType, 
  onConfirm, 
  onCancel, 
  loading 
}: ActionConfirmModalProps) {
  const actionText = {
    'start': '开始执行',
    'restart': '重启任务'
  }[actionType || 'start'];

  return (
    <Modal
      title="确认操作"
      visible={visible}
      onConfirm={onConfirm}
      onCancel={onCancel}
      confirmLoading={loading}
    >
      <div>
        确定要{actionText}评测任务 &ldquo;{jobId}&rdquo; 吗？
      </div>
    </Modal>
  );
}