"use client";

import React, { useState } from "react";
import { Card, Typography, Tag, Collapse } from "@arco-design/web-react";
import { IconCaretDown, IconCaretRight } from "@arco-design/web-react/icon";
import { QueryItem, GtRule, EvaluationTask, Tool, FunctionCall } from "../api/evaluation";

const { Text } = Typography;
const { Item: CollapsePanel } = Collapse;

interface EvaluationTaskItem extends EvaluationTask {
  title: string;
}

interface EvaluationCase {
  id?: string;
  title: string;
  user?: string;
  evaluationTasks: EvaluationTaskItem[];
  createTime?: string;
}

interface EvaluationPreviewProps {
  evaluationCase: EvaluationCase;
  showId?: boolean;
  showUser?: boolean;
  showCreateTime?: boolean;
}

export default function EvaluationPreview({ 
  evaluationCase, 
  showId = false, 
  showUser = false, 
  showCreateTime = false 
}: EvaluationPreviewProps) {
  const [expandedQueries, setExpandedQueries] = useState<Record<string, boolean>>({});

  // 切换查询内容的展开/收起状态
  const toggleQueryExpansion = (taskIndex: number, queryIndex: number) => {
    const key = `${taskIndex}_${queryIndex}`;
    setExpandedQueries(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // 获取查询内容的显示状态
  const isQueryExpanded = (taskIndex: number, queryIndex: number) => {
    const key = `${taskIndex}_${queryIndex}`;
    return !!expandedQueries[key];
  };

  // 获取查询内容的预览文本（截取前100个字符）
  const getQueryPreview = (content: string) => {
    if (content.length <= 100) return content;
    return content.substring(0, 100) + "...";
  };


  return (
    <Card>
      {showId && evaluationCase.id && (
        <div className="mb-4">
          <Text className="font-medium">ID：</Text>
          <Text>{evaluationCase.id}</Text>
        </div>
      )}
      
      <div className="mb-4">
        <Text className="font-medium">标题：</Text>
        <Text>{evaluationCase.title}</Text>
      </div>
      
      {showUser && evaluationCase.user && (
        <div className="mb-4">
          <Text className="font-medium">用户：</Text>
          <Text>{evaluationCase.user}</Text>
        </div>
      )}
      
      {showCreateTime && evaluationCase.createTime && (
        <div className="mb-4">
          <Text className="font-medium">创建时间：</Text>
          <Text>{new Date(evaluationCase.createTime).toLocaleString()}</Text>
        </div>
      )}
      
      <div className="mb-4">
        <Text className="font-medium">评测用例：</Text>
        {evaluationCase.evaluationTasks.map((task, taskIndex) => (
          <div key={taskIndex} className="mt-4 p-4 bg-gray-50 rounded-lg">
            <Text className="font-medium mb-2">{task.title}</Text>
            
            <div className="mb-4">
              <Text className="font-medium">查询内容：</Text>
              <div className="mt-2">
                {task.query.map((item, queryIndex) => (
                  <div key={queryIndex} className="mb-3">
                    <div 
                      className="flex items-center mb-1 cursor-pointer hover:bg-gray-100 p-1 rounded"
                      onClick={() => toggleQueryExpansion(taskIndex, queryIndex)}
                    >
                       <div className="ml-2">
                        {isQueryExpanded(taskIndex, queryIndex) ? 
                          <IconCaretDown /> : 
                          <IconCaretRight />
                        }
                      </div>
                      <Tag color={item.role === 'user' ? 'blue' : item.role === 'assistant' ? 'green' : item.role === 'system' ? 'orange' : 'purple'}>
                        {item.role === 'user' ? '用户' : item.role === 'assistant' ? '助手' : item.role === 'system' ? '系统' : '工具'}
                      </Tag>
                      {item.tool_call_id && (
                        <Tag color="purple" className="ml-2">ID: {item.tool_call_id}</Tag>
                      )}
                    </div>
                    
                    <div className="p-2 bg-gray-100 rounded">
                      {isQueryExpanded(taskIndex, queryIndex) ? (
                        <pre className="whitespace-pre-wrap">{item.content}</pre>
                      ) : (
                        <pre className="whitespace-pre-wrap">{getQueryPreview(item.content)}</pre>
                      )}
                      
                      {/* 显示工具调用信息 */}
                      {item.tool_calls && item.tool_calls.length > 0 && (
                        <div className="mt-2">
                          <Text className="font-medium text-sm">工具调用：</Text>
                          {item.tool_calls.map((toolCall, toolCallIndex) => (
                            <div key={toolCallIndex} className="mt-1 p-2 bg-blue-50 rounded">
                              <div className="text-sm">
                                <Text className="font-medium">函数：</Text> {toolCall.function?.name}
                              </div>
                              <div className="text-sm mt-1">
                                <Text className="font-medium">参数：</Text>
                                <pre className="whitespace-pre-wrap text-xs">{toolCall.function?.arguments}</pre>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <Collapse defaultActiveKey={[]} className="mb-4">
                <CollapsePanel header="评测规则" key="rules" name="rules">
                  <div className="mt-2">
                    {task.gtRules.map((rule, index) => (
                      <div key={index} className="mb-3">
                        <div className="p-2 bg-gray-100 rounded mb-1">
                          <pre className="whitespace-pre-wrap">{rule.rule}</pre>
                        </div>
                        <div className="p-2 bg-green-50 rounded">
                          <pre className="whitespace-pre-wrap">{rule.result}</pre>
                        </div>
                      </div>
                    ))}
                  </div>
                </CollapsePanel>
              </Collapse>
            </div>

            {/* 工具配置显示部分 */}
            {task.tools && task.tools.length > 0 && (
              <div>
                <Collapse defaultActiveKey={[]} className="mb-4">
                  <CollapsePanel header="工具配置" key="tools" name="tools">
                    <div className="mt-2">
                      {task.tools.map((tool, toolIndex) => (
                        <div key={toolIndex} className="mb-3 p-3 bg-purple-50 rounded">
                          <div className="mb-2">
                            <Text className="font-medium">工具ID：</Text>
                            <Text>{tool.id || '未设置'}</Text>
                          </div>
                          <div className="mb-2">
                            <Text className="font-medium">工具类型：</Text>
                            <Text>{tool.type || 'function'}</Text>
                          </div>
                          <div className="mb-2">
                            <Text className="font-medium">函数名称：</Text>
                            <Text>{tool.function?.name || '未设置'}</Text>
                          </div>
                          {tool.function?.description && (
                            <div className="mb-2">
                              <Text className="font-medium">函数描述：</Text>
                              <Text>{tool.function.description}</Text>
                            </div>
                          )}
                          <div>
                            <Text className="font-medium">函数参数：</Text>
                            <pre className="whitespace-pre-wrap text-xs bg-white p-2 rounded mt-1">
                              {tool.function?.arguments || '{}'}
                            </pre>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CollapsePanel>
                </Collapse>
              </div>
            )}
          </div>
        ))}
      </div>
    </Card>
  );
}