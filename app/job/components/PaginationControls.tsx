"use client";

import React from "react";
import { Select, Button, Space, Typography } from "@arco-design/web-react";

const { Text } = Typography;

interface PaginationControlsProps {
  total: number;
  currentPage: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

export function PaginationControls({ 
  total, 
  currentPage, 
  pageSize, 
  onPageChange, 
  onPageSizeChange 
}: PaginationControlsProps) {
  const totalPages = Math.ceil(total / pageSize);

  return (
    <div className="mt-4 flex justify-between items-center">
      <div>
        <Text type="secondary">
          共 {total || 0} 条记录
        </Text>
      </div>
      <div className="flex items-center space-x-2">
        <Select 
          value={pageSize} 
          onChange={onPageSizeChange}
          style={{ width: 100 }}
        >
          <Select.Option value={10}>10条/页</Select.Option>
          <Select.Option value={20}>20条/页</Select.Option>
          <Select.Option value={50}>50条/页</Select.Option>
          <Select.Option value={100}>100条/页</Select.Option>
        </Select>
        <div className="flex items-center space-x-1">
          <Button 
            disabled={currentPage === 1}
            onClick={() => onPageChange(currentPage - 1)}
          >
            上一页
          </Button>
          <Text className="mx-2">
            第 {currentPage} 页，共 {totalPages} 页
          </Text>
          <Button 
            disabled={currentPage >= totalPages}
            onClick={() => onPageChange(currentPage + 1)}
          >
            下一页
          </Button>
        </div>
      </div>
    </div>
  );
}