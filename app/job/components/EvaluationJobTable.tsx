"use client";

import React from "react";
import Link from "next/link";
import { Table, Space, Button, Tag, Spin, Typography } from "@arco-design/web-react";
import { IconEye, IconDelete, IconPlayCircle, IconRefresh } from "@arco-design/web-react/icon";
import { EvaluationJob, getStatusColor, getStatusText, getGtStatusColor, getGtStatusText } from "../api/evaluation";


interface EvaluationJobTableProps {
  data: EvaluationJob[];
  loading: boolean;
  onStartJob: (job: EvaluationJob) => void;
  onViewDetail: (job: EvaluationJob) => void;
  onDeleteJob: (job: EvaluationJob) => void;
  onRestartJob: (job: EvaluationJob) => void;
  selectedRowKeys?: (string | number)[];
  onSelectionChange?: (selectedRowKeys: (string | number)[]) => void;
}

export function EvaluationJobTable({ 
  data, 
  loading, 
  onStartJob, 
  onViewDetail, 
  onDeleteJob, 
  onRestart<PERSON>ob, 
  selectedRowKeys = [], 
  onSelectionChange 
}: EvaluationJobTableProps) {
  const columns = [
    {
      title: "任务ID",
      dataIndex: "id",
      width: 120,
      render: (col: string) => (
        <div className="max-w-[120px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "用例ID",
      dataIndex: "caseId",
      width: 120,
      render: (col: string) => (
        <div className="max-w-[120px] truncate" title={col}>
          <Link href={`/job/cases?id=${col}`} className="text-blue-600 hover:text-blue-800 hover:underline">
            {col}
          </Link>
        </div>
      ),
    },
    {
      title: "标题",
      dataIndex: "title",
      width: 180,
      render: (col: string) => (
        <div className="max-w-[200px] truncate" title={col}>
          {col}
        </div>
      ),
    },
    {
      title: "用户",
      dataIndex: "user",
      width: 120,
      render: (col: string) => col || "-",
    },
    {
      title: "模型",
      dataIndex: "model",
      width: 160,
      render: (col: string) => col || "-",
    },
    {
      title: "运行次数",
      dataIndex: "runCount",
      width: 70,
      render: (col: number) => col || 1,
    },
    {
      title: "平均耗时(秒)",
      dataIndex: "avgTime",
      width: 100,
      render: (_: unknown, record: EvaluationJob) => {
        // 计算平均耗时
        if (record.subTasks && record.subTasks.length > 0) {
          const completedSubTasks = record.subTasks.filter(task => 
            task.status === 'completed' && task.modelApiCallTime
          );
          
          if (completedSubTasks.length === 0) {
            return "-";
          }
          
          const totalTime = completedSubTasks.reduce((sum, task) => 
            sum + (task.modelApiCallTime || 0), 0
          );
          
          const avgTimeMs = totalTime / completedSubTasks.length;
          return `${(avgTimeMs / 1000).toFixed(2)}`;
        }
        
        // 如果没有子任务，检查任务本身的耗时
        if (record.createTime && record.endTime) {
          const startTime = new Date(record.createTime).getTime();
          const endTime = new Date(record.endTime).getTime();
          const totalTimeMs = endTime - startTime;
          return `${(totalTimeMs / 1000).toFixed(2)}`;
        }
        
        return "-";
      },
    },
    {
      title: "50分位耗时(秒)",
      dataIndex: "medianTime",
      width: 100,
      render: (_: unknown, record: EvaluationJob) => {
        // 计算50分位耗时
        if (record.subTasks && record.subTasks.length > 0) {
          const completedSubTasks = record.subTasks.filter(task => 
            task.status === 'completed' && task.modelApiCallTime
          );
          
          if (completedSubTasks.length === 0) {
            return "-";
          }
          
          // 获取所有耗时并排序
          const times = completedSubTasks.map(task => task.modelApiCallTime || 0).sort((a, b) => a - b);
          
          // 计算中位数
          const mid = Math.floor(times.length / 2);
          const medianTimeMs = times.length % 2 !== 0 
            ? times[mid] 
            : (times[mid - 1] + times[mid]) / 2;
            
          return `${(medianTimeMs / 1000).toFixed(2)}`;
        }
        
        // 如果没有子任务，使用任务本身的耗时作为50分位
        if (record.createTime && record.endTime) {
          const startTime = new Date(record.createTime).getTime();
          const endTime = new Date(record.endTime).getTime();
          const totalTimeMs = endTime - startTime;
          return `${(totalTimeMs / 1000).toFixed(2)}`;
        }
        
        return "-";
      },
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 100,
      render: (col: string, record: EvaluationJob) => {
        const shouldShowLoading = col === 'running';
        
        // 如果有子任务，检查子任务状态
        let subTaskStatus = '';
        if (record.subTasks && record.subTasks.length > 0) {
          const runningSubTasks = record.subTasks.filter(task => task.status === 'running');
          const failedSubTasks = record.subTasks.filter(task => task.status === 'failed');
          const completedSubTasks = record.subTasks.filter(task => task.status === 'completed');
          
          if (runningSubTasks.length > 0) {
            subTaskStatus = 'running';
          } else if (failedSubTasks.length === record.subTasks.length) {
            subTaskStatus = 'failed';
          } else if (completedSubTasks.length === record.subTasks.length) {
            subTaskStatus = 'completed';
          }
        }
        
        const displayStatus = subTaskStatus || col;
        
        return (
          <div className="flex items-center space-x-1">
            {(shouldShowLoading || subTaskStatus === 'running') && (
              <Spin  />
            )}
            <Tag color={getStatusColor(displayStatus)}>{getStatusText(displayStatus)}</Tag>
          </div>
        );
      },
    },
    {
      title: "评测状态",
      dataIndex: "gtStatus",
      width: 150,
      render: (_: unknown, record: EvaluationJob) => {
        // 如果有子任务，计算子任务的评测状态
        if (record.subTasks && record.subTasks.length > 0) {
          const completedSubTasks = record.subTasks.filter(task => task.status === 'completed');
          
          if (completedSubTasks.length === 0) {
            return <Tag color="gray">-</Tag>;
          }
          
          const passedSubTasks = completedSubTasks.filter(task => {
            if (Array.isArray(task.gtStatus)) {
              return task.gtStatus.every(status => status === true);
            }
            return task.gtStatus === true;
          });
          const failedSubTasks = completedSubTasks.filter(task => {
            if (Array.isArray(task.gtStatus)) {
              return task.gtStatus.every(status => status === false);
            }
            return task.gtStatus === false;
          });
          
          if (passedSubTasks.length === completedSubTasks.length) {
            return <Tag color="green">全部通过</Tag>;
          } else if (failedSubTasks.length === completedSubTasks.length) {
            return <Tag color="red">全部不通过</Tag>;
          } else {
            return <Tag color="orange">部分通过 ({passedSubTasks.length}/{completedSubTasks.length})</Tag>;
          }
        }
        
        // 如果没有子任务，显示任务的 gtStatus
        if (Array.isArray(record.gtStatus)) {
          // 处理 gtStatus 为数组的情况
          if (record.gtStatus.length === 0) {
            return <Tag color="gray">-</Tag>;
          }
          
          const allPassed = record.gtStatus.every(status => status === true);
          const allFailed = record.gtStatus.every(status => status === false);
          
          if (allPassed) {
            return <Tag color="green">全部通过</Tag>;
          } else if (allFailed) {
            return <Tag color="red">全部不通过</Tag>;
          } else {
            const passedCount = record.gtStatus.filter(status => status === true).length;
            return <Tag color="orange">部分通过 ({passedCount}/{record.gtStatus.length})</Tag>;
          }
        }
        
        // 处理 gtStatus 为布尔值的情况（向后兼容）
        return <Tag color={getGtStatusColor(record.gtStatus !== undefined && record.gtStatus !== null ? record.gtStatus as boolean : undefined)}>
          {getGtStatusText(record.gtStatus !== undefined && record.gtStatus !== null ? record.gtStatus as boolean : undefined)}
        </Tag>;
      },
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      width: 180,
      render: (col: string) => col ? new Date(col).toLocaleString() : "-",
    },
    {
      title: "结束时间",
      dataIndex: "endTime",
      width: 180,
      render: (col: string) => col ? new Date(col).toLocaleString() : "-",
    },
    {
      title: "操作",
      width: 200,
      fixed: 'right' as const,
      render: (_: unknown, record: EvaluationJob) => (
        <div className="flex flex-col space-y-2">
          {/* 第一排按钮 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<IconPlayCircle />}
              onClick={() => onStartJob(record)}
              type="text"
              disabled={(() => {
                // 检查任务状态，只有pending状态的任务可以开始执行
                let canStart = record.status === 'pending';
                
                // 如果有子任务，检查子任务状态
                if (record.subTasks && record.subTasks.length > 0) {
                  const allPending = record.subTasks.every(task => task.status === 'pending' || !task.status);
                  canStart = canStart && allPending;
                }
                
                return !canStart;
              })()}
            >
              开始
            </Button>
            <Button 
              size="small" 
              icon={<IconRefresh />}
              onClick={() => onRestartJob(record)}
              type="text"
              status="warning"
            >
              重启
            </Button>
          </Space>
          {/* 第二排按钮 */}
          <Space size="small">
            <Button 
              size="small" 
              icon={<IconEye />}
              onClick={() => onViewDetail(record)}
              type="text"
            >
              详情
            </Button>
            <Button 
              size="small" 
              icon={<IconDelete />}
              onClick={() => onDeleteJob(record)}
              type="text"
              status="danger"
            >
              删除
            </Button>
          </Space>
        </div>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    type: 'checkbox' as const,
    selectedRowKeys,
    onChange: (newSelectedRowKeys: (string | number)[]) => {
      if (onSelectionChange) {
        onSelectionChange(newSelectedRowKeys);
      }
    },
  };

  return (
    <Table
      columns={columns}
      data={data}
      loading={loading}
      pagination={false}
      border={false}
      scroll={{ x: true, y: 'calc(100vh - 330px)' }}
      rowSelection={rowSelection}
    />
  );
}