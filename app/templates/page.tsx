"use client";

import { PageHeader } from "@/app/components/PageHeader";
import {
  Grid,
  Form,
  FormInstance,
  Input,
  Select,
  Table,
  TableColumnProps,
  Button,
  Typography,
} from "@arco-design/web-react";
import { apiClient } from "@/app/api/request";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo, useState, useRef, useEffect, useCallback } from "react";
import {
  OpsListTemplatesRequest,
  TemplateVersion,
} from "@/app/bam/aime/namespaces/ops";
import { IconPlus, IconEdit, IconEye } from "@arco-design/web-react/icon";
import { IconSettings, IconCheck, IconMinus } from "@arco-design/web-react/icon";
import TemplateViewEditModal from "../components/templates/TemplateViewEditModal";
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation'; // 导入 useRouter

const columns: TableColumnProps[] = [
  {
    title: "id",
    dataIndex: "template_id",
    width: 300,
    // render: (text) => (
    //   <Typography.Paragraph copyable ellipsis={{ rows: 1, expandable: true }}>
    //     {text}
    //   </Typography.Paragraph>
    // ),
  },
  {
    title: "name",
    dataIndex: "name",
    width: 300,
    render: (text) => (
      <Typography.Paragraph ellipsis={{ rows: 1, expandable: true }}>
        {text}
      </Typography.Paragraph>
    ),
  },
  {
    title: "status",
    dataIndex: "status",
    width: 100,
  },
  {
    title: "creator",
    dataIndex: "creator",
    width: 150,
  },
  {
    title: "session_id",
    dataIndex: "session_id",
    width: 300,
  },
  {
    title: "query_template",
    dataIndex: "prompt_content",
    width: 300,
    render: (text) => (
      <Typography.Paragraph ellipsis={{ rows: 1, expandable: true }}>
        {text}
      </Typography.Paragraph>
    ),
  },
  // {
  //   title: "exp_progress_plan",
  //   dataIndex: "plan",
  //   width: 300,
  //   render: (text) => (
  //     <Typography.Paragraph ellipsis={{ rows: 1, expandable: true }}>
  //       {text}
  //     </Typography.Paragraph>
  //   ),
  // },
  // {
  //   title: "plan_steps",
  //   dataIndex: "plan_steps",
  //   width: 300,
  //   render: (text) => (
  //     <Typography.Paragraph ellipsis={{ rows: 1, expandable: true }}>
  //       {text}
  //     </Typography.Paragraph>
  //   ),
  // },
  // {
  //   title: "exp_sop",
  //   dataIndex: "exp_sop",
  //   width: 300,
  //   render: (text) => (
  //     <Typography.Paragraph ellipsis={{ rows: 1, expandable: true }}>
  //       {text}
  //     </Typography.Paragraph>
  //   ),
  // },
  // {
  //   title: "support_mcps",
  //   dataIndex: "support_mcps",
  //   width: 300,
  //   render: (text) => (
  //     <Typography.Paragraph ellipsis={{ rows: 1, expandable: true }}>
  //       {text}
  //     </Typography.Paragraph>
  //   ),
  // },
  {
    title: "expired",
    dataIndex: "expired",
    width: 100,
    align: "center",
    render: (expired) => (
      <div title={expired ? "是" : "否"}>
        {expired ? (
          <IconCheck style={{ color: '#165dff', fontSize: 16 }} />
        ) : (
          <IconMinus style={{ color: '#86909c', fontSize: 16 }} />
        )}
      </div>
    ),
  },
  {
    title: "edited",
    dataIndex: "edited",
    width: 100,
    align: "center",
    render: (edited) => (
      <div title={edited ? "是" : "否"}>
        {edited ? (
          <IconCheck style={{ color: '#165dff', fontSize: 16 }} />
        ) : (
          <IconMinus style={{ color: '#86909c', fontSize: 16 }} />
        )}
      </div>
    ),
  },
  {
    title: "SOP",
    dataIndex: "exp_sop",
    width: 100,
    align: "center",
    render: (expSop) => {
      let hasSop = false;
      try {
        if (expSop) {
          const expSopData = JSON.parse(expSop);
          hasSop = expSopData != null && expSopData.name != null && expSopData.name !== "";
        }
      } catch (error) {
        // 如果解析失败，认为没有SOP
        hasSop = false;
      }
      
      return (
        <div title={hasSop ? "是" : "否"}>
          {hasSop ? (
            <IconCheck style={{ color: '#165dff', fontSize: 16 }} />
          ) : (
            <IconMinus style={{ color: '#86909c', fontSize: 16 }} />
          )}
        </div>
      );
    },
  },
  {
    title: "创建时间",
    dataIndex: "created_at",
    width: 180,
    render: (text) => {
      if (!text) return '-';
      try {
        const date = new Date(text);
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
      } catch (error) {
        return text;
      }
    },
  },
];

export default function TemplateOpsPage() {
  const [requestData, setRequestData] = useState<OpsListTemplatesRequest>({});
  const [visible, setVisible] = useState(false);
  const [targetItem, setTargetItem] = useState<TemplateVersion | undefined>();
  const [isEdit, setIsEdit] = useState(false);
  const searchParams = useSearchParams(); // 获取 URL 参数
  // 修改为 FormInstance 类型
  const formRef = useRef<FormInstance>(null);
  const debounceTimeoutRef = useRef<ReturnType<typeof setTimeout> | undefined>(undefined);
  const router = useRouter(); // 获取 router 实例

  // 在组件挂载时从 URL 参数中读取数据并设置到表单
  useEffect(() => {
    const params: OpsListTemplatesRequest = {};
    const keys: (keyof OpsListTemplatesRequest)[] = ['name', 'template_id', 'share_id', 'run_session_id', 'session_id'];
    keys.forEach(key => {
      const value = searchParams.get(key);
      if (value) {
        params[key] = value as any;
      }
    });
    setRequestData(params);
    // 使用 setFieldsValue 方法将参数填入表单
    if (formRef.current) {
      formRef.current.setFieldsValue(params);
    }
  }, [searchParams]);

  useEffect(() => {
    // 组件卸载时清除定时器
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

   
  const onFormChange = useCallback(
    (_changeValue: any, values: OpsListTemplatesRequest) => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      debounceTimeoutRef.current = setTimeout(() => {
        const newRequestData = Object.entries(values).reduce(
          (acc, [key, value]) => {
            if (value !== "") {
              acc[key as keyof OpsListTemplatesRequest] = value;
            }
            return acc;
          },
          {} as OpsListTemplatesRequest
        );
        setRequestData(newRequestData);

        // 更新 URL 查询参数
        const searchParams = new URLSearchParams();
        Object.entries(newRequestData).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.set(key, String(value));
          }
        });
        const newUrl = `?${searchParams.toString()}`;
        router.push(newUrl, { scroll: false });
      }, 800);
    },
    [router] // 把 router 添加到依赖项数组
  );

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["opsTemplate", requestData],
    queryFn: async () => {
      const response = await apiClient.OpsListTemplates(requestData);
      return response.templates || [];
    },
  });

  const tableData = useMemo(() => {
    return data?.map((item) => {
      return {
        ...item,
        key: item.template_id,
        plan_steps: JSON.stringify(item.plan_steps, null, 2),
        exp_sop: JSON.stringify(item.exp_sop, null, 2),
        support_mcps: JSON.stringify(item.support_mcps, null, 2),
        // 直接使用布尔值，由 render 函数处理显示
        edited: item.edited,
        expired: item.expired,
      };
    });
  }, [data]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleTableItemViewClick = (item: any) => {
    const target = data?.find((i) => i.template_id === item.template_id);
    if (target) {
      setTargetItem(target);
    }
    setIsEdit(false);
    setVisible(true);
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleTableItemEditClick = (item: any) => {
    const target = data?.find((i) => i.template_id === item.template_id);
    if (target) {
      setTargetItem(target);
    }
    setIsEdit(true);
    setVisible(true);
  };

  const handleOk = () => {
    setVisible(false);
    refetch();
  };

  return (
    <div>
      <PageHeader title="模版管理" />
      <Form ref={formRef} layout="vertical" className="mt-6" onValuesChange={onFormChange}>
        <Grid.Row gutter={18}>
          <Grid.Col span={6}>
            <Form.Item label="Name" field="name">
              <Input placeholder="请输入名称" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="TemplateID" field="template_id">
              <Input placeholder="请输入 ID" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="ShareID" field="share_id">
              <Input placeholder="请输入 ShareID" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="RunSessionID" field="run_session_id">
              <Input placeholder="请输入 RunSessionID" allowClear />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item label="SessionID" field="session_id">
              <Input placeholder="请输入 SessionID" allowClear />
            </Form.Item>
          </Grid.Col>
        </Grid.Row>
      </Form>
      <Table
        columns={columns.concat({
          title: "Operation",
          dataIndex: "operation",
          render: (col, item) => (
            <div className="flex gap-2">
              <Button
                type="secondary"
                size="mini"
                icon={<IconEye />}
                title="查看模板"
                onClick={() => handleTableItemViewClick(item)}
              />
              <Button
                type="primary"
                size="mini"
                icon={<IconEdit />}
                title="编辑模板"
                onClick={() => handleTableItemEditClick(item)}
              />
            </div>
          ),
          fixed: "right",
          width: 120,
        })}
        scroll={{
          x: 800,
          y: 600,
        }}
        pagination={false}
        data={tableData}
        loading={isLoading}
      />
      <TemplateViewEditModal
        visible={visible}
        data={targetItem}
        isEdit={isEdit}
        onCancel={() => setVisible(false)}
        onOk={handleOk}
      />
    </div>
  );
}


