"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useStore } from "@nanostores/react";
import { authErrorMessageAtom, redirectInProgressAtom } from "../store/auth";
import { motion } from "framer-motion";
import { Button } from "@radix-ui/themes";
import { HomeIcon } from "lucide-react";

export default function NoPermissionPage() {
  const authErrorMessage = useStore(authErrorMessageAtom);
  const router = useRouter();

  useEffect(() => {
    redirectInProgressAtom.set(false);
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 p-4">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="p-8 bg-white rounded-xl shadow-lg text-center max-w-md w-full border border-gray-100"
      >
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-6"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-20 h-20 text-red-500 mx-auto"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"
            />
          </svg>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="text-2xl font-bold mb-4 text-gray-800"
        >
          无权访问
        </motion.h2>
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="text-gray-600 mb-8 leading-relaxed"
        >
          {authErrorMessage || "您没有权限访问此页面或资源。"}
        </motion.p>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="mt-6"
        >
          <Button
            size="3"
            variant="solid"
            color="green"
            onClick={() => router.push("/")}
            className="mx-auto py-1"
          >
            <HomeIcon size={18} />
            返回首页
          </Button>
        </motion.div>
      </motion.div>
    </div>
  );
}
