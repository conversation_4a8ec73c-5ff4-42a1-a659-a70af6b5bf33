import { useInfiniteQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { apiClient } from "../api/request";
import { AgentConfig } from "../bam/aime/namespaces/agent";

interface UseInfiniteAgentConfigsOptions {
  agentId?: string;
  searchTerm?: string;
  pageSize?: number;
  enabled?: boolean;
}

/**
 * 支持搜索和无限加载的AgentConfigs Hook
 * 使用React Query的useInfiniteQuery实现无限滚动加载
 */
export const useInfiniteAgentConfigs = ({
  agentId,
  searchTerm = "",
  pageSize = 20,
  enabled = true,
}: UseInfiniteAgentConfigsOptions = {}) => {
  const queryResult = useInfiniteQuery({
    queryKey: ["infiniteAgentConfigs", agentId, searchTerm, pageSize],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await apiClient.ListAgentConfigs({
        agent_id: agentId,
        page_num: pageParam,
        page_size: pageSize,
        name: searchTerm || undefined, // 只有在有搜索词时才传递name参数
      });
      return {
        agentConfigs: response.agent_configs || [],
        total: response.total || 0,
        currentPage: pageParam,
        hasMore: (response.agent_configs?.length || 0) >= pageSize,
        agent: response.agent, // 保留agent信息
      };
    },
    getNextPageParam: (lastPage) => {
      // 如果最后一页的数据量等于pageSize，说明可能还有更多数据
      return lastPage.hasMore ? lastPage.currentPage + 1 : undefined;
    },
    initialPageParam: 1,
    enabled: Boolean(agentId) && enabled,
    staleTime: 1000 * 60 * 5, // 5分钟内不重新请求
  });

  // 将所有页面的agentConfigs数据合并成一个数组
  const allAgentConfigs = useMemo(() => {
    return queryResult.data?.pages.flatMap((page) => page.agentConfigs) || [];
  }, [queryResult.data]);

  // 获取总数（从第一页获取）
  const total = useMemo(() => {
    return queryResult.data?.pages[0]?.total || 0;
  }, [queryResult.data]);

  // 获取agent信息（从第一页获取）
  const agent = useMemo(() => {
    return queryResult.data?.pages[0]?.agent;
  }, [queryResult.data]);

  return {
    ...queryResult,
    agentConfigs: allAgentConfigs,
    total,
    agent,
    hasNextPage: queryResult.hasNextPage,
    fetchNextPage: queryResult.fetchNextPage,
    isFetchingNextPage: queryResult.isFetchingNextPage,
  };
};

/**
 * 获取所有AgentConfigs的Hook（用于自动关联功能）
 * 支持搜索功能，但默认加载更多数据以保持兼容性
 */
export const useAllAgentConfigs = (searchTerm?: string) => {
  const { agentConfigs, isLoading, error, refetch } = useInfiniteAgentConfigs({
    searchTerm,
    pageSize: 100, // 保持较大的pageSize以减少请求次数
  });

  return {
    data: agentConfigs,
    isLoading,
    error,
    refetch,
  };
};

/**
 * 获取特定Agent的AgentConfigs的Hook
 * 支持搜索和无限加载
 */
export const useAgentConfigsByAgent = (agentId?: string, searchTerm?: string) => {
  const { agentConfigs, isLoading, error, refetch, agent } = useInfiniteAgentConfigs({
    agentId,
    searchTerm,
    pageSize: 50, // 单个Agent的配置通常不会太多
    enabled: !!agentId, // 只有在有agentId时才启用查询
  });

  return {
    data: agentConfigs,
    agent,
    isLoading,
    error,
    refetch,
  };
};