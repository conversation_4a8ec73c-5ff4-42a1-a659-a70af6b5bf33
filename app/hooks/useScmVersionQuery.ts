import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@/app/api/request";
import { GetScmVersionRequest, ScmVersion } from "@/app/bam/aime/namespaces/deploy";
import { AgentConfigType } from "@/app/bam/aime/namespaces/agent";

interface UseScmVersionQueryOptions {
  searchValue?: string;
  type?: string | null;
  enabled?: boolean;
}

/**
 * 自定义 hook，使用 react-query 获取 ScmVersion 数据并缓存
 * 
 * @param options 查询选项
 * @returns react-query 查询结果
 */
export function useScmVersionQuery({
  searchValue = "",
  type = null,
  enabled = true,
}: UseScmVersionQueryOptions = {}) {
  return useQuery({
    queryKey: ["scmVersions", type, searchValue],
    queryFn: async () => {
      let req: GetScmVersionRequest = {};
      
      // 根据类型设置请求参数
      if (type === AgentConfigType.AgentConfigTypeBase) {
        req = {
          type_list: 'online',
          branch: 'master'
        };
      } else if (type === AgentConfigType.AgentConfigTypeAbTest) {
        req = {
          type_list: 'online,test',
        };
      }

      // 添加搜索参数
      if (searchValue) {
        req.version = searchValue;
      }

      // 如果是基础配置类型且没有搜索值，直接返回空数组
      // 这是为了匹配原组件中的逻辑
      if (type === AgentConfigType.AgentConfigTypeBase && !searchValue) {
        return { scm_versions: [] };
      }

      try {
        const response = await apiClient.GetScmVersion(req);
        return response;
      } catch (error) {
        console.error("获取二进制源列表失败:", error);
        // 抛出错误，让 react-query 处理错误状态
        throw error;
      }
    },
    enabled,
    staleTime: 1000 * 60 * 5, // 5分钟内数据不会过期
    retry: 1, // 失败时最多重试1次
  });
}
