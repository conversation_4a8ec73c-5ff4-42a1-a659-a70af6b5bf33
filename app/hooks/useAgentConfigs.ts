import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@/app/api/request";
import { AgentConfigType } from "@/app/bam/aime/namespaces/agent";

interface UseAgentConfigsOptions {
  agentId?: string;
  pageNum?: number;
  pageSize?: number;
  type?: string;
}

export function useAgentConfigs({
  agentId,
  pageNum = 1,
  pageSize = 10,
  type,
}: UseAgentConfigsOptions = {}) {
  return useQuery({
    queryKey: ["agentConfigs", agentId, pageNum, pageSize, type],
    queryFn: async () => {
      const response = await apiClient.ListAgentConfigs({
        agent_id: agentId === "all" ? undefined : agentId,
        page_num: pageNum,
        page_size: pageSize,
        type,
      });
      return response;
    },
  });
}

export function useBaseConfigs(agentId?: string, pageSize: number = 100) {
  return useAgentConfigs({
    agentId,
    pageSize,
    type: AgentConfigType.AgentConfigTypeBase,
  });
}

export function useOtherConfigs(agentId?: string, pageNum?: number, pageSize: number = 10) {
  return useAgentConfigs({
    agentId,
    pageNum,
    pageSize,
    type: `${AgentConfigType.AgentConfigTypeFeature},${AgentConfigType.AgentConfigTypeUser}`,
  });
} 

export function useAbConfigs(agentId?: string, pageNum?: number, pageSize: number = 10) {
  return useAgentConfigs({
    agentId,
    pageNum,
    pageSize,
    type: AgentConfigType.AgentConfigTypeAbTest,
  });
} 