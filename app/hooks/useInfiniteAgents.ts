import { useInfiniteQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { apiClient } from "../api/request";
import { Agent } from "../bam/aime/namespaces/agent";

interface UseInfiniteAgentsOptions {
  searchTerm?: string;
  pageSize?: number;
  enabled?: boolean;
}

/**
 * 支持搜索和无限加载的Agents Hook
 * 使用React Query的useInfiniteQuery实现无限滚动加载
 */
export const useInfiniteAgents = ({
  searchTerm = "",
  pageSize = 20,
  enabled = true,
}: UseInfiniteAgentsOptions = {}) => {
  const queryResult = useInfiniteQuery({
    queryKey: ["infiniteAgents", searchTerm, pageSize],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await apiClient.ListAgents({
        page_num: pageParam,
        page_size: pageSize,
        name: searchTerm || undefined, // 只有在有搜索词时才传递name参数
      });
      return {
        agents: response.agents || [],
        total: response.total || 0,
        currentPage: pageParam,
        hasMore: (response.agents?.length || 0) === pageSize,
      };
    },
    getNextPageParam: (lastPage) => {
      // 如果最后一页的数据量等于pageSize，说明可能还有更多数据
      return lastPage.hasMore ? lastPage.currentPage + 1 : undefined;
    },
    initialPageParam: 1,
    enabled,
    staleTime: 1000 * 60 * 5, // 5分钟内不重新请求
  });

  // 将所有页面的agents数据合并成一个数组
  const allAgents = useMemo(() => {
    return queryResult.data?.pages.flatMap((page) => page.agents) || [];
  }, [queryResult.data]);

  // 获取总数（从第一页获取）
  const total = useMemo(() => {
    return queryResult.data?.pages[0]?.total || 0;
  }, [queryResult.data]);

  return {
    ...queryResult,
    agents: allAgents,
    total,
    hasNextPage: queryResult.hasNextPage,
    fetchNextPage: queryResult.fetchNextPage,
    isFetchingNextPage: queryResult.isFetchingNextPage,
  };
};

/**
 * 简化版的useInfiniteAgents，返回扁平化的agents数组
 * 主要用于替换现有的useAgents hook
 */
export const useAgentsWithSearch = (searchTerm?: string) => {
  const { agents, isLoading, error, refetch } = useInfiniteAgents({
    searchTerm,
    pageSize: 100, // 保持较大的pageSize以减少请求次数
  });

  return {
    data: agents,
    isLoading,
    error,
    refetch,
  };
};