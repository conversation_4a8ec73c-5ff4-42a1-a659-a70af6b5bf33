import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../api/request";
import { Agent } from "../bam/aime/namespaces/agent";

interface UseAgentsOptions {
  pageSize?: number;
  searchTerm?: string;
}

/**
 * 获取Agents列表的Hook
 * @param options - 可选参数
 * @param options.pageSize - 每页数量，默认200（移除了硬编码的100限制）
 * @param options.searchTerm - 搜索关键词
 */
export const useAgents = (options: UseAgentsOptions = {}) => {
  const { pageSize = 100, searchTerm } = options;
  
  return useQuery<Agent[]>({
    queryKey: ["agents", pageSize, searchTerm],
    queryFn: async () => {
      const response = await apiClient.ListAgents({
        page_num: 1,
        page_size: pageSize,
        name: searchTerm || undefined, // 支持按名称搜索
      });
      return response.agents || [];
    },
    staleTime: 1000 * 60 * 5, // 5分钟内不重新请求
  });
};