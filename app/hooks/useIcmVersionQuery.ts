import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@/app/api/request";
import { GetIcmVersionRequest, IcmVersion } from "@/app/bam/aime/namespaces/deploy";
import { AgentConfigType } from "@/app/bam/aime/namespaces/agent";

interface UseIcmVersionQueryOptions {
  searchValue?: string;
  type?: string | null;
  enabled?: boolean;
  imageType?: string;
}

/**
 * 自定义 hook，使用 react-query 获取 IcmVersion 数据并缓存
 * 
 * @param options 查询选项
 * @returns react-query 查询结果
 */
export function useIcmVersionQuery({
  searchValue = "",
  type = null,
  enabled = true,
  imageType = "",
}: UseIcmVersionQueryOptions = {}) {
  return useQuery({
    queryKey: ["icmVersions", type, searchValue, imageType],
    queryFn: async () => {
      const req: GetIcmVersionRequest = {
        config_type: type as string, 
      };
      
      // // 根据类型设置请求参数
      // if (type === AgentConfigType.AgentConfigTypeBase) {
      //   req = {
      //     specific_tag: 'master',
      //   };
      // }

      // 添加搜索参数
      if (searchValue) {
        req.version = searchValue;
      }

      if (imageType) {
        req.image_type = imageType;
      }

      try {
        const response = await apiClient.GetIcmVersion(req);
        return response;
      } catch (error) {
        console.error("获取镜像列表失败:", error);
        // 抛出错误，让 react-query 处理错误状态
        throw error;
      }
    },
    enabled,
    staleTime: 1000 * 60 * 5, // 5分钟内数据不会过期
    retry: 1, // 失败时最多重试1次
  });
}